'use server'

// This file is for App Router only
// For Pages Router, use server-pages.ts instead
import { createServerClient as createSupabaseServerClient } from "@supabase/ssr"
import type { Database } from "@/lib/database.types"
import { cookies } from "next/headers"

// Create a server client that correctly uses cookies for authentication
export async function createServerClient() {
  // This function is only for use in App Router
  // It will throw an error if used in Pages Router
  if (typeof window !== 'undefined') {
    throw new Error('createServerClient should only be used in server components')
  }

  try {
    // Log the environment variables for debugging
    console.log('Server: Supabase URL:', process.env.NEXT_PUBLIC_SUPABASE_URL)
    console.log('Server: Supabase Key exists:', !!process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY)

    // Get the cookies store - await in Next.js 15
    const cookieStore = await cookies()

    // Create the Supabase client with proper cookie handling
    return createSupabaseServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get(name) {
            try {
              // Get the actual cookies from the cookie store
              return cookieStore.get(name)?.value
            } catch (error) {
              console.error(`Error getting cookie "${name}":`, error)
              return undefined
            }
          },
          set(name, value, options) {
            try {
              // Set the cookie with the provided options
              cookieStore.set(name, value, options)
            } catch (error) {
              // Log any errors but don't throw
              console.error(`Error setting cookie "${name}":`, error)
            }
          },
          remove(name, options) {
            try {
              // Remove the cookie with the provided options
              cookieStore.delete(name, options)
            } catch (error) {
              // Log any errors but don't throw
              console.error(`Error removing cookie "${name}":`, error)
            }
          },
        },
      }
    )
  } catch (error) {
    console.error('Error creating server client:', error)
    // Return a fallback client without cookies
    return createSupabaseServerClient<Database>(
      process.env.NEXT_PUBLIC_SUPABASE_URL!,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!,
      {
        cookies: {
          get: () => undefined,
          set: () => {},
          remove: () => {},
        },
      }
    )
  }
}
