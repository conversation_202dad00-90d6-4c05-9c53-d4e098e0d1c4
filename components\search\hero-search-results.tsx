"use client"

import { useState, useEffect, useRef } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { motion } from "@/lib/motion-stub"
import UniversalSearch from "@/components/search/universal-search"
import SearchContainer from "@/components/search/search-container"
import SearchButton from "@/components/search/search-button"
import { useDebounce } from "@/hooks/use-debounce"
import ToolCard from "@/components/tool-card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { Skeleton } from "@/components/ui/skeleton"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  AlertCircle,
  Search as SearchIcon,
  Lightbulb,
  RefreshCw,
  Clock,
  Sparkles,
  TrendingUp,
  X
} from "lucide-react"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { quickSearch, SearchResult } from "@/lib/search/search-service"

interface SearchResultsProps {
  searchParams: {
    q?: string
    categories?: string
    pricing?: string
    verified?: string
    featured?: string
    free?: string
  }
}

interface Tool {
  id: number
  company_name: string | null
  short_description: string | null
  visit_website_url: string | null
  logo_url: string | null
  primary_task: string | null
  pricing: string | null
  is_verified: boolean | null
  is_featured: boolean | null
  click_count: number | null
  slug?: string | null
  full_description?: string | null
}

interface Category {
  id: string
  name: string
  count: number
}

export default function HeroSearchResults({ searchParams }: SearchResultsProps) {
  const router = useRouter()
  const [tools, setTools] = useState<Tool[]>([])
  const [allTools, setAllTools] = useState<Tool[]>([]) // Store all tools for filtering
  const [suggestions, setSuggestions] = useState<Tool[]>([])
  const [relatedCategories, setRelatedCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const [searchDuration, setSearchDuration] = useState(0)
  const [categories, setCategories] = useState<Category[]>([])
  const [pricingOptions, setPricingOptions] = useState<string[]>([])
  const [currentSearchQuery, setCurrentSearchQuery] = useState("")
  const [showSearchHint, setShowSearchHint] = useState(false)
  const searchRef = useRef<HTMLDivElement>(null)

  const {
    q: query = "",
    categories: categoriesParam = "",
    pricing = "all",
    verified,
    featured,
    free
  } = searchParams

  // Handle result selection
  const handleResultSelect = (result: any) => {
    if (result.slug) {
      router.push(`/Tool/${result.slug}`)
    }
  }

  // Handle click outside to close search - removed since UniversalSearch handles this internally

  // Debounced search query for instant filtering
  const debouncedSearchQuery = useDebounce(currentSearchQuery, 300)

  // Handle instant filtering for the universal search
  const handleInstantFilter = (searchQuery: string) => {
    setCurrentSearchQuery(searchQuery)

    // Don't update URL immediately for instant filtering - only update on form submission
    // This prevents URL pollution while typing
  }

  // Parse filters
  const selectedCategories = categoriesParam ? categoriesParam.split(',') : []
  const hasActiveFilters = selectedCategories.length > 0 ||
                          pricing !== "all" ||
                          verified === "true" ||
                          featured === "true" ||
                          free === "true"

  // Enhanced instant filtering function
  const filterToolsInstantly = (searchTerm: string, toolsList: Tool[]) => {
    if (!searchTerm.trim()) {
      return toolsList
    }

    const searchLower = searchTerm.toLowerCase().trim()
    return toolsList.filter(tool => {
      // Search in multiple fields for better results
      const nameMatch = tool.company_name?.toLowerCase().includes(searchLower)
      const descriptionMatch = tool.short_description?.toLowerCase().includes(searchLower)
      const categoryMatch = tool.primary_task?.toLowerCase().includes(searchLower)
      const fullDescriptionMatch = tool.full_description?.toLowerCase().includes(searchLower)

      return nameMatch || descriptionMatch || categoryMatch || fullDescriptionMatch
    })
  }

  // Handle search button click and form submission
  const handleSearch = (searchTerm: string) => {
    if (searchTerm.trim()) {
      setCurrentSearchQuery(searchTerm.trim())

      // Update URL
      const newParams = new URLSearchParams(window.location.search)
      newParams.set('q', searchTerm.trim())
      const newUrl = `${window.location.pathname}?${newParams.toString()}`
      window.history.replaceState({}, '', newUrl)

      // Trigger search
      performSearchWithQuery(searchTerm.trim())
    }
  }

  // Load categories and pricing options
  useEffect(() => {
    const loadFilterOptions = async () => {
      try {
        const supabase = createBrowserClient()

        // Get categories
        const { data: categoriesData } = await supabase
          .from('tools')
          .select('primary_task')
          .not('primary_task', 'is', null)

        if (categoriesData) {
          const categoryMap = new Map<string, number>()
          categoriesData.forEach(item => {
            if (item.primary_task) {
              categoryMap.set(item.primary_task, (categoryMap.get(item.primary_task) || 0) + 1)
            }
          })

          const categoryList: Category[] = Array.from(categoryMap.entries()).map(([name, count]) => ({
            id: name.toLowerCase().replace(/\s+/g, '-'),
            name,
            count
          }))

          setCategories(categoryList)
        }

        // Get pricing options
        const { data: pricingData } = await supabase
          .from('tools')
          .select('pricing')
          .not('pricing', 'is', null)

        if (pricingData) {
          const pricingSet = new Set<string>()
          pricingData.forEach(item => {
            if (item.pricing) {
              pricingSet.add(item.pricing)
            }
          })
          setPricingOptions(Array.from(pricingSet))
        }
      } catch (err) {
        // Error handled silently
      }
    }

    loadFilterOptions()
  }, [])

  // Optimized search function with better performance
  const performSearchWithQuery = async (searchQuery: string) => {
    setIsLoading(true)
    setError(null)
    const startTime = Date.now()

    try {
      const searchFilters = {
        category: selectedCategories.length > 0 ? selectedCategories[0] : undefined,
        pricing: pricing !== "all" ? pricing : undefined,
        features: []
      }

      if (searchQuery.trim()) {
        // Use optimized quickSearch with larger limit for search results page
        const { data: searchResults, error } = await quickSearch(searchQuery.trim(), 24, searchFilters)

        if (error) {
          throw new Error(`Search error: ${error.message || 'Unknown error'}`)
        }

        // Transform SearchResult[] to Tool[] - simplified
        const transformedTools: Tool[] = (searchResults || []).map(result => ({
          id: result.id,
          company_name: result.company_name,
          short_description: result.short_description,
          visit_website_url: result.visit_website_url,
          logo_url: result.logo_url,
          primary_task: result.primary_task,
          pricing: result.pricing,
          is_verified: result.is_verified,
          is_featured: result.is_featured,
          click_count: result.click_count,
          slug: result.slug,
          full_description: result.full_description
        }))

        setAllTools(transformedTools)
        setTools(transformedTools)

        // Only get suggestions if no results found
        if (transformedTools.length === 0) {
          await getNoResultsSuggestions()
        } else {
          // Clear suggestions immediately for better performance
          setSuggestions([])
          setRelatedCategories([])
        }
      } else {
        // No query - get popular/featured tools
        await getPopularToolsFallback()
      }

      setSearchDuration(Date.now() - startTime)
    } catch (err) {
      setError(err instanceof Error ? err.message : "Search failed")
      await getPopularToolsFallback()
    } finally {
      setIsLoading(false)
    }
  }

  // Search function using current search query
  const performSearch = async () => {
    return performSearchWithQuery(currentSearchQuery)
  }

  // Get suggestions when no results found - optimized
  const getNoResultsSuggestions = async () => {
    try {
      const supabase = createBrowserClient()

      // Get popular tools as suggestions with proper fields
      const { data: suggestionsData } = await supabase
        .from('tools')
        .select(`
          id,
          company_name,
          short_description,
          visit_website_url,
          logo_url,
          primary_task,
          pricing,
          is_verified,
          is_featured,
          click_count,
          slug,
          full_description
        `)
        .order('is_featured', { ascending: false })
        .order('click_count', { ascending: false })
        .limit(8)

      // Transform to Tool[] format
      const transformedSuggestions: Tool[] = (suggestionsData || []).map(result => ({
        id: result.id,
        company_name: result.company_name,
        short_description: result.short_description,
        visit_website_url: result.visit_website_url,
        logo_url: result.logo_url,
        primary_task: result.primary_task,
        pricing: result.pricing,
        is_verified: result.is_verified,
        is_featured: result.is_featured,
        click_count: result.click_count,
        slug: result.slug,
        full_description: result.full_description
      }))

      setSuggestions(transformedSuggestions)

      // Get related categories based on search query
      if (currentSearchQuery.trim()) {
        const { data: categoriesData } = await supabase
          .from('tools')
          .select('primary_task')
          .ilike('primary_task', `%${currentSearchQuery.trim()}%`)
          .not('primary_task', 'is', null)
          .limit(6)

        if (categoriesData) {
          const categoryMap = new Map<string, number>()
          categoriesData.forEach(item => {
            if (item.primary_task) {
              categoryMap.set(item.primary_task, (categoryMap.get(item.primary_task) || 0) + 1)
            }
          })

          const relatedCats: Category[] = Array.from(categoryMap.entries()).map(([name, count]) => ({
            id: name.toLowerCase().replace(/\s+/g, '-'),
            name,
            count
          }))

          setRelatedCategories(relatedCats)
        }
      }
    } catch (err) {
      console.error('Error getting suggestions:', err)
      // Fallback to empty arrays
      setSuggestions([])
      setRelatedCategories([])
    }
  }



  // Optimized fallback for errors and no-query scenarios
  const getPopularToolsFallback = async () => {
    try {
      const supabase = createBrowserClient()
      const { data: popularData } = await supabase
        .from('tools')
        .select(`
          id,
          company_name,
          short_description,
          visit_website_url,
          logo_url,
          primary_task,
          pricing,
          is_verified,
          is_featured,
          click_count,
          slug,
          full_description
        `)
        .order('is_featured', { ascending: false })
        .order('click_count', { ascending: false })
        .limit(16)

      const transformedTools: Tool[] = (popularData || []).map(result => ({
        id: result.id,
        company_name: result.company_name,
        short_description: result.short_description,
        visit_website_url: result.visit_website_url,
        logo_url: result.logo_url,
        primary_task: result.primary_task,
        pricing: result.pricing,
        is_verified: result.is_verified,
        is_featured: result.is_featured,
        click_count: result.click_count,
        slug: result.slug,
        full_description: result.full_description
      }))

      setAllTools(transformedTools)
      setTools(transformedTools)
      setSuggestions([])
      setRelatedCategories([])
    } catch (fallbackErr) {
      console.error('Fallback error:', fallbackErr)
    }
  }

  // Single effect to handle all search triggers - optimized
  useEffect(() => {
    // Initialize search query from URL
    setCurrentSearchQuery(query)

    // Perform search when URL params change
    if (query || hasActiveFilters) {
      performSearchWithQuery(query)
    } else {
      // No search query and no filters - show popular tools
      getPopularToolsFallback()
    }
  }, [query, categoriesParam, pricing, verified, featured, free])

  // Effect for instant filtering - works when user is typing in search bar
  useEffect(() => {
    if (allTools.length > 0) {
      // Apply instant filtering based on current search query
      const filteredTools = filterToolsInstantly(debouncedSearchQuery, allTools)
      setTools(filteredTools)
    }
  }, [debouncedSearchQuery, allTools])

  // Show search hint when user is actively searching
  useEffect(() => {
    if (currentSearchQuery.length > 0 && currentSearchQuery !== query) {
      setShowSearchHint(true)
      const timer = setTimeout(() => setShowSearchHint(false), 3000) // Reduced to 3 seconds
      return () => clearTimeout(timer)
    } else {
      setShowSearchHint(false)
    }
  }, [currentSearchQuery, query])

  const title = currentSearchQuery
    ? `Search results for "${currentSearchQuery}"`
    : hasActiveFilters
      ? "Filtered AI Tools"
      : "Discover AI Tools"

  return (
    <div className="space-y-6 sm:space-y-8">
      {/* Dynamic Search Hint - Shows when user is searching */}
      {showSearchHint && currentSearchQuery.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="fixed top-16 sm:top-20 left-0 right-0 z-40 bg-gradient-to-r from-blue-500/95 to-purple-500/95 backdrop-blur-xl text-white py-3 px-4 shadow-xl border-b border-white/20"
        >
          <div className="container mx-auto px-4 text-center">
            <div className="flex items-center justify-center gap-2 text-sm sm:text-base">
              <SearchIcon className="h-4 w-4 flex-shrink-0 animate-pulse" />
              <span className="font-medium truncate">
                Filtering results for "{currentSearchQuery}"
              </span>
              <span className="hidden sm:inline text-xs opacity-80">
                • Press Enter to search database
              </span>
              <button
                onClick={() => setShowSearchHint(false)}
                className="ml-2 hover:bg-white/20 rounded-full p-1 transition-colors flex-shrink-0"
                aria-label="Close hint"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          </div>
        </motion.div>
      )}
      {/* Hero Search Section */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ duration: 0.6 }}
        className="relative"
      >
        {/* Background Gradient */}
        <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-blue-950/20 dark:via-background dark:to-purple-950/20 rounded-2xl sm:rounded-3xl -z-10" />

        <div className="relative bg-white/80 dark:bg-card/80 backdrop-blur-sm rounded-2xl sm:rounded-3xl border shadow-xl p-4 sm:p-6 lg:p-8">
          <div className="text-center mb-6 sm:mb-8">
            <motion.h1
              className="text-2xl sm:text-3xl md:text-4xl lg:text-5xl font-bold bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent mb-3 sm:mb-4"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
            >
              {title}
            </motion.h1>
            <motion.p
              className="text-sm sm:text-base lg:text-lg text-muted-foreground max-w-2xl mx-auto px-2"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.3 }}
            >
              Use our advanced search and filters to find the perfect AI tools for your needs
            </motion.p>
          </div>

          {/* Search Component - Matching tools/categories page design */}
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: 0.4 }}
          >
            <div className="relative" ref={searchRef}>
              <SearchContainer maxWidth="3xl">
                <div className="flex items-center p-3">
                  <div className="flex-1">
                    <UniversalSearch
                      mode="hybrid"
                      context="general"
                      variant="hero"
                      size="lg"
                      placeholder="Search AI tools..."
                      className="w-full"
                      rounded="full"
                      glass={false}
                      showKeyboardShortcut={false}
                      fullWidth={true}
                      showInstantResults={true}
                      maxResults={8}
                      autoFocus={false}
                      showSearchButton={false}
                      initialValue={currentSearchQuery}
                      onInstantFilter={handleInstantFilter}
                      onSearch={handleSearch}
                      onResultSelect={handleResultSelect}
                    />
                  </div>

                  {/* Search Button */}
                  <div className="flex-shrink-0 ml-3">
                    <SearchButton
                      onClick={() => handleSearch(currentSearchQuery)}
                      size="md"
                      showText={true}
                      text="Search"
                    />
                  </div>
                </div>
              </SearchContainer>

              {/* Search Guidance */}
              <div className="mt-3 text-center">
                <p className="text-xs sm:text-sm text-muted-foreground flex items-center justify-center gap-2">
                  <Sparkles className="h-3 w-3 text-blue-500" />
                  <span>Start typing to see instant results</span>
                  <span className="hidden sm:inline">• Press Enter or click Search for full results</span>
                </p>
              </div>
            </div>
          </motion.div>
        </div>
      </motion.div>

      {/* Loading State */}
      {isLoading && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          className="space-y-6"
        >
          <div className="flex flex-col items-center justify-center gap-4 py-8">
            <div className="relative">
              <SearchIcon className="h-8 w-8 text-primary animate-pulse" />
              <div className="absolute inset-0 rounded-full bg-primary/20 animate-ping" />
            </div>
            <div className="text-center">
              <span className="text-lg font-medium text-primary animate-pulse block">
                {currentSearchQuery ? `Searching for "${currentSearchQuery}"...` : 'Loading AI tools...'}
              </span>
              <span className="text-sm text-muted-foreground mt-1 block">
                Finding the best tools for you
              </span>
            </div>
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <Card key={i} className="border shadow-sm">
                <CardContent className="p-3 sm:p-4 lg:p-6">
                  <div className="flex items-start gap-2 sm:gap-3 lg:gap-4 mb-3 sm:mb-4">
                    <Skeleton className="h-10 w-10 sm:h-12 sm:w-12 lg:h-14 lg:w-14 rounded-lg sm:rounded-xl" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 sm:h-5 lg:h-6 w-full" />
                      <Skeleton className="h-3 sm:h-4 w-3/4" />
                    </div>
                  </div>
                  <Skeleton className="h-3 sm:h-4 w-full mb-2" />
                  <Skeleton className="h-3 sm:h-4 w-4/5" />
                </CardContent>
              </Card>
            ))}
          </div>
        </motion.div>
      )}

      {/* Error State */}
      {error && !isLoading && (
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
        >
          <Alert variant="destructive" className="border-red-200 bg-red-50">
            <AlertCircle className="h-5 w-5" />
            <AlertTitle className="text-lg font-semibold">Search Error</AlertTitle>
            <AlertDescription className="mt-2">
              <p className="mb-4">{error}</p>
              <Button
                variant="outline"
                size="sm"
                onClick={performSearch}
                className="border-red-300 hover:bg-red-100"
              >
                <RefreshCw className="h-4 w-4 mr-2" />
                Try Again
              </Button>
            </AlertDescription>
          </Alert>
        </motion.div>
      )}

      {/* Results Section */}
      {!isLoading && !error && (
        <>
          {tools.length > 0 ? (
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ delay: 0.2 }}
              className="space-y-6"
            >
              {/* Results Header */}
              <div className="bg-white/80 dark:bg-card/80 backdrop-blur-sm rounded-lg sm:rounded-xl p-4 sm:p-6 border shadow-lg">
                <div className="flex flex-col sm:flex-row sm:justify-between sm:items-center gap-3 sm:gap-4">
                  <div className="flex flex-col sm:flex-row sm:items-center gap-2 sm:gap-4">
                    <div className="flex items-center gap-2">
                      <div className="h-2 w-2 bg-green-500 rounded-full animate-pulse"></div>
                      <span className="text-lg sm:text-xl font-semibold text-foreground">
                        Found {tools.length} amazing {tools.length === 1 ? 'tool' : 'tools'}
                      </span>
                    </div>
                    {currentSearchQuery && (
                      <div className="flex items-center gap-2">
                        <div className="bg-blue-100 dark:bg-blue-900/30 text-blue-700 dark:text-blue-300 px-3 sm:px-4 py-1.5 sm:py-2 rounded-full border border-blue-200 dark:border-blue-700 text-xs sm:text-sm font-medium">
                          <SearchIcon className="h-3 w-3 mr-1.5 sm:mr-2 inline" />
                          "{currentSearchQuery}"
                        </div>
                        {currentSearchQuery !== query && (
                          <div className="bg-orange-100 dark:bg-orange-900/30 text-orange-700 dark:text-orange-300 px-2 py-1 rounded-md text-xs font-medium">
                            <Sparkles className="h-3 w-3 mr-1 inline" />
                            Instant Filter
                          </div>
                        )}
                      </div>
                    )}
                  </div>

                  <div className="flex items-center gap-2 text-xs sm:text-sm text-muted-foreground">
                    <Clock className="h-3 w-3 sm:h-4 sm:w-4" />
                    <span>Found in {searchDuration}ms</span>
                  </div>
                </div>
              </div>

              {/* Results Grid */}
              <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
                {tools.map((tool, index) => (
                  <motion.div
                    key={tool.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ delay: index * 0.05 }}
                  >
                    <ToolCard
                      tool={{
                        id: tool?.id?.toString() || '',
                        slug: tool?.slug || '',
                        company_name: tool?.company_name || 'Unknown Tool',
                        short_description: tool?.short_description || 'No description available',
                        visit_website_url: tool?.visit_website_url || `https://www.google.com/search?q=${encodeURIComponent(tool?.company_name || 'AI tool')}`,
                        logo_url: tool?.logo_url || '',
                        primary_task: tool?.primary_task || 'General',
                        pricing: tool?.pricing || 'Unknown',
                        rating: tool?.rating || 0,
                        review_count: tool?.review_count || 0,
                        is_verified: tool?.is_verified || false,
                        is_featured: tool?.is_featured || false,
                        click_count: tool?.click_count || 0,
                        isNew: tool?.new || false
                      }}
                      highlight={currentSearchQuery}
                      className="hover:shadow-xl transition-all duration-300 hover:-translate-y-1 bg-white/80 dark:bg-card/80 backdrop-blur-sm border shadow-lg"
                    />
                  </motion.div>
                ))}
              </div>
            </motion.div>
          ) : (
            /* No Results Found */
            <motion.div
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              className="space-y-8"
            >
              {/* No Results Message */}
              <Alert className="border-orange-200 bg-orange-50 dark:bg-orange-950/20">
                <AlertCircle className="h-6 w-6 text-orange-500" />
                <AlertTitle className="text-xl sm:text-2xl font-semibold text-orange-600 dark:text-orange-400">
                  🔍 No tools found
                </AlertTitle>
                <AlertDescription className="mt-4 text-base">
                  {currentSearchQuery ? (
                    <div className="space-y-4">
                      <p>
                        We couldn't find any AI tools matching{" "}
                        <span className="font-semibold text-blue-600 bg-blue-100 dark:bg-blue-900/30 px-2 py-1 rounded-md">
                          "{currentSearchQuery}"
                        </span>
                        {hasActiveFilters && " with your selected filters"}.
                      </p>
                      <div className="bg-gradient-to-r from-orange-50 to-yellow-50 dark:from-orange-900/20 dark:to-yellow-900/20 rounded-lg p-4 border border-orange-200 dark:border-orange-800">
                        <p className="text-sm font-medium text-orange-700 dark:text-orange-300 mb-3 flex items-center gap-2">
                          <Lightbulb className="h-4 w-4" />
                          Try these suggestions:
                        </p>
                        <ul className="text-sm text-orange-600 dark:text-orange-400 space-y-2">
                          <li className="flex items-start gap-2">
                            <span className="text-orange-500 mt-0.5">•</span>
                            <span>Use broader search terms (e.g., "writing" instead of "content creation")</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-orange-500 mt-0.5">•</span>
                            <span>Remove some filters to see more results</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-orange-500 mt-0.5">•</span>
                            <span>Check your spelling and try alternative terms</span>
                          </li>
                          <li className="flex items-start gap-2">
                            <span className="text-orange-500 mt-0.5">•</span>
                            <span>Browse our popular tools and categories below</span>
                          </li>
                        </ul>
                      </div>
                    </div>
                  ) : (
                    <div className="space-y-3">
                      <p>No tools found with the selected filters.</p>
                      <p className="text-sm text-muted-foreground">Try adjusting your search criteria or browse our popular tools below.</p>
                    </div>
                  )}
                </AlertDescription>
              </Alert>

              {/* Related Categories */}
              {relatedCategories.length > 0 && (
                <Card className="bg-white/80 dark:bg-card/80 backdrop-blur-sm border shadow-xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-3 text-xl">
                      <div className="p-2 bg-gradient-to-br from-blue-400 to-purple-500 rounded-lg">
                        <SearchIcon className="h-5 w-5 text-white" />
                      </div>
                      <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                        Related Categories
                      </span>
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-2 sm:grid-cols-3 lg:grid-cols-4 gap-3">
                      {relatedCategories.map((category, index) => (
                        <motion.div
                          key={category.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <Button
                            variant="outline"
                            className="w-full h-auto p-3 flex flex-col items-center gap-2 hover:bg-blue-50 dark:hover:bg-blue-950/20 hover:border-blue-300 dark:hover:border-blue-700 transition-all duration-200"
                            onClick={() => {
                              window.location.href = `/search?categories=${encodeURIComponent(category.name)}`
                            }}
                          >
                            <span className="font-medium text-sm text-center">{category.name}</span>
                            <span className="text-xs text-muted-foreground">{category.count} tools</span>
                          </Button>
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}

              {/* Popular Suggestions */}
              {suggestions.length > 0 && (
                <Card className="bg-white/80 dark:bg-card/80 backdrop-blur-sm border shadow-xl">
                  <CardHeader className="pb-4">
                    <CardTitle className="flex items-center gap-3 text-2xl">
                      <div className="p-3 bg-gradient-to-br from-yellow-400 to-orange-500 rounded-xl">
                        <Lightbulb className="h-6 w-6 text-white" />
                      </div>
                      <span className="bg-gradient-to-r from-yellow-600 via-orange-600 to-red-600 bg-clip-text text-transparent">
                        Popular AI Tools You Might Love
                      </span>
                      <TrendingUp className="h-5 w-5 text-green-500" />
                    </CardTitle>
                  </CardHeader>
                  <CardContent>
                    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-3 sm:gap-4">
                      {suggestions.map((tool, index) => (
                        <motion.div
                          key={tool.id}
                          initial={{ opacity: 0, y: 20 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ delay: index * 0.1 }}
                        >
                          <ToolCard
                            tool={{
                              id: tool?.id?.toString() || '',
                              slug: tool?.slug || '',
                              company_name: tool?.company_name || 'Unknown Tool',
                              short_description: tool?.short_description || 'No description available',
                              visit_website_url: tool?.visit_website_url || `https://www.google.com/search?q=${encodeURIComponent(tool?.company_name || 'AI tool')}`,
                              logo_url: tool?.logo_url || '',
                              primary_task: tool?.primary_task || 'General',
                              pricing: tool?.pricing || 'Unknown',
                              rating: tool?.rating || 0,
                              review_count: tool?.review_count || 0,
                              is_verified: tool?.is_verified || false,
                              is_featured: tool?.is_featured || false,
                              click_count: tool?.click_count || 0,
                              isNew: tool?.new || false
                            }}
                            className="hover:shadow-lg transition-all duration-300 hover:-translate-y-1 bg-white/60 dark:bg-card/60 backdrop-blur-sm border"
                            size="compact"
                          />
                        </motion.div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              )}
            </motion.div>
          )}
        </>
      )}
    </div>
  )
}
