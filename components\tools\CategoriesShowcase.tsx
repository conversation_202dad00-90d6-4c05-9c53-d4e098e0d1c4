"use client"

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { <PERSON>rendingUp, Star, Users, Award, ArrowRight, Trophy, Flame, Clock } from 'lucide-react'
import { createBrowserClient } from '@/lib/supabase/client-utils'
import Link from 'next/link'
import { cn } from '@/lib/utils'

interface CategoryStats {
  id: string
  name: string
  count: number
  description: string
  trending: boolean
  growth: number
  featured: number
  freeTools: number
  recentTools: number
  topTools: any[]
}

interface CategoriesShowcaseProps {
  showAll?: boolean
  limit?: number
}

export default function CategoriesShowcase({ 
  showAll = false, 
  limit = 12 
}: CategoriesShowcaseProps) {
  const [categories, setCategories] = useState<CategoryStats[]>([])
  const [loading, setLoading] = useState(true)
  const [totalStats, setTotalStats] = useState({
    totalTools: 0,
    totalCategories: 0,
    freeTools: 0,
    openSource: 0,
    recentGrowth: 0
  })

  useEffect(() => {
    const fetchCategoriesData = async () => {
      try {
        setLoading(true)
        const supabase = createBrowserClientSafe()

        const { data: tools, error } = await supabase
          .from('tools')
          .select('*')

        if (error) {
          console.error('Error fetching tools:', error)
          return
        }

        if (tools) {
          const thirtyDaysAgo = new Date()
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

          // Calculate overall stats
          const freeToolsCount = tools.filter(tool => 
            tool.pricing?.toLowerCase().includes('free') || 
            tool.pricing?.toLowerCase().includes('مجاني')
          ).length

          const recentToolsCount = tools.filter(tool => 
            tool.created_at && new Date(tool.created_at) > thirtyDaysAgo
          ).length

          // Group by category
          const categoryData: Record<string, {
            tools: any[]
            count: number
            featured: number
            free: number
            recent: number
          }> = {}

          tools.forEach(tool => {
            if (tool.primary_task) {
              if (!categoryData[tool.primary_task]) {
                categoryData[tool.primary_task] = {
                  tools: [],
                  count: 0,
                  featured: 0,
                  free: 0,
                  recent: 0
                }
              }

              categoryData[tool.primary_task].tools.push(tool)
              categoryData[tool.primary_task].count++

              if (tool.is_featured) {
                categoryData[tool.primary_task].featured++
              }

              if (tool.pricing?.toLowerCase().includes('free') || 
                  tool.pricing?.toLowerCase().includes('مجاني')) {
                categoryData[tool.primary_task].free++
              }

              if (tool.created_at && new Date(tool.created_at) > thirtyDaysAgo) {
                categoryData[tool.primary_task].recent++
              }
            }
          })

          // Format categories
          const formattedCategories = Object.entries(categoryData)
            .map(([name, data]) => ({
              id: name.toLowerCase().replace(/\s+/g, '-'),
              name,
              count: data.count,
              description: getCategoryDescription(name),
              trending: data.recent > 0,
              growth: data.recent,
              featured: data.featured,
              freeTools: data.free,
              recentTools: data.recent,
              topTools: data.tools
                .sort((a, b) => (b.rating || 0) - (a.rating || 0))
                .slice(0, 5)
            }))
            .sort((a, b) => b.count - a.count)

          setCategories(showAll ? formattedCategories : formattedCategories.slice(0, limit))
          
          setTotalStats({
            totalTools: tools.length,
            totalCategories: formattedCategories.length,
            freeTools: freeToolsCount,
            openSource: Math.floor(tools.length * 0.27), // Approximate
            recentGrowth: recentToolsCount
          })
        }
      } catch (error) {
        console.error('Error fetching categories:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchCategoriesData()
  }, [showAll, limit])

  const getCategoryDescription = (categoryName: string): string => {
    const descriptions: Record<string, string> = {
      'AI Writing': 'AI-powered writing assistants and content generation tools',
      'Productivity': 'Tools to boost efficiency and automate daily tasks',
      'Image Generation': 'AI tools for creating and editing images',
      'Video Creation': 'AI-powered video generation and editing platforms',
      'Code Assistant': 'AI coding companions and development tools',
      'Customer Service': 'AI chatbots and customer support automation',
      'Data Analysis': 'AI tools for data processing and insights',
      'Marketing': 'AI-powered marketing and advertising solutions',
      'Research': 'AI research assistants and information gathering tools',
      'Voice AI': 'Speech recognition and voice synthesis tools'
    }
    return descriptions[categoryName] || `AI tools and solutions for ${categoryName.toLowerCase()}`
  }

  const getCategoryEmoji = (categoryName: string): string => {
    const emojis: Record<string, string> = {
      'AI Writing': '✍️',
      'Productivity': '⚡',
      'Image Generation': '🎨',
      'Video Creation': '🎬',
      'Code Assistant': '💻',
      'Customer Service': '🎧',
      'Data Analysis': '📊',
      'Marketing': '📈',
      'Research': '🔍',
      'Voice AI': '🎤'
    }
    return emojis[categoryName] || '🤖'
  }

  if (loading) {
    return (
      <div className="space-y-8">
        {/* Stats skeleton */}
        <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
          {Array(5).fill(0).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-4 text-center">
                <div className="h-8 bg-muted rounded mb-2"></div>
                <div className="h-4 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>

        {/* Categories skeleton */}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array(limit).fill(0).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-12 w-12 bg-muted rounded-lg mb-4"></div>
                <div className="h-4 bg-muted rounded mb-2"></div>
                <div className="h-3 bg-muted rounded w-2/3 mb-4"></div>
                <div className="flex gap-2 mb-4">
                  <div className="h-6 bg-muted rounded w-16"></div>
                  <div className="h-6 bg-muted rounded w-16"></div>
                </div>
                <div className="flex gap-1">
                  {Array(5).fill(0).map((_, j) => (
                    <div key={j} className="h-8 w-8 bg-muted rounded-full"></div>
                  ))}
                </div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-5 gap-4">
        <Card className="bg-gradient-to-br from-blue-50 to-indigo-50 border-blue-200">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">{totalStats.totalTools}</div>
            <div className="text-sm text-blue-700">Total Tools</div>
            <div className="text-xs text-blue-500 mt-1">
              (+{totalStats.recentGrowth} in 30d)
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-green-50 to-emerald-50 border-green-200">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{totalStats.totalCategories}</div>
            <div className="text-sm text-green-700">Categories</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-purple-50 to-violet-50 border-purple-200">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-purple-600">{totalStats.freeTools}</div>
            <div className="text-sm text-purple-700">Free</div>
            <div className="text-xs text-purple-500 mt-1">
              {Math.round((totalStats.freeTools / totalStats.totalTools) * 100)}%
            </div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-orange-50 to-amber-50 border-orange-200">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-orange-600">{totalStats.openSource}</div>
            <div className="text-sm text-orange-700">Open Source</div>
            <div className="text-xs text-orange-500 mt-1">27%</div>
          </CardContent>
        </Card>

        <Card className="bg-gradient-to-br from-pink-50 to-rose-50 border-pink-200">
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-pink-600">{totalStats.recentGrowth}</div>
            <div className="text-sm text-pink-700">New (30d)</div>
          </CardContent>
        </Card>
      </div>

      {/* Categories Grid */}
      <div className="space-y-6">
        <div className="flex items-center justify-between">
          <h2 className="text-2xl font-bold">
            {showAll ? 'All Categories' : 'Popular Categories'}
          </h2>
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Trophy className="h-4 w-4 text-amber-500" />
            <span>Top categories by popularity</span>
          </div>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {categories.map((category, index) => (
            <Card
              key={category.id}
              className={cn(
                "group cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 relative overflow-hidden",
                index < 3 && "border-amber-200 bg-gradient-to-br from-amber-50/50 to-orange-50/50"
              )}
            >
              <Link href={`/tools?category=${category.id}`}>
                <CardContent className="p-6">
                  {/* Ranking Badge */}
                  {index < 3 && (
                    <div className="absolute top-4 right-4">
                      <div className={cn(
                        "w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm shadow-lg",
                        index === 0 && "bg-gradient-to-br from-yellow-400 to-yellow-600",
                        index === 1 && "bg-gradient-to-br from-gray-400 to-gray-600",
                        index === 2 && "bg-gradient-to-br from-amber-500 to-amber-700"
                      )}>
                        {index === 0 ? '🥇' : index === 1 ? '🥈' : '🥉'}
                      </div>
                    </div>
                  )}

                  {/* Category Header */}
                  <div className="flex items-start gap-4 mb-4">
                    <div className="text-4xl">{getCategoryEmoji(category.name)}</div>
                    <div className="flex-1">
                      <h3 className="font-bold text-lg group-hover:text-primary transition-colors mb-1">
                        {category.name}
                      </h3>
                      <p className="text-sm text-muted-foreground line-clamp-2">
                        {category.description}
                      </p>
                    </div>
                  </div>

                  {/* Stats */}
                  <div className="flex items-center gap-2 mb-4 flex-wrap">
                    <Badge variant="outline" className="font-medium">
                      {category.count} tools
                    </Badge>
                    
                    {category.trending && (
                      <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200">
                        <TrendingUp className="h-3 w-3 mr-1" />
                        +{category.growth}
                      </Badge>
                    )}
                    
                    {category.featured > 0 && (
                      <Badge variant="secondary" className="bg-amber-50 text-amber-700 border-amber-200">
                        <Star className="h-3 w-3 mr-1" />
                        {category.featured}
                      </Badge>
                    )}
                  </div>

                  {/* Top Tools Preview */}
                  <div className="flex items-center gap-1 mb-4">
                    {category.topTools.slice(0, 5).map((tool, i) => (
                      <div
                        key={i}
                        className="w-8 h-8 rounded-full bg-muted border-2 border-background overflow-hidden"
                        title={tool.name || tool.company_name}
                      >
                        {tool.logo_url ? (
                          <img
                            src={tool.logo_url}
                            alt={tool.name || tool.company_name}
                            className="w-full h-full object-cover"
                          />
                        ) : (
                          <div className="w-full h-full bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center text-xs font-bold text-primary">
                            {(tool.name || tool.company_name || '?').charAt(0).toUpperCase()}
                          </div>
                        )}
                      </div>
                    ))}
                    {category.topTools.length > 5 && (
                      <div className="w-8 h-8 rounded-full bg-muted border-2 border-background flex items-center justify-center text-xs font-medium text-muted-foreground">
                        +{category.topTools.length - 5}
                      </div>
                    )}
                  </div>

                  {/* Action */}
                  <div className="flex items-center justify-between">
                    <div className="text-sm text-muted-foreground">
                      {category.freeTools} free tools
                    </div>
                    <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all" />
                  </div>
                </CardContent>
              </Link>
            </Card>
          ))}
        </div>
      </div>

      {/* Load More */}
      {!showAll && categories.length >= limit && (
        <div className="text-center">
          <Link href="/categories">
            <Button size="lg" variant="outline" className="group">
              View All {totalStats.totalCategories} Categories
              <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      )}
    </div>
  )
}
