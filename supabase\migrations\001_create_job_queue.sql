-- Create job queue table for free queue system
CREATE TABLE IF NOT EXISTS job_queue (
  id UUID DEFAULT gen_random_uuid() PRIMARY KEY,
  type TEXT NOT NULL CHECK (type IN ('crawl', 'ai_rewrite', 'publish', 'batch_update')),
  payload JSONB NOT NULL,
  status TEXT NOT NULL DEFAULT 'pending' CHECK (status IN ('pending', 'processing', 'completed', 'failed')),
  retries INTEGER DEFAULT 0,
  max_retries INTEGER DEFAULT 3,
  scheduled_at TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  started_at TIMESTAMPTZ,
  completed_at TIMESTAMPTZ,
  error_message TEXT,
  result JSONB,
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for performance
CREATE INDEX idx_job_queue_status_scheduled ON job_queue (status, scheduled_at);
CREATE INDEX idx_job_queue_type ON job_queue (type);
CREATE INDEX idx_job_queue_created_at ON job_queue (created_at DESC);

-- <PERSON>reate function to update updated_at automatically
CREATE OR REPLACE FUNCTION update_updated_at_column()
RETURNS TRIGGER AS $$
BEGIN
    NEW.updated_at = NOW();
    RETURN NEW;
END;
$$ language 'plpgsql';

-- Create trigger for updated_at
CREATE TRIGGER update_job_queue_updated_at 
    BEFORE UPDATE ON job_queue 
    FOR EACH ROW 
    EXECUTE FUNCTION update_updated_at_column();

-- Create function to clean old completed jobs (older than 7 days)
CREATE OR REPLACE FUNCTION cleanup_old_jobs()
RETURNS void AS $$
BEGIN
    DELETE FROM job_queue 
    WHERE status IN ('completed', 'failed') 
    AND completed_at < NOW() - INTERVAL '7 days';
END;
$$ language 'plpgsql';

-- Create a view for job statistics
CREATE OR REPLACE VIEW job_queue_stats AS
SELECT 
    type,
    status,
    COUNT(*) as count,
    AVG(EXTRACT(EPOCH FROM (completed_at - started_at))) as avg_duration_seconds
FROM job_queue 
WHERE started_at IS NOT NULL
GROUP BY type, status;

-- RLS policies (Row Level Security)
ALTER TABLE job_queue ENABLE ROW LEVEL SECURITY;

-- Allow service role to do everything
CREATE POLICY "Service role can manage job queue" ON job_queue
    FOR ALL USING (auth.role() = 'service_role');

-- Allow authenticated users to view their own jobs (if we add user_id later)
CREATE POLICY "Users can view job queue stats" ON job_queue
    FOR SELECT USING (true);

-- Grant permissions
GRANT ALL ON job_queue TO service_role;
GRANT SELECT ON job_queue_stats TO authenticated;
GRANT SELECT ON job_queue_stats TO anon;

-- Create cache storage table for free cache system
CREATE TABLE IF NOT EXISTS cache_storage (
  key TEXT PRIMARY KEY,
  data JSONB NOT NULL,
  timestamp TIMESTAMPTZ NOT NULL DEFAULT NOW(),
  ttl BIGINT NOT NULL, -- TTL in milliseconds
  tags TEXT[] DEFAULT '{}',
  created_at TIMESTAMPTZ DEFAULT NOW(),
  updated_at TIMESTAMPTZ DEFAULT NOW()
);

-- Create indexes for cache performance
CREATE INDEX idx_cache_storage_timestamp ON cache_storage (timestamp);
CREATE INDEX idx_cache_storage_tags ON cache_storage USING GIN (tags);
CREATE INDEX idx_cache_storage_ttl ON cache_storage (timestamp, ttl);

-- Create trigger for cache updated_at
CREATE TRIGGER update_cache_storage_updated_at
    BEFORE UPDATE ON cache_storage
    FOR EACH ROW
    EXECUTE FUNCTION update_updated_at_column();

-- RLS for cache storage
ALTER TABLE cache_storage ENABLE ROW LEVEL SECURITY;

-- Allow all operations for service role
CREATE POLICY "Service role can manage cache" ON cache_storage
    FOR ALL USING (auth.role() = 'service_role');

-- Allow read access for authenticated users
CREATE POLICY "Authenticated users can read cache" ON cache_storage
    FOR SELECT USING (auth.role() = 'authenticated');

-- Grant permissions for cache
GRANT ALL ON cache_storage TO service_role;
GRANT SELECT ON cache_storage TO authenticated;
