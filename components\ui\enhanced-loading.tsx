"use client"

import { memo } from "react"
import { <PERSON><PERSON>2, <PERSON><PERSON><PERSON>, <PERSON><PERSON>, <PERSON> } from "lucide-react"
import { cn } from "@/lib/utils"

interface LoadingSpinnerProps {
  size?: "sm" | "md" | "lg" | "xl"
  variant?: "default" | "dots" | "pulse" | "bounce" | "ai"
  className?: string
  text?: string
  fullScreen?: boolean
}

const LoadingSpinner = memo(function LoadingSpinner({
  size = "md",
  variant = "default",
  className,
  text,
  fullScreen = false
}: LoadingSpinnerProps) {
  const sizeClasses = {
    sm: "h-4 w-4",
    md: "h-6 w-6", 
    lg: "h-8 w-8",
    xl: "h-12 w-12"
  }

  const textSizeClasses = {
    sm: "text-sm",
    md: "text-base",
    lg: "text-lg", 
    xl: "text-xl"
  }

  const containerClasses = cn(
    "flex items-center justify-center",
    fullScreen && "min-h-screen w-full",
    !fullScreen && "py-8",
    className
  )

  const renderSpinner = () => {
    switch (variant) {
      case "dots":
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  "rounded-full bg-primary animate-pulse",
                  sizeClasses[size]
                )}
                style={{
                  animationDelay: `${i * 0.2}s`,
                  animationDuration: "1s"
                }}
              />
            ))}
          </div>
        )

      case "pulse":
        return (
          <div className={cn(
            "rounded-full bg-primary/20 animate-pulse",
            sizeClasses[size]
          )}>
            <div className={cn(
              "rounded-full bg-primary animate-ping",
              sizeClasses[size]
            )} />
          </div>
        )

      case "bounce":
        return (
          <div className="flex space-x-1">
            {[0, 1, 2].map((i) => (
              <div
                key={i}
                className={cn(
                  "rounded-full bg-primary animate-bounce",
                  size === "sm" ? "h-2 w-2" : 
                  size === "md" ? "h-3 w-3" :
                  size === "lg" ? "h-4 w-4" : "h-6 w-6"
                )}
                style={{
                  animationDelay: `${i * 0.1}s`
                }}
              />
            ))}
          </div>
        )

      case "ai":
        return (
          <div className="relative">
            <div className={cn(
              "rounded-full border-2 border-primary/20 animate-spin",
              sizeClasses[size]
            )}>
              <div className="absolute inset-0 rounded-full border-2 border-transparent border-t-primary animate-spin" 
                   style={{ animationDirection: "reverse", animationDuration: "0.8s" }} />
            </div>
            <div className="absolute inset-0 flex items-center justify-center">
              <Brain className={cn(
                "text-primary animate-pulse",
                size === "sm" ? "h-2 w-2" :
                size === "md" ? "h-3 w-3" :
                size === "lg" ? "h-4 w-4" : "h-6 w-6"
              )} />
            </div>
          </div>
        )

      default:
        return (
          <Loader2 className={cn(
            "animate-spin text-primary",
            sizeClasses[size]
          )} />
        )
    }
  }

  return (
    <div className={containerClasses}>
      <div className="flex flex-col items-center gap-3">
        {renderSpinner()}
        {text && (
          <p className={cn(
            "text-muted-foreground font-medium animate-pulse",
            textSizeClasses[size]
          )}>
            {text}
          </p>
        )}
      </div>
    </div>
  )
})

// Skeleton components for different content types
export const ToolCardSkeleton = memo(function ToolCardSkeleton() {
  return (
    <div className="bg-card border rounded-lg p-4 space-y-3 animate-pulse">
      <div className="flex items-start gap-3">
        <div className="w-12 h-12 bg-muted rounded-xl" />
        <div className="flex-1 space-y-2">
          <div className="h-4 bg-muted rounded w-3/4" />
          <div className="flex gap-2">
            <div className="h-5 bg-muted rounded-full w-16" />
            <div className="h-5 bg-muted rounded-full w-20" />
          </div>
        </div>
        <div className="w-8 h-8 bg-muted rounded" />
      </div>
      <div className="space-y-2">
        <div className="h-3 bg-muted rounded w-full" />
        <div className="h-3 bg-muted rounded w-2/3" />
      </div>
      <div className="flex justify-between items-center">
        <div className="flex gap-1">
          {[...Array(5)].map((_, i) => (
            <div key={i} className="w-3 h-3 bg-muted rounded" />
          ))}
        </div>
        <div className="h-4 bg-muted rounded w-12" />
      </div>
      <div className="grid grid-cols-2 gap-2 pt-2 border-t">
        <div className="h-8 bg-muted rounded" />
        <div className="h-8 bg-muted rounded" />
      </div>
    </div>
  )
})

export const ToolGridSkeleton = memo(function ToolGridSkeleton({ 
  count = 12 
}: { 
  count?: number 
}) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
      {[...Array(count)].map((_, i) => (
        <ToolCardSkeleton key={i} />
      ))}
    </div>
  )
})

export const SearchSkeleton = memo(function SearchSkeleton() {
  return (
    <div className="space-y-6 animate-pulse">
      <div className="h-12 bg-muted rounded-lg w-full" />
      <div className="flex gap-4">
        <div className="h-10 bg-muted rounded w-24" />
        <div className="h-10 bg-muted rounded w-32" />
        <div className="h-10 bg-muted rounded w-28" />
      </div>
      <ToolGridSkeleton count={8} />
    </div>
  )
})

export default LoadingSpinner
