import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { ArrowLeft, Search } from "lucide-react"

export default function ToolNotFound() {
  return (
    <div className="flex-1 py-24">
      <div className="container mx-auto px-4 text-center">
        <h1 className="text-4xl font-bold mb-4">Tool Not Found</h1>
        <p className="text-xl text-muted-foreground mb-8">
          The tool you're looking for doesn't exist or has been removed.
        </p>
        
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
          <Button asChild>
            <Link href="/tools">
              <ArrowLeft className="mr-2 h-4 w-4" />
              Browse All Tools
            </Link>
          </Button>
          
          <Button variant="outline" asChild>
            <Link href="/search">
              <Search className="mr-2 h-4 w-4" />
              Search for Tools
            </Link>
          </Button>
        </div>
      </div>
    </div>
  )
}
