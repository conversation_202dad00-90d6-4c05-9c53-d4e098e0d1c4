"use client"

import { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { User, LogOut, Settings } from "lucide-react"
import { useAuth } from "@/hooks/use-auth"
import { toast } from "sonner"

export function AuthSection() {
  const [mounted, setMounted] = useState(false)
  const { user, isLoading, isAuthenticated, signOut } = useAuth()

  // Prevent hydration mismatch
  useEffect(() => {
    setMounted(true)
  }, [])

  // Handle sign out - memoized لتجنب re-renders
  const handleSignOut = useCallback(async () => {
    try {
      await signOut()
      toast.success('Successfully signed out')
    } catch (error) {
      console.error('Sign out failed:', error)
      toast.error('Failed to sign out')
    }
  }, [signOut])

  // Always show loading state until mounted and auth is loaded
  if (!mounted || isLoading) {
    return (
      <div className="w-16 lg:w-20 h-9 bg-muted animate-pulse rounded-full"></div>
    )
  }

  // إذا كان مسجل دخول
  if (isAuthenticated && user) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm" 
            className="rounded-full h-9 w-9 p-0 glass-dark theme-transition min-h-[44px] min-w-[44px] touch-manipulation"
          >
            <User className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuItem asChild>
            <Link href="/dashboard" className="flex items-center">
              <Settings className="mr-2 h-4 w-4" />
              Dashboard
            </Link>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
            <LogOut className="mr-2 h-4 w-4" />
            Sign Out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  // إذا لم يكن مسجل دخول
  return (
    <Button 
      asChild 
      size="sm" 
      className="rounded-full bg-primary hover:bg-primary/90 text-primary-foreground px-3 lg:px-4 py-1.5 h-9 shadow-sm hover:shadow-lg theme-transition hover-button text-sm min-h-[44px] touch-manipulation"
    >
      <Link href="/auth">Sign In</Link>
    </Button>
  )
}

// مكون للموبايل
export function MobileAuthSection({ onMenuClose }: { onMenuClose: () => void }) {
  const [mounted, setMounted] = useState(false)
  const { user, isLoading, isAuthenticated, signOut } = useAuth()

  useEffect(() => {
    setMounted(true)
  }, [])

  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('Successfully signed out')
      onMenuClose()
    } catch (error) {
      console.error('Sign out failed:', error)
      toast.error('Failed to sign out')
    }
  }

  if (!mounted || isLoading) {
    return (
      <div className="w-full h-12 bg-muted animate-pulse rounded-xl"></div>
    )
  }

  if (isAuthenticated && user) {
    return (
      <div className="space-y-2">
        <Link
          href="/dashboard"
          className="flex items-center px-4 py-4 rounded-xl text-base font-medium text-foreground hover:text-primary hover:bg-muted/70 transition-all duration-200 min-h-[48px] touch-manipulation border border-transparent hover:border-primary/20"
          onClick={onMenuClose}
        >
          <Settings className="w-5 h-5 mr-3" />
          Dashboard
        </Link>
        <button
          onClick={handleSignOut}
          className="flex items-center w-full px-4 py-4 rounded-xl text-base font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30 transition-all duration-200 min-h-[48px] touch-manipulation border border-transparent hover:border-red-200 dark:hover:border-red-800"
        >
          <LogOut className="w-5 h-5 mr-3" />
          Sign Out
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <Button 
        asChild 
        className="w-full rounded-xl bg-primary hover:bg-primary/90 shadow-md hover:shadow-lg py-4 h-12 text-base text-primary-foreground font-medium touch-manipulation border border-primary/20"
      >
        <Link href="/auth" onClick={onMenuClose}>
          Sign In
        </Link>
      </Button>
    </div>
  )
}
