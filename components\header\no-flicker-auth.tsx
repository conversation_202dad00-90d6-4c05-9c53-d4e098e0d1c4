"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { User, LogOut, Settings } from "lucide-react"
import { supabase } from "@/lib/supabase/client"
import { toast } from "sonner"

// حل نهائي لمشكلة الوميض
export function NoFlickerAuth() {
  const [mounted, setMounted] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    setMounted(true)
    
    // تحقق من المستخدم الحالي
    const checkUser = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        setUser(session?.user || null)
      } catch (error) {
        console.error('Auth check error:', error)
        setUser(null)
      } finally {
        setIsLoading(false)
      }
    }

    checkUser()

    // الاستماع لتغييرات الـ auth
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user || null)
        setIsLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut()
      toast.success('Successfully signed out')
    } catch (error) {
      console.error('Sign out failed:', error)
      toast.error('Failed to sign out')
    }
  }

  // عدم إظهار أي شيء حتى يتم التحميل
  if (!mounted) {
    return (
      <div className="w-20 h-9 bg-transparent"></div>
    )
  }

  // أثناء التحميل
  if (isLoading) {
    return (
      <div className="w-20 h-9 bg-muted/30 animate-pulse rounded-full"></div>
    )
  }

  // إذا كان مسجل دخول
  if (user) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm" 
            className="rounded-full h-9 w-9 p-0 glass-dark theme-transition min-h-[44px] min-w-[44px] touch-manipulation"
          >
            <User className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuItem asChild>
            <Link href="/dashboard" className="flex items-center">
              <Settings className="mr-2 h-4 w-4" />
              Dashboard
            </Link>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem onClick={handleSignOut} className="text-red-600">
            <LogOut className="mr-2 h-4 w-4" />
            Sign Out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  // غير مسجل دخول
  return (
    <Button 
      asChild 
      size="sm" 
      className="rounded-full bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-1.5 h-9 shadow-sm hover:shadow-lg theme-transition hover-button text-sm min-h-[44px] touch-manipulation"
    >
      <Link href="/auth">Sign In</Link>
    </Button>
  )
}

export function NoFlickerMobileAuth({ onMenuClose }: { onMenuClose: () => void }) {
  const [mounted, setMounted] = useState(false)
  const [user, setUser] = useState<any>(null)
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    setMounted(true)
    
    const checkUser = async () => {
      try {
        const { data: { session } } = await supabase.auth.getSession()
        setUser(session?.user || null)
      } catch (error) {
        console.error('Auth check error:', error)
        setUser(null)
      } finally {
        setIsLoading(false)
      }
    }

    checkUser()

    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        setUser(session?.user || null)
        setIsLoading(false)
      }
    )

    return () => subscription.unsubscribe()
  }, [])

  const handleSignOut = async () => {
    try {
      await supabase.auth.signOut()
      toast.success('Successfully signed out')
      onMenuClose()
    } catch (error) {
      console.error('Sign out failed:', error)
      toast.error('Failed to sign out')
    }
  }

  if (!mounted) {
    return <div className="w-full h-12 bg-transparent"></div>
  }

  if (isLoading) {
    return <div className="w-full h-12 bg-muted/30 animate-pulse rounded-xl"></div>
  }

  if (user) {
    return (
      <div className="space-y-2">
        <Link
          href="/dashboard"
          className="flex items-center px-4 py-4 rounded-xl text-base font-medium text-foreground hover:text-primary hover:bg-muted/70 transition-all duration-200 min-h-[48px] touch-manipulation border border-transparent hover:border-primary/20"
          onClick={onMenuClose}
        >
          <Settings className="w-5 h-5 mr-3" />
          Dashboard
        </Link>
        <button
          onClick={handleSignOut}
          className="flex items-center w-full px-4 py-4 rounded-xl text-base font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30 transition-all duration-200 min-h-[48px] touch-manipulation border border-transparent hover:border-red-200 dark:hover:border-red-800"
        >
          <LogOut className="w-5 h-5 mr-3" />
          Sign Out
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <Button 
        asChild 
        className="w-full rounded-xl bg-primary hover:bg-primary/90 shadow-md hover:shadow-lg py-4 h-12 text-base text-primary-foreground font-medium touch-manipulation border border-primary/20"
      >
        <Link href="/auth" onClick={onMenuClose}>
          Sign In
        </Link>
      </Button>
    </div>
  )
}
