'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw, Home, Search } from 'lucide-react'
import Link from 'next/link'

export default function ToolError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to console and potentially to an error reporting service
    const errorDetails = {
      message: error?.message || 'Unknown error',
      digest: error?.digest || 'no-digest',
      stack: error?.stack || 'no-stack',
      timestamp: new Date().toISOString(),
      url: typeof window !== 'undefined' ? window.location.href : 'unknown',
      name: error?.name || 'Error'
    }

    console.error('🚨 Tool page error:', errorDetails)

    // You can also send this to an error reporting service like Sentry
    // Sentry.captureException(error)
  }, [error])

  const getErrorMessage = () => {
    if (error.message.includes('not found')) {
      return {
        title: 'Tool Not Found',
        description: 'The AI tool you\'re looking for doesn\'t exist or has been removed.',
        suggestion: 'Try searching for a similar tool or browse our directory.'
      }
    }

    if (error.message.includes('network') || error.message.includes('fetch')) {
      return {
        title: 'Connection Error',
        description: 'We\'re having trouble connecting to our servers.',
        suggestion: 'Please check your internet connection and try again.'
      }
    }

    if (error.message.includes('timeout')) {
      return {
        title: 'Request Timeout',
        description: 'The request took too long to complete.',
        suggestion: 'Our servers might be busy. Please try again in a moment.'
      }
    }

    return {
      title: 'Something Went Wrong',
      description: 'An unexpected error occurred while loading this tool.',
      suggestion: 'Please try refreshing the page or contact support if the problem persists.'
    }
  }

  const errorInfo = getErrorMessage()

  return (
    <div className="min-h-screen bg-background flex items-center justify-center p-4">
      <div className="w-full max-w-2xl">
        <Card className="border-destructive/20 bg-destructive/5">
          <CardHeader className="text-center pb-4">
            <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
              <AlertTriangle className="w-8 h-8 text-destructive" />
            </div>
            <CardTitle className="text-2xl font-bold text-destructive">
              {errorInfo.title}
            </CardTitle>
            <CardDescription className="text-lg">
              {errorInfo.description}
            </CardDescription>
          </CardHeader>
          
          <CardContent className="space-y-6">
            <div className="text-center">
              <p className="text-muted-foreground mb-6">
                {errorInfo.suggestion}
              </p>
              
              {/* Error Details (only in development) */}
              {process.env.NODE_ENV === 'development' && (
                <details className="text-left bg-muted p-4 rounded-lg mb-6">
                  <summary className="cursor-pointer font-medium text-sm mb-2">
                    Technical Details (Development Only)
                  </summary>
                  <pre className="text-xs text-muted-foreground overflow-auto">
                    {error.message}
                    {error.digest && `\nDigest: ${error.digest}`}
                  </pre>
                </details>
              )}
            </div>

            {/* Action Buttons */}
            <div className="flex flex-col sm:flex-row gap-3 justify-center">
              <Button 
                onClick={reset}
                className="flex items-center gap-2"
                size="lg"
              >
                <RefreshCw className="w-4 h-4" />
                Try Again
              </Button>
              
              <Button 
                variant="outline" 
                asChild
                size="lg"
              >
                <Link href="/tools" className="flex items-center gap-2">
                  <Search className="w-4 h-4" />
                  Browse Tools
                </Link>
              </Button>
              
              <Button 
                variant="ghost" 
                asChild
                size="lg"
              >
                <Link href="/" className="flex items-center gap-2">
                  <Home className="w-4 h-4" />
                  Go Home
                </Link>
              </Button>
            </div>

            {/* Help Section */}
            <div className="text-center pt-6 border-t border-border">
              <p className="text-sm text-muted-foreground mb-2">
                Still having trouble?
              </p>
              <div className="flex flex-col sm:flex-row gap-2 justify-center text-sm">
                <Link 
                  href="/contact" 
                  className="text-primary hover:underline"
                >
                  Contact Support
                </Link>
                <span className="hidden sm:inline text-muted-foreground">•</span>
                <Link 
                  href="/help" 
                  className="text-primary hover:underline"
                >
                  Help Center
                </Link>
                <span className="hidden sm:inline text-muted-foreground">•</span>
                <Link 
                  href="/status" 
                  className="text-primary hover:underline"
                >
                  System Status
                </Link>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Additional Error Context */}
        <div className="mt-6 text-center">
          <p className="text-xs text-muted-foreground">
            Error ID: {error.digest || 'unknown'} • 
            Time: {new Date().toLocaleString()}
          </p>
        </div>
      </div>
    </div>
  )
}
