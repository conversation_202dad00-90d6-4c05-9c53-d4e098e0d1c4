'use client'

import { cn } from "@/lib/utils"
import { Loader2, Search, Sparkles } from "lucide-react"

interface LoadingProps {
  size?: 'sm' | 'md' | 'lg'
  variant?: 'spinner' | 'dots' | 'pulse' | 'search' | 'sparkles'
  text?: string
  className?: string
}

export function Loading({ 
  size = 'md', 
  variant = 'spinner', 
  text, 
  className 
}: LoadingProps) {
  const sizeClasses = {
    sm: 'h-4 w-4',
    md: 'h-6 w-6',
    lg: 'h-8 w-8'
  }

  const textSizeClasses = {
    sm: 'text-sm',
    md: 'text-base',
    lg: 'text-lg'
  }

  if (variant === 'spinner') {
    return (
      <div className={cn("flex items-center justify-center space-x-2", className)}>
        <Loader2 className={cn("animate-spin text-primary", sizeClasses[size])} />
        {text && (
          <span className={cn("text-muted-foreground", textSizeClasses[size])}>
            {text}
          </span>
        )}
      </div>
    )
  }

  if (variant === 'search') {
    return (
      <div className={cn("flex items-center justify-center space-x-2", className)}>
        <Search className={cn("animate-pulse text-primary", sizeClasses[size])} />
        {text && (
          <span className={cn("text-muted-foreground", textSizeClasses[size])}>
            {text}
          </span>
        )}
      </div>
    )
  }

  if (variant === 'sparkles') {
    return (
      <div className={cn("flex items-center justify-center space-x-2", className)}>
        <Sparkles className={cn("animate-pulse text-primary", sizeClasses[size])} />
        {text && (
          <span className={cn("text-muted-foreground", textSizeClasses[size])}>
            {text}
          </span>
        )}
      </div>
    )
  }

  if (variant === 'dots') {
    return (
      <div className={cn("flex items-center justify-center space-x-1", className)}>
        <div className="flex space-x-1">
          <div className={cn("rounded-full bg-primary animate-bounce", 
            size === 'sm' ? 'h-2 w-2' : size === 'md' ? 'h-3 w-3' : 'h-4 w-4'
          )} style={{ animationDelay: '0ms' }} />
          <div className={cn("rounded-full bg-primary animate-bounce", 
            size === 'sm' ? 'h-2 w-2' : size === 'md' ? 'h-3 w-3' : 'h-4 w-4'
          )} style={{ animationDelay: '150ms' }} />
          <div className={cn("rounded-full bg-primary animate-bounce", 
            size === 'sm' ? 'h-2 w-2' : size === 'md' ? 'h-3 w-3' : 'h-4 w-4'
          )} style={{ animationDelay: '300ms' }} />
        </div>
        {text && (
          <span className={cn("ml-2 text-muted-foreground", textSizeClasses[size])}>
            {text}
          </span>
        )}
      </div>
    )
  }

  if (variant === 'pulse') {
    return (
      <div className={cn("flex items-center justify-center space-x-2", className)}>
        <div className={cn("rounded-full bg-primary animate-pulse", sizeClasses[size])} />
        {text && (
          <span className={cn("text-muted-foreground animate-pulse", textSizeClasses[size])}>
            {text}
          </span>
        )}
      </div>
    )
  }

  return null
}

// Loading Overlay Component
export function LoadingOverlay({ 
  isLoading, 
  children, 
  text = "Loading...",
  variant = 'spinner'
}: {
  isLoading: boolean
  children: React.ReactNode
  text?: string
  variant?: LoadingProps['variant']
}) {
  return (
    <div className="relative">
      {children}
      {isLoading && (
        <div className="absolute inset-0 flex items-center justify-center bg-background/80 backdrop-blur-sm">
          <Loading variant={variant} text={text} size="lg" />
        </div>
      )}
    </div>
  )
}

// Page Loading Component
export function PageLoading({ text = "Loading page..." }: { text?: string }) {
  return (
    <div className="flex min-h-[400px] items-center justify-center">
      <div className="text-center space-y-4">
        <Loading variant="sparkles" size="lg" />
        <p className="text-lg text-muted-foreground">{text}</p>
      </div>
    </div>
  )
}

// Button Loading State
export function ButtonLoading({ text = "Loading..." }: { text?: string }) {
  return (
    <div className="flex items-center space-x-2">
      <Loader2 className="h-4 w-4 animate-spin" />
      <span>{text}</span>
    </div>
  )
}
