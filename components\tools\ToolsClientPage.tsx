"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Search, SlidersHorizontal } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import {
  Sheet,
  <PERSON><PERSON><PERSON><PERSON>nt,
  <PERSON><PERSON><PERSON><PERSON>er,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  She<PERSON><PERSON><PERSON>ger,
} from "@/components/ui/sheet"
import ToolsFilters from "@/components/tools-filters"

interface ToolsClientPageProps {
  initialSearchTerm: string
  selectedCategory: string
  selectedPricing: string
  selectedSortBy: string
  selectedFeatures: string[]
}

export default function ToolsClientPage({
  initialSearchTerm,
  selectedCategory,
  selectedPricing,
  selectedSortBy,
  selectedFeatures,
}: ToolsClientPageProps) {
  const router = useRouter()
  const [isFiltersOpen, setIsFiltersOpen] = useState(false)
  const [searchTerm, setSearchTerm] = useState(initialSearchTerm)

  // Handle search form submission
  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    
    // Build query parameters
    const params = new URLSearchParams()
    
    if (searchTerm) {
      params.set("search", searchTerm)
    }
    
    if (selectedCategory) {
      params.set("category", selectedCategory)
    }
    
    if (selectedPricing) {
      params.set("pricing", selectedPricing)
    }
    
    if (selectedSortBy && selectedSortBy !== "featured") {
      params.set("sortBy", selectedSortBy)
    }
    
    if (selectedFeatures.length > 0) {
      params.set("features", selectedFeatures.join(','))
    }
    
    // Navigate to the search results
    router.push(`/tools?${params.toString()}`)
  }

  return (
    <>
      {/* Mobile filters button */}
      <Sheet open={isFiltersOpen} onOpenChange={setIsFiltersOpen}>
        <SheetTrigger asChild>
          <Button variant="outline" className="md:hidden">
            <SlidersHorizontal className="h-4 w-4 mr-2" />
            Filters
          </Button>
        </SheetTrigger>
        <SheetContent side="left" className="w-[300px] sm:w-[400px]">
          <SheetHeader>
            <SheetTitle>Filters</SheetTitle>
          </SheetHeader>
          <div className="py-4">
            <ToolsFilters 
              selectedCategory={selectedCategory}
              selectedPricing={selectedPricing}
              selectedSortBy={selectedSortBy}
              selectedFeatures={selectedFeatures}
              onClose={() => setIsFiltersOpen(false)}
            />
          </div>
        </SheetContent>
      </Sheet>
      
      {/* Enhanced Search form */}
      <form onSubmit={handleSearch} className="w-full">
        <div className="relative group">
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-accent/20 to-primary/20 rounded-xl blur-xl opacity-0 group-hover:opacity-100 transition-opacity duration-500"></div>
          <div className="relative bg-background/95 backdrop-blur-sm border-2 border-border/50 rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 hover:border-primary/30">
            <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-muted-foreground group-hover:text-primary transition-colors" size={20} />
            <Input
              type="text"
              placeholder="Search for AI tools, categories, or features..."
              className="pl-12 pr-24 h-14 text-lg border-0 bg-transparent focus:ring-0 focus:outline-none placeholder:text-muted-foreground/70"
              value={searchTerm}
              onChange={(e) => setSearchTerm(e.target.value)}
            />
            <Button
              type="submit"
              size="lg"
              className="absolute right-2 top-1/2 -translate-y-1/2 h-10 px-6 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 shadow-md hover:shadow-lg transition-all duration-200"
            >
              Search
            </Button>
          </div>
        </div>

        {/* Search suggestions */}
        {searchTerm && (
          <div className="mt-2 text-center">
            <p className="text-sm text-muted-foreground">
              Press Enter to search or try:
              <span className="ml-1 text-primary font-medium">AI writing</span>,
              <span className="ml-1 text-primary font-medium">image generation</span>,
              <span className="ml-1 text-primary font-medium">productivity</span>
            </p>
          </div>
        )}
      </form>
    </>
  )
}
