import { Skeleton } from "@/components/ui/skeleton"
import { But<PERSON> } from "@/components/ui/button"
import { ChevronLeft } from "lucide-react"
import Link from "next/link"

export default function ToolLoading() {
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section Skeleton */}
      <div className="relative overflow-hidden bg-gradient-to-br from-blue-50/80 via-purple-50/60 to-pink-50/80 dark:from-blue-950/30 dark:via-purple-950/20 dark:to-pink-950/30">
        <div className="absolute inset-0 bg-grid-pattern opacity-40" />

        <div className="relative pt-20 pb-12">
          <div className="container-tight">
            {/* Back Button */}
            <div className="mb-8">
              <Button
                variant="ghost"
                size="sm"
                className="group hover:bg-background/80 backdrop-blur-sm border border-border/50 shadow-sm hover:shadow-md transition-all duration-200"
                asChild
              >
                <Link href="/tools">
                  <ChevronLeft className="mr-2 h-4 w-4 group-hover:-translate-x-1 transition-transform duration-200" />
                  Back to Tools
                </Link>
              </Button>
            </div>

            {/* Tool Header Skeleton */}
            <div className="relative bg-background/95 dark:bg-card/95 backdrop-blur-xl rounded-3xl border border-border/60 shadow-2xl shadow-primary/5 dark:shadow-primary/10 p-6 sm:p-8 lg:p-10">
              <div className="relative flex flex-col lg:flex-row gap-6 lg:gap-8">
                <div className="flex flex-col sm:flex-row gap-6 lg:flex-1">
                  {/* Logo Skeleton */}
                  <div className="relative group flex-shrink-0">
                    <Skeleton className="w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-3xl" />
                  </div>

                  {/* Title and Description Skeleton */}
                  <div className="flex-1 space-y-4 min-w-0">
                    <div className="space-y-3">
                      <div className="flex items-start gap-3 flex-wrap">
                        <Skeleton className="h-8 sm:h-10 lg:h-12 xl:h-14 w-64 sm:w-80 lg:w-96" />
                        <Skeleton className="h-6 w-6 rounded-full" />
                      </div>

                      <div className="flex items-center gap-3 flex-wrap">
                        <Skeleton className="h-6 w-20 rounded-full" />
                        <Skeleton className="h-6 w-16 rounded-full" />
                        <Skeleton className="h-6 w-24 rounded-full" />
                      </div>
                    </div>

                    <Skeleton className="h-16 w-full max-w-2xl" />
                  </div>
                </div>

                {/* Action Buttons Skeleton */}
                <div className="flex flex-col gap-4 lg:w-64">
                  <div className="flex gap-3 lg:flex-col">
                    <Skeleton className="h-12 flex-1 lg:w-full rounded-xl" />
                    <Skeleton className="h-12 w-12 rounded-xl" />
                    <Skeleton className="h-12 w-12 rounded-xl" />
                  </div>

                  <Skeleton className="h-16 w-full rounded-2xl" />
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Main Content Skeleton */}
      <div className="relative">
        <div className="container-tight py-12">
          <div className="grid grid-cols-1 lg:grid-cols-3 xl:grid-cols-4 gap-6 sm:gap-8 lg:gap-10 xl:gap-12">
            {/* Main Content Area Skeleton */}
            <div className="lg:col-span-2 xl:col-span-3 space-y-6 sm:space-y-8">
              {/* Tabs Skeleton */}
              <div className="bg-background/95 dark:bg-card/95 backdrop-blur-xl rounded-3xl border border-border/60 shadow-xl shadow-primary/5 dark:shadow-primary/10 overflow-hidden">
                <div className="flex border-b border-border/30">
                  <Skeleton className="h-12 w-24 rounded-none" />
                  <Skeleton className="h-12 w-24 rounded-none" />
                  <Skeleton className="h-12 w-24 rounded-none" />
                  <Skeleton className="h-12 w-16 rounded-none" />
                </div>

                <div className="p-6 sm:p-8 lg:p-10 space-y-6">
                  <Skeleton className="h-64 w-full rounded-3xl" />
                  <div className="space-y-4">
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-full" />
                    <Skeleton className="h-4 w-3/4" />
                  </div>
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                    <Skeleton className="h-20 rounded-xl" />
                    <Skeleton className="h-20 rounded-xl" />
                    <Skeleton className="h-20 rounded-xl" />
                    <Skeleton className="h-20 rounded-xl" />
                  </div>
                </div>
              </div>
            </div>

            {/* Sidebar Skeleton */}
            <div className="lg:col-span-1 xl:col-span-1">
              <div className="lg:sticky lg:top-24 space-y-4 sm:space-y-6">
                {/* Quick Info Card Skeleton */}
                <div className="bg-gradient-to-br from-blue-50/80 via-purple-50/60 to-pink-50/80 dark:from-blue-950/30 dark:via-purple-950/20 dark:to-pink-950/30 border border-border/60 shadow-xl shadow-primary/5 dark:shadow-primary/10 backdrop-blur-sm rounded-2xl overflow-hidden">
                  <div className="p-4 bg-gradient-to-r from-primary/5 to-accent/5 border-b border-border/30">
                    <Skeleton className="h-6 w-24" />
                  </div>
                  <div className="p-6 space-y-3">
                    <Skeleton className="h-12 w-full rounded-xl" />
                    <Skeleton className="h-12 w-full rounded-xl" />
                    <Skeleton className="h-12 w-full rounded-xl" />
                    <Skeleton className="h-12 w-full rounded-xl" />
                  </div>
                </div>

                {/* Related Tools Skeleton */}
                <div className="bg-background/95 dark:bg-card/95 backdrop-blur-xl rounded-2xl border border-border/60 shadow-xl shadow-primary/5 dark:shadow-primary/10 overflow-hidden">
                  <div className="p-4 border-b border-border/30">
                    <Skeleton className="h-6 w-32" />
                  </div>
                  <div className="p-4 space-y-3">
                    {Array.from({ length: 3 }).map((_, i) => (
                      <div key={i} className="flex items-center gap-3 p-3 rounded-xl border border-border/30">
                        <Skeleton className="w-12 h-12 rounded-xl" />
                        <div className="flex-1 space-y-2">
                          <Skeleton className="h-4 w-full" />
                          <Skeleton className="h-3 w-3/4" />
                        </div>
                      </div>
                    ))}
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Related Tools Section Skeleton */}
      <div className="relative bg-gradient-to-b from-background via-secondary/10 to-background">
        <div className="container-tight py-16">
          <div className="text-center mb-12">
            <Skeleton className="h-8 w-64 mx-auto mb-4" />
            <Skeleton className="h-4 w-96 mx-auto" />
          </div>

          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
            {Array.from({ length: 8 }).map((_, i) => (
              <div key={i} className="bg-background/95 dark:bg-card/95 backdrop-blur-xl rounded-2xl border border-border/60 shadow-xl shadow-primary/5 dark:shadow-primary/10 overflow-hidden">
                <div className="p-6 space-y-4">
                  <div className="flex items-center gap-3">
                    <Skeleton className="w-12 h-12 rounded-xl" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-full" />
                      <Skeleton className="h-3 w-3/4" />
                    </div>
                  </div>
                  <Skeleton className="h-3 w-full" />
                  <Skeleton className="h-3 w-2/3" />
                  <div className="flex items-center justify-between">
                    <Skeleton className="h-6 w-16 rounded-full" />
                    <Skeleton className="h-8 w-20 rounded-lg" />
                  </div>
                </div>
              </div>
            ))}
          </div>
        </div>
      </div>
    </div>
  )
}