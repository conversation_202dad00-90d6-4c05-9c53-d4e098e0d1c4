"use client"

import { useState, useEffect } from 'react'

interface ProgressiveEnhancementFeatures {
  canUseAdvancedFeatures: boolean
  hasIntersectionObserver: boolean
  hasWebP: boolean
  hasWebGL: boolean
  hasServiceWorker: boolean
  hasLocalStorage: boolean
  hasSessionStorage: boolean
  hasGeolocation: boolean
  hasNotifications: boolean
  hasClipboard: boolean
  hasWebShare: boolean
  hasFileAPI: boolean
  hasWebWorkers: boolean
  hasIndexedDB: boolean
  hasWebRTC: boolean
  hasWebAssembly: boolean
  hasResizeObserver: boolean
  hasMutationObserver: boolean
  hasPerformanceObserver: boolean
  hasRequestIdleCallback: boolean
  hasMatchMedia: boolean
  hasTouchSupport: boolean
  hasPointerEvents: boolean
  hasGamepadAPI: boolean
  hasVibrationAPI: boolean
  hasDeviceOrientation: boolean
  hasNetworkInformation: boolean
  hasBatteryAPI: boolean
  hasPaymentRequest: boolean
  hasCredentialsAPI: boolean
  hasWebAuthentication: boolean
  connectionType: string
  deviceMemory: number | undefined
  hardwareConcurrency: number
  maxTouchPoints: number
  colorScheme: 'light' | 'dark' | 'no-preference'
  reducedMotion: boolean
  highContrast: boolean
  forcedColors: boolean
}

export const useProgressiveEnhancement = (): ProgressiveEnhancementFeatures => {
  const [features, setFeatures] = useState<ProgressiveEnhancementFeatures>({
    canUseAdvancedFeatures: false,
    hasIntersectionObserver: false,
    hasWebP: false,
    hasWebGL: false,
    hasServiceWorker: false,
    hasLocalStorage: false,
    hasSessionStorage: false,
    hasGeolocation: false,
    hasNotifications: false,
    hasClipboard: false,
    hasWebShare: false,
    hasFileAPI: false,
    hasWebWorkers: false,
    hasIndexedDB: false,
    hasWebRTC: false,
    hasWebAssembly: false,
    hasResizeObserver: false,
    hasMutationObserver: false,
    hasPerformanceObserver: false,
    hasRequestIdleCallback: false,
    hasMatchMedia: false,
    hasTouchSupport: false,
    hasPointerEvents: false,
    hasGamepadAPI: false,
    hasVibrationAPI: false,
    hasDeviceOrientation: false,
    hasNetworkInformation: false,
    hasBatteryAPI: false,
    hasPaymentRequest: false,
    hasCredentialsAPI: false,
    hasWebAuthentication: false,
    connectionType: 'unknown',
    deviceMemory: undefined,
    hardwareConcurrency: 1,
    maxTouchPoints: 0,
    colorScheme: 'no-preference',
    reducedMotion: false,
    highContrast: false,
    forcedColors: false
  })

  useEffect(() => {
    if (typeof window === 'undefined') return

    const checkFeatures = () => {
      // Core API checks
      const hasIntersectionObserver = 'IntersectionObserver' in window
      const hasResizeObserver = 'ResizeObserver' in window
      const hasMutationObserver = 'MutationObserver' in window
      const hasPerformanceObserver = 'PerformanceObserver' in window
      const hasRequestIdleCallback = 'requestIdleCallback' in window
      const hasMatchMedia = 'matchMedia' in window
      const hasServiceWorker = 'serviceWorker' in navigator
      const hasLocalStorage = (() => {
        try {
          const test = 'test'
          localStorage.setItem(test, test)
          localStorage.removeItem(test)
          return true
        } catch {
          return false
        }
      })()
      const hasSessionStorage = (() => {
        try {
          const test = 'test'
          sessionStorage.setItem(test, test)
          sessionStorage.removeItem(test)
          return true
        } catch {
          return false
        }
      })()

      // Media and graphics
      const hasWebP = (() => {
        try {
          const canvas = document.createElement('canvas')
          return canvas.toDataURL('image/webp').indexOf('data:image/webp') === 0
        } catch {
          return false
        }
      })()

      const hasWebGL = (() => {
        try {
          const canvas = document.createElement('canvas')
          return !!(canvas.getContext('webgl') || canvas.getContext('experimental-webgl'))
        } catch {
          return false
        }
      })()

      // Device APIs
      const hasGeolocation = 'geolocation' in navigator
      const hasNotifications = 'Notification' in window
      const hasClipboard = 'clipboard' in navigator
      const hasWebShare = 'share' in navigator
      const hasFileAPI = 'File' in window && 'FileReader' in window
      const hasWebWorkers = 'Worker' in window
      const hasIndexedDB = 'indexedDB' in window
      const hasWebRTC = 'RTCPeerConnection' in window
      const hasWebAssembly = 'WebAssembly' in window
      const hasGamepadAPI = 'getGamepads' in navigator
      const hasVibrationAPI = 'vibrate' in navigator
      const hasDeviceOrientation = 'DeviceOrientationEvent' in window
      const hasPaymentRequest = 'PaymentRequest' in window
      const hasCredentialsAPI = 'credentials' in navigator
      const hasWebAuthentication = 'PublicKeyCredential' in window

      // Input capabilities
      const hasTouchSupport = 'ontouchstart' in window || navigator.maxTouchPoints > 0
      const hasPointerEvents = 'PointerEvent' in window
      const maxTouchPoints = navigator.maxTouchPoints || 0

      // Network information
      const connection = (navigator as any).connection || (navigator as any).mozConnection || (navigator as any).webkitConnection
      const hasNetworkInformation = !!connection
      const connectionType = connection?.effectiveType || 'unknown'

      // Device capabilities
      const deviceMemory = (navigator as any).deviceMemory
      const hardwareConcurrency = navigator.hardwareConcurrency || 1
      const hasBatteryAPI = 'getBattery' in navigator

      // User preferences
      const colorScheme = hasMatchMedia ? (
        window.matchMedia('(prefers-color-scheme: dark)').matches ? 'dark' :
        window.matchMedia('(prefers-color-scheme: light)').matches ? 'light' : 'no-preference'
      ) : 'no-preference'

      const reducedMotion = hasMatchMedia ? window.matchMedia('(prefers-reduced-motion: reduce)').matches : false
      const highContrast = hasMatchMedia ? window.matchMedia('(prefers-contrast: high)').matches : false
      const forcedColors = hasMatchMedia ? window.matchMedia('(forced-colors: active)').matches : false

      // Determine if advanced features can be used
      const canUseAdvancedFeatures = hasIntersectionObserver && hasWebP && hasLocalStorage && hasServiceWorker

      setFeatures({
        canUseAdvancedFeatures,
        hasIntersectionObserver,
        hasWebP,
        hasWebGL,
        hasServiceWorker,
        hasLocalStorage,
        hasSessionStorage,
        hasGeolocation,
        hasNotifications,
        hasClipboard,
        hasWebShare,
        hasFileAPI,
        hasWebWorkers,
        hasIndexedDB,
        hasWebRTC,
        hasWebAssembly,
        hasResizeObserver,
        hasMutationObserver,
        hasPerformanceObserver,
        hasRequestIdleCallback,
        hasMatchMedia,
        hasTouchSupport,
        hasPointerEvents,
        hasGamepadAPI,
        hasVibrationAPI,
        hasDeviceOrientation,
        hasNetworkInformation,
        hasBatteryAPI,
        hasPaymentRequest,
        hasCredentialsAPI,
        hasWebAuthentication,
        connectionType,
        deviceMemory,
        hardwareConcurrency,
        maxTouchPoints,
        colorScheme,
        reducedMotion,
        highContrast,
        forcedColors
      })
    }

    checkFeatures()

    // Listen for changes in user preferences
    if ('matchMedia' in window) {
      const mediaQueries = [
        window.matchMedia('(prefers-color-scheme: dark)'),
        window.matchMedia('(prefers-reduced-motion: reduce)'),
        window.matchMedia('(prefers-contrast: high)'),
        window.matchMedia('(forced-colors: active)')
      ]

      const handleChange = () => checkFeatures()

      mediaQueries.forEach(mq => {
        if (mq.addEventListener) {
          mq.addEventListener('change', handleChange)
        } else {
          // Fallback for older browsers
          mq.addListener(handleChange)
        }
      })

      return () => {
        mediaQueries.forEach(mq => {
          if (mq.removeEventListener) {
            mq.removeEventListener('change', handleChange)
          } else {
            // Fallback for older browsers
            mq.removeListener(handleChange)
          }
        })
      }
    }
  }, [])

  return features
}

// Utility hooks for specific features
export const useIntersectionObserver = () => {
  const { hasIntersectionObserver } = useProgressiveEnhancement()
  return hasIntersectionObserver
}

export const useWebPSupport = () => {
  const { hasWebP } = useProgressiveEnhancement()
  return hasWebP
}

export const useLocalStorageSupport = () => {
  const { hasLocalStorage } = useProgressiveEnhancement()
  return hasLocalStorage
}

export const useReducedMotion = () => {
  const { reducedMotion } = useProgressiveEnhancement()
  return reducedMotion
}

export const useColorSchemePreference = () => {
  const { colorScheme } = useProgressiveEnhancement()
  return colorScheme
}

export const useTouchSupport = () => {
  const { hasTouchSupport, maxTouchPoints } = useProgressiveEnhancement()
  return { hasTouchSupport, maxTouchPoints }
}

export const useNetworkInformation = () => {
  const { hasNetworkInformation, connectionType } = useProgressiveEnhancement()
  return { hasNetworkInformation, connectionType }
}

export const useDeviceCapabilities = () => {
  const { deviceMemory, hardwareConcurrency } = useProgressiveEnhancement()
  return { deviceMemory, hardwareConcurrency }
}

export default useProgressiveEnhancement
