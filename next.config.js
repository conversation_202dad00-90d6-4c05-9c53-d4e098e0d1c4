/** @type {import('next').NextConfig} */
const nextConfig = {
  reactStrictMode: false,
  eslint: {
    ignoreDuringBuilds: true,
  },
  typescript: {
    ignoreBuildErrors: true,
  },
  images: {
    remotePatterns: [
      {
        protocol: 'https',
        hostname: 'images.unsplash.com',
      },
      {
        protocol: 'https',
        hostname: 'via.placeholder.com',
      },
      {
        protocol: 'https',
        hostname: 'logo.clearbit.com',
      },
      {
        protocol: 'https',
        hostname: '*.supabase.co',
      },
      {
        protocol: 'https',
        hostname: 'lh3.googleusercontent.com',
      },
      {
        protocol: 'https',
        hostname: 'avatars.githubusercontent.com',
      },
      {
        protocol: 'https',
        hostname: 'media.theresanaiforthat.com',
      }
    ],
    formats: ['image/webp', 'image/avif'],
    minimumCacheTTL: 60,
    dangerouslyAllowSVG: false, // Better security
    contentSecurityPolicy: "default-src 'self'; script-src 'none'; sandbox;",
  },
  experimental: {
    optimizePackageImports: ['lucide-react'],
  },
  serverExternalPackages: ['@supabase/supabase-js'],

  webpack: (config, { isServer, webpack }) => {
    if (isServer) {
      // AGGRESSIVE: Inject polyfills into ALL server entries
      const originalEntry = config.entry;
      config.entry = async () => {
        const entries = await originalEntry();

        // Add polyfills to ALL entries
        Object.keys(entries).forEach(key => {
          if (Array.isArray(entries[key])) {
            entries[key].unshift(require.resolve('./lib/polyfills.js'));
          } else if (typeof entries[key] === 'string') {
            entries[key] = [require.resolve('./lib/polyfills.js'), entries[key]];
          }
        });

        return entries;
      };

      // Aggressive fix for "self is not defined" error
      config.resolve.fallback = {
        ...config.resolve.fallback,
        fs: false,
        net: false,
        tls: false,
        crypto: false,
        stream: false,
        url: false,
        zlib: false,
        http: false,
        https: false,
        assert: false,
        os: false,
        path: false,
      }

      // Provide polyfills for server-side - AGGRESSIVE FIX
      config.plugins.push(
        new webpack.ProvidePlugin({
          process: 'process/browser',
          Buffer: ['buffer', 'Buffer'],
        })
      )

      // AGGRESSIVE: Add banner to inject polyfills at the top of EVERY file
      config.plugins.push(
        new webpack.BannerPlugin({
          banner: `
            if (typeof self === 'undefined' && typeof global !== 'undefined') {
              global.self = global;
            }
            if (typeof window === 'undefined' && typeof global !== 'undefined') {
              global.window = global;
            }
            if (typeof document === 'undefined' && typeof global !== 'undefined') {
              global.document = {
                createElement: function() { return {}; },
                getElementById: function() { return null; },
                querySelector: function() { return null; },
                querySelectorAll: function() { return []; },
                addEventListener: function() {},
                removeEventListener: function() {},
                head: { appendChild: function() {} },
                body: { appendChild: function() {}, removeChild: function() {} },
                cookie: '',
                documentElement: {},
                readyState: 'complete'
              };
            }
          `,
          raw: true,
          entryOnly: false,
        })
      )

      // SIMPLIFIED: Single DefinePlugin with all replacements
      config.plugins.push(
        new webpack.DefinePlugin({
          'typeof self': '"object"',
          'typeof window': '"undefined"',
          'typeof document': '"undefined"',
          'typeof navigator': '"undefined"',
          'typeof global': '"object"',
          'process.browser': JSON.stringify(false),
        })
      )
    }

    return config
  },
  // Performance optimization
  poweredByHeader: false,
  compress: true,

  // Redirects for SEO - redirect old /tool/ URLs to /Tool/
  async redirects() {
    return [
      {
        source: '/tool/:slug*',
        destination: '/Tool/:slug*',
        permanent: true, // 301 redirect for SEO
      },
    ]
  },

  // Optimize headers for performance and security
  async headers() {
    return [
      {
        source: '/(.*)',
        headers: [
          {
            key: 'X-DNS-Prefetch-Control',
            value: 'on'
          },
          {
            key: 'X-Frame-Options',
            value: 'DENY'
          },
          {
            key: 'X-Content-Type-Options',
            value: 'nosniff'
          },
          {
            key: 'Referrer-Policy',
            value: 'origin-when-cross-origin'
          },
          {
            key: 'Permissions-Policy',
            value: 'camera=(), microphone=(), geolocation=()'
          },
          {
            key: 'X-XSS-Protection',
            value: '1; mode=block'
          }
        ],
      },
      {
        source: '/fonts/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=31536000, immutable'
          }
        ]
      },
      {
        source: '/images/(.*)',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400'
          }
        ]
      },
      {
        source: '/sitemap.xml',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=3600, s-maxage=3600'
          }
        ]
      },
      {
        source: '/robots.txt',
        headers: [
          {
            key: 'Cache-Control',
            value: 'public, max-age=86400'
          }
        ]
      }
    ]
  }
}

module.exports = nextConfig
