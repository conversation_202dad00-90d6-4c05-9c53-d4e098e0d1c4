"use client"

import { useState, useRef, useEffect } from "react"
import { useRouter } from "next/navigation"
import { Search, Filter, X, ChevronDown, Sparkles, Zap, CheckCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { motion, AnimatePresence } from "framer-motion"
import UniversalSearch from "./universal-search"

interface ModernSearchProps {
  categories: Array<{ id: string; name: string; count: number }>
  pricingOptions: string[]
  className?: string
}

export default function ModernSearch({ categories, pricingOptions, className }: ModernSearchProps) {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("all")
  const [selectedPricing, setSelectedPricing] = useState("all")
  const [selectedSort, setSelectedSort] = useState("relevance")
  const [quickFilters, setQuickFilters] = useState<string[]>([])
  const [showFilters, setShowFilters] = useState(false)
  const router = useRouter()

  // Quick filter options
  const quickFilterOptions = [
    { id: "verified", label: "Verified", icon: CheckCircle, color: "bg-green-500" },
    { id: "featured", label: "Featured", icon: Sparkles, color: "bg-yellow-500" },
    { id: "free", label: "Free", icon: Zap, color: "bg-blue-500" },
  ]

  // Handle search
  const handleSearch = () => {
    const params = new URLSearchParams()
    
    if (searchQuery.trim()) {
      params.set("q", searchQuery.trim())
    }
    
    if (selectedCategory !== "all") {
      params.set("category", selectedCategory)
    }
    
    if (selectedPricing !== "all") {
      params.set("pricing", selectedPricing)
    }
    
    if (selectedSort !== "relevance") {
      params.set("sortBy", selectedSort)
    }
    
    if (quickFilters.length > 0) {
      params.set("filters", quickFilters.join(","))
    }
    
    router.push(`/search?${params.toString()}`)
  }

  // Toggle quick filter
  const toggleQuickFilter = (filterId: string) => {
    setQuickFilters(prev => 
      prev.includes(filterId) 
        ? prev.filter(f => f !== filterId)
        : [...prev, filterId]
    )
  }

  // Clear all filters
  const clearAllFilters = () => {
    setSelectedCategory("all")
    setSelectedPricing("all")
    setSelectedSort("relevance")
    setQuickFilters([])
  }

  // Check if any filters are active
  const hasActiveFilters = selectedCategory !== "all" || 
                          selectedPricing !== "all" || 
                          selectedSort !== "relevance" || 
                          quickFilters.length > 0

  return (
    <div className={`w-full max-w-4xl mx-auto ${className}`}>
      {/* Main Search Bar */}
      <div className="relative">
        <div className="flex items-center bg-white dark:bg-slate-900 rounded-2xl shadow-lg border border-slate-200 dark:border-slate-700 overflow-hidden transition-all duration-300 hover:shadow-xl focus-within:shadow-xl focus-within:ring-2 focus-within:ring-primary/20">
          {/* Search Input */}
          <div className="flex-1 relative">
            <UniversalSearch
              mode="navigation"
              context="general"
              variant="hero"
              size="lg"
              placeholder="Search for AI tools, features, or categories..."
              className="border-0 shadow-none bg-transparent rounded-none h-14"
              showInstantResults={true}
              maxResults={8}
              showSearchButton={true}
              onSearch={(query) => {
                setSearchQuery(query)
                handleSearch()
              }}
              filters={{
                category: selectedCategory !== "all" ? selectedCategory : undefined,
                pricing: selectedPricing !== "all" ? selectedPricing : undefined,
                features: quickFilters
              }}
            />
          </div>

          {/* Quick Category Filter */}
          <div className="hidden md:flex items-center px-4 border-l border-slate-200 dark:border-slate-700">
            <Select value={selectedCategory} onValueChange={setSelectedCategory}>
              <SelectTrigger className="border-0 bg-transparent h-12 w-40 focus:ring-0">
                <SelectValue placeholder="Category" />
              </SelectTrigger>
              <SelectContent className="rounded-xl shadow-xl border-slate-200 dark:border-slate-700">
                <SelectItem value="all" className="rounded-lg">All Categories</SelectItem>
                {categories.slice(0, 8).map((category) => (
                  <SelectItem key={category.id} value={category.name} className="rounded-lg">
                    <div className="flex items-center justify-between w-full">
                      <span>{category.name}</span>
                      <Badge variant="secondary" className="ml-2 text-xs">
                        {category.count}
                      </Badge>
                    </div>
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Advanced Filters Toggle */}
          <Popover open={showFilters} onOpenChange={setShowFilters}>
            <PopoverTrigger asChild>
              <Button
                variant="ghost"
                size="lg"
                className={`h-14 px-6 rounded-none border-l border-slate-200 dark:border-slate-700 transition-all duration-200 ${
                  hasActiveFilters ? "text-primary bg-primary/5" : "text-slate-600 dark:text-slate-400"
                }`}
              >
                <Filter className="h-5 w-5 mr-2" />
                <span className="hidden sm:inline">Filters</span>
                {hasActiveFilters && (
                  <Badge className="ml-2 h-5 w-5 p-0 text-xs bg-primary">
                    {[selectedCategory !== "all", selectedPricing !== "all", selectedSort !== "relevance", ...quickFilters].filter(Boolean).length}
                  </Badge>
                )}
                <ChevronDown className={`h-4 w-4 ml-2 transition-transform duration-200 ${showFilters ? "rotate-180" : ""}`} />
              </Button>
            </PopoverTrigger>
            
            <PopoverContent 
              className="w-96 p-0 rounded-2xl shadow-2xl border-slate-200 dark:border-slate-700" 
              align="end"
              sideOffset={8}
            >
              <div className="p-6 space-y-6">
                {/* Header */}
                <div className="flex items-center justify-between">
                  <h3 className="font-semibold text-lg">Advanced Filters</h3>
                  {hasActiveFilters && (
                    <Button
                      variant="ghost"
                      size="sm"
                      onClick={clearAllFilters}
                      className="text-slate-500 hover:text-red-500 h-8 px-3"
                    >
                      <X className="h-4 w-4 mr-1" />
                      Clear all
                    </Button>
                  )}
                </div>

                {/* Quick Filters */}
                <div>
                  <label className="text-sm font-medium mb-3 block">Quick Filters</label>
                  <div className="flex flex-wrap gap-2">
                    {quickFilterOptions.map((filter) => (
                      <Button
                        key={filter.id}
                        variant={quickFilters.includes(filter.id) ? "default" : "outline"}
                        size="sm"
                        onClick={() => toggleQuickFilter(filter.id)}
                        className="h-9 px-4 rounded-full transition-all duration-200"
                      >
                        <div className={`w-2 h-2 rounded-full mr-2 ${
                          quickFilters.includes(filter.id) ? "bg-white" : filter.color
                        }`} />
                        {filter.label}
                      </Button>
                    ))}
                  </div>
                </div>

                {/* Pricing Filter */}
                <div>
                  <label className="text-sm font-medium mb-3 block">Pricing</label>
                  <Select value={selectedPricing} onValueChange={setSelectedPricing}>
                    <SelectTrigger className="h-11 rounded-xl">
                      <SelectValue placeholder="Any pricing" />
                    </SelectTrigger>
                    <SelectContent className="rounded-xl">
                      <SelectItem value="all" className="rounded-lg">Any pricing</SelectItem>
                      {pricingOptions.map((option) => (
                        <SelectItem key={option} value={option} className="rounded-lg">
                          {option}
                        </SelectItem>
                      ))}
                    </SelectContent>
                  </Select>
                </div>

                {/* Sort Filter */}
                <div>
                  <label className="text-sm font-medium mb-3 block">Sort by</label>
                  <Select value={selectedSort} onValueChange={setSelectedSort}>
                    <SelectTrigger className="h-11 rounded-xl">
                      <SelectValue placeholder="Best match" />
                    </SelectTrigger>
                    <SelectContent className="rounded-xl">
                      <SelectItem value="relevance" className="rounded-lg">Best match</SelectItem>
                      <SelectItem value="featured" className="rounded-lg">Featured first</SelectItem>
                      <SelectItem value="newest" className="rounded-lg">Newest</SelectItem>
                      <SelectItem value="popular" className="rounded-lg">Most popular</SelectItem>
                    </SelectContent>
                  </Select>
                </div>

                {/* Apply Button */}
                <Button 
                  onClick={() => {
                    handleSearch()
                    setShowFilters(false)
                  }}
                  className="w-full h-11 rounded-xl bg-primary hover:bg-primary/90 shadow-lg"
                >
                  <Search className="h-4 w-4 mr-2" />
                  Apply Filters
                </Button>
              </div>
            </PopoverContent>
          </Popover>

          {/* Search Button */}
          <Button
            onClick={handleSearch}
            className="h-14 px-8 rounded-none bg-primary hover:bg-primary/90 text-white shadow-none border-0"
          >
            <Search className="h-5 w-5 mr-2" />
            <span className="hidden sm:inline">Search</span>
          </Button>
        </div>

        {/* Active Filters Display */}
        <AnimatePresence>
          {hasActiveFilters && (
            <motion.div
              initial={{ opacity: 0, y: -10 }}
              animate={{ opacity: 1, y: 0 }}
              exit={{ opacity: 0, y: -10 }}
              className="mt-3 flex flex-wrap gap-2 items-center"
            >
              <span className="text-sm text-slate-600 dark:text-slate-400">Active filters:</span>
              
              {selectedCategory !== "all" && (
                <Badge variant="secondary" className="rounded-full">
                  Category: {selectedCategory}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1 hover:bg-red-100 hover:text-red-600"
                    onClick={() => setSelectedCategory("all")}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
              
              {selectedPricing !== "all" && (
                <Badge variant="secondary" className="rounded-full">
                  Pricing: {selectedPricing}
                  <Button
                    variant="ghost"
                    size="sm"
                    className="h-4 w-4 p-0 ml-1 hover:bg-red-100 hover:text-red-600"
                    onClick={() => setSelectedPricing("all")}
                  >
                    <X className="h-3 w-3" />
                  </Button>
                </Badge>
              )}
              
              {quickFilters.map(filterId => {
                const filter = quickFilterOptions.find(f => f.id === filterId)
                return filter ? (
                  <Badge key={filterId} variant="secondary" className="rounded-full">
                    <div className={`w-2 h-2 rounded-full mr-1 ${filter.color}`} />
                    {filter.label}
                    <Button
                      variant="ghost"
                      size="sm"
                      className="h-4 w-4 p-0 ml-1 hover:bg-red-100 hover:text-red-600"
                      onClick={() => toggleQuickFilter(filterId)}
                    >
                      <X className="h-3 w-3" />
                    </Button>
                  </Badge>
                ) : null
              })}
              
              <Button
                variant="ghost"
                size="sm"
                onClick={clearAllFilters}
                className="text-slate-500 hover:text-red-500 h-7 px-2 rounded-full"
              >
                Clear all
              </Button>
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  )
}
