import { supabase } from './client'

/**
 * Rating updater utility for calculating and updating tool ratings
 */
export class RatingUpdater {
  private supabase: any

  constructor() {
    this.supabase = supabase
    if (!this.supabase) {
      throw new Error('Failed to initialize Supabase client')
    }
  }

  /**
   * Calculate and update average ratings for all tools based on reviews
   */
  async updateAllToolRatings(): Promise<{ updated: number; errors: number }> {
    try {
      console.log('Starting rating update process...')
      
      // Get all reviews grouped by tool_id
      const { data: reviews, error: reviewsError } = await this.supabase
        .from('reviews')
        .select('tool_id, rating')
      
      if (reviewsError) {
        console.error('Error fetching reviews:', reviewsError)
        return { updated: 0, errors: 1 }
      }

      if (!reviews || reviews.length === 0) {
        console.log('No reviews found to process')
        return { updated: 0, errors: 0 }
      }

      // Calculate average ratings per tool
      const ratingMap = new Map<number, { sum: number; count: number }>()
      
      reviews.forEach((review: any) => {
        const toolId = review.tool_id
        const rating = review.rating
        
        if (!ratingMap.has(toolId)) {
          ratingMap.set(toolId, { sum: 0, count: 0 })
        }
        
        const current = ratingMap.get(toolId)!
        current.sum += rating
        current.count += 1
      })

      console.log(`Processing ratings for ${ratingMap.size} tools...`)

      // Update tools table with calculated averages
      let updated = 0
      let errors = 0

      for (const [toolId, { sum, count }] of ratingMap.entries()) {
        try {
          const averageRating = Math.round((sum / count) * 10) / 10 // Round to 1 decimal place
          
          const { error: updateError } = await this.supabase
            .from('tools')
            .update({ rating: averageRating })
            .eq('id', toolId)

          if (updateError) {
            console.error(`Error updating rating for tool ${toolId}:`, updateError)
            errors++
          } else {
            updated++
          }
        } catch (error) {
          console.error(`Error processing tool ${toolId}:`, error)
          errors++
        }
      }

      console.log(`Rating update completed: ${updated} updated, ${errors} errors`)
      return { updated, errors }
      
    } catch (error) {
      console.error('Error in updateAllToolRatings:', error)
      return { updated: 0, errors: 1 }
    }
  }

  /**
   * Update rating for a specific tool
   */
  async updateToolRating(toolId: number): Promise<boolean> {
    try {
      // Get all reviews for this tool
      const { data: reviews, error: reviewsError } = await this.supabase
        .from('reviews')
        .select('rating')
        .eq('tool_id', toolId)
      
      if (reviewsError) {
        console.error(`Error fetching reviews for tool ${toolId}:`, reviewsError)
        return false
      }

      if (!reviews || reviews.length === 0) {
        // No reviews, set rating to 0
        const { error: updateError } = await this.supabase
          .from('tools')
          .update({ rating: 0 })
          .eq('id', toolId)

        if (updateError) {
          console.error(`Error resetting rating for tool ${toolId}:`, updateError)
          return false
        }
        return true
      }

      // Calculate average rating
      const sum = reviews.reduce((acc: number, review: any) => acc + review.rating, 0)
      const averageRating = Math.round((sum / reviews.length) * 10) / 10

      // Update tool rating
      const { error: updateError } = await this.supabase
        .from('tools')
        .update({ rating: averageRating })
        .eq('id', toolId)

      if (updateError) {
        console.error(`Error updating rating for tool ${toolId}:`, updateError)
        return false
      }

      console.log(`Updated rating for tool ${toolId}: ${averageRating} (from ${reviews.length} reviews)`)
      return true
      
    } catch (error) {
      console.error(`Error updating rating for tool ${toolId}:`, error)
      return false
    }
  }

  /**
   * Get tools that need rating updates (have reviews but rating is 0 or null)
   */
  async getToolsNeedingRatingUpdate(): Promise<number[]> {
    try {
      // Get tools that have reviews but rating is 0 or null
      const { data: toolsWithReviews, error } = await this.supabase
        .from('reviews')
        .select('tool_id')
        .not('tool_id', 'is', null)

      if (error) {
        console.error('Error fetching tools with reviews:', error)
        return []
      }

      if (!toolsWithReviews || toolsWithReviews.length === 0) {
        return []
      }

      // Get unique tool IDs
      const toolIds = [...new Set(toolsWithReviews.map((r: any) => r.tool_id))]

      // Check which of these tools have rating 0 or null
      const { data: toolsNeedingUpdate, error: toolsError } = await this.supabase
        .from('tools')
        .select('id')
        .in('id', toolIds)
        .or('rating.is.null,rating.eq.0')

      if (toolsError) {
        console.error('Error fetching tools needing update:', toolsError)
        return []
      }

      return toolsNeedingUpdate?.map((t: any) => t.id) || []
      
    } catch (error) {
      console.error('Error in getToolsNeedingRatingUpdate:', error)
      return []
    }
  }
}

/**
 * Helper function to create a rating updater instance
 */
export function createRatingUpdater() {
  return new RatingUpdater()
}

/**
 * Quick helper to update all tool ratings
 */
export async function updateAllToolRatings() {
  const updater = createRatingUpdater()
  return updater.updateAllToolRatings()
}

/**
 * Quick helper to update a specific tool's rating
 */
export async function updateToolRating(toolId: number) {
  const updater = createRatingUpdater()
  return updater.updateToolRating(toolId)
}
