// CRITICAL: Import polyfills first for SSR compatibility
import '@/lib/polyfills.js'

// AGGRESSIVE: Inline polyfill injection
if (typeof global !== 'undefined' && typeof self === 'undefined') {
  global.self = global as any;
  global.window = global as any;
}

import type { Metadata, Viewport } from 'next'
import './globals.css'
import { cn } from '@/lib/utils'
import Header from '@/components/header'
import Footer from '@/components/footer'
import { Providers } from './providers'
import { inter, robotoMono } from './fonts'
import { ConditionalLayout } from '@/components/ConditionalLayout'
import GoogleAnalytics from '@/components/analytics/GoogleAnalytics'
import AutoTracker from '@/components/analytics/AutoTracker'
import TrackingInitializer from '@/components/analytics/TrackingInitializer'
import { generateOrganizationData, generateWebSiteData, combineStructuredData } from '@/lib/seo/structured-data'
// Removed CookieBanner - using modern approach without popups
import { features } from '@/lib/config'

export const metadata: Metadata = {
  title: {
    default: 'AiAnyTool.com - Discover the Best AI Tools Directory',
    template: '%s | AiAnyTool.com'
  },
  description: 'Discover, compare, and find the perfect AI tools for your business. Browse 1000+ AI tools across all categories with reviews, pricing, and detailed comparisons. Your ultimate AI tools directory.',
  metadataBase: new URL('https://aianytool.com'),
  keywords: [
    'AI tools', 'artificial intelligence', 'AI software', 'AI directory', 'AI tools comparison',
    'best AI tools', 'AI tools for business', 'machine learning tools', 'AI productivity tools',
    'AI writing tools', 'AI image generators', 'AI chatbots', 'AI automation tools',
    'AI tools reviews', 'AI tools pricing', 'AI tools directory', 'find AI tools'
  ],
  authors: [{ name: 'AiAnyTool Team', url: 'https://aianytool.com' }],
  creator: 'AiAnyTool.com',
  publisher: 'AiAnyTool.com',
  formatDetection: {
    email: false,
    address: false,
    telephone: false,
  },
  icons: {
    icon: [
      { url: '/favicon.ico', sizes: '32x32' },
      { url: '/favicon.svg', type: 'image/svg+xml' }
    ],
    apple: '/apple-touch-icon.png',
    shortcut: '/favicon.svg',
  },
  openGraph: {
    type: 'website',
    locale: 'en_US',
    url: 'https://aianytool.com',
    title: 'AiAnyTool.com - Discover the Best AI Tools Directory',
    description: 'Discover, compare, and find the perfect AI tools for your business. Browse 1000+ AI tools across all categories with reviews, pricing, and detailed comparisons.',
    siteName: 'AiAnyTool.com',
    images: [
      {
        url: 'https://aianytool.com/og-image.jpg',
        width: 1200,
        height: 630,
        alt: 'AiAnyTool.com - Your Ultimate AI Tools Directory with 1000+ Curated AI Tools',
        type: 'image/jpeg',
        secureUrl: 'https://aianytool.com/og-image.jpg',
      },
      {
        url: 'https://aianytool.com/og-image-square.jpg',
        width: 1200,
        height: 1200,
        alt: 'AiAnyTool.com - AI Tools Directory for Business and Personal Use',
        type: 'image/jpeg',
        secureUrl: 'https://aianytool.com/og-image-square.jpg',
      },
    ],
    countryName: 'United States',
    emails: ['<EMAIL>'],
    phoneNumbers: [],
    faxNumbers: [],
    alternateLocale: ['en_GB', 'en_CA'],
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AiAnyTool.com - Discover the Best AI Tools Directory',
    description: 'Discover, compare, and find the perfect AI tools for your business. Browse 1000+ AI tools with reviews and pricing.',
    images: ['https://aianytool.com/og-image.jpg'],
    creator: '@aianytool',
    site: '@aianytool',
  },
  robots: {
    index: true,
    follow: true,
    googleBot: {
      index: true,
      follow: true,
      'max-video-preview': -1,
      'max-image-preview': 'large',
      'max-snippet': -1,
    },
  },
  manifest: '/manifest.json',
  alternates: {
    canonical: 'https://aianytool.com',
  },
  verification: {
    google: process.env.GOOGLE_SITE_VERIFICATION || 'google-site-verification-placeholder',
    yandex: process.env.YANDEX_VERIFICATION || 'yandex-verification-placeholder',
    yahoo: process.env.YAHOO_VERIFICATION || 'yahoo-verification-placeholder',
    other: {
      'msvalidate.01': process.env.BING_VERIFICATION || 'bing-verification-placeholder',
    },
  },
  other: {
    'mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-capable': 'yes',
    'apple-mobile-web-app-status-bar-style': 'default',
    'apple-mobile-web-app-title': 'AiAnyTool',
    'application-name': 'AiAnyTool',
    'msapplication-TileColor': '#3b82f6',
    'msapplication-config': '/browserconfig.xml',
    'theme-color': '#3b82f6',
    'fo-verify': 'b2d19f7f-3832-4cf8-96a9-29ed76de6e24',
  },
}

// Mobile viewport configuration
export const viewport: Viewport = {
  themeColor: [
    { media: '(prefers-color-scheme: light)', color: '#ffffff' },
    { media: '(prefers-color-scheme: dark)', color: '#000000' },
  ],
  width: 'device-width',
  initialScale: 1,
  maximumScale: 5,
  userScalable: true,
}

export default function RootLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return (
    <html
      lang="en"
      suppressHydrationWarning
      className={cn(
        inter.variable,
        robotoMono.variable,
      )}
    >
      <body className={cn("min-h-screen flex flex-col bg-background font-sans antialiased")}>
        <Providers>
          <ConditionalLayout
            header={<Header />}
            footer={<Footer />}
          >
            {children}
          </ConditionalLayout>
        </Providers>

        {/* Structured Data */}
        <script
          type="application/ld+json"
          dangerouslySetInnerHTML={{
            __html: combineStructuredData([
              generateOrganizationData(),
              generateWebSiteData()
            ])
          }}
        />

        {/* Google Analytics */}
        {process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID && (
          <>
            <GoogleAnalytics GA_MEASUREMENT_ID={process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID} />
            <AutoTracker />
            <TrackingInitializer />
          </>
        )}

        {/* Cookie Consent Banner - Removed for better UX */}

        {/* PWA Service Worker Registration - Temporarily Disabled */}
        {/* <script
          dangerouslySetInnerHTML={{
            __html: `
              if (typeof window !== 'undefined' && 'serviceWorker' in navigator) {
                window.addEventListener('load', function() {
                  navigator.serviceWorker.register('/sw.js')
                    .then(function(registration) {
                      console.log('SW registered: ', registration);
                    })
                    .catch(function(registrationError) {
                      console.log('SW registration failed: ', registrationError);
                    });
                });
              }
            `,
          }}
        /> */}
      </body>
    </html>
  )
}
