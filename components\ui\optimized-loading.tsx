"use client"

import { memo } from 'react'
import { cn } from '@/lib/utils'

interface OptimizedLoadingProps {
  className?: string
  count?: number
  variant?: 'card' | 'list' | 'grid'
}

// مكون محسن للتحميل السريع
const OptimizedLoading = memo(function OptimizedLoading({ 
  className, 
  count = 8, 
  variant = 'card' 
}: OptimizedLoadingProps) {
  const items = Array.from({ length: count }, (_, i) => i)

  if (variant === 'list') {
    return (
      <div className={cn("space-y-3", className)}>
        {items.map((i) => (
          <div key={i} className="flex items-center space-x-4 p-4 bg-muted/30 rounded-lg animate-pulse">
            <div className="w-12 h-12 bg-muted/60 rounded-lg" />
            <div className="flex-1 space-y-2">
              <div className="h-4 bg-muted/60 rounded w-3/4" />
              <div className="h-3 bg-muted/60 rounded w-1/2" />
            </div>
          </div>
        ))}
      </div>
    )
  }

  if (variant === 'grid') {
    return (
      <div className={cn("grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4", className)}>
        {items.map((i) => (
          <div key={i} className="bg-muted/30 rounded-lg p-4 animate-pulse">
            <div className="w-full h-32 bg-muted/60 rounded-lg mb-3" />
            <div className="h-4 bg-muted/60 rounded mb-2" />
            <div className="h-3 bg-muted/60 rounded w-2/3" />
          </div>
        ))}
      </div>
    )
  }

  // Default card variant
  return (
    <div className={cn("grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4", className)}>
      {items.map((i) => (
        <div key={i} className="bg-card border rounded-lg p-4 animate-pulse">
          <div className="flex items-center space-x-3 mb-3">
            <div className="w-10 h-10 bg-muted/60 rounded-lg" />
            <div className="flex-1">
              <div className="h-4 bg-muted/60 rounded mb-1" />
              <div className="h-3 bg-muted/60 rounded w-2/3" />
            </div>
          </div>
          <div className="space-y-2">
            <div className="h-3 bg-muted/60 rounded" />
            <div className="h-3 bg-muted/60 rounded w-4/5" />
          </div>
        </div>
      ))}
    </div>
  )
})

export default OptimizedLoading