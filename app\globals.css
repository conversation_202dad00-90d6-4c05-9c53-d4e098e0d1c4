/* Font loading moved to fonts.ts for performance optimization */

@tailwind base;
@tailwind components;
@tailwind utilities;



body {
  font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
  font-feature-settings: 'cv02', 'cv03', 'cv04', 'cv11';
  font-variant-numeric: tabular-nums;
  line-height: 1.6;
  letter-spacing: -0.01em;
}

/* Enhanced typography */
h1, h2, h3, h4, h5, h6 {
  font-family: 'Poppins', 'Inter', sans-serif;
  font-weight: 600;
  line-height: 1.3;
  letter-spacing: -0.02em;
}

/* Better text contrast */
.text-muted-foreground {
  color: hsl(240 3.8% 25%) !important; /* Darker for better readability */
}

.dark .text-muted-foreground {
  color: hsl(240 5% 75%) !important; /* Lighter in dark mode */
}

/* Enhanced readability classes */
.text-readable {
  color: hsl(240 10% 15%);
  font-weight: 500;
}

.dark .text-readable {
  color: hsl(240 5% 85%);
}

.text-description {
  color: hsl(240 5% 25%);
  font-size: 1.05rem;
  line-height: 1.7;
}

/* Search component improvements */
.search-container {
  position: relative;
  z-index: 10;
}

.search-results-container {
  position: absolute;
  top: 100%;
  left: 0;
  right: 0;
  z-index: 50;
  margin-top: 0.5rem;
}

/* Prevent layout shift during search loading */
.search-input-loading {
  transition: all 0.2s ease-in-out;
}

/* Custom scrollbar for search results */
.scrollbar-thin {
  scrollbar-width: thin;
}

.scrollbar-thin::-webkit-scrollbar {
  width: 4px;
}

.scrollbar-thin::-webkit-scrollbar-track {
  background: transparent;
}

.scrollbar-thin::-webkit-scrollbar-thumb {
  background: hsl(var(--muted));
  border-radius: 2px;
}

.scrollbar-thin::-webkit-scrollbar-thumb:hover {
  background: hsl(var(--muted-foreground) / 0.5);
}

.dark .text-description {
  color: hsl(240 5% 75%);
}

@layer utilities {
  .text-balance {
    text-wrap: balance;
  }

  /* Container styles */
  .container-tight {
    @apply container mx-auto px-4 sm:px-6 lg:px-8 max-w-5xl;
  }

  .container-wide {
    @apply container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl;
  }

  /* Touch-friendly utilities */
  .touch-target {
    @apply min-h-[44px] min-w-[44px];
  }

  .touch-manipulation {
    touch-action: manipulation;
  }

  /* Mobile-optimized text */
  .text-mobile-friendly {
    @apply text-base leading-relaxed;
  }

  /* Improved tap highlights */
  .tap-highlight-none {
    -webkit-tap-highlight-color: transparent;
  }

  /* Text overflow utilities */
  .text-ellipsis-mobile {
    @apply truncate max-w-full;
  }

  .text-wrap-mobile {
    word-wrap: break-word;
    overflow-wrap: break-word;
    hyphens: auto;
  }

  /* Responsive text sizing */
  .text-responsive {
    @apply text-sm sm:text-base;
  }

  .text-responsive-lg {
    @apply text-base sm:text-lg;
  }

  /* Better line clamping for mobile */
  .line-clamp-mobile-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.4;
  }

  .line-clamp-mobile-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
    line-height: 1.4;
  }

  /* Header responsive utilities */
  .header-responsive {
    @apply flex items-center justify-between w-full;
  }

  .header-logo-responsive {
    @apply flex items-center flex-shrink-0 min-w-0;
  }

  .header-nav-responsive {
    @apply hidden lg:flex items-center space-x-1 xl:space-x-2 flex-grow justify-center;
  }

  .header-actions-responsive {
    @apply flex items-center gap-1 sm:gap-2 lg:gap-3 flex-shrink-0 min-w-0;
  }

  /* Logo container improvements */
  .logo-container {
    @apply relative flex-shrink-0 overflow-hidden flex items-center justify-center bg-white dark:bg-gray-800;
    padding: 4px;
  }

  .logo-image {
    @apply object-contain transition-transform duration-300;
    max-width: 100%;
    max-height: 100%;
    width: auto;
    height: auto;
  }

  .group:hover .logo-image {
    @apply scale-110;
  }

  /* Fallback for broken images */
  .logo-image[src=""], .logo-image:not([src]) {
    @apply opacity-0;
  }

  .logo-image::before {
    content: '';
    @apply absolute inset-0 bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800;
  }

  /* Responsive breakpoint utilities */
  @media (max-width: 640px) {
    .mobile-only {
      display: block !important;
    }
    .mobile-hidden {
      display: none !important;
    }
  }

  @media (min-width: 641px) and (max-width: 1023px) {
    .tablet-only {
      display: block !important;
    }
    .tablet-hidden {
      display: none !important;
    }
  }

  @media (min-width: 1024px) {
    .desktop-only {
      display: block !important;
    }
    .desktop-hidden {
      display: none !important;
    }
  }

  /* Mobile menu improvements */
  .mobile-menu {
    background: rgba(255, 255, 255, 0.95);
    backdrop-filter: blur(20px);
    -webkit-backdrop-filter: blur(20px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .dark .mobile-menu {
    background: rgba(0, 0, 0, 0.98);
    border: 1px solid rgba(255, 255, 255, 0.1);
  }

  .mobile-menu-item {
    background: rgba(255, 255, 255, 0.8);
    backdrop-filter: blur(10px);
    -webkit-backdrop-filter: blur(10px);
  }

  .dark .mobile-menu-item {
    background: rgba(255, 255, 255, 0.05);
  }

  /* Gradient text */
  .text-gradient {
    @apply bg-clip-text text-transparent bg-gradient-to-r from-primary via-primary/80 to-primary;
  }

  /* تحسين الرسوم المتحركة للأداء */
  .animate-fadeIn {
    animation: fadeIn 0.3s ease-out forwards;
  }

  .animate-slideUp {
    animation: slideUp 0.3s ease-out forwards;
  }

  .animate-slideDown {
    animation: slideDown 0.3s ease-out forwards;
  }

  .animate-slideInRight {
    animation: slideInRight 0.3s ease-out forwards;
  }

  .animate-scaleIn {
    animation: scaleIn 0.3s ease-out forwards;
  }

  .animate-shimmer {
    animation: shimmer 1.5s linear infinite;
  }

  /* تحسين الأداء: تقليل الرسوم المتحركة للمستخدمين الذين يفضلون ذلك */
  @media (prefers-reduced-motion: reduce) {
    .animate-fadeIn,
    .animate-slideUp,
    .animate-slideDown,
    .animate-slideInRight,
    .animate-scaleIn,
    .animate-shimmer {
      animation: none;
    }
  }

  /* Dark mode specific animations - reduced glow */
  .animate-glow {
    animation: glow 3s ease-in-out infinite alternate;
  }

  .animate-glow-subtle {
    animation: glow-subtle 4s ease-in-out infinite alternate;
  }

  /* Disable glow for specific elements */
  .no-glow {
    animation: none !important;
    box-shadow: none !important;
  }

  .minimal-glow {
    animation: glow-minimal 5s ease-in-out infinite alternate;
  }

  .animate-float {
    animation: float 3s ease-in-out infinite;
  }

  .animate-pulse-slow {
    animation: pulse 3s cubic-bezier(0.4, 0, 0.6, 1) infinite;
  }

  /* تحسين انتقالات الثيم */
  .theme-transition {
    @apply transition-all duration-300 ease-in-out;
  }

  /* تحسينات الأداء العامة */
  .performance-optimized {
    will-change: transform;
    transform: translateZ(0);
    backface-visibility: hidden;
  }

  /* تحسين الأداء للعناصر المتحركة */
  .smooth-transform {
    transform: translateZ(0);
    will-change: transform;
  }

  /* تحسين الأداء للنصوص */
  .optimized-text {
    text-rendering: optimizeSpeed;
    font-smooth: never;
    -webkit-font-smoothing: subpixel-antialiased;
  }

  /* Glass morphism effect for dark mode */
  .glass-dark {
    @apply bg-background/80 backdrop-blur-xl border border-border/50;
    box-shadow:
      0 8px 32px 0 rgba(31, 38, 135, 0.37),
      inset 0 1px 0 0 rgba(255, 255, 255, 0.05);
  }

  .dark .glass-dark {
    @apply bg-background/60 backdrop-blur-xl border border-border/30;
    box-shadow:
      0 8px 32px 0 rgba(0, 0, 0, 0.5),
      inset 0 1px 0 0 rgba(255, 255, 255, 0.1);
  }

  /* Animation delays */
  .delay-50 {
    animation-delay: 50ms;
  }

  .delay-100 {
    animation-delay: 100ms;
  }

  .delay-150 {
    animation-delay: 150ms;
  }

  .delay-200 {
    animation-delay: 200ms;
  }

  .delay-250 {
    animation-delay: 250ms;
  }

  .delay-300 {
    animation-delay: 300ms;
  }

  .delay-350 {
    animation-delay: 350ms;
  }

  .delay-400 {
    animation-delay: 400ms;
  }

  .delay-450 {
    animation-delay: 450ms;
  }

  .delay-500 {
    animation-delay: 500ms;
  }

  /* Animation durations */
  .duration-fast {
    animation-duration: 0.3s;
  }

  .duration-normal {
    animation-duration: 0.5s;
  }

  .duration-slow {
    animation-duration: 0.8s;
  }

  /* Consistent hover effects */
  .hover-primary {
    @apply transition-all duration-200 ease-out hover:bg-primary/10 hover:text-primary hover:border-primary/30;
  }

  .hover-accent {
    @apply transition-all duration-200 ease-out hover:bg-accent/10 hover:text-accent-foreground hover:border-accent/30;
  }

  .hover-secondary {
    @apply transition-all duration-200 ease-out hover:bg-secondary/80 hover:text-secondary-foreground;
  }

  .hover-muted {
    @apply transition-all duration-200 ease-out hover:bg-muted/50 hover:text-foreground;
  }

  .hover-card {
    @apply transition-all duration-300 ease-out hover:shadow-lg hover:-translate-y-1 hover:border-primary/20;
  }

  .dark .hover-card {
    @apply hover:shadow-xl hover:shadow-primary/10;
  }

  .hover-link {
    @apply transition-all duration-200 ease-out hover:text-primary hover:underline hover:underline-offset-4;
  }

  .hover-button {
    @apply transition-all duration-200 ease-out hover:scale-105 hover:shadow-md active:scale-95;
  }

  /* Background patterns */
  .bg-grid-pattern {
    background-image: url('data:image/svg+xml,%3Csvg width="60" height="60" viewBox="0 0 60 60" xmlns="http://www.w3.org/2000/svg"%3E%3Cg fill="none" fill-rule="evenodd"%3E%3Cg fill="%239C92AC" fill-opacity="0.05"%3E%3Ccircle cx="30" cy="30" r="2"/%3E%3C/g%3E%3C/g%3E%3C/svg%3E');
  }

  .bg-dots-pattern {
    background-image: radial-gradient(circle, rgba(156, 146, 172, 0.1) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* Container Classes */
  .container-tight {
    @apply container max-w-5xl mx-auto px-4 sm:px-6;
  }

  .container-wide {
    @apply container max-w-7xl mx-auto px-4 sm:px-6;
  }

  /* Modern Gradients */
  .gradient-primary {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
  }

  .gradient-secondary {
    background: linear-gradient(135deg, hsl(var(--secondary)) 0%, hsl(var(--muted)) 100%);
  }

  .gradient-text {
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    -webkit-background-clip: text;
    -webkit-text-fill-color: transparent;
    background-clip: text;
  }

  .gradient-border {
    position: relative;
    background: hsl(var(--background));
  }

  .gradient-border::before {
    content: '';
    position: absolute;
    inset: 0;
    padding: 1px;
    background: linear-gradient(135deg, hsl(var(--primary)) 0%, hsl(var(--accent)) 100%);
    border-radius: inherit;
    mask: linear-gradient(#fff 0 0) content-box, linear-gradient(#fff 0 0);
    mask-composite: xor;
    -webkit-mask-composite: xor;
  }

  /* Modern Effects */
  .glass-effect {
    background: rgba(255, 255, 255, 0.1);
    backdrop-filter: blur(10px);
    border: 1px solid rgba(255, 255, 255, 0.2);
  }

  .glow-effect {
    box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.3);
  }

  .floating-animation {
    animation: floating 3s ease-in-out infinite;
  }

  @keyframes floating {
    0%, 100% { transform: translateY(0px); }
    50% { transform: translateY(-10px); }
  }

  @keyframes pulse-glow {
    0%, 100% { box-shadow: 0 0 20px rgba(var(--primary-rgb), 0.3); }
    50% { box-shadow: 0 0 30px rgba(var(--primary-rgb), 0.6); }
  }

  @keyframes shimmer {
    0% { transform: translateX(-100%); }
    100% { transform: translateX(100%); }
  }

  @keyframes bounce-in {
    0% { transform: scale(0.3); opacity: 0; }
    50% { transform: scale(1.05); }
    70% { transform: scale(0.9); }
    100% { transform: scale(1); opacity: 1; }
  }

  @keyframes slide-up-fade {
    0% { transform: translateY(30px); opacity: 0; }
    100% { transform: translateY(0); opacity: 1; }
  }

  /* Advanced Effects */
  .shimmer-effect {
    position: relative;
    overflow: hidden;
  }

  .shimmer-effect::before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 100%;
    height: 100%;
    background: linear-gradient(90deg, transparent, rgba(255,255,255,0.2), transparent);
    transform: translateX(-100%);
    animation: shimmer 2s infinite;
  }

  .pulse-glow {
    animation: pulse-glow 2s ease-in-out infinite;
  }

  .bounce-in {
    animation: bounce-in 0.6s ease-out;
  }

  .slide-up-fade {
    animation: slide-up-fade 0.6s ease-out;
  }

  /* Hover Effects */
  .hover-lift {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  .hover-lift:hover {
    transform: translateY(-8px) scale(1.02);
    box-shadow: 0 20px 40px rgba(0,0,0,0.1);
  }

  .hover-glow:hover {
    box-shadow: 0 0 30px rgba(var(--primary-rgb), 0.4);
  }

  /* Text utilities */
  .line-clamp-1 {
    display: -webkit-box;
    -webkit-line-clamp: 1;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-2 {
    display: -webkit-box;
    -webkit-line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  .line-clamp-3 {
    display: -webkit-box;
    -webkit-line-clamp: 3;
    -webkit-box-orient: vertical;
    overflow: hidden;
  }

  /* Tool page specific styles */
  .tool-hero-gradient {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(var(--background)) 40%,
      hsl(var(--primary) / 0.05) 100%);
  }

  .tool-card-hover {
    @apply transition-all duration-300 ease-out hover:shadow-xl hover:shadow-primary/10 hover:-translate-y-2;
  }

  .sticky-header-blur {
    backdrop-filter: blur(12px);
    -webkit-backdrop-filter: blur(12px);
    background: rgba(var(--background-rgb), 0.95);
  }

  .rating-stars-glow .fill-yellow-400 {
    filter: drop-shadow(0 0 2px rgba(251, 191, 36, 0.5));
  }

  .review-card-border {
    border-left: 4px solid hsl(var(--primary));
    background: linear-gradient(90deg,
      hsl(var(--primary) / 0.05) 0%,
      transparent 100%);
  }

  .pros-cons-grid {
    display: grid;
    grid-template-columns: 1fr;
    gap: 2rem;
  }

  @media (min-width: 1024px) {
    .pros-cons-grid {
      grid-template-columns: 1fr 1fr;
    }
  }

  .feature-highlight {
    @apply bg-gradient-to-r from-green-50 to-emerald-50 dark:from-green-950/20 dark:to-emerald-950/20 border-green-200 dark:border-green-800;
  }

  .technical-info-card {
    @apply bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20 border-blue-200 dark:border-blue-800;
  }

  /* Scrollbar utilities */
  .scrollbar-hide {
    -ms-overflow-style: none;
    scrollbar-width: none;
  }

  .scrollbar-hide::-webkit-scrollbar {
    display: none;
  }

  .scrollbar-thin {
    scrollbar-width: thin;
  }

  .scrollbar-thin::-webkit-scrollbar {
    width: 4px;
    height: 4px;
  }

  .scrollbar-thin::-webkit-scrollbar-track {
    background: hsl(var(--muted));
  }

  .scrollbar-thin::-webkit-scrollbar-thumb {
    background: hsl(var(--muted-foreground) / 0.3);
    border-radius: 2px;
  }

  .scrollbar-thin::-webkit-scrollbar-thumb:hover {
    background: hsl(var(--muted-foreground) / 0.5);
  }

  /* Enhanced Tools Page Styles */
  .tools-hero-gradient {
    background: linear-gradient(135deg,
      hsl(var(--background)) 0%,
      hsl(var(--primary) / 0.03) 25%,
      hsl(var(--accent) / 0.03) 50%,
      hsl(var(--primary) / 0.03) 75%,
      hsl(var(--background)) 100%);
  }

  .tools-search-glow {
    box-shadow:
      0 0 0 1px hsl(var(--border)),
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .tools-search-glow:hover {
    box-shadow:
      0 0 0 1px hsl(var(--primary) / 0.3),
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05),
      0 0 20px hsl(var(--primary) / 0.1);
  }

  .tools-card-hover {
    transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
  }

  /* Fix for card overlapping issues */
  .tools-grid {
    display: grid;
    gap: 1rem;
  }

  .tools-grid > * {
    min-height: 240px;
    height: auto;
  }

  .tools-card-hover:hover {
    transform: translateY(-4px) scale(1.02);
    box-shadow:
      0 20px 25px -5px rgba(0, 0, 0, 0.1),
      0 10px 10px -5px rgba(0, 0, 0, 0.04),
      0 0 30px hsl(var(--primary) / 0.1);
  }

  .filter-button-active {
    background: linear-gradient(135deg, hsl(var(--primary)), hsl(var(--primary) / 0.9));
    box-shadow:
      0 4px 6px -1px hsl(var(--primary) / 0.3),
      0 2px 4px -1px hsl(var(--primary) / 0.2);
  }

  .animate-stagger-in {
    animation: stagger-in 0.6s ease-out forwards;
  }

  @keyframes stagger-in {
    0% {
      opacity: 0;
      transform: translateY(20px);
    }
    100% {
      opacity: 1;
      transform: translateY(0);
    }
  }

  /* Grid pattern for hero section */
  .bg-grid-pattern {
    background-image:
      linear-gradient(rgba(var(--foreground-rgb), 0.03) 1px, transparent 1px),
      linear-gradient(90deg, rgba(var(--foreground-rgb), 0.03) 1px, transparent 1px);
    background-size: 20px 20px;
  }

  /* Enhanced card shadows */
  .card-shadow-soft {
    box-shadow:
      0 1px 3px 0 rgba(0, 0, 0, 0.1),
      0 1px 2px 0 rgba(0, 0, 0, 0.06);
  }

  .card-shadow-medium {
    box-shadow:
      0 4px 6px -1px rgba(0, 0, 0, 0.1),
      0 2px 4px -1px rgba(0, 0, 0, 0.06);
  }

  .card-shadow-large {
    box-shadow:
      0 10px 15px -3px rgba(0, 0, 0, 0.1),
      0 4px 6px -2px rgba(0, 0, 0, 0.05);
  }

}

/* Animation classes */
.animate-fadeIn {
  animation: fadeIn 0.6s ease-out forwards;
}

.animate-slideUp {
  animation: slideUp 0.6s ease-out forwards;
}

.animate-slideDown {
  animation: slideDown 0.6s ease-out forwards;
}

.animate-slideInRight {
  animation: slideInRight 0.6s ease-out forwards;
}

.animate-scaleIn {
  animation: scaleIn 0.6s ease-out forwards;
}

/* Animation keyframes */
@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-30px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(30px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes slideUp {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideDown {
  from {
    opacity: 0;
    transform: translateY(-20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

@keyframes slideInRight {
  from {
    opacity: 0;
    transform: translateX(-20px);
  }
  to {
    opacity: 1;
    transform: translateX(0);
  }
}

@keyframes scaleIn {
  from {
    opacity: 0;
    transform: scale(0.9);
  }
  to {
    opacity: 1;
    transform: scale(1);
  }
}

@keyframes shimmer {
  0% {
    background-position: -200% 0;
  }
  100% {
    background-position: 200% 0;
  }
}

@keyframes glow {
  from {
    box-shadow: 0 0 3px rgba(59, 130, 246, 0.3);
  }
  to {
    box-shadow: 0 0 8px rgba(59, 130, 246, 0.5), 0 0 12px rgba(59, 130, 246, 0.2);
  }
}

@keyframes glow-subtle {
  from {
    box-shadow: 0 0 2px rgba(59, 130, 246, 0.2);
  }
  to {
    box-shadow: 0 0 4px rgba(59, 130, 246, 0.3);
  }
}

@keyframes glow-minimal {
  from {
    box-shadow: 0 0 1px rgba(59, 130, 246, 0.1);
  }
  to {
    box-shadow: 0 0 2px rgba(59, 130, 246, 0.2);
  }
}

@keyframes float {
  0%, 100% {
    transform: translateY(0px);
  }
  50% {
    transform: translateY(-10px);
  }
}

/* Dark mode specific keyframes */
@keyframes darkGlow {
  from {
    box-shadow: 0 0 5px rgba(147, 197, 253, 0.3);
  }
  to {
    box-shadow: 0 0 20px rgba(147, 197, 253, 0.6), 0 0 30px rgba(147, 197, 253, 0.3);
  }
}

@layer base {
  :root {
    --background: 0 0% 100%;
    --foreground: 222.2 84% 4.9%;
    --card: 0 0% 100%;
    --card-foreground: 222.2 84% 4.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 222.2 84% 4.9%;
    --primary: 221.2 83.2% 53.3%;
    --primary-foreground: 210 40% 98%;
    --secondary: 210 40% 96.1%;
    --secondary-foreground: 222.2 47.4% 11.2%;
    --muted: 210 40% 96.1%;
    --muted-foreground: 215.4 16.3% 46.9%;
    --accent: 262.1 83.3% 57.8%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 210 40% 98%;
    --border: 214.3 31.8% 91.4%;
    --input: 214.3 31.8% 91.4%;
    --ring: 221.2 83.2% 53.3%;
    --chart-1: 12 76% 61%;
    --chart-2: 173 58% 39%;
    --chart-3: 197 37% 24%;
    --chart-4: 43 74% 66%;
    --chart-5: 27 87% 67%;
    --radius: 0.5rem;
    --sidebar-background: 0 0% 98%;
    --sidebar-foreground: 240 5.3% 26.1%;
    --sidebar-primary: 240 5.9% 10%;
    --sidebar-primary-foreground: 0 0% 98%;
    --sidebar-accent: 240 4.8% 95.9%;
    --sidebar-accent-foreground: 240 5.9% 10%;
    --sidebar-border: 220 13% 91%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
  .dark {
    --background: 224 71% 4%;
    --foreground: 213 31% 91%;
    --card: 224 71% 4%;
    --card-foreground: 213 31% 91%;
    --popover: 224 71% 4%;
    --popover-foreground: 213 31% 91%;
    --primary: 217.2 91.2% 59.8%;
    --primary-foreground: 222.2 47.4% 11.2%;
    --secondary: 215 27.9% 16.9%;
    --secondary-foreground: 213 31% 91%;
    --muted: 215 27.9% 16.9%;
    --muted-foreground: 217.9 10.6% 64.9%;
    --accent: 262.1 83.3% 57.8%;
    --accent-foreground: 210 40% 98%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 210 40% 98%;
    --border: 215 27.9% 16.9%;
    --input: 215 27.9% 16.9%;
    --ring: 224.3 76.3% 48%;
    --chart-1: 220 70% 50%;
    --chart-2: 160 60% 45%;
    --chart-3: 30 80% 55%;
    --chart-4: 280 65% 60%;
    --chart-5: 340 75% 55%;
    --sidebar-background: 224 71% 4%;
    --sidebar-foreground: 213 31% 91%;
    --sidebar-primary: 224.3 76.3% 48%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 215 27.9% 16.9%;
    --sidebar-accent-foreground: 213 31% 91%;
    --sidebar-border: 215 27.9% 16.9%;
    --sidebar-ring: 217.2 91.2% 59.8%;
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground transition-colors duration-300;
    font-family: 'Inter', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    font-feature-settings: "ss01", "ss02", "cv01", "cv02", "cv03";
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
  }

  h1, h2, h3, h4, h5, h6 {
    font-family: 'Manrope', 'Inter', sans-serif;
    font-weight: 700;
    letter-spacing: -0.025em;
  }

  code, pre {
    font-family: 'JetBrains Mono', 'Fira Code', monospace;
  }

  /* Smooth transitions for dark mode */
  html {
    @apply transition-colors duration-300;
  }

  /* Enhanced dark mode styling */
  .dark {
    color-scheme: dark;
  }

  /* Improved scrollbar for dark mode */
  ::-webkit-scrollbar {
    width: 8px;
    height: 8px;
  }

  ::-webkit-scrollbar-track {
    @apply bg-background;
  }

  ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/30 rounded-full;
  }

  ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/50;
  }

  .dark ::-webkit-scrollbar-thumb {
    @apply bg-muted-foreground/40;
  }

  .dark ::-webkit-scrollbar-thumb:hover {
    @apply bg-muted-foreground/60;
  }
}

/* Grid utilities for equal height cards */
.grid-equal-height {
  display: grid;
  grid-auto-rows: 1fr;
}

.card-equal-height {
  display: flex;
  flex-direction: column;
  height: 100%;
}

.card-content-auto {
  flex: 1;
  display: flex;
  flex-direction: column;
}

.card-actions-bottom {
  margin-top: auto;
}

/* Improved card spacing */
.card-compact {
  @apply p-3 space-y-3;
}

.card-compact > * + * {
  @apply mt-3;
}

/* Status badge improvements */
.status-badge {
  @apply inline-flex items-center justify-center w-5 h-5 rounded-full shadow-md hover:scale-110 transition-transform duration-200;
}

.status-badge-featured {
  @apply bg-gradient-to-br from-amber-400 to-amber-500;
}

.status-badge-verified {
  @apply bg-gradient-to-br from-blue-500 to-blue-600;
}

.status-badge-new {
  @apply bg-gradient-to-br from-green-500 to-emerald-600;
}

/* Background pattern for enhanced design */
.bg-grid-pattern {
  background-image: radial-gradient(circle at 1px 1px, rgba(0,0,0,0.15) 1px, transparent 0);
  background-size: 20px 20px;
}

.dark .bg-grid-pattern {
  background-image: radial-gradient(circle at 1px 1px, rgba(255,255,255,0.1) 1px, transparent 0);
}

/* Glow effect for animated text - simple method */
.dark .text-shadow-glow {
  text-shadow: 0 0 2px rgba(255, 255, 255, 0.2), 0 0 15px rgba(79, 70, 229, 0.5);
}

@media (min-width: 640px) {
  .text-shadow-glow {
    text-shadow:
      -1.5px -1.5px 0 #f1c40f,
      1.5px -1.5px 0 #f1c40f,
      -1.5px 1.5px 0 #f1c40f,
      1.5px 1.5px 0 #f1c40f,
      0 0 10px rgba(129, 140, 248, 0.4);
  }

  .dark .text-shadow-glow {
    text-shadow:
      -1.5px -1.5px 0 #f1c40f,
      1.5px -1.5px 0 #f1c40f,
      -1.5px 1.5px 0 #f1c40f,
      1.5px 1.5px 0 #f1c40f,
      0 0 10px rgba(129, 140, 248, 0.7),
      0 0 20px rgba(129, 140, 248, 0.4);
  }
}

@media (min-width: 1024px) {
  .text-shadow-glow {
    text-shadow:
      -2px -2px 0 #f1c40f,
      2px -2px 0 #f1c40f,
      -2px 2px 0 #f1c40f,
      2px 2px 0 #f1c40f,
      0 0 10px rgba(129, 140, 248, 0.4);
  }

  .dark .text-shadow-glow {
    text-shadow:
      -2px -2px 0 #f1c40f,
      2px -2px 0 #f1c40f,
      -2px 2px 0 #f1c40f,
      2px 2px 0 #f1c40f,
      0 0 10px rgba(129, 140, 248, 0.7),
      0 0 20px rgba(129, 140, 248, 0.4);
  }
}
