'use client'

import { useState, useEffect } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Star, Globe, ExternalLink, Heart, Share2, CheckCircle } from 'lucide-react'
import { cn } from '@/lib/utils'

interface StickyToolHeaderProps {
  tool: any
  averageRating?: number
  reviewsCount: number
  onVisitWebsite: () => void
  onTabChange: (tab: string) => void
  activeTab: string
  className?: string
}

export function StickyToolHeader({
  tool,
  averageRating,
  reviewsCount,
  onVisitWebsite,
  onTabChange,
  activeTab,
  className
}: StickyToolHeaderProps) {
  const [isSticky, setIsSticky] = useState(false)
  const [headerHeight, setHeaderHeight] = useState(68)

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY
      // Adjust threshold to account for navbar height and content
      setIsSticky(scrollPosition > 400)

      // Calculate header height based on scroll position
      // Header uses py-3 (~60px) when not scrolled, py-2 (~56px) when scrolled
      const isHeaderScrolled = scrollPosition > 20
      setHeaderHeight(isHeaderScrolled ? 56 : 60)
    }

    window.addEventListener('scroll', handleScroll)
    return () => window.removeEventListener('scroll', handleScroll)
  }, [])

  const RatingStars = ({ rating }: { rating: number }) => (
    <div className="flex gap-0.5">
      {[1, 2, 3, 4, 5].map((star) => (
        <Star
          key={star}
          size={14}
          className={star <= rating ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground/30"}
        />
      ))}
    </div>
  )

  return (
    <div
      className={cn(
        "transition-all duration-300",
        isSticky
          ? "fixed left-0 right-0 bg-background/95 backdrop-blur-sm border-b border-border shadow-lg z-40"
          : "relative bg-transparent",
        className
      )}
      style={{
        top: isSticky ? `${headerHeight}px` : '0px'
      }}
    >
      <div className="container max-w-7xl mx-auto px-4">
        <div className={cn(
          "flex items-center justify-between transition-all duration-300",
          isSticky ? "py-3" : "py-0"
        )}>
          {/* Tool Info */}
          <div className="flex items-center gap-4 min-w-0 flex-1">
            {isSticky && (
              <div className="w-10 h-10 rounded-lg overflow-hidden bg-muted flex-shrink-0">
                <img
                  src={tool.logo_url || "https://via.placeholder.com/40?text=AI"}
                  alt={`${tool.company_name} logo`}
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    (e.target as HTMLImageElement).src = "https://via.placeholder.com/40?text=AI"
                  }}
                />
              </div>
            )}

            <div className="min-w-0 flex-1">
              <div className="flex items-center gap-2 mb-1">
                <h1 className={cn(
                  "font-bold truncate transition-all duration-300",
                  isSticky ? "text-lg" : "text-2xl md:text-3xl"
                )}>
                  {tool.company_name}
                </h1>
                {tool.is_verified && (
                  <Badge className="bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200 gap-1 flex-shrink-0">
                    <CheckCircle className="h-3 w-3" />
                    Verified
                  </Badge>
                )}
              </div>

              {isSticky && averageRating && (
                <div className="flex items-center gap-2 text-sm">
                  <RatingStars rating={Math.round(averageRating)} />
                  <span className="font-medium">{averageRating}</span>
                  <span className="text-muted-foreground">
                    ({reviewsCount} {reviewsCount === 1 ? 'review' : 'reviews'})
                  </span>
                </div>
              )}
            </div>
          </div>

          {/* Navigation Tabs */}
          <div className="hidden md:flex items-center gap-1 mx-4">
            {['overview', 'features', 'reviews'].map((tab) => (
              <Button
                key={tab}
                variant={activeTab === tab ? "default" : "ghost"}
                size="sm"
                onClick={() => onTabChange(tab)}
                className={cn(
                  "capitalize transition-all duration-200",
                  activeTab === tab && "bg-primary text-primary-foreground"
                )}
              >
                {tab}
                {tab === 'reviews' && reviewsCount > 0 && (
                  <Badge variant="secondary" className="ml-2 text-xs">
                    {reviewsCount}
                  </Badge>
                )}
              </Button>
            ))}
          </div>

          {/* Action Buttons */}
          <div className="flex items-center gap-2 flex-shrink-0">
            <Button
              size={isSticky ? "sm" : "default"}
              className="bg-primary hover:bg-primary/90 text-primary-foreground shadow-lg hover:shadow-xl transition-all gap-2"
              onClick={onVisitWebsite}
            >
              <Globe className="h-4 w-4" />
              {isSticky ? "Visit" : "Visit Website"}
              <ExternalLink className="h-3 w-3" />
            </Button>

            {!isSticky && (
              <>
                <Button variant="outline" size="default" className="gap-2 hover:bg-red-50 dark:hover:bg-red-950/20">
                  <Heart className="h-4 w-4" />
                  Save
                </Button>

                <Button variant="outline" size="default" className="gap-2">
                  <Share2 className="h-4 w-4" />
                  Share
                </Button>
              </>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
