"use client"

import { useRouter, useSearchParams } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Label } from "@/components/ui/label"
import { Separator } from "@/components/ui/separator"
import { ScrollArea } from "@/components/ui/scroll-area"
import { cn } from "@/lib/utils"

interface Category {
  id: string
  name: string
  count: number
}

interface ToolsFiltersServerProps {
  categories: Category[]
  pricingOptions: string[]
}

export default function ToolsFiltersServer({
  categories,
  pricingOptions,
}: ToolsFiltersServerProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  // Get current filter values
  const currentCategory = searchParams.get("category") || ""
  const currentPricing = searchParams.get("pricing") || ""
  const currentSortBy = searchParams.get("sortBy") || "featured"
  const currentSearch = searchParams.get("search") || ""
  const currentFeaturesParam = searchParams.get("features") || ""
  const currentFeatures = currentFeaturesParam ? currentFeaturesParam.split(',') : []
  
  // Common features for AI tools
  const commonFeatures = [
    "api-access",
    "free-tier",
    "mobile-app",
    "browser-extension",
    "offline-mode",
    "team-collaboration",
    "custom-templates",
    "multilingual",
  ]
  
  // Handle filter changes
  const handleFilterChange = (type: string, value: string) => {
    const params = new URLSearchParams(searchParams.toString())
    
    if (value) {
      params.set(type, value)
    } else {
      params.delete(type)
    }
    
    router.push(`/tools?${params.toString()}`)
  }
  
  // Handle feature toggle
  const handleFeatureToggle = (feature: string, checked: boolean) => {
    const params = new URLSearchParams(searchParams.toString())
    let features = currentFeatures
    
    if (checked) {
      features.push(feature)
    } else {
      features = features.filter(f => f !== feature)
    }
    
    if (features.length > 0) {
      params.set("features", features.join(','))
    } else {
      params.delete("features")
    }
    
    router.push(`/tools?${params.toString()}`)
  }

  return (
    <div className="space-y-6">
      {/* Categories */}
      <div>
        <h3 className="font-semibold mb-4 text-foreground flex items-center gap-2">
          <div className="w-2 h-2 bg-primary rounded-full"></div>
          Categories
        </h3>
        <ScrollArea className="h-[240px] pr-3">
          <div className="space-y-1">
            <Button
              variant={!currentCategory ? "default" : "ghost"}
              size="sm"
              className={cn(
                "w-full justify-between text-left h-9 transition-all duration-200",
                !currentCategory
                  ? "bg-primary text-primary-foreground shadow-sm"
                  : "hover:bg-muted/50 hover:text-foreground"
              )}
              onClick={() => handleFilterChange("category", "")}
            >
              <span>All Categories</span>
              <span className="text-xs opacity-70">
                {categories.reduce((acc, cat) => acc + cat.count, 0)}
              </span>
            </Button>

            {categories.map((category) => (
              <Button
                key={category.id}
                variant={currentCategory === category.id ? "default" : "ghost"}
                size="sm"
                className={cn(
                  "w-full justify-between text-left h-9 transition-all duration-200",
                  currentCategory === category.id
                    ? "bg-primary text-primary-foreground shadow-sm"
                    : "hover:bg-muted/50 hover:text-foreground"
                )}
                onClick={() => handleFilterChange("category", category.id)}
              >
                <span className="truncate">{category.name}</span>
                <span className="text-xs opacity-70 ml-2 flex-shrink-0">
                  {category.count}
                </span>
              </Button>
            ))}
          </div>
        </ScrollArea>
      </div>


      <Separator className="bg-border/50" />

      {/* Sort By */}
      <div>
        <h3 className="font-semibold mb-4 text-foreground flex items-center gap-2">
          <div className="w-2 h-2 bg-blue-500 rounded-full"></div>
          Sort By
        </h3>
        <RadioGroup
          value={currentSortBy}
          onValueChange={(value) => handleFilterChange("sortBy", value)}
          className="space-y-2"
        >
          <div className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
            <RadioGroupItem value="featured" id="sort-featured" className="border-2" />
            <Label htmlFor="sort-featured" className="font-medium cursor-pointer flex-1">✨ Featured</Label>
          </div>
          <div className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
            <RadioGroupItem value="newest" id="sort-newest" className="border-2" />
            <Label htmlFor="sort-newest" className="font-medium cursor-pointer flex-1">🕒 Newest</Label>
          </div>
          <div className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
            <RadioGroupItem value="popular" id="sort-popular" className="border-2" />
            <Label htmlFor="sort-popular" className="font-medium cursor-pointer flex-1">🔥 Most Popular</Label>
          </div>
          <div className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
            <RadioGroupItem value="rating" id="sort-rating" className="border-2" />
            <Label htmlFor="sort-rating" className="font-medium cursor-pointer flex-1">⭐ Highest Rated</Label>
          </div>
        </RadioGroup>
      </div>

      <Separator className="bg-border/50" />

      {/* Features */}
      <div>
        <h3 className="font-semibold mb-4 text-foreground flex items-center gap-2">
          <div className="w-2 h-2 bg-purple-500 rounded-full"></div>
          Features
        </h3>
        <div className="space-y-2">
          {commonFeatures.map((feature) => (
            <div key={feature} className="flex items-center space-x-3 p-2 rounded-lg hover:bg-muted/50 transition-colors">
              <Checkbox
                id={`feature-${feature}`}
                checked={currentFeatures.includes(feature)}
                onCheckedChange={(checked) => handleFeatureToggle(feature, checked as boolean)}
                className="border-2"
              />
              <Label htmlFor={`feature-${feature}`} className="font-medium cursor-pointer flex-1">
                {feature.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
              </Label>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
