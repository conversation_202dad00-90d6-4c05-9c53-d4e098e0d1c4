"use client"

import { useEffect, useState } from 'react'
import SafeComponent from '@/components/routing/SafeComponent'
import { LoadingGrid } from '@/components/ui/loading-grid'

interface SafeToolsSectionProps {
  title: string
  description: string
  queryType: "featured" | "top-rated" | "recent" | "all"
  limit?: number
  variant?: "primary" | "secondary" | "accent" | "subtle" | "none"
}

export default function SafeToolsSection(props: SafeToolsSectionProps) {
  const [isAppRouter, setIsAppRouter] = useState<boolean | null>(null)
  
  useEffect(() => {
    // Check if we're in App Router by looking for the presence of next/navigation
    // This is a simple heuristic that works in most cases
    try {
      // If this import succeeds, we're likely in App Router
      require('next/navigation')
      setIsAppRouter(true)
    } catch (error) {
      // If it fails, we're likely in Pages Router
      setIsAppRouter(false)
    }
  }, [])

  if (isAppRouter === null) {
    // Still determining router type
    return <LoadingGrid count={props.limit || 12} columns={4} />
  }

  if (isAppRouter) {
    // Use App Router version
    return (
      <SafeComponent
        fallback={<LoadingGrid count={props.limit || 12} columns={4} />}
        importComponent={() => import('@/components/home/<USER>')}
        componentProps={props}
      />
    )
  } else {
    // Use Pages Router version
    return (
      <SafeComponent
        fallback={<LoadingGrid count={props.limit || 12} columns={4} />}
        importComponent={() => import('@/components/home/<USER>')}
        componentProps={props}
      />
    )
  }
}
