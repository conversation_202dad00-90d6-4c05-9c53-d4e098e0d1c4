"use client"

import { useState, useEffect, useCallback, useMemo } from "react"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { AlertCircle } from "lucide-react"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { But<PERSON> } from "@/components/ui/button"
import { motion } from "@/lib/motion-stub"
import ToolCard from "@/components/tool-card"
import { QUERY_TIMEOUTS, QUERY_LIMITS, executeQueryWithTimeout } from "@/lib/query-timeout-config"
import { createToolsQueryBuilder, type SearchFilters } from "@/lib/supabase/query-optimizer"

export interface Tool {
  id: number | string
  name?: string
  description?: string
  logo?: string
  category?: string
  rating?: number
  reviewCount?: number
  pricing?: string
  url?: string
  slug?: string
  isFeatured?: boolean
  isNew?: boolean

  short_description?: string
  full_description?: string
  logo_url?: string
  primary_task?: string
  visit_website_url?: string
  featured_image_url?: string
  click_count?: number
  created_at?: string
  updated_at?: string
  applicable_tasks?: any[]
  company_name?: string
  cons?: any[]
  pros?: any[]
  faqs?: any
  detail_url?: string
  is_featured?: boolean
  is_verified?: boolean
  isVerified?: boolean
}

interface ToolGridProps {
  limit?: number
  queryType?: "featured" | "top-rated" | "recent" | "popular" | "all"
  searchTerm?: string
  categoryFilter?: string
  searchQuery?: string
  category?: string
  pricing?: string
  sortBy?: string
  columnsPerRow?: number
  features?: string[]
  tools?: Tool[]
}

export default function ToolGrid({
  limit = 20,
  queryType = "all",
  searchTerm,
  categoryFilter,
  searchQuery = "",
  category = "",
  pricing = "",
  sortBy = "featured",
  columnsPerRow = 4,
  features = [],
  tools: providedTools
}: ToolGridProps) {
  const [loadedTools, setLoadedTools] = useState<Tool[]>([])
  const [isLoading, setIsLoading] = useState(!providedTools)
  const [error, setError] = useState<Error | null>(null)
  const effectiveSearchTerm = searchQuery || searchTerm || ""
  const effectiveCategoryFilter = category || categoryFilter || ""
  const effectiveSortBy = sortBy !== "featured" ? sortBy : queryType

  // Create a cache key for the current query parameters
  // Use a stable reference for features.join(',') to prevent unnecessary re-renders
  const featuresKey = useMemo(() => features.join(','), [features]);

  const createCacheKey = useCallback(() => {
    return `tools-${queryType}-${limit}-${effectiveSearchTerm}-${effectiveCategoryFilter}-${pricing}-${effectiveSortBy}-${featuresKey}`
  }, [queryType, limit, effectiveSearchTerm, effectiveCategoryFilter, pricing, effectiveSortBy, featuresKey]);

  // Function to fetch tools from API
  const fetchToolsFromAPI = useCallback(async (showLoading = true) => {
    try {
      if (showLoading) {
        setIsLoading(true)
      }

      // Use the optimized query builder
      const queryBuilder = createToolsQueryBuilder()

      // Prepare filters
      const filters: SearchFilters = {
        category: effectiveCategoryFilter || undefined,
        pricing: pricing || undefined,
        featured: queryType === "featured" ? true : undefined,
        sortBy: effectiveSortBy || 'default'
      }

      // Create cache key for this specific query
      const cacheKey = createCacheKey()

      let result

      // Use search query if there's a search term, otherwise use regular query
      if (effectiveSearchTerm && effectiveSearchTerm.trim().length >= 2) {
        result = await queryBuilder.buildSearchQuery(effectiveSearchTerm, filters, {
          limit: limit || QUERY_LIMITS.TOOLS_GRID,
          timeout: QUERY_TIMEOUTS.SEARCH,
          useCache: true,
          cacheKey
        })
      } else {
        const query = queryBuilder.buildQuery(filters, {
          limit: limit || QUERY_LIMITS.TOOLS_GRID,
          timeout: QUERY_TIMEOUTS.MEDIUM,
          useCache: true,
          cacheKey
        })
        result = query
      }

      // Execute the query with timeout handling
      const { data: fetchedData, error: queryError, fromFallback, fromCache } = await queryBuilder.executeQuery(result, {
        timeout: QUERY_TIMEOUTS.MEDIUM,
        retries: 1,
        useCache: true,
        cacheKey
      })

      if (queryError && !fromFallback && !fromCache) {
        console.error("Error executing optimized query:", queryError)
        throw queryError
      }

      if (fromFallback) {
        console.warn("Using fallback data due to timeout or error")
      }

      if (fromCache) {
        console.log("Using cached data")
      }

      // Additional caching for session storage (backup cache)
      if (fetchedData && fetchedData.length > 0 && !fromCache) {
        try {
          sessionStorage.setItem(cacheKey, JSON.stringify(fetchedData))
        } catch (e) {
          console.warn("Failed to cache data:", e)
        }
      }

      // Process the fetched tools
      const toolsData = fetchedData || []
      const mappedTools = toolsData.map(tool => ({
        id: tool.id,
        name: tool.company_name || tool.name || "",
        company_name: tool.company_name || tool.name || "",
        description: tool.short_description || tool.description || "",
        short_description: tool.short_description || tool.description || "",
        logo: tool.logo_url || tool.logo || "",
        logo_url: tool.logo_url || tool.logo || "",
        category: tool.primary_task || tool.category || "",
        primary_task: tool.primary_task || tool.category || "",
        rating: tool.rating || 4,
        reviewCount: tool.reviewCount || 0,
        pricing: tool.pricing || "",
        url: tool.visit_website_url || tool.detail_url || tool.url || "#",
        visit_website_url: tool.visit_website_url || "",
        detail_url: tool.detail_url || "",
        slug: tool.slug || "",
        isFeatured: Boolean(tool.is_featured || tool.isFeatured),
        isVerified: Boolean(tool.is_verified || tool.isVerified),
        is_featured: tool.is_featured || tool.isFeatured,
        is_verified: tool.is_verified || tool.isVerified,
        isNew: new Date(tool.created_at || "").getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000,
        click_count: tool.click_count || 0,
        ...tool
      }))

      // Custom sort function that prioritizes Featured > Verified > Others
      const sortedTools = [...mappedTools].sort((a, b) => {
        // First priority: Featured tools
        if (Boolean(a.is_featured || a.isFeatured) && !Boolean(b.is_featured || b.isFeatured)) return -1
        if (!Boolean(a.is_featured || a.isFeatured) && Boolean(b.is_featured || b.isFeatured)) return 1

        // Second priority: Verified tools
        if (Boolean(a.is_verified || a.isVerified) && !Boolean(b.is_verified || b.isVerified)) return -1
        if (!Boolean(a.is_verified || a.isVerified) && Boolean(b.is_verified || b.isVerified)) return 1

        // Third priority: Rating
        if ((a.rating || 0) > (b.rating || 0)) return -1
        if ((a.rating || 0) < (b.rating || 0)) return 1

        // Fourth priority: Category (if specified in filters)
        if (effectiveCategoryFilter && a.primary_task === effectiveCategoryFilter && b.primary_task !== effectiveCategoryFilter) return -1
        if (effectiveCategoryFilter && a.primary_task !== effectiveCategoryFilter && b.primary_task === effectiveCategoryFilter) return 1

        // Fifth priority: By click count (popularity)
        return (b.click_count || 0) - (a.click_count || 0)
      })

      // Apply limit after sorting
      const limitedTools = limit ? sortedTools.slice(0, limit) : sortedTools
      setLoadedTools(limitedTools)
    } catch (err) {
      console.error("Error in tools query:", err)
      setError(err instanceof Error ? err : new Error('Unknown error occurred'))
    } finally {
      setIsLoading(false)
    }
  }, [queryType, limit, effectiveSearchTerm, effectiveCategoryFilter, pricing, effectiveSortBy, featuresKey]);

  // Effect to load tools on component mount or when dependencies change
  useEffect(() => {
    // Skip if tools are provided directly
    if (providedTools) {
      return;
    }

    // Check if we have cached data
    try {
      const cachedData = sessionStorage.getItem(createCacheKey())
      if (cachedData) {
        const parsedData = JSON.parse(cachedData)
        setLoadedTools(parsedData)
        setIsLoading(false)

        // Still fetch fresh data in the background after a short delay
        setTimeout(() => fetchToolsFromAPI(false), 1000)
        return
      }
    } catch (e) {
      console.warn("Failed to read from cache:", e)
    }

    // Fetch fresh data
    fetchToolsFromAPI(true)
  }, [providedTools, fetchToolsFromAPI])

  // Process provided tools
  useEffect(() => {
    if (providedTools) {
      const mappedTools = providedTools.map(tool => ({
        id: tool.id,
        name: tool.company_name || tool.name || "",
        company_name: tool.company_name || tool.name || "",
        description: tool.short_description || tool.description || "",
        short_description: tool.short_description || tool.description || "",
        logo: tool.logo_url || tool.logo || "",
        logo_url: tool.logo_url || tool.logo || "",
        category: tool.primary_task || tool.category || "",
        primary_task: tool.primary_task || tool.category || "",
        rating: tool.rating || 4,
        reviewCount: tool.reviewCount || 0,
        pricing: tool.pricing || "",
        url: tool.visit_website_url || tool.detail_url || tool.url || "#",
        visit_website_url: tool.visit_website_url || "",
        detail_url: tool.detail_url || "",
        slug: tool.slug || "",
        isFeatured: Boolean(tool.is_featured || tool.isFeatured),
        isVerified: Boolean(tool.is_verified || tool.isVerified),
        is_featured: tool.is_featured || tool.isFeatured,
        is_verified: tool.is_verified || tool.isVerified,
        isNew: new Date(tool.created_at || "").getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000,
        click_count: tool.click_count || 0,
        ...tool
      }))

      // Apply limit
      const limitedTools = limit ? mappedTools.slice(0, limit) : mappedTools
      setLoadedTools(limitedTools)
    }
  }, [providedTools, limit])

  if (isLoading) {
    return <ToolGridSkeleton count={limit || 8} columnsPerRow={columnsPerRow} />
  }

  if (error) {
    return (
      <Alert variant="destructive" className="mb-6">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Loading Error</AlertTitle>
        <AlertDescription className="flex flex-col gap-2">
          <p>There was an error loading the tools. Please try again later.</p>
          <Button
            onClick={() => fetchToolsFromAPI(true)}
            className="w-fit"
            variant="outline"
            size="sm"
          >
            Try Again
          </Button>
        </AlertDescription>
      </Alert>
    )
  }

  if (!loadedTools.length) {
    return <EmptyToolsMessage />
  }

  // Improved grid classes with more consistent breakpoints and better mobile experience
  let gridColsClasses = ""
  switch (columnsPerRow) {
    case 1:
      gridColsClasses = "grid-cols-1"
      break
    case 2:
      gridColsClasses = "grid-cols-1 sm:grid-cols-2"
      break
    case 3:
      // Better mobile-first approach with proper stepping
      gridColsClasses = "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
      break
    case 4:
    default:
      // Optimized for mobile: 1 -> 2 -> 3 -> 4 columns
      gridColsClasses = "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4"
      break
    case 5:
      gridColsClasses = "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-5"
      break
  }

  return (
    <div className={`tools-grid grid ${gridColsClasses} gap-3 sm:gap-4`}>
      {loadedTools.map((tool, index) => {
        // Create a truly unique key by combining id with index
        const uniqueKey = `tool-${tool.id}-${index}`;

        // تحسين الأداء: تقليل الرسوم المتحركة للعناصر الأولى فقط
        if (index < 4) {
          return (
            <motion.div
              key={uniqueKey}
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{
                delay: index * 0.05,  // تأخير أقل
                duration: 0.2  // مدة أقصر
              }}
              className="flex"
            >
              <ToolCard tool={tool} className="w-full" />
            </motion.div>
          );
        } else {
          // بدون رسوم متحركة للعناصر الأخرى لتحسين الأداء
          return (
            <div key={uniqueKey} className="flex">
              <ToolCard tool={tool} className="w-full" />
            </div>
          );
        }
      })}
    </div>
  )
}

// ToolGridSkeleton component

function ToolGridSkeleton({ count, columnsPerRow = 4 }: { count: number; columnsPerRow?: number }) {
  const [displayCount, setDisplayCount] = useState(4); // Default to 4 for SSR

  // Improved grid classes with more consistent breakpoints and better mobile experience
  let gridColsClasses = ""
  switch (columnsPerRow) {
    case 1:
      gridColsClasses = "grid-cols-1"
      break
    case 2:
      gridColsClasses = "grid-cols-1 sm:grid-cols-2"
      break
    case 3:
      // Better mobile-first approach with proper stepping
      gridColsClasses = "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"
      break
    case 4:
    default:
      // Optimized for mobile: 1 -> 2 -> 3 -> 4 columns
      gridColsClasses = "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4"
      break
    case 5:
      gridColsClasses = "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 2xl:grid-cols-5"
      break
  }

  // Define handleResize outside useEffect and memoize it
  const handleResize = useCallback(() => {
    const width = window.innerWidth;
    if (width >= 1280) {
      setDisplayCount(Math.min(count, 8)); // Desktop
    } else if (width >= 768) {
      setDisplayCount(Math.min(count, 6)); // Tablet
    } else {
      setDisplayCount(Math.min(count, 4)); // Mobile
    }
  }, [count, setDisplayCount]);

  // Move window-dependent logic to useEffect
  useEffect(() => {
    // Initial calculation
    handleResize();

    // Add event listener
    window.addEventListener('resize', handleResize);

    // Cleanup
    return () => window.removeEventListener('resize', handleResize);
  }, [handleResize]);

  // Use fixed delay classes instead of dynamic ones
  const getDelayClass = (index: number) => {
    if (index === 0) return "delay-50";
    if (index === 1) return "delay-100";
    if (index === 2) return "delay-150";
    if (index === 3) return "delay-200";
    if (index === 4) return "delay-250";
    if (index === 5) return "delay-300";
    return "delay-300";
  };

  return (
    <div className="flex flex-col items-center justify-center py-6 min-h-[300px]">
      <div className="flex items-center justify-center gap-2 mb-4">
        <div className="animate-pulse h-3 w-3 rounded-full bg-blue-500"></div>
        <div className="animate-pulse h-3 w-3 rounded-full bg-blue-500 delay-200"></div>
        <div className="animate-pulse h-3 w-3 rounded-full bg-blue-500 delay-400"></div>
        <span className="ml-2 text-slate-600 dark:text-slate-400">Loading tools...</span>
      </div>

      <div className={`tools-grid grid ${gridColsClasses} gap-3 sm:gap-4 w-full`}>
        {/* Render skeleton items with fixed delay classes */}
        {Array(displayCount).fill(0).map((_, index) => (
          <div
            key={index}
            className={`min-h-[240px] animate-pulse rounded-xl border border-gray-200 dark:border-gray-800 bg-white dark:bg-gray-900 p-4 ${getDelayClass(index)}`}
          >
            <div className="flex gap-3">
              <div className="h-11 w-11 rounded-xl bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600"></div>
              <div className="flex-1 space-y-2 relative">
                {/* Status icons skeleton */}
                <div className="absolute -top-1 right-0 flex gap-1">
                  <div className="w-7 h-7 bg-gradient-to-br from-gray-200 to-gray-300 dark:from-gray-700 dark:to-gray-600 rounded-full ring-2 ring-white/50 dark:ring-gray-900/50"></div>
                </div>
                <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded w-3/4 pr-8"></div>
                <div className="flex flex-col gap-1.5 mt-1">
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2"></div>
                  <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
                </div>
              </div>
            </div>
            <div className="space-y-2 mt-2.5">
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-full"></div>
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3"></div>
            </div>
            <div className="mt-2.5">
              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/3"></div>
            </div>
            <div className="mt-auto pt-3 flex gap-1.5 items-center">
              <div className="flex-1 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              <div className="h-10 w-10 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
              <div className="flex-1 h-8 bg-gray-200 dark:bg-gray-700 rounded-lg"></div>
            </div>
          </div>
        ))}
      </div>
    </div>
  )
}

function EmptyToolsMessage() {
  return (
    <div className="text-center py-12 px-4">
      <h3 className="text-xl font-medium mb-2">No tools found</h3>
      <p className="text-slate-600 dark:text-slate-400 max-w-md mx-auto">
        Try adjusting your search criteria or filters to find AI tools matching your requirements.
      </p>
    </div>
  )
}
