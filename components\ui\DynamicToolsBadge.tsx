"use client"

import { Spark<PERSON> } from "lucide-react"
import { useFormattedStats } from "@/hooks/use-stats"

interface DynamicToolsBadgeProps {
  className?: string
  showIcon?: boolean
  iconSize?: number
  loadingText?: string
}

export default function DynamicToolsBadge({ 
  className = "inline-block rounded-full bg-primary/10 px-4 py-1.5 text-sm font-medium text-primary",
  showIcon = true,
  iconSize = 16,
  loadingText = "Loading..."
}: DynamicToolsBadgeProps) {
  const { totalToolsFormatted, isLoading } = useFormattedStats()

  return (
    <span className={className}>
      {showIcon && (
        <Sparkles 
          size={iconSize} 
          className="mr-1.5 inline-block animate-pulse" 
        />
      )}
      {isLoading ? loadingText : `${totalToolsFormatted}+ AI tools`}
    </span>
  )
}
