// Simple stub for framer-motion to avoid SSR issues
import React from 'react'

// Motion component stub
export const motion = {
  div: React.forwardRef<HTMLDivElement, any>((props, ref) => <div ref={ref} {...props} />),
  span: React.forwardRef<HTMLSpanElement, any>((props, ref) => <span ref={ref} {...props} />),
  h1: React.forwardRef<HTMLHeadingElement, any>((props, ref) => <h1 ref={ref} {...props} />),
  h2: React.forwardRef<HTMLHeadingElement, any>((props, ref) => <h2 ref={ref} {...props} />),
  h3: React.forwardRef<HTMLHeadingElement, any>((props, ref) => <h3 ref={ref} {...props} />),
  p: React.forwardRef<HTMLParagraphElement, any>((props, ref) => <p ref={ref} {...props} />),
  button: React.forwardRef<HTMLButtonElement, any>((props, ref) => <button ref={ref} {...props} />),
  section: React.forwardRef<HTMLElement, any>((props, ref) => <section ref={ref} {...props} />),
  article: React.forwardRef<HTMLElement, any>((props, ref) => <article ref={ref} {...props} />),
  header: React.forwardRef<HTMLElement, any>((props, ref) => <header ref={ref} {...props} />),
  footer: React.forwardRef<HTMLElement, any>((props, ref) => <footer ref={ref} {...props} />),
  nav: React.forwardRef<HTMLElement, any>((props, ref) => <nav ref={ref} {...props} />),
  ul: React.forwardRef<HTMLUListElement, any>((props, ref) => <ul ref={ref} {...props} />),
  li: React.forwardRef<HTMLLIElement, any>((props, ref) => <li ref={ref} {...props} />),
  a: React.forwardRef<HTMLAnchorElement, any>((props, ref) => <a ref={ref} {...props} />),
  img: React.forwardRef<HTMLImageElement, any>((props, ref) => <img ref={ref} {...props} />),
}

// AnimatePresence stub
export const AnimatePresence: React.FC<{ children: React.ReactNode; mode?: string }> = ({ children }) => {
  return <>{children}</>
}

// useInView stub
export const useInView = () => true

// useAnimation stub
export const useAnimation = () => ({
  start: () => {},
  stop: () => {},
  set: () => {},
})

// Variants stub
export const variants = {}

// Export default motion
export default motion
