#!/usr/bin/env node

/**
 * <PERSON>ript to check sitemap generation and verify all tools are included
 * Run with: node scripts/check-sitemap.js
 */

const { createClient } = require('@supabase/supabase-js')
require('dotenv').config({ path: '.env.local' })

async function checkSitemap() {
  console.log('🔍 Checking sitemap generation...\n')

  try {
    // Create Supabase client
    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
        }
      }
    )

    // Get total tools count
    const { count: totalTools, error: countError } = await supabase
      .from('tools')
      .select('*', { count: 'exact', head: true })

    if (countError) {
      throw new Error(`Error getting total tools count: ${countError.message}`)
    }

    console.log(`📊 Total tools in database: ${totalTools}`)

    // Get tools with slugs (these will be in sitemap)
    const { data: toolsWithSlugs, error: slugError } = await supabase
      .from('tools')
      .select('slug, company_name, updated_at, created_at')
      .not('slug', 'is', null)

    if (slugError) {
      throw new Error(`Error getting tools with slugs: ${slugError.message}`)
    }

    console.log(`✅ Tools with slugs (in sitemap): ${toolsWithSlugs.length}`)

    // Get tools without slugs (missing from sitemap)
    const { data: toolsWithoutSlugs, error: noSlugError } = await supabase
      .from('tools')
      .select('id, company_name')
      .is('slug', null)

    if (noSlugError) {
      throw new Error(`Error getting tools without slugs: ${noSlugError.message}`)
    }

    console.log(`⚠️  Tools without slugs (missing from sitemap): ${toolsWithoutSlugs.length}`)

    if (toolsWithoutSlugs.length > 0) {
      console.log('\n🔧 Tools missing slugs:')
      toolsWithoutSlugs.slice(0, 10).forEach(tool => {
        console.log(`   - ID: ${tool.id}, Name: ${tool.company_name || 'No name'}`)
      })
      if (toolsWithoutSlugs.length > 10) {
        console.log(`   ... and ${toolsWithoutSlugs.length - 10} more`)
      }
    }

    // Check for problematic slugs
    console.log('\n🔍 Checking for problematic slugs...')
    const problematicSlugs = toolsWithSlugs.filter(tool => {
      const slug = tool.slug
      return (
        slug.includes('.') ||           // Contains dots
        slug.includes(' ') ||           // Contains spaces
        slug.includes('_') ||           // Contains underscores
        slug.length > 100 ||            // Too long
        slug !== slug.toLowerCase() ||  // Not lowercase
        /[^a-z0-9-]/.test(slug)        // Contains invalid characters
      )
    })

    if (problematicSlugs.length > 0) {
      console.log(`⚠️  Found ${problematicSlugs.length} tools with problematic slugs:`)
      problematicSlugs.slice(0, 5).forEach(tool => {
        console.log(`   - "${tool.slug}" (${tool.company_name})`)
      })
      if (problematicSlugs.length > 5) {
        console.log(`   ... and ${problematicSlugs.length - 5} more`)
      }
    } else {
      console.log('✅ All slugs look good!')
    }

    // Get categories
    const { data: categoriesData, error: catError } = await supabase
      .from('tools')
      .select('primary_task')
      .not('primary_task', 'is', null)

    if (catError) {
      throw new Error(`Error getting categories: ${catError.message}`)
    }

    const uniqueCategories = [...new Set(categoriesData.map(item => item.primary_task))]
    console.log(`📂 Unique categories: ${uniqueCategories.length}`)

    // Calculate sitemap totals
    const staticPages = 9 // Home, Tools, Categories, Submit, Search, Privacy, Terms, Dashboard, Profile
    const toolPages = toolsWithSlugs.length
    const categoryPages = uniqueCategories.length
    const totalSitemapUrls = staticPages + toolPages + categoryPages

    console.log('\n📋 Sitemap Summary:')
    console.log(`   Static pages: ${staticPages}`)
    console.log(`   Tool pages: ${toolPages}`)
    console.log(`   Category pages: ${categoryPages}`)
    console.log(`   Total URLs in sitemap: ${totalSitemapUrls}`)

    // Coverage analysis
    const coverage = ((toolsWithSlugs.length / totalTools) * 100).toFixed(1)
    console.log(`\n📈 Sitemap Coverage: ${coverage}% of all tools`)

    if (coverage < 95) {
      console.log('⚠️  Warning: Less than 95% of tools are included in sitemap')
      console.log('   Consider adding slugs to tools without them')
    } else {
      console.log('✅ Excellent coverage! Most tools are included in sitemap')
    }

    console.log('\n🎯 Recommendations:')
    if (toolsWithoutSlugs.length > 0) {
      console.log('   1. Add slugs to tools without them to improve sitemap coverage')
    }
    console.log('   2. Submit updated sitemap to Google Search Console')
    console.log('   3. Monitor indexing status in search console')
    console.log('   4. Check sitemap at: https://aianytool.com/sitemap.xml')

  } catch (error) {
    console.error('❌ Error checking sitemap:', error.message)
    process.exit(1)
  }
}

// Run the check
checkSitemap()
