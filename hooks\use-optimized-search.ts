"use client"

import { useState, useCallback, useRef, useEffect } from 'react'
import { useFastDebounce } from './use-debounce'
import { withSessionCache, CACHE_DURATION } from '@/lib/performance-utils'
import { createBrowserClient } from '@/lib/supabase/client-utils'
import { QUERY_TIMEOUTS, QUERY_LIMITS } from '@/lib/query-timeout-config'

interface SearchOptions {
  minLength?: number
  debounceMs?: number
  cacheEnabled?: boolean
  maxResults?: number
}

interface SearchResult {
  id: string | number
  name: string
  description: string
  category: string
  logo?: string
  [key: string]: any
}

interface UseOptimizedSearchReturn {
  query: string
  setQuery: (query: string) => void
  results: SearchResult[]
  isLoading: boolean
  error: string | null
  hasSearched: boolean
  clearResults: () => void
}

/**
 * Hook محسن للبحث مع تخزين مؤقت وتحسينات الأداء
 */
export function useOptimizedSearch(
  options: SearchOptions = {}
): UseOptimizedSearchReturn {
  const {
    minLength = 2,
    debounceMs = 300,
    cacheEnabled = true,
    maxResults = QUERY_LIMITS.SEARCH_RESULTS
  } = options

  const [query, setQuery] = useState('')
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasSearched, setHasSearched] = useState(false)

  const debouncedQuery = useFastDebounce(query, debounceMs)
  const abortControllerRef = useRef<AbortController | null>(null)
  const supabase = createBrowserClient()

  // دالة البحث المحسنة
  const performSearch = useCallback(async (searchTerm: string) => {
    if (!searchTerm || searchTerm.length < minLength) {
      setResults([])
      setHasSearched(false)
      return
    }

    // إلغاء البحث السابق إذا كان قيد التنفيذ
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }

    abortControllerRef.current = new AbortController()
    const signal = abortControllerRef.current.signal

    setIsLoading(true)
    setError(null)
    setHasSearched(true)

    try {
      const cacheKey = `search:${searchTerm}:${maxResults}`
      
      const searchFunction = async () => {
        // استعلام محسن للبحث
        const { data, error: searchError } = await supabase
          .from('tools')
          .select(`
            id,
            company_name,
            short_description,
            logo_url,
            primary_task,
            pricing,
            visit_website_url,
            detail_url,
            slug,
            is_featured,
            is_verified,
            click_count
          `)
          .or(`company_name.ilike.%${searchTerm}%,short_description.ilike.%${searchTerm}%,primary_task.ilike.%${searchTerm}%`)
          .order('is_featured', { ascending: false })
          .order('is_verified', { ascending: false })
          .order('click_count', { ascending: false })
          .limit(maxResults)
          .abortSignal(signal)

        if (searchError) throw searchError

        // تحويل البيانات إلى تنسيق موحد
        return (data || []).map(tool => ({
          id: tool.id,
          name: tool.company_name || '',
          description: tool.short_description || '',
          category: tool.primary_task || '',
          logo: tool.logo_url || '',
          ...tool
        }))
      }

      let searchResults: SearchResult[]

      if (cacheEnabled) {
        searchResults = await withSessionCache(
          cacheKey,
          searchFunction,
          CACHE_DURATION.SHORT
        )
      } else {
        searchResults = await searchFunction()
      }

      // التحقق من عدم إلغاء البحث
      if (!signal.aborted) {
        setResults(searchResults)
      }
    } catch (err: any) {
      if (!signal.aborted) {
        console.error('Search error:', err)
        setError(err.message || 'حدث خطأ أثناء البحث')
        setResults([])
      }
    } finally {
      if (!signal.aborted) {
        setIsLoading(false)
      }
    }
  }, [minLength, maxResults, cacheEnabled, supabase])

  // تنفيذ البحث عند تغيير الاستعلام المؤخر
  useEffect(() => {
    performSearch(debouncedQuery)
  }, [debouncedQuery, performSearch])

  // تنظيف عند إلغاء التحميل
  useEffect(() => {
    return () => {
      if (abortControllerRef.current) {
        abortControllerRef.current.abort()
      }
    }
  }, [])

  const clearResults = useCallback(() => {
    setQuery('')
    setResults([])
    setError(null)
    setHasSearched(false)
    if (abortControllerRef.current) {
      abortControllerRef.current.abort()
    }
  }, [])

  return {
    query,
    setQuery,
    results,
    isLoading,
    error,
    hasSearched,
    clearResults
  }
}

/**
 * Hook مبسط للبحث السريع
 */
export function useQuickSearch() {
  return useOptimizedSearch({
    minLength: 1,
    debounceMs: 200,
    maxResults: 8
  })
}

/**
 * Hook للبحث المتقدم
 */
export function useAdvancedSearch() {
  return useOptimizedSearch({
    minLength: 2,
    debounceMs: 400,
    maxResults: 24,
    cacheEnabled: true
  })
}