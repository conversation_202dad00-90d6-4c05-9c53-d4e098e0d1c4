"use client"

import { useRouter, useSearchParams } from 'next/navigation'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Star, Zap, Gift, TrendingUp, Clock, Award, Filter } from 'lucide-react'
import { cn } from '@/lib/utils'

interface QuickFiltersProps {
  className?: string
}

export default function QuickFilters({ className }: QuickFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()
  
  const currentCategory = searchParams.get('category') || ''
  const currentPricing = searchParams.get('pricing') || ''
  const currentSortBy = searchParams.get('sortBy') || ''

  const quickFilters = [
    {
      label: 'All Tools',
      icon: <Filter className="h-4 w-4" />,
      action: () => router.push('/tools'),
      active: !currentCategory && !currentPricing && !currentSortBy,
      color: 'bg-white text-gray-700 border-gray-200 hover:bg-gray-50 shadow-sm'
    },
    {
      label: 'Featured',
      icon: <Star className="h-4 w-4" />,
      action: () => {
        const params = new URLSearchParams(searchParams.toString())
        params.set('sortBy', 'featured')
        params.delete('search')
        router.push(`/tools?${params.toString()}`)
      },
      active: currentSortBy === 'featured',
      color: 'bg-amber-50 text-amber-700 border-amber-200 hover:bg-amber-100 shadow-sm'
    },
    {
      label: 'Free Tools',
      icon: <Gift className="h-4 w-4" />,
      action: () => {
        const params = new URLSearchParams(searchParams.toString())
        params.set('pricing', 'Free')
        params.delete('search')
        router.push(`/tools?${params.toString()}`)
      },
      active: currentPricing === 'Free',
      color: 'bg-green-50 text-green-700 border-green-200 hover:bg-green-100'
    },
    {
      label: 'Trending',
      icon: <TrendingUp className="h-4 w-4" />,
      action: () => {
        const params = new URLSearchParams(searchParams.toString())
        params.set('sortBy', 'popular')
        params.delete('search')
        router.push(`/tools?${params.toString()}`)
      },
      active: currentSortBy === 'popular',
      color: 'bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100'
    },
    {
      label: 'Newest',
      icon: <Clock className="h-4 w-4" />,
      action: () => {
        const params = new URLSearchParams(searchParams.toString())
        params.set('sortBy', 'newest')
        params.delete('search')
        router.push(`/tools?${params.toString()}`)
      },
      active: currentSortBy === 'newest',
      color: 'bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100'
    },
    {
      label: 'Top Rated',
      icon: <Award className="h-4 w-4" />,
      action: () => {
        const params = new URLSearchParams(searchParams.toString())
        params.set('sortBy', 'rating')
        params.delete('search')
        router.push(`/tools?${params.toString()}`)
      },
      active: currentSortBy === 'rating',
      color: 'bg-yellow-50 text-yellow-700 border-yellow-200 hover:bg-yellow-100'
    }
  ]

  const popularCategories = [
    {
      label: 'AI Writing',
      category: 'ai-writing',
      emoji: '✍️'
    },
    {
      label: 'Productivity',
      category: 'productivity',
      emoji: '⚡'
    },
    {
      label: 'Image Gen',
      category: 'image-generation',
      emoji: '🎨'
    },
    {
      label: 'Code Assistant',
      category: 'code-assistant',
      emoji: '💻'
    },
    {
      label: 'Customer Service',
      category: 'customer-service',
      emoji: '🎧'
    }
  ]

  const handleCategoryClick = (category: string) => {
    const params = new URLSearchParams(searchParams.toString())
    if (currentCategory === category) {
      params.delete('category')
    } else {
      params.set('category', category)
    }
    router.push(`/tools?${params.toString()}`)
  }

  return (
    <div className={cn("space-y-4", className)}>
      {/* Quick Action Filters */}
      <div className="flex flex-wrap gap-2 justify-center">
        {quickFilters.map((filter, index) => (
          <Button
            key={index}
            variant="outline"
            size="sm"
            onClick={filter.action}
            className={cn(
              "transition-all duration-200 hover:scale-105",
              filter.active 
                ? "ring-2 ring-primary/20 shadow-md" 
                : "",
              filter.color
            )}
          >
            {filter.icon}
            <span className="ml-2">{filter.label}</span>
          </Button>
        ))}
      </div>

      {/* Popular Categories */}
      <div className="flex flex-wrap gap-2 justify-center">
        <span className="text-sm text-muted-foreground mr-2 self-center">Popular:</span>
        {popularCategories.map((cat, index) => (
          <Badge
            key={index}
            variant="outline"
            className={cn(
              "cursor-pointer transition-all duration-200 hover:scale-105 hover:shadow-md",
              currentCategory === cat.category
                ? "bg-primary text-primary-foreground border-primary"
                : "hover:bg-muted/50"
            )}
            onClick={() => handleCategoryClick(cat.category)}
          >
            <span className="mr-1">{cat.emoji}</span>
            {cat.label}
          </Badge>
        ))}
      </div>
    </div>
  )
}
