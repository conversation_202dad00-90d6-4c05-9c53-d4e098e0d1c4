"use client"

import { createBrowserClientSafe } from "@/lib/supabase/client-browser"
import ToolCard from "@/components/tool-card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { useState, useEffect } from "react"

interface ToolsGridServerProps {
  searchQuery?: string
  category?: string
  pricing?: string
  sortBy?: string
  features?: string[]
  limit?: number
  columnsPerRow?: number
  onToolsCountChange?: (count: number) => void
}

export default function ToolsGridServer({
  searchQuery = "",
  category = "",
  pricing = "",
  sortBy = "featured",
  features = [],
  limit = 12,
  columnsPerRow = 4,
  onToolsCountChange,
}: ToolsGridServerProps) {
  const [tools, setTools] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  // Determine grid columns class based on columnsPerRow
  const gridColsClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 sm:grid-cols-2",
    3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
  }[columnsPerRow] || "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"

  useEffect(() => {
    const fetchTools = async () => {
      try {
        setLoading(true)
        const supabase = createBrowserClientSafe()

        // Start building the query
        let query = supabase.from("tools").select("*")

        // Apply search filter if provided
        if (searchQuery) {
          query = query.or(
            `company_name.ilike.%${searchQuery}%,short_description.ilike.%${searchQuery}%,full_description.ilike.%${searchQuery}%`
          )
        }

        // Apply category filter if provided
        if (category) {
          query = query.eq("primary_task", category.replace(/-/g, ' '))
        }

        // Apply pricing filter if provided
        if (pricing) {
          query = query.eq("pricing", pricing)
        }

        // Apply features filter if provided
        if (features.length > 0) {
          features.forEach(feature => {
            query = query.contains("features", [feature.replace(/-/g, ' ')])
          })
        }

        // Apply sorting
        if (sortBy === "newest") {
          query = query.order("created_at", { ascending: false })
        } else if (sortBy === "popular") {
          query = query.order("click_count", { ascending: false })
        } else if (sortBy === "rating") {
          query = query.order("rating", { ascending: false })
        } else {
          // Default to featured
          query = query.order("is_featured", { ascending: false }).order("click_count", { ascending: false })
        }

        // Apply limit
        query = query.limit(limit)

        // Execute the query
        const { data, error: queryError } = await query

        if (queryError) {
          throw queryError
        }

        setTools(data || [])
        setError(null)

        // Update tools count
        if (onToolsCountChange) {
          onToolsCountChange(data?.length || 0)
        }
      } catch (err) {
        console.error("Error fetching tools:", err)
        setError(err instanceof Error ? err : new Error('Unknown error occurred'))
      } finally {
        setLoading(false)
      }
    }

    fetchTools()
  }, [searchQuery, category, pricing, sortBy, features, limit, onToolsCountChange])

  // Show loading state
  if (loading) {
    return (
      <div className="space-y-4">
        <div className="h-8 w-32 bg-muted animate-pulse rounded"></div>
        <div className={`grid ${gridColsClasses} gap-4`}>
          {Array(4).fill(0).map((_, i) => (
            <div key={i} className="h-64 bg-muted animate-pulse rounded-lg"></div>
          ))}
        </div>
      </div>
    )
  }

  // Show error state
  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load tools. Please try again later.
        </AlertDescription>
      </Alert>
    )
  }

  // If no tools found
  if (!tools || tools.length === 0) {
    return (
      <Alert variant="default" className="bg-muted">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>No tools found</AlertTitle>
        <AlertDescription>
          Try adjusting your search or filter criteria to find more tools.
        </AlertDescription>
      </Alert>
    )
  }

  // Render the grid
  return (
    <div className="space-y-4">
      {/* Results summary */}
      <div className="flex items-center justify-between text-sm text-muted-foreground">
        <span>
          Showing {tools.length} {tools.length === 1 ? 'tool' : 'tools'}
          {searchQuery && ` for "${searchQuery}"`}
          {category && ` in ${category.replace(/-/g, ' ')}`}
        </span>
        <span className="hidden sm:block">
          {columnsPerRow === 2 && "Large cards"}
          {columnsPerRow === 3 && "Medium cards"}
          {columnsPerRow === 4 && "Compact cards"}
        </span>
      </div>

      {/* Tools grid with enhanced styling */}
      <div className={`grid ${gridColsClasses} gap-6 auto-rows-fr`}>
        {tools.map((tool, index) => (
          <div
            key={tool.id}
            className="animate-in fade-in slide-in-from-bottom-4 duration-500"
            style={{ animationDelay: `${index * 50}ms` }}
          >
            <ToolCard
              tool={tool}
              className="h-full hover:scale-[1.02] transition-transform duration-200"
            />
          </div>
        ))}
      </div>

      {/* Empty state enhancement */}
      {tools.length === 0 && (
        <div className="text-center py-12">
          <div className="w-16 h-16 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
            <svg className="w-8 h-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
              <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
            </svg>
          </div>
          <h3 className="text-lg font-semibold mb-2">No tools found</h3>
          <p className="text-muted-foreground mb-4">
            Try adjusting your search criteria or browse all categories
          </p>
        </div>
      )}
    </div>
  )
}
