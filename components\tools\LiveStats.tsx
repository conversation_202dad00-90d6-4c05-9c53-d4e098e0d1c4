"use client"

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { TrendingUp, Users, Star, Zap, Clock, Award } from 'lucide-react'
import { createBrowserClient } from '@/lib/supabase/client-utils'
import { cn } from '@/lib/utils'

interface LiveStatsProps {
  className?: string
  compact?: boolean
}

interface StatsData {
  totalTools: number
  totalCategories: number
  freeTools: number
  featuredTools: number
  recentTools: number
  averageRating: number
  growth30d: number
}

export default function LiveStats({ className, compact = false }: LiveStatsProps) {
  const [stats, setStats] = useState<StatsData>({
    totalTools: 0,
    totalCategories: 0,
    freeTools: 0,
    featuredTools: 0,
    recentTools: 0,
    averageRating: 0,
    growth30d: 0
  })
  const [loading, setLoading] = useState(true)
  const [animatedStats, setAnimatedStats] = useState<StatsData>({
    totalTools: 0,
    totalCategories: 0,
    freeTools: 0,
    featuredTools: 0,
    recentTools: 0,
    averageRating: 0,
    growth30d: 0
  })

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true)
        const supabase = createBrowserClient()

        const { data: tools, error } = await supabase
          .from('tools')
          .select('*')

        if (error) {
          console.error('Error fetching stats:', error)
          return
        }

        if (tools) {
          const thirtyDaysAgo = new Date()
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

          const totalTools = tools.length
          const categories = new Set(tools.map(tool => tool.primary_task).filter(Boolean))
          const totalCategories = categories.size
          
          const freeTools = tools.filter(tool => 
            tool.pricing?.toLowerCase().includes('free') || 
            tool.pricing?.toLowerCase().includes('مجاني')
          ).length

          const featuredTools = tools.filter(tool => tool.is_featured).length
          
          const recentTools = tools.filter(tool => 
            tool.created_at && new Date(tool.created_at) > thirtyDaysAgo
          ).length

          const ratingsSum = tools.reduce((sum, tool) => sum + (tool.rating || 0), 0)
          const averageRating = totalTools > 0 ? ratingsSum / totalTools : 0

          const newStats = {
            totalTools,
            totalCategories,
            freeTools,
            featuredTools,
            recentTools,
            averageRating,
            growth30d: recentTools
          }

          setStats(newStats)
        }
      } catch (error) {
        console.error('Error fetching stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  // Animate numbers
  useEffect(() => {
    if (loading || stats.totalTools === 0) return

    const duration = 1500 // 1.5 seconds
    const steps = 50
    const stepDuration = duration / steps

    let currentStep = 0
    const interval = setInterval(() => {
      currentStep++
      const progress = Math.min(currentStep / steps, 1)

      setAnimatedStats({
        totalTools: Math.floor(stats.totalTools * progress),
        totalCategories: Math.floor(stats.totalCategories * progress),
        freeTools: Math.floor(stats.freeTools * progress),
        featuredTools: Math.floor(stats.featuredTools * progress),
        recentTools: Math.floor(stats.recentTools * progress),
        averageRating: stats.averageRating * progress,
        growth30d: Math.floor(stats.growth30d * progress)
      })

      if (currentStep >= steps) {
        clearInterval(interval)
        setAnimatedStats(stats)
      }
    }, stepDuration)

    return () => clearInterval(interval)
  }, [stats, loading])

  const statsItems = [
    {
      label: 'Total Tools',
      value: animatedStats.totalTools.toLocaleString(),
      icon: <Zap className="h-5 w-5" />,
      color: 'text-blue-600',
      bgColor: 'bg-blue-50',
      borderColor: 'border-blue-200',
      growth: `+${animatedStats.growth30d} in 30d`,
      showGrowth: true
    },
    {
      label: 'Categories',
      value: animatedStats.totalCategories.toString(),
      icon: <Users className="h-5 w-5" />,
      color: 'text-green-600',
      bgColor: 'bg-green-50',
      borderColor: 'border-green-200'
    },
    {
      label: 'Free',
      value: animatedStats.freeTools.toLocaleString(),
      icon: <Award className="h-5 w-5" />,
      color: 'text-purple-600',
      bgColor: 'bg-purple-50',
      borderColor: 'border-purple-200',
      percentage: stats.totalTools > 0 ? Math.round((animatedStats.freeTools / stats.totalTools) * 100) : 0
    },
    {
      label: 'Featured',
      value: animatedStats.featuredTools.toString(),
      icon: <Star className="h-5 w-5" />,
      color: 'text-amber-600',
      bgColor: 'bg-amber-50',
      borderColor: 'border-amber-200'
    },
    {
      label: 'Avg Rating',
      value: animatedStats.averageRating.toFixed(1),
      icon: <Star className="h-5 w-5" />,
      color: 'text-yellow-600',
      bgColor: 'bg-yellow-50',
      borderColor: 'border-yellow-200'
    },
    {
      label: 'New (30d)',
      value: animatedStats.recentTools.toString(),
      icon: <Clock className="h-5 w-5" />,
      color: 'text-indigo-600',
      bgColor: 'bg-indigo-50',
      borderColor: 'border-indigo-200'
    }
  ]

  if (compact) {
    return (
      <div className={cn("flex items-center gap-4 text-sm", className)}>
        <Badge variant="outline" className="bg-blue-50 text-blue-700 border-blue-200">
          <Zap className="h-3 w-3 mr-1" />
          {loading ? '...' : stats.totalTools.toLocaleString()} Tools
        </Badge>
        <Badge variant="outline" className="bg-green-50 text-green-700 border-green-200">
          <Users className="h-3 w-3 mr-1" />
          {loading ? '...' : stats.totalCategories} Categories
        </Badge>
        <Badge variant="outline" className="bg-purple-50 text-purple-700 border-purple-200">
          <Award className="h-3 w-3 mr-1" />
          {loading ? '...' : stats.freeTools} Free
        </Badge>
        {stats.growth30d > 0 && (
          <Badge variant="outline" className="bg-orange-50 text-orange-700 border-orange-200">
            <TrendingUp className="h-3 w-3 mr-1" />
            +{stats.growth30d} New
          </Badge>
        )}
      </div>
    )
  }

  return (
    <div className={cn("grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4", className)}>
      {statsItems.map((item, index) => (
        <Card
          key={index}
          className={cn(
            "transition-all duration-300 hover:scale-105 hover:shadow-lg",
            item.bgColor,
            item.borderColor
          )}
        >
          <CardContent className="p-4 text-center">
            <div className={cn("flex items-center justify-center mb-2", item.color)}>
              <div className="p-2 rounded-full bg-white/50">
                {item.icon}
              </div>
            </div>
            
            <div className={cn("text-2xl font-bold mb-1", item.color)}>
              {loading ? (
                <div className="h-8 bg-white/50 rounded animate-pulse"></div>
              ) : (
                item.value
              )}
            </div>
            
            <div className="text-xs font-medium text-gray-600 mb-1">
              {item.label}
            </div>
            
            {item.showGrowth && item.growth && !loading && (
              <div className="text-xs text-green-600 font-medium">
                {item.growth}
              </div>
            )}
            
            {item.percentage !== undefined && !loading && (
              <div className="text-xs text-gray-500">
                {item.percentage}%
              </div>
            )}
          </CardContent>
        </Card>
      ))}
    </div>
  )
}
