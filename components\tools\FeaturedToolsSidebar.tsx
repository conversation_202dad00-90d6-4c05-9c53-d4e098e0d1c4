'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Star, ExternalLink, TrendingUp, Clock } from 'lucide-react'
import { createClient } from '@supabase/supabase-js'
import { Skeleton } from '@/components/ui/skeleton'
import { useRouter } from 'next/navigation'

const supabase = process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
  ? createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY
    )
  : null

interface FeaturedTool {
  id: number
  company_name: string
  short_description: string
  logo_url?: string
  slug?: string
  primary_task?: string
  pricing?: string
  visit_website_url?: string
  click_count?: number
  created_at: string
}

interface FeaturedToolsSidebarProps {
  currentToolId?: number
  className?: string
}

export function FeaturedToolsSidebar({ currentToolId, className }: FeaturedToolsSidebarProps) {
  const [featuredTools, setFeaturedTools] = useState<FeaturedTool[]>([])
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const fetchFeaturedTools = async () => {
      try {
        let query = supabase
          .from('tools')
          .select('id, company_name, short_description, logo_url, slug, primary_task, pricing, visit_website_url, click_count, created_at')
          .order('click_count', { ascending: false, nullsFirst: false })
          .limit(6)

        if (currentToolId) {
          query = query.neq('id', currentToolId)
        }

        const { data, error } = await query

        if (error) throw error
        setFeaturedTools(data || [])
      } catch (error) {
        console.error('Error fetching featured tools:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchFeaturedTools()
  }, [currentToolId])

  const handleToolClick = (tool: FeaturedTool) => {
    const slug = tool.slug || tool.id.toString()
    router.push(`/Tool/${slug}`)
  }

  const handleVisitWebsite = async (e: React.MouseEvent, tool: FeaturedTool) => {
    e.stopPropagation()
    if (tool.visit_website_url) {
      try {
        await supabase.rpc('increment_tool_click_count', { tool_id: tool.id })
      } catch (error) {
        console.error('Error tracking click:', error)
      }
      window.open(tool.visit_website_url, '_blank', 'noopener,noreferrer')
    }
  }

  const isNewTool = (createdAt: string) => {
    const now = new Date()
    const created = new Date(createdAt)
    const diffInDays = (now.getTime() - created.getTime()) / (1000 * 3600 * 24)
    return diffInDays <= 7
  }

  if (loading) {
    return (
      <div className={className}>
        <Card className="sticky top-28">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg flex items-center gap-2">
              <TrendingUp className="h-5 w-5" />
              Featured Tools
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            {Array.from({ length: 6 }).map((_, i) => (
              <div key={i} className="flex gap-3 pb-4 border-b border-border/50 last:border-b-0 last:pb-0">
                <Skeleton className="h-10 w-10 rounded-lg" />
                <div className="flex-1 space-y-2">
                  <Skeleton className="h-4 w-3/4" />
                  <Skeleton className="h-3 w-full" />
                  <div className="flex gap-1">
                    <Skeleton className="h-4 w-12" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
              </div>
            ))}
          </CardContent>
        </Card>
      </div>
    )
  }

  if (featuredTools.length === 0) {
    return null
  }

  return (
    <div className={className}>
      <div className="space-y-6">
        {/* Featured Tools Card */}
        <Card className="shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              Featured Tools
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
          {featuredTools.map((tool, index) => (
            <div
              key={tool.id}
              className="group cursor-pointer pb-4 border-b border-border/50 last:border-b-0 last:pb-0 hover:bg-muted/30 -m-2 p-2 rounded-lg transition-colors"
              onClick={() => handleToolClick(tool)}
            >
              <div className="flex gap-3">
                <div className="relative">
                  <div className="w-10 h-10 rounded-lg overflow-hidden bg-muted flex-shrink-0">
                    <img
                      src={tool.logo_url || "https://via.placeholder.com/40?text=AI"}
                      alt={`${tool.company_name} logo`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = "https://via.placeholder.com/40?text=AI"
                      }}
                    />
                  </div>
                  {index < 3 && (
                    <div className="absolute -top-1 -right-1 w-5 h-5 bg-primary text-white text-xs rounded-full flex items-center justify-center font-bold">
                      {index + 1}
                    </div>
                  )}
                  {isNewTool(tool.created_at) && (
                    <div className="absolute -top-1 -left-1 w-4 h-4 bg-green-500 rounded-full flex items-center justify-center">
                      <Clock className="h-2 w-2 text-white" />
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <div className="flex items-start justify-between mb-1">
                    <h4 className="font-semibold text-sm truncate pr-2 group-hover:text-primary transition-colors">
                      {tool.company_name}
                    </h4>
                    <Button
                      variant="ghost"
                      size="icon"
                      className="h-6 w-6 opacity-0 group-hover:opacity-60 hover:opacity-100 transition-opacity"
                      onClick={(e) => handleVisitWebsite(e, tool)}
                    >
                      <ExternalLink className="h-3 w-3" />
                    </Button>
                  </div>

                  <p className="text-xs text-muted-foreground mb-2 line-clamp-2">
                    {tool.short_description}
                  </p>

                  <div className="flex items-center justify-between">
                    <div className="flex gap-1 flex-wrap">
                      {tool.primary_task && (
                        <Badge variant="secondary" className="text-xs py-0 px-1">
                          {tool.primary_task}
                        </Badge>
                      )}
                      {tool.pricing && (
                        <Badge variant="outline" className="text-xs py-0 px-1">
                          {tool.pricing}
                        </Badge>
                      )}
                    </div>

                    {tool.click_count && tool.click_count > 0 && (
                      <div className="flex items-center gap-1 text-xs text-muted-foreground">
                        <TrendingUp className="h-3 w-3" />
                        <span>{tool.click_count.toLocaleString()}</span>
                      </div>
                    )}
                  </div>
                </div>
              </div>
            </div>
          ))}

            <Button
              variant="outline"
              className="w-full mt-4"
              onClick={() => router.push('/tools')}
            >
              Browse All Tools
            </Button>
          </CardContent>
        </Card>

        {/* Categories Card */}
        <Card className="shadow-lg">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg flex items-center gap-2">
              <Star className="h-5 w-5 text-primary" />
              Popular Categories
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-3">
            {['Text Generation', 'Image Generation', 'Code Assistant', 'Data Analysis', 'Video Editing', 'Audio Processing'].map((category, index) => (
              <div
                key={category}
                className="flex items-center justify-between p-3 rounded-lg bg-muted/30 hover:bg-muted/50 cursor-pointer transition-colors"
                onClick={() => router.push(`/tools?category=${encodeURIComponent(category)}`)}
              >
                <span className="font-medium text-sm">{category}</span>
                <Badge variant="secondary" className="text-xs">
                  {Math.floor(Math.random() * 50) + 10}
                </Badge>
              </div>
            ))}
          </CardContent>
        </Card>

        {/* Quick Stats Card */}
        <Card className="shadow-lg bg-gradient-to-br from-primary/5 to-primary/10">
          <CardHeader className="pb-4">
            <CardTitle className="text-lg flex items-center gap-2">
              <TrendingUp className="h-5 w-5 text-primary" />
              Platform Stats
            </CardTitle>
          </CardHeader>
          <CardContent className="space-y-4">
            <div className="grid grid-cols-1 gap-3">
              <div className="text-center p-3 bg-background/50 rounded-lg">
                <div className="text-2xl font-bold text-primary">500+</div>
                <div className="text-xs text-muted-foreground">AI Tools</div>
              </div>
              <div className="text-center p-3 bg-background/50 rounded-lg">
                <div className="text-2xl font-bold text-green-600">50K+</div>
                <div className="text-xs text-muted-foreground">Users</div>
              </div>
              <div className="text-center p-3 bg-background/50 rounded-lg">
                <div className="text-2xl font-bold text-blue-600">1M+</div>
                <div className="text-xs text-muted-foreground">Tool Visits</div>
              </div>
            </div>
          </CardContent>
        </Card>

        {/* Newsletter Card */}
        <Card className="shadow-lg bg-gradient-to-br from-blue-50 to-indigo-50 dark:from-blue-950/20 dark:to-indigo-950/20">
          <CardContent className="p-6 text-center">
            <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
              <Star className="h-6 w-6 text-primary" />
            </div>
            <h3 className="font-semibold mb-2">Stay Updated</h3>
            <p className="text-sm text-muted-foreground mb-4">
              Get weekly updates on the latest AI tools and trends.
            </p>
            <Button size="sm" className="w-full">
              Subscribe
            </Button>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
