'use client'

import { useEffect, useState, useRef } from 'react'
import { motion } from 'framer-motion'
import { createBrowserClient } from '@/lib/supabase/client-utils'
import { useFormattedStats } from '@/hooks/use-stats'
import { <PERSON><PERSON>, <PERSON>olderKanban, Zap, RefreshCw } from 'lucide-react'

interface StatsData {
  totalTools: number
  totalCategories: number
  newToday: number
  weeklyUpdates: string
}

export default function DynamicStats() {
  const { totalTools, categories, isLoading: statsLoading } = useFormattedStats()
  const [stats, setStats] = useState<StatsData>({
    totalTools: 0,
    totalCategories: 0,
    newToday: 0,
    weeklyUpdates: 'Weekly'
  })
  const [displayStats, setDisplayStats] = useState<StatsData>({
    totalTools: 0,
    totalCategories: 0,
    newToday: 0,
    weeklyUpdates: 'Weekly'
  })
  const [loading, setLoading] = useState(true)
  const animationRef = useRef<number | null>(null)
  const startTimeRef = useRef<number | null>(null)

  useEffect(() => {
    async function fetchStats() {
      try {
        const supabase = createBrowserClient()

        // Get total tools count
        const { count: totalTools, error: toolsError } = await supabase
          .from('tools')
          .select('*', { count: 'exact', head: true })

        // Get distinct categories count
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('tools')
          .select('primary_task')
          .not('primary_task', 'is', null)

        // Get new tools added today
        const today = new Date()
        today.setHours(0, 0, 0, 0)
        const { count: newToday, error: newTodayError } = await supabase
          .from('tools')
          .select('*', { count: 'exact', head: true })
          .gte('created_at', today.toISOString())

        if (toolsError || categoriesError || newTodayError) {
          console.error('Error fetching stats:', toolsError || categoriesError || newTodayError)
          return
        }

        // Count distinct categories (removing duplicates)
        const uniqueCategories = new Set(categoriesData?.map(item => item.primary_task))

        setStats({
          totalTools: totalTools || 0,
          totalCategories: uniqueCategories.size || 0,
          newToday: newToday || 0,
          weeklyUpdates: 'Weekly'
        })
      } catch (error) {
        console.error('Error fetching stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [])

  // Fallback to sample data if loading takes too long
  useEffect(() => {
    const fallbackTimer = setTimeout(() => {
      if (loading) {
        setStats({
          totalTools: 11992,
          totalCategories: 436,
          newToday: 8,
          weeklyUpdates: 'Weekly'
        })
        setLoading(false)
      }
    }, 3000)

    return () => clearTimeout(fallbackTimer)
  }, [loading])

  // Animation logic for counting up
  useEffect(() => {
    if (loading || stats.totalTools === 0) return

    // Animate numbers counting up
    const duration = 2000 // 2 seconds duration

    const animateNumbers = (timestamp: number) => {
      if (!startTimeRef.current) startTimeRef.current = timestamp
      const elapsed = timestamp - startTimeRef.current
      const progress = Math.min(elapsed / duration, 1)

      // Easing function for smoother animation
      const easeOutQuad = (t: number) => t * (2 - t)
      const easedProgress = easeOutQuad(progress)

      setDisplayStats({
        totalTools: Math.round(easedProgress * stats.totalTools),
        totalCategories: Math.round(easedProgress * stats.totalCategories),
        newToday: Math.round(easedProgress * stats.newToday),
        weeklyUpdates: stats.weeklyUpdates
      })

      if (progress < 1) {
        animationRef.current = requestAnimationFrame(animateNumbers)
      }
    }

    animationRef.current = requestAnimationFrame(animateNumbers)

    return () => {
      if (animationRef.current) {
        cancelAnimationFrame(animationRef.current)
      }
    }
  }, [loading, stats])

  const formatNumber = (num: number) => {
    return num.toLocaleString('en-US')
  }

  return (
    <motion.div
      className="grid grid-cols-2 md:grid-cols-4 gap-2 max-w-4xl mx-auto bg-white/30 dark:bg-slate-900/30 py-2 px-2.5 rounded-2xl backdrop-blur-sm shadow-sm border border-white/20 dark:border-slate-800/30"
      initial={{ y: 20, opacity: 0 }}
      animate={{ y: 0, opacity: 1 }}
      transition={{ delay: 0.3 }}
    >
      <motion.div
        className="bg-white/90 backdrop-blur-sm dark:bg-slate-800/90 py-1.5 px-2.5 rounded-xl shadow-md border border-slate-100 dark:border-slate-700 text-center transform transition-all duration-300 hover:scale-105 hover:shadow-lg group"
        whileHover={{ y: -3 }}
      >
        <div className="flex items-center">
          <div className="bg-blue-100 dark:bg-blue-900/50 p-1.5 rounded-full mr-2">
            <Wrench className="w-3 h-3 text-blue-600 dark:text-blue-400" />
          </div>
          <div className="text-left">
            <div className="text-base md:text-lg font-bold text-slate-900 dark:text-white flex items-center">
              {!loading ? (
                <span className="tabular-nums transition-all duration-300 relative overflow-hidden group-hover:text-blue-600 dark:group-hover:text-blue-400">
                  {formatNumber(displayStats.totalTools)}
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-blue-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
                </span>
              ) : (
                <div className="h-5 w-14 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
              )}
            </div>
            <div className="text-[10px] text-slate-500 dark:text-slate-400">Total Tools</div>
          </div>
        </div>
      </motion.div>

      <motion.div
        className="bg-white/90 backdrop-blur-sm dark:bg-slate-800/90 py-1.5 px-2.5 rounded-xl shadow-md border border-slate-100 dark:border-slate-700 text-center transform transition-all duration-300 hover:scale-105 hover:shadow-lg group"
        whileHover={{ y: -3 }}
      >
        <div className="flex items-center">
          <div className="bg-purple-100 dark:bg-purple-900/50 p-1.5 rounded-full mr-2">
            <FolderKanban className="w-3 h-3 text-purple-600 dark:text-purple-400" />
          </div>
          <div className="text-left">
            <div className="text-base md:text-lg font-bold text-slate-900 dark:text-white flex items-center">
              {!loading ? (
                <span className="tabular-nums transition-all duration-300 relative overflow-hidden group-hover:text-purple-600 dark:group-hover:text-purple-400">
                  {formatNumber(displayStats.totalCategories)}
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-purple-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
                </span>
              ) : (
                <div className="h-5 w-10 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
              )}
            </div>
            <div className="text-[10px] text-slate-500 dark:text-slate-400">Categories</div>
          </div>
        </div>
      </motion.div>

      <motion.div
        className="bg-white/90 backdrop-blur-sm dark:bg-slate-800/90 py-1.5 px-2.5 rounded-xl shadow-md border border-slate-100 dark:border-slate-700 text-center transform transition-all duration-300 hover:scale-105 hover:shadow-lg group"
        whileHover={{ y: -3 }}
      >
        <div className="flex items-center">
          <div className="bg-green-100 dark:bg-green-900/50 p-1.5 rounded-full mr-2">
            <Zap className="w-3 h-3 text-green-600 dark:text-green-400" />
          </div>
          <div className="text-left">
            <div className="text-base md:text-lg font-bold text-slate-900 dark:text-white flex items-center">
              {!loading ? (
                <span className="tabular-nums transition-all duration-300 relative overflow-hidden group-hover:text-green-600 dark:group-hover:text-green-400">
                  {formatNumber(displayStats.newToday)}
                  <span className="absolute bottom-0 left-0 w-full h-0.5 bg-green-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
                </span>
              ) : (
                <div className="h-5 w-5 bg-slate-200 dark:bg-slate-700 rounded animate-pulse"></div>
              )}
            </div>
            <div className="text-[10px] text-slate-500 dark:text-slate-400">New Today</div>
          </div>
        </div>
      </motion.div>

      <motion.div
        className="bg-white/90 backdrop-blur-sm dark:bg-slate-800/90 py-1.5 px-2.5 rounded-xl shadow-md border border-slate-100 dark:border-slate-700 text-center transform transition-all duration-300 hover:scale-105 hover:shadow-lg group"
        whileHover={{ y: -3 }}
      >
        <div className="flex items-center">
          <div className="bg-amber-100 dark:bg-amber-900/50 p-1.5 rounded-full mr-2">
            <RefreshCw className="w-3 h-3 text-amber-600 dark:text-amber-400" />
          </div>
          <div className="text-left">
            <div className="text-base md:text-lg font-bold text-slate-900 dark:text-white flex items-center">
              <span className="tabular-nums transition-all duration-300 relative overflow-hidden group-hover:text-amber-600 dark:group-hover:text-amber-400">
                {stats.weeklyUpdates}
                <span className="absolute bottom-0 left-0 w-full h-0.5 bg-amber-500 scale-x-0 group-hover:scale-x-100 transition-transform duration-300 origin-left"></span>
              </span>
            </div>
            <div className="text-[10px] text-slate-500 dark:text-slate-400">Updates</div>
          </div>
        </div>
      </motion.div>
    </motion.div>
  )
}