"use client"

import UniversalSearch from "./universal-search"

interface PageSearchProps {
  placeholder?: string
  className?: string
  variant?: 'page' | 'sidebar'
  size?: 'sm' | 'md' | 'lg'
  showFilters?: boolean
}

export default function PageSearch({
  placeholder = "Search tools...",
  className,
  variant = 'page',
  size = 'md',
  showFilters = false
}: PageSearchProps) {
  return (
    <div className={className}>
      <UniversalSearch
        mode="navigation"
        context="general"
        variant={variant}
        size={size}
        placeholder={placeholder}
        rounded="lg"
        fullWidth={true}
        showInstantResults={true}
        showSearchButton={true}
      />
      
      {showFilters && (
        <div className="mt-4 text-sm text-muted-foreground">
          <p>Use filters to narrow down your search results</p>
        </div>
      )}
    </div>
  )
}
