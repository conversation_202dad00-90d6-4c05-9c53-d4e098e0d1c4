import { createClient } from "@supabase/supabase-js"
import { cookies } from "next/headers"
import { redirect } from "next/navigation"
import type { Database } from "@/lib/database.types"
import { createServerClient } from "./server"

/**
 * Get the current session in App Router server components
 * This function should only be used in App Router server components
 *
 * @returns The current session or null if not authenticated
 */
export async function getSession() {
  try {
    // Use the async createServerClient for App Router
    const supabase = await createServerClient()

    const {
      data: { session },
    } = await supabase.auth.getSession()

    if (session) {
      console.log(`Auth getSession: Session found for user ${session.user.id}`)
    } else {
      console.log('Auth getSession: No session found')
    }

    return session
  } catch (error) {
    console.error('Error in getSession:', error)
    return null
  }
}

/**
 * Get the current user in App Router server components
 * This function should only be used in App Router server components
 *
 * @returns The current user or null if not authenticated
 */
export async function getUser() {
  try {
    // Use the async createServerClient for App Router
    const supabase = await createServerClient()

    const { data: { user }, error } = await supabase.auth.getUser()

    if (error) {
      console.error('Error in getUser:', error)
      return null
    }

    if (user) {
      console.log(`Auth getUser: User found ${user.id}`)
    } else {
      console.log('Auth getUser: No user found')
    }

    return user
  } catch (error) {
    console.error('Error in getUser:', error)
    return null
  }
}

/**
 * Require authentication in App Router server components
 * This function should only be used in App Router server components
 *
 * @param redirectTo Optional URL to redirect to if not authenticated
 * @returns The current session and user
 */
export async function requireAuth(redirectTo: string = "/auth") {
  const session = await getSession()

  if (!session) {
    console.log(`Auth requireAuth: No session found, redirecting to ${redirectTo}`)
    redirect(redirectTo)
  }

  // Use getUser for better security
  const user = await getUser()

  if (!user) {
    console.log(`Auth requireAuth: No user found despite having session, redirecting to ${redirectTo}`)
    redirect(redirectTo)
  }

  return { session, user }
}

/**
 * Require admin role in App Router server components
 * This function should only be used in App Router server components
 *
 * @param redirectTo Optional URL to redirect to if not an admin
 * @returns The current session, user, and profile
 */
export async function requireAdmin(redirectTo: string = "/") {
  // First check if the user is authenticated
  const { session, user } = await requireAuth("/auth?returnUrl=/admin")

  try {
    // Use the async createServerClient for App Router
    const supabase = await createServerClient()

    // Check if user has admin role
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('role')
      .eq('id', user.id)
      .single()

    if (error) {
      console.error(`Auth requireAdmin: Error checking admin role: ${error.message}`)
      redirect(redirectTo)
    }

    if (!profile || profile.role !== 'admin') {
      console.log(`Auth requireAdmin: User ${user.id} is not an admin, redirecting to ${redirectTo}`)
      redirect(redirectTo)
    }

    console.log(`Auth requireAdmin: User ${user.id} is an admin`)
    return { session, user, profile }
  } catch (error) {
    console.error('Error in requireAdmin:', error)
    redirect(redirectTo)
  }
}
