"use client"

import { forwardRef } from "react"
import { Card, CardProps } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface EnhancedCardProps extends CardProps {
  glass?: boolean
  glow?: boolean
  float?: boolean
  hover?: boolean
  gradient?: boolean
}

const EnhancedCard = forwardRef<HTMLDivElement, EnhancedCardProps>(
  ({ className, glass, glow, float, hover, gradient, children, ...props }, ref) => {
    return (
      <Card
        ref={ref}
        className={cn(
          "theme-transition",
          glass && "glass-dark",
          glow && "animate-glow",
          float && "animate-float",
          hover && "hover:scale-105 hover:shadow-xl dark:hover:shadow-2xl dark:hover:shadow-primary/10",
          gradient && "bg-gradient-to-br from-background to-secondary/50 dark:from-background dark:to-secondary/20",
          className
        )}
        {...props}
      >
        {children}
      </Card>
    )
  }
)

EnhancedCard.displayName = "EnhancedCard"

export { EnhancedCard }
