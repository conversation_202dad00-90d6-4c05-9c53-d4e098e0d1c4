// AGGRESSIVE Global polyfills for server-side rendering
// This MUST run before any other code

// Method 1: Direct assignment
if (typeof self === 'undefined') {
  if (typeof global !== 'undefined') {
    global.self = global;
  } else if (typeof globalThis !== 'undefined') {
    globalThis.self = globalThis;
  }
}

// Method 2: Object.defineProperty for immutable assignment
if (typeof global !== 'undefined' && typeof window === 'undefined') {
  try {
    Object.defineProperty(global, 'self', {
      value: global,
      writable: false,
      configurable: false
    });

    Object.defineProperty(global, 'window', {
      value: global,
      writable: false,
      configurable: false
    });
  } catch (e) {
    // Fallback if defineProperty fails
    global.self = global;
    global.window = global;
  }

  // Define document for server-side
  if (typeof global.document === 'undefined') {
    global.document = {
      createElement: () => ({}),
      getElementById: () => null,
      querySelector: () => null,
      querySelectorAll: () => [],
      addEventListener: () => {},
      removeEventListener: () => {},
      head: { appendChild: () => {} },
      body: { appendChild: () => {}, removeChild: () => {} },
      cookie: '',
      documentElement: {},
      readyState: 'complete',
    };
  }

  // Define navigator for server-side
  if (typeof global.navigator === 'undefined') {
    global.navigator = {
      userAgent: 'Node.js',
      onLine: true,
      share: undefined,
      clipboard: { writeText: () => Promise.resolve() },
      serviceWorker: undefined,
    };
  }

  // Define location for server-side
  if (typeof global.location === 'undefined') {
    global.location = {
      href: 'http://localhost:3000',
      origin: 'http://localhost:3000',
      pathname: '/',
      search: '',
      hash: '',
      reload: () => {},
    };
  }

  // Define localStorage for server-side
  if (typeof global.localStorage === 'undefined') {
    global.localStorage = {
      getItem: () => null,
      setItem: () => {},
      removeItem: () => {},
      clear: () => {},
      length: 0,
      key: () => null,
    };
  }

  // Define sessionStorage for server-side
  if (typeof global.sessionStorage === 'undefined') {
    global.sessionStorage = {
      getItem: () => null,
      setItem: () => {},
      removeItem: () => {},
      clear: () => {},
      length: 0,
      key: () => null,
    };
  }

  // Define IntersectionObserver for server-side
  if (typeof global.IntersectionObserver === 'undefined') {
    global.IntersectionObserver = class {
      constructor() {}
      observe() {}
      unobserve() {}
      disconnect() {}
    };
  }

  // Define ResizeObserver for server-side
  if (typeof global.ResizeObserver === 'undefined') {
    global.ResizeObserver = class {
      constructor() {}
      observe() {}
      unobserve() {}
      disconnect() {}
    };
  }

  // Define MutationObserver for server-side
  if (typeof global.MutationObserver === 'undefined') {
    global.MutationObserver = class {
      constructor() {}
      observe() {}
      disconnect() {}
    };
  }
}
