'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { supabase } from '@/lib/supabase/client'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Star, Share2, ExternalLink, CheckCircle, ThumbsUp, ThumbsDown, AlertTriangle } from 'lucide-react'

interface Tool {
  id: number
  company_name: string
  short_description: string
  full_description?: string
  logo_url?: string
  primary_task?: string
  pricing?: string
  visit_website_url?: string
  slug: string
  applicable_tasks?: string[]
  pros?: string[]
  cons?: string[]
}

interface ToolPageProps {
  slug: string
}

export default function ToolPageSimple({ slug }: ToolPageProps) {
  const [tool, setTool] = useState<Tool | null>(null)
  const [loading, setLoading] = useState(true)
  const [notFound, setNotFound] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const router = useRouter()

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: 'smooth' })
      setActiveTab(sectionId)
    }
  }

  useEffect(() => {
    const fetchTool = async () => {
      if (!slug) return

      try {
        const { data, error } = await supabase
          .from('tools')
          .select('*')
          .eq('slug', slug)
          .single()

        if (error || !data) {
          setNotFound(true)
        } else {
          setTool(data)
        }
      } catch (error) {
        console.error('Error fetching tool:', error)
        setNotFound(true)
      } finally {
        setLoading(false)
      }
    }

    fetchTool()
  }, [slug])

  const handleVisitWebsite = () => {
    if (tool?.visit_website_url) {
      try {
        const url = tool.visit_website_url.startsWith('http') 
          ? tool.visit_website_url 
          : `https://${tool.visit_website_url}`
        
        new URL(url)
        window.open(url, '_blank', 'noopener,noreferrer')
      } catch (error) {
        console.error('Invalid URL:', tool.visit_website_url)
        const fallbackUrl = `https://${tool.visit_website_url.replace(/^https?:\/\//, '')}`
        try {
          new URL(fallbackUrl)
          window.open(fallbackUrl, '_blank', 'noopener,noreferrer')
        } catch (fallbackError) {
          console.error('Could not open website URL:', tool.visit_website_url)
        }
      }
    }
  }

  if (loading) {
    return (
      <div className="min-h-screen bg-background">
        <div className="container max-w-6xl mx-auto px-4 py-8">
          <div className="animate-pulse space-y-6">
            <div className="h-8 bg-muted rounded w-1/3"></div>
            <div className="flex gap-4">
              <div className="w-16 h-16 bg-muted rounded"></div>
              <div className="space-y-2 flex-1">
                <div className="h-8 bg-muted rounded w-1/2"></div>
                <div className="h-4 bg-muted rounded w-3/4"></div>
              </div>
            </div>
          </div>
        </div>
      </div>
    )
  }

  if (notFound || !tool) {
    return (
      <div className="min-h-screen bg-background flex items-center justify-center">
        <div className="text-center">
          <h1 className="text-2xl font-bold mb-2">Tool not found</h1>
          <p className="text-muted-foreground mb-4">The tool you're looking for doesn't exist.</p>
          <Button onClick={() => router.push('/')}>Go Home</Button>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-background">
      {/* Breadcrumb */}
      <div className="border-b bg-muted/30">
        <div className="container max-w-6xl mx-auto px-4 py-3">
          <div className="flex items-center gap-2 text-sm text-muted-foreground">
            <Button variant="link" className="p-0 h-auto" onClick={() => router.push('/')}>
              Home
            </Button>
            <span>/</span>
            <Button variant="link" className="p-0 h-auto" onClick={() => router.push('/tools')}>
              Categories
            </Button>
            <span>/</span>
            <Button variant="link" className="p-0 h-auto">
              {tool.primary_task || 'AI Agents Platform'}
            </Button>
            <span>/</span>
            <span className="text-foreground">{tool.company_name}</span>
          </div>
        </div>
      </div>

      <div className="container max-w-6xl mx-auto px-4 py-8">
        <div className="grid grid-cols-1 lg:grid-cols-4 gap-8">
          {/* Main Content */}
          <div className="lg:col-span-3">
            {/* BUZZ Badge */}
            <div className="mb-4">
              <div className="inline-flex items-center gap-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full text-sm font-medium text-green-700 dark:text-green-400">
                BUZZ: 99%
              </div>
            </div>

            {/* Tool Header */}
            <div className="flex items-start gap-4 mb-6">
              <div className="w-16 h-16 rounded-lg overflow-hidden border bg-white flex-shrink-0">
                <img
                  src={tool.logo_url || "https://via.placeholder.com/64?text=AI"}
                  alt={`${tool.company_name} logo`}
                  className="w-full h-full object-cover"
                />
              </div>
              
              <div className="flex-1">
                <h1 className="text-3xl font-bold mb-2">
                  <a 
                    href={tool.visit_website_url} 
                    target="_blank" 
                    rel="noopener noreferrer" 
                    className="hover:underline flex items-center gap-2"
                  >
                    {tool.company_name}
                    <ExternalLink className="h-5 w-5" />
                  </a>
                </h1>
                
                <div className="flex items-center gap-4 mb-3">
                  <div className="flex items-center gap-1">
                    <span className="font-bold text-lg">5.0</span>
                    <div className="flex">
                      {[1, 2, 3, 4, 5].map((star) => (
                        <Star key={star} className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                      ))}
                    </div>
                    <span className="text-muted-foreground">(3)</span>
                    <Button variant="link" className="p-0 h-auto text-sm text-blue-600 hover:underline">
                      Write a Review
                    </Button>
                  </div>
                </div>

                <div className="flex flex-wrap gap-2 mb-4">
                  <Badge variant="secondary" className="bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200">
                    {tool.primary_task || 'AI Agents Platform'}
                  </Badge>
                  <Badge variant="outline">Horizontal</Badge>
                  <Badge variant="outline">Closed Source</Badge>
                </div>

                <div className="flex items-center gap-3 mb-4">
                  <div className="inline-flex items-center gap-2 px-3 py-1 bg-green-100 dark:bg-green-900/30 rounded-full text-sm font-medium text-green-700 dark:text-green-400">
                    BUZZ: 99%
                  </div>
                </div>

                <p className="text-base text-muted-foreground leading-relaxed mb-6">
                  {tool.short_description}
                </p>

                <div className="flex items-center gap-6 text-sm mb-6">
                  <div className="text-center">
                    <div className="font-bold text-lg">1,890</div>
                    <div className="text-muted-foreground text-xs">Views</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-lg">33</div>
                    <div className="text-muted-foreground text-xs">Likes</div>
                  </div>
                  <div className="text-center">
                    <div className="font-bold text-lg">17</div>
                    <div className="text-muted-foreground text-xs">Shares</div>
                  </div>
                </div>

                <div className="flex gap-3">
                  <Button
                    size="lg"
                    className="bg-green-600 hover:bg-green-700 text-white"
                    onClick={handleVisitWebsite}
                  >
                    Visit Website
                  </Button>
                  <Button variant="outline" size="lg">
                    <Share2 className="h-4 w-4" />
                  </Button>
                </div>
              </div>
            </div>

            {/* Copy embed code */}
            <div className="mb-6">
              <Button variant="outline" size="sm" className="text-xs">
                Copy embed code
              </Button>
            </div>

            {/* Featured Badge */}
            <div className="mb-6 flex justify-center">
              <div className="bg-gradient-to-r from-blue-500 to-purple-600 text-white px-4 py-2 rounded-lg text-sm font-medium">
                Featured on AI Agents Directory
              </div>
            </div>

            {/* Sticky Navigation Tabs */}
            <div className="sticky top-0 z-10 bg-background border-b mb-8">
              <div className="flex gap-8 px-0">
                {[
                  { id: 'overview', label: 'Overview' },
                  { id: 'features', label: 'Features' },
                  { id: 'use-cases', label: 'Use Cases' },
                  { id: 'pricing', label: 'Pricing' },
                  { id: 'alternatives', label: 'Alternatives' },
                  { id: 'reviews', label: 'Reviews' }
                ].map((tab) => (
                  <button
                    key={tab.id}
                    className={`py-4 px-0 border-b-2 font-semibold text-base transition-colors ${
                      activeTab === tab.id
                        ? 'border-primary text-primary'
                        : 'border-transparent text-muted-foreground hover:text-foreground'
                    }`}
                    onClick={() => scrollToSection(tab.id)}
                  >
                    {tab.label}
                    {tab.id === 'reviews' && <span className="ml-2 text-sm">3</span>}
                  </button>
                ))}
              </div>
            </div>
          </div>

          {/* Sidebar */}
          <div className="lg:col-span-1">
            <div className="space-y-6 sticky top-24">
              {/* Featured Shapes */}
              <div className="text-center">
                <div className="grid grid-cols-3 gap-2 mb-4">
                  {[...Array(9)].map((_, i) => (
                    <div key={i} className="w-8 h-8 bg-muted rounded-full"></div>
                  ))}
                </div>
              </div>

              {/* Loading placeholder */}
              <div className="bg-muted/30 rounded-lg p-4 text-center">
                <p className="text-sm text-muted-foreground">Loading featured agents...</p>
              </div>

              <div className="bg-muted/30 rounded-lg p-4 text-center">
                <p className="text-sm text-muted-foreground">Loading featured agencies...</p>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
