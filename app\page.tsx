import { Metadata } from 'next'
import { Suspense } from 'react'
import HeroServer from '@/components/home/<USER>'
import ToolsSection from '@/components/home/<USER>'
import TrendingToolsSection from '@/components/home/<USER>'
import Grad<PERSON><PERSON>ackground from '@/components/ui/GradientBackground'
import MotionWrapper from '@/components/ui/MotionWrapper'
import GlassCard from '@/components/ui/GlassCard'
import { Button } from '@/components/ui/button'
import { SkeletonGrid, SkeletonStats } from '@/components/ui/fast-loading'
import OptimizedLoading from '@/components/ui/optimized-loading'
import { WebsiteJsonLd } from '@/components/seo/json-ld-schema'
import Link from 'next/link'
import { ArrowRight, Sparkles, Search, Star } from 'lucide-react'

export const dynamic = 'force-dynamic'

export const metadata: Metadata = {
  title: 'AI Tools Directory - Discover and Compare the Best AI Tools',
  description: 'Discover the best AI tools for productivity, creativity, and business. Our comprehensive directory helps you find and compare the perfect AI solutions for your needs.',
  keywords: 'AI tools, artificial intelligence, machine learning tools, productivity tools, AI directory, best AI tools',
}

export default function HomePage() {
  return (
    <>
      {/* JSON-LD Schema for SEO */}
      <WebsiteJsonLd
        name="AiAnyTool - AI Tools Directory"
        url="https://aianytool.com"
        description="Discover the best AI tools for productivity, creativity, and business. Our comprehensive directory helps you find and compare the perfect AI solutions for your needs."
      />

      <div className="flex min-h-screen flex-col bg-background">
      {/* Hero Section */}
      <Suspense fallback={
        <div className="bg-gradient-to-br from-primary/10 to-accent/10 py-12">
          <div className="container-tight text-center">
            <div className="h-8 bg-muted/60 rounded-full w-32 mx-auto mb-4 animate-pulse"></div>
            <div className="h-16 bg-muted/60 rounded w-3/4 mx-auto mb-4 animate-pulse"></div>
            <div className="h-6 bg-muted/60 rounded w-1/2 mx-auto mb-8 animate-pulse"></div>
            <SkeletonStats />
          </div>
        </div>
      }>
        <HeroServer />
      </Suspense>

      {/* Client Components Sections - تحسين التحميل */}
      <Suspense fallback={
        <div className="bg-background/80 pt-4 pb-4">
          <div className="container-tight">
            <div className="h-6 bg-muted/50 rounded w-40 mx-auto mb-2 animate-pulse"></div>
            <div className="h-3 bg-muted/50 rounded w-56 mx-auto mb-6 animate-pulse"></div>
            <OptimizedLoading count={6} variant="card" />
          </div>
        </div>
      }>
        {/* Featured Tools Section */}
        <div className="relative">
          <div className="bg-background/80 pt-4 md:pt-6">
            <ToolsSection
              title="Featured Tools"
              description="Discover our carefully curated selection of the best AI tools"
              queryType="featured"
              variant="primary"
            />
          </div>
        </div>
      </Suspense>

      <Suspense fallback={
        <div className="pt-4 pb-4 bg-secondary/20 dark:bg-secondary/5">
          <div className="container-tight">
            <div className="h-6 bg-muted/50 rounded w-40 mx-auto mb-2 animate-pulse"></div>
            <div className="h-3 bg-muted/50 rounded w-56 mx-auto mb-6 animate-pulse"></div>
            <OptimizedLoading count={6} variant="card" />
          </div>
        </div>
      }>
        {/* Top Rated Tools Section */}
        <div className="relative">
          <div className="pt-4 pb-4 bg-secondary/30 dark:bg-secondary/10">
            <ToolsSection
              title="Top Rated Tools"
              description="Explore the highest rated AI tools by our community"
              queryType="top-rated"
              variant="secondary"
            />
          </div>
        </div>
      </Suspense>

      <Suspense fallback={
        <div className="pt-4 pb-4">
          <div className="container-tight">
            <div className="h-8 bg-muted/60 rounded w-48 mx-auto mb-2 animate-pulse"></div>
            <div className="h-4 bg-muted/60 rounded w-64 mx-auto mb-8 animate-pulse"></div>
            <SkeletonGrid count={8} />
          </div>
        </div>
      }>
        {/* Recently Added Tools Section */}
        <div className="relative">
          <div className="pt-4 pb-4">
            <ToolsSection
              title="Recently Added Tools"
              description="Check out the latest AI tools added to our collection"
              queryType="recent"
              variant="accent"
            />
          </div>
        </div>
      </Suspense>

      <Suspense fallback={
        <div className="bg-slate-50 dark:bg-slate-900/50 py-6">
          <div className="container-tight">
            <div className="h-8 bg-muted/60 rounded w-48 mx-auto mb-8 animate-pulse"></div>
            <SkeletonGrid count={12} />
          </div>
        </div>
      }>
        {/* Category Browser Section */}
        <TrendingToolsSection />
      </Suspense>

      {/* CTA Section */}
      <Suspense fallback={<div className="h-64 bg-primary/10 animate-pulse"></div>}>
        <div className="relative">
          <GradientBackground variant="primary" className="py-6 md:py-8" intensity="light">
            <div className="container-tight">
              <MotionWrapper animation="fadeIn">
                <GlassCard
                  className="bg-background/80 backdrop-blur-lg border border-border dark:bg-background/30 p-6 md:p-10 text-center"
                  glowEffect
                  hoverEffect
                >
                  <div className="relative mb-5">
                    <Sparkles size={36} className="mx-auto text-primary" />
                  </div>

                  <h2 className="text-xl sm:text-2xl font-bold">
                    Ready to find your <span className="text-primary">perfect AI tool?</span>
                  </h2>
                  <p className="mt-3 text-muted-foreground max-w-2xl mx-auto text-sm">
                    Browse our curated collection of the best AI tools to boost your productivity, creativity, and efficiency.
                  </p>
                  <div className="mt-6 flex flex-col sm:flex-row gap-3 justify-center">
                    <Button
                      className="rounded-lg px-5 py-2 font-medium relative overflow-hidden group"
                      variant="default"
                      asChild
                    >
                      <Link href="/tools">
                        <span className="relative z-10 flex items-center">
                          <Search size={16} className="mr-2" />
                          Explore Tools
                          <ArrowRight size={16} className="ml-2 transition-transform group-hover:translate-x-1" />
                        </span>
                      </Link>
                    </Button>
                    <Button
                      variant="outline"
                      className="rounded-lg px-5 py-2 font-medium hover:border-primary"
                      asChild
                    >
                      <Link href="/categories" className="flex items-center">
                        <Star size={16} className="mr-2" />
                        Browse Categories
                      </Link>
                    </Button>
                  </div>
                </GlassCard>
              </MotionWrapper>
            </div>
          </GradientBackground>
        </div>
      </Suspense>
      </div>
    </>
  )
}
