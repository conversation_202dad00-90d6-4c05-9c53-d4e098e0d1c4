export type Json = string | number | boolean | null | { [key: string]: Json | undefined } | Json[]

export interface Database {
  public: {
    Tables: {
      tools: {
        Row: {
          id: number
          company_name: string | null
          short_description: string | null
          full_description: string | null
          logo_url: string | null
          featured_image_url: string | null
          visit_website_url: string | null
          detail_url: string | null
          primary_task: string | null
          applicable_tasks: string[] | null
          pros: string[] | null
          features: string[] | null
          pricing: string | null
          is_featured: boolean | null
          is_verified: boolean | null
          click_count: number | null
          slug: string | null
          created_at: string | null
          updated_at: string | null
          faqs: Json | null
        }
        Insert: {
          id?: number
          company_name?: string | null
          short_description?: string | null
          full_description?: string | null
          logo_url?: string | null
          featured_image_url?: string | null
          visit_website_url?: string | null
          detail_url?: string | null
          primary_task?: string | null
          applicable_tasks?: string[] | null
          pros?: string[] | null
          features?: string[] | null
          pricing?: string | null
          is_featured?: boolean | null
          is_verified?: boolean | null
          click_count?: number | null
          slug?: string | null
          created_at?: string | null
          updated_at?: string | null
          faqs?: Json | null
        }
        Update: {
          id?: number
          company_name?: string | null
          short_description?: string | null
          full_description?: string | null
          logo_url?: string | null
          featured_image_url?: string | null
          visit_website_url?: string | null
          detail_url?: string | null
          primary_task?: string | null
          applicable_tasks?: string[] | null
          pros?: string[] | null
          features?: string[] | null
          pricing?: string | null
          is_featured?: boolean | null
          is_verified?: boolean | null
          click_count?: number | null
          slug?: string | null
          created_at?: string | null
          updated_at?: string | null
          faqs?: Json | null
        }
      }
      reviews: {
        Row: {
          id: number
          tool_id: number | null
          user_id: string | null
          rating: number | null
          comment: string | null
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id?: number
          tool_id?: number | null
          user_id?: string | null
          rating?: number | null
          comment?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: number
          tool_id?: number | null
          user_id?: string | null
          rating?: number | null
          comment?: string | null
          created_at?: string | null
          updated_at?: string | null
        }
      }
      favorites: {
        Row: {
          id: number
          user_id: string | null
          tool_id: number | null
          created_at: string | null
        }
        Insert: {
          id?: number
          user_id?: string | null
          tool_id?: number | null
          created_at?: string | null
        }
        Update: {
          id?: number
          user_id?: string | null
          tool_id?: number | null
          created_at?: string | null
        }
      }
      profiles: {
        Row: {
          id: string
          role: string
          created_at: string | null
          updated_at: string | null
        }
        Insert: {
          id: string
          role?: string
          created_at?: string | null
          updated_at?: string | null
        }
        Update: {
          id?: string
          role?: string
          created_at?: string | null
          updated_at?: string | null
        }
      }
      tool_submissions: {
        Row: {
          id: string
          tool_name: string
          description: string
          category: string
          pricing: string
          website_url: string
          contact_email: string
          additional_info: string | null
          user_id: string | null
          status: string | null
          created_at: string | null
          updated_at: string | null
          features: string[] | null
        }
        Insert: {
          id?: string
          tool_name: string
          description: string
          category: string
          pricing: string
          website_url: string
          contact_email: string
          additional_info?: string | null
          user_id?: string | null
          status?: string | null
          created_at?: string | null
          updated_at?: string | null
          features?: string[] | null
        }
        Update: {
          id?: string
          tool_name?: string
          description?: string
          category?: string
          pricing?: string
          website_url?: string
          contact_email?: string
          additional_info?: string | null
          user_id?: string | null
          status?: string | null
          created_at?: string | null
          updated_at?: string | null
          features?: string[] | null
        }
      }

    }
    Views: {
      [_ in never]: never
    }
    Functions: {
      [_ in never]: never
    }
    Enums: {
      [_ in never]: never
    }
  }
}
