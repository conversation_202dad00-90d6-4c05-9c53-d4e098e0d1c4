// Cookie Consent Components
export { default as CookieBanner } from './CookieBanner'
export { default as CookieSettingsModal } from './CookieSettingsModal'
export { default as CookieSettingsLink } from './CookieSettingsLink'

// Re-export cookie consent utilities
export {
  hasConsent,
  getCookiePreferences,
  saveCookiePreferences,
  acceptAllCookies,
  acceptEssentialOnly,
  resetCookiePreferences,
  getConsentStatus,
  dismissBanner,
  forceEnableAnalytics,
  COOKIE_CATEGORIES,
  COOKIE_CONFIG,
  type CookiePreferences,
  type CookieCategory
} from '@/lib/cookie-consent'

// Export client-side hook
export { useCookieConsent } from '@/hooks/use-cookie-consent'
