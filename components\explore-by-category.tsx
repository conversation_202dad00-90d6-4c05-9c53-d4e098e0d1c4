import Link from "next/link"
import {
  MessageSquare,
  ImageIcon,
  FileAudio,
  FileVideo,
  Database,
  Code,
  Pencil,
  Lightbulb,
  Briefcase,
} from "lucide-react"

export default function ExploreByCategory() {
  const categories = [
    {
      icon: <MessageSquare className="h-6 w-6" />,
      name: "AI Chatbots",
      count: 42,
      href: "/categories/ai-chatbots",
    },
    {
      icon: <ImageIcon className="h-6 w-6" />,
      name: "Image Generation",
      count: 38,
      href: "/categories/image-generation",
    },
    {
      icon: <Code className="h-6 w-6" />,
      name: "Code Assistants",
      count: 29,
      href: "/categories/code-assistants",
    },
    {
      icon: <Database className="h-6 w-6" />,
      name: "Data Analysis",
      count: 24,
      href: "/categories/data-analysis",
    },
    {
      icon: <Pencil className="h-6 w-6" />,
      name: "Writing & Content",
      count: 35,
      href: "/categories/writing-content",
    },
    {
      icon: <FileVideo className="h-6 w-6" />,
      name: "Video Creation",
      count: 18,
      href: "/categories/video-creation",
    },
    {
      icon: <FileAudio className="h-6 w-6" />,
      name: "Audio & Music",
      count: 22,
      href: "/categories/audio-music",
    },
    {
      icon: <Lightbulb className="h-6 w-6" />,
      name: "Research Tools",
      count: 15,
      href: "/categories/research-tools",
    },
    {
      icon: <Briefcase className="h-6 w-6" />,
      name: "Marketing & SEO",
      count: 27,
      href: "/categories/marketing-seo",
    },
  ]

  return (
    <div className="text-center">
      <h2 className="text-3xl font-bold mb-4">Explore by Category</h2>
      <p className="text-slate-600 dark:text-slate-400 mb-12 max-w-2xl mx-auto">
        Browse our comprehensive collection of AI tools organized by category to find exactly what you need
      </p>

      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-5 gap-4">
        {categories.map((category, index) => (
          <Link
            key={index}
            href={category.href}
            className="flex flex-col items-center p-4 bg-white dark:bg-slate-800 rounded-lg shadow-sm hover:shadow-md transition-shadow"
          >
            <div className="p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full mb-3">{category.icon}</div>
            <h3 className="font-medium mb-1">{category.name}</h3>
            <p className="text-sm text-slate-500 dark:text-slate-400">{category.count} tools</p>
          </Link>
        ))}
      </div>

      <div className="mt-8">
        <Link href="/categories" className="text-blue-600 dark:text-blue-400 hover:underline">
          View all categories →
        </Link>
      </div>
    </div>
  )
}
