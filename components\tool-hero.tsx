"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ExternalLink, Share2, CheckCircle, Star, Heart } from "lucide-react"
import { useState } from "react"
import { useToast } from "@/components/ui/use-toast"

interface ToolHeroProps {
  tool: any
  averageRating: number
  ratingCount: number
}

export default function ToolHero({ tool, averageRating, ratingCount }: ToolHeroProps) {
  const [isFavorite, setIsFavorite] = useState(false)
  const { toast } = useToast()

  const handleFavorite = () => {
    setIsFavorite(!isFavorite)
    toast({
      title: isFavorite ? "Removed from favorites" : "Added to favorites",
      description: isFavorite ? "Tool removed from your favorites" : "Tool added to your favorites",
    })
  }

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: tool.company_name,
          text: tool.short_description,
          url: window.location.href,
        })
      } catch (error) {
        console.error("Error sharing:", error)
      }
    } else {
      // Fallback for browsers that don't support the Web Share API
      navigator.clipboard.writeText(window.location.href)
      toast({
        title: "Link copied to clipboard",
        description: "You can now share this tool with others",
      })
    }
  }

  return (
    <div className="bg-white dark:bg-slate-800 shadow-md">
      <div className="container mx-auto px-4 py-8">
        <div className="flex flex-col md:flex-row gap-6">
          <div className="flex-shrink-0 flex items-center justify-center w-24 h-24 md:w-32 md:h-32 bg-slate-100 dark:bg-slate-700 rounded-lg p-2">
            <Image
              src={tool.logo_url || "/placeholder.svg?height=128&width=128"}
              alt={tool.company_name}
              width={128}
              height={128}
              className="object-contain"
            />
          </div>

          <div className="flex-grow">
            <div className="flex items-center gap-2 mb-2">
              <h1 className="text-3xl font-bold">{tool.company_name}</h1>
              {tool.is_verified && <CheckCircle className="h-6 w-6 text-emerald-500" />}
            </div>

            <div className="flex items-center gap-2 mb-4">
              <div className="flex items-center">
                {[1, 2, 3, 4, 5].map((star) => (
                  <Star
                    key={star}
                    className={`h-5 w-5 ${
                      star <= Math.round(averageRating) ? "fill-amber-400 text-amber-400" : "text-gray-300"
                    }`}
                  />
                ))}
                <span className="ml-2 text-sm font-medium">
                  {averageRating.toFixed(1)} ({ratingCount} {ratingCount === 1 ? "review" : "reviews"})
                </span>
              </div>
            </div>

            <p className="text-lg text-slate-600 dark:text-slate-400 mb-4">{tool.short_description}</p>

            <div className="flex flex-wrap gap-2">
              <Badge variant="secondary" className="text-sm">
                {tool.primary_task}
              </Badge>
              <Badge variant="outline" className="text-sm">
                {tool.pricing}
              </Badge>
              {tool.is_featured && (
                <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white">Featured</Badge>
              )}
            </div>
          </div>

          <div className="flex flex-col gap-3 md:items-end">
            <Button asChild className="w-full md:w-auto">
              <a
                href={tool.visit_website_url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center gap-2"
              >
                <span>Visit Website</span>
                <ExternalLink className="h-4 w-4" />
              </a>
            </Button>

            <div className="flex gap-2">
              <Button
                variant="outline"
                size="icon"
                title={isFavorite ? "Remove from favorites" : "Add to favorites"}
                onClick={handleFavorite}
                className={isFavorite ? "text-red-500 border-red-200 dark:border-red-800" : ""}
              >
                <Heart className={`h-4 w-4 ${isFavorite ? "fill-current" : ""}`} />
              </Button>

              <Button variant="outline" size="icon" title="Share" onClick={handleShare}>
                <Share2 className="h-4 w-4" />
              </Button>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
