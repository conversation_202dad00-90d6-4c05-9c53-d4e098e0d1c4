'use client'

import { useState, useEffect } from 'react'
import Link from 'next/link'
import { supabase } from '@/lib/supabase/client'
import { motion } from 'framer-motion'
import {
  MessageSquare,
  ImageIcon,
  FileAudio,
  FileVideo,
  Database,
  Code,
  Pencil,
  Lightbulb,
  Briefcase,
  Sparkles,
  Brain,
  Globe,
  BarChart,
  Users,
  Monitor,
  Mic,
  Camera,
  Bot,
  Settings,
  ArrowUpDown,
  SortAsc,
  Hash
} from 'lucide-react'
import { Button } from './ui/button'

interface Category {
  name: string
  slug: string
  count: number
  icon?: React.ReactNode
}

export default function DynamicCategories() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [sortOrder, setSortOrder] = useState<'alphabetical' | 'count'>('count')

  // Category icon mapping - add specific icons for common categories
  const categoryIcons: Record<string, React.ReactNode> = {
    'ai chatbots': <MessageSquare className="h-6 w-6 text-blue-600" />,
    'chatbots': <MessageSquare className="h-6 w-6 text-blue-600" />,
    'image generation': <ImageIcon className="h-6 w-6 text-purple-600" />,
    'code assistants': <Code className="h-6 w-6 text-indigo-600" />,
    'coding': <Code className="h-6 w-6 text-indigo-600" />,
    'data analysis': <Database className="h-6 w-6 text-emerald-600" />,
    'writing': <Pencil className="h-6 w-6 text-amber-600" />,
    'content creation': <Pencil className="h-6 w-6 text-amber-600" />,
    'video creation': <FileVideo className="h-6 w-6 text-red-600" />,
    'video': <FileVideo className="h-6 w-6 text-red-600" />,
    'audio': <FileAudio className="h-6 w-6 text-green-600" />,
    'music': <FileAudio className="h-6 w-6 text-green-600" />,
    'research': <Lightbulb className="h-6 w-6 text-yellow-600" />,
    'marketing': <Briefcase className="h-6 w-6 text-cyan-600" />,
    'seo': <Globe className="h-6 w-6 text-blue-600" />,
    'analytics': <BarChart className="h-6 w-6 text-indigo-600" />,
    'productivity': <Settings className="h-6 w-6 text-gray-600" />,
    'social media': <Users className="h-6 w-6 text-pink-600" />,
    'design': <Monitor className="h-6 w-6 text-purple-600" />,
    'voice': <Mic className="h-6 w-6 text-red-600" />,
    'photography': <Camera className="h-6 w-6 text-blue-600" />,
    'assistants': <Bot className="h-6 w-6 text-green-600" />,
    'ai assistant': <Bot className="h-6 w-6 text-green-600" />,
    'machine learning': <Brain className="h-6 w-6 text-violet-600" />
  }

  // Default category background colors
  const bgColors = [
    'bg-blue-100 dark:bg-blue-900/30',
    'bg-purple-100 dark:bg-purple-900/30',
    'bg-indigo-100 dark:bg-indigo-900/30',
    'bg-emerald-100 dark:bg-emerald-900/30',
    'bg-amber-100 dark:bg-amber-900/30',
    'bg-red-100 dark:bg-red-900/30',
    'bg-green-100 dark:bg-green-900/30',
    'bg-yellow-100 dark:bg-yellow-900/30',
    'bg-cyan-100 dark:bg-cyan-900/30',
    'bg-rose-100 dark:bg-rose-900/30',
    'bg-violet-100 dark:bg-violet-900/30',
    'bg-fuchsia-100 dark:bg-fuchsia-900/30'
  ]

  // Create a slug from category name
  const createSlug = (name: string) => {
    return name
      .toLowerCase()
      .replace(/[^a-z0-9]+/g, '-')
      .replace(/-+/g, '-')
      .replace(/^-|-$/g, '')
  }

  // Function to get category icon
  const getCategoryIcon = (categoryName: string) => {
    const lowerCaseName = categoryName.toLowerCase()
    // First check for exact match
    if (categoryIcons[lowerCaseName]) {
      return categoryIcons[lowerCaseName]
    }
    
    // Check for partial matches
    for (const key in categoryIcons) {
      if (lowerCaseName.includes(key) || key.includes(lowerCaseName)) {
        return categoryIcons[key]
      }
    }
    
    // Return default icon if no match found
    return <Sparkles className="h-6 w-6 text-violet-600" />
  }

  // Get background color based on category index
  const getBgColor = (index: number) => {
    return bgColors[index % bgColors.length]
  }

  // Fetch categories from database
  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true)
      try {
        // Query primary_task values from tools table
        const { data: tasksData, error: tasksError } = await supabase
          .from('tools')
          .select('primary_task')
          .not('primary_task', 'is', null)
        
        if (tasksError) {
          console.error('Error fetching categories:', tasksError)
          setLoading(false)
          return
        }
        
        if (tasksData && tasksData.length > 0) {
          // Create a map to count tools per category
          const categoryCountMap = new Map<string, number>()
          
          // Count occurrences of each primary_task
          tasksData.forEach(item => {
            if (item.primary_task) {
              const category = item.primary_task.trim()
              categoryCountMap.set(
                category, 
                (categoryCountMap.get(category) || 0) + 1
              )
            }
          })
          
          // Convert map to array of category objects
          const categoriesArray: Category[] = Array.from(categoryCountMap.entries())
            .map(([name, count]) => ({
              name,
              slug: createSlug(name),
              count,
              icon: getCategoryIcon(name)
            }))
          
          setCategories(categoriesArray)
        }
      } catch (error) {
        console.error('Error in categories fetch:', error)
      } finally {
        setLoading(false)
      }
    }
    
    fetchCategories()
  }, [])

  // Sort categories based on current sort order
  const sortedCategories = [...categories].sort((a, b) => {
    if (sortOrder === 'alphabetical') {
      return a.name.localeCompare(b.name)
    } else {
      return b.count - a.count // Sort by count (descending)
    }
  })

  // Toggle sort order
  const toggleSortOrder = () => {
    setSortOrder(current => 
      current === 'alphabetical' ? 'count' : 'alphabetical'
    )
  }

  return (
    <div className="px-4 py-8">
      <div className="flex justify-between items-center mb-6">
        <h1 className="text-3xl font-bold">Browse AI Tools by Category</h1>
        <Button 
          onClick={toggleSortOrder} 
          variant="outline"
          className="flex items-center gap-2"
        >
          {sortOrder === 'alphabetical' ? (
            <>
              <SortAsc className="h-4 w-4" />
              <span>Sort A-Z</span>
            </>
          ) : (
            <>
              <Hash className="h-4 w-4" />
              <span>Sort by Count</span>
            </>
          )}
        </Button>
      </div>

      {loading ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6">
          {Array(12).fill(0).map((_, index) => (
            <div key={index} className="flex flex-col items-center p-6 bg-white dark:bg-slate-800 rounded-lg shadow-sm animate-pulse">
              <div className="w-16 h-16 bg-slate-200 dark:bg-slate-700 rounded-full mb-4"></div>
              <div className="h-6 w-32 bg-slate-200 dark:bg-slate-700 rounded-md mb-2"></div>
              <div className="h-4 w-20 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
            </div>
          ))}
        </div>
      ) : (
        <motion.div 
          className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-6"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ duration: 0.3 }}
        >
          {sortedCategories.map((category, index) => (
            <motion.div
              key={category.slug}
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.3, delay: index * 0.05 }}
              whileHover={{ y: -5, transition: { duration: 0.2 } }}
            >
              <Link
                href={`/category/${category.slug}`}
                className="flex flex-col items-center p-6 bg-white dark:bg-slate-800 rounded-lg shadow-sm hover:shadow-md transition-shadow"
              >
                <div className={`p-4 ${getBgColor(index)} rounded-full mb-4`}>
                  {category.icon}
                </div>
                <h3 className="text-xl font-semibold mb-2">{category.name}</h3>
                <p className="text-sm text-slate-500 dark:text-slate-400">{category.count} tools</p>
              </Link>
            </motion.div>
          ))}
        </motion.div>
      )}
    </div>
  )
} 