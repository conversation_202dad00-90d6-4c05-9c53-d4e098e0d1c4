import { memo } from "react"
import { Skeleton } from "@/components/ui/skeleton"

interface LoadingGridProps {
  count?: number
  columns?: number
  className?: string
  cardClassName?: string
  variant?: 'default' | 'simple' | 'minimal'
  showAnimation?: boolean
}

// استخدام memo لمنع إعادة التصيير غير الضرورية
export const LoadingGrid = memo(function LoadingGrid({
  count = 6,
  columns = 3,
  className = '',
  cardClassName = '',
  variant = 'default',
  showAnimation = true
}: LoadingGridProps) {
  // تحديد فئات أعمدة الشبكة بناءً على عدد الأعمدة - محسّن للموبايل
  const gridColsClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 sm:grid-cols-2",
    3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 sm:grid-cols-2 md:grid-cols-3 xl:grid-cols-4",
  }[columns] || "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3"

  // تحسين الأداء عن طريق إنشاء مصفوفة ثابتة الحجم بدلاً من استخدام Array.from في كل مرة
  const items = Array.from({ length: Math.min(count, 12) }).map((_, index) => (
    <LoadingCard
      key={index}
      variant={variant}
      className={cardClassName}
      showAnimation={showAnimation}
    />
  ));

  return (
    <div className={`grid ${gridColsClasses} gap-3 sm:gap-4 ${className}`}>
      {items}
    </div>
  )
});

interface LoadingCardProps {
  variant?: 'default' | 'simple' | 'minimal'
  className?: string
  showAnimation?: boolean
}

// استخدام memo لمنع إعادة التصيير غير الضرورية
const LoadingCard = memo(function LoadingCard({
  variant = 'default',
  className = '',
  showAnimation = true
}: LoadingCardProps) {
  // تحديد فئة الرسوم المتحركة
  const animationClass = showAnimation ? 'animate-pulse' : '';

  // تقديم بطاقات تحميل مختلفة بناءً على المتغير
  if (variant === 'minimal') {
    return (
      <div className={`rounded-lg border bg-card ${animationClass} ${className}`}>
        <div className="p-3 space-y-2">
          <Skeleton className="h-4 w-2/3" />
          <Skeleton className="h-3 w-1/2" />
        </div>
      </div>
    );
  }

  if (variant === 'simple') {
    return (
      <div className={`flex flex-col space-y-2 rounded-lg border p-3 ${animationClass} ${className}`}>
        <div className="flex items-center space-x-3">
          <Skeleton className="h-10 w-10 rounded-full" />
          <Skeleton className="h-4 w-[120px]" />
        </div>
        <Skeleton className="h-16 w-full" />
        <Skeleton className="h-6 w-24" />
      </div>
    );
  }

  // النمط الافتراضي - محسّن للموبايل
  return (
    <div className={`rounded-xl border bg-card shadow-sm overflow-hidden flex flex-col min-h-[220px] sm:min-h-[240px] ${animationClass} ${className}`}>
      <div className="p-4 sm:p-5 flex-grow">
        <div className="flex items-start gap-3 mb-3">
          <Skeleton className="w-10 h-10 sm:w-11 sm:h-11 rounded-xl flex-shrink-0" />
          <div className="flex-1 space-y-2">
            <Skeleton className="h-4 sm:h-5 w-3/4" />
            <Skeleton className="h-3 sm:h-4 w-1/2" />
          </div>
        </div>

        <div className="space-y-2 mt-3">
          <Skeleton className="h-3 sm:h-4 w-full" />
          <Skeleton className="h-3 sm:h-4 w-5/6" />
          <Skeleton className="h-3 sm:h-4 w-4/6" />
        </div>

        <div className="flex gap-1 mt-3 sm:mt-4">
          <Skeleton className="h-4 w-4 sm:h-3.5 sm:w-3.5 rounded-full" />
          <Skeleton className="h-4 w-4 sm:h-3.5 sm:w-3.5 rounded-full" />
          <Skeleton className="h-4 w-4 sm:h-3.5 sm:w-3.5 rounded-full" />
          <Skeleton className="h-4 w-4 sm:h-3.5 sm:w-3.5 rounded-full" />
          <Skeleton className="h-4 w-4 sm:h-3.5 sm:w-3.5 rounded-full" />
          <Skeleton className="h-3 w-16 ml-2" />
        </div>
      </div>

      <div className="mt-auto p-4 sm:p-5 pt-0">
        <div className="flex gap-2">
          <Skeleton className="h-11 sm:h-10 flex-1 rounded-lg" />
          <Skeleton className="h-11 w-11 sm:h-10 sm:w-10 rounded-lg" />
          <Skeleton className="h-11 sm:h-10 flex-1 rounded-lg" />
        </div>
      </div>
    </div>
  );
});
