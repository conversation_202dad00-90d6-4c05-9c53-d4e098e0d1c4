import { createBrowserClient } from "@/lib/supabase/client-utils"

interface SearchFilters {
  categories?: string[]
  pricing?: string
  verified?: boolean
  featured?: boolean
  free?: boolean
}

interface Tool {
  id: number
  company_name: string | null
  short_description: string | null
  visit_website_url: string | null
  logo_url?: string | null
  primary_task: string | null
  pricing: string | null
  is_verified?: boolean | null
  is_featured?: boolean | null
  click_count?: number | null
  slug?: string | null
}

export async function searchTools(
  query: string = "",
  filters: SearchFilters = {},
  limit: number = 50
): Promise<Tool[]> {
  const supabase = createBrowserClient()
  
  try {
    let queryBuilder = supabase
      .from('tools')
      .select(`
        id,
        company_name,
        short_description,
        visit_website_url,
        logo_url,
        primary_task,
        pricing,
        is_verified,
        is_featured,
        click_count,
        slug
      `)
      .order('is_featured', { ascending: false })
      .order('is_verified', { ascending: false })
      .order('click_count', { ascending: false })
      .limit(limit)

    // Apply text search if query provided
    if (query.trim()) {
      // Use full-text search with available fields only
      queryBuilder = queryBuilder.or(`
        company_name.ilike.%${query}%,
        short_description.ilike.%${query}%,
        primary_task.ilike.%${query}%
      `)
    }

    // Apply category filters
    if (filters.categories && filters.categories.length > 0) {
      queryBuilder = queryBuilder.in('primary_task', filters.categories)
    }

    // Apply pricing filter
    if (filters.pricing) {
      queryBuilder = queryBuilder.eq('pricing', filters.pricing)
    }

    // Apply boolean filters
    if (filters.verified) {
      queryBuilder = queryBuilder.eq('is_verified', true)
    }

    if (filters.featured) {
      queryBuilder = queryBuilder.eq('is_featured', true)
    }

    if (filters.free) {
      queryBuilder = queryBuilder.in('pricing', ['Free', 'Freemium'])
    }

    const { data, error } = await queryBuilder

    if (error) {
      console.error('Search error:', error)
      throw new Error('Failed to search tools')
    }

    return data || []
  } catch (error) {
    console.error('Search tools error:', error)
    throw error
  }
}

export async function searchToolsWithFallback(
  query: string = "",
  filters: SearchFilters = {},
  limit: number = 50
): Promise<{ tools: Tool[], hasFallback: boolean }> {
  try {
    // First, try exact search
    const exactResults = await searchTools(query, filters, limit)
    
    if (exactResults.length > 0) {
      return { tools: exactResults, hasFallback: false }
    }

    // If no exact results, try broader search
    const broaderFilters = { ...filters }
    
    // Remove some filters for broader search
    if (filters.categories && filters.categories.length > 1) {
      // Try with fewer categories
      broaderFilters.categories = filters.categories.slice(0, 1)
    }

    const broaderResults = await searchTools(query, broaderFilters, limit)
    
    if (broaderResults.length > 0) {
      return { tools: broaderResults, hasFallback: true }
    }

    // If still no results, try without filters but with query
    if (query.trim()) {
      const queryOnlyResults = await searchTools(query, {}, limit)
      if (queryOnlyResults.length > 0) {
        return { tools: queryOnlyResults, hasFallback: true }
      }
    }

    // Last resort: popular tools
    const popularResults = await getPopularTools(limit)
    return { tools: popularResults, hasFallback: true }

  } catch (error) {
    console.error('Search with fallback error:', error)
    throw error
  }
}

async function getPopularTools(limit: number = 20): Promise<Tool[]> {
  const supabase = createBrowserClient()
  
  try {
    const { data, error } = await supabase
      .from('tools')
      .select(`
        id,
        company_name,
        short_description,
        visit_website_url,
        logo_url,
        primary_task,
        pricing,
        is_verified,
        is_featured,
        click_count,
        slug
      `)
      .order('is_featured', { ascending: false })
      .order('is_verified', { ascending: false })
      .order('click_count', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Get popular tools error:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Get popular tools error:', error)
    return []
  }
}

export async function getSearchSuggestions(query: string): Promise<string[]> {
  if (!query.trim()) return []

  const supabase = createBrowserClient()
  
  try {
    // Get suggestions from tool names and categories
    const { data, error } = await supabase
      .from('tools')
      .select('company_name, primary_task')
      .or(`company_name.ilike.%${query}%, primary_task.ilike.%${query}%`)
      .limit(10)

    if (error) {
      console.error('Get suggestions error:', error)
      return []
    }

    const suggestions = new Set<string>()
    
    data?.forEach(tool => {
      if (tool.company_name && tool.company_name.toLowerCase().includes(query.toLowerCase())) {
        suggestions.add(tool.company_name)
      }
      if (tool.primary_task && tool.primary_task.toLowerCase().includes(query.toLowerCase())) {
        suggestions.add(tool.primary_task)
      }
    })

    return Array.from(suggestions).slice(0, 5)
  } catch (error) {
    console.error('Get search suggestions error:', error)
    return []
  }
}

export async function getRelatedCategories(query: string): Promise<string[]> {
  if (!query.trim()) return []

  const supabase = createBrowserClient()
  
  try {
    const { data, error } = await supabase
      .from('tools')
      .select('primary_task')
      .or(`company_name.ilike.%${query}%, short_description.ilike.%${query}%`)
      .limit(20)

    if (error) {
      console.error('Get related categories error:', error)
      return []
    }

    const categories = new Set<string>()
    data?.forEach(tool => {
      if (tool.primary_task) {
        categories.add(tool.primary_task)
      }
    })

    return Array.from(categories).slice(0, 5)
  } catch (error) {
    console.error('Get related categories error:', error)
    return []
  }
}
