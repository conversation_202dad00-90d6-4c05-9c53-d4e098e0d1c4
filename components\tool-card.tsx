"use client"

import { <PERSON>, <PERSON>L<PERSON>, Heart, <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON>R<PERSON> } from "lucide-react"
import Link from "next/link"
import { cn } from "@/lib/utils"
import { useState, useEffect, memo } from "react"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { useSupabase } from "@/hooks/use-supabase"
import { toast } from "sonner"
import { Badge } from "@/components/ui/badge"
import OptimizedImage from "@/components/ui/optimized-image"

export interface Tool {
  id: string | number
  name?: string
  company_name?: string
  description?: string
  short_description?: string
  logo?: string
  logo_url?: string
  category?: string
  primary_task?: string
  rating?: number
  reviewCount?: number
  pricing?: string
  url?: string
  visit_website_url?: string
  detail_url?: string
  slug?: string
  isFeatured?: boolean
  isNew?: boolean
  isVerified?: boolean
  is_featured?: boolean
  is_verified?: boolean
  full_description?: string
  featured_image_url?: string
  click_count?: number
  created_at?: string
  updated_at?: string
  applicable_tasks?: any[]
  cons?: any[]
  pros?: any[]
  faqs?: any
}

interface ToolCardProps {
  tool: Tool
  className?: string
  highlight?: string
  size?: string
}

export const ToolCard = memo(({ tool, className, highlight, size }: ToolCardProps) => {
  // Safety check for tool object
  if (!tool) {
    console.error('ToolCard: tool prop is undefined')
    return null
  }

  const { supabase, isInitialized } = useSupabase()
  const [isFavorite, setIsFavorite] = useState(false)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [imgError, setImgError] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [userRating, setUserRating] = useState<number | null>(null)
  const [hoveredRating, setHoveredRating] = useState<number | null>(null)
  const [isRatingSubmitting, setIsRatingSubmitting] = useState(false)
  const [authChecked, setAuthChecked] = useState(false)

  // Safe property extraction with fallbacks
  const name = tool?.name || tool?.company_name || "Unknown Tool"
  const description = tool?.description || tool?.short_description || "No description available"
  const logo = tool?.logo || tool?.logo_url || ""
  const category = tool?.category || tool?.primary_task || "General"
  const url = tool?.visit_website_url || tool?.detail_url || tool?.url || "#"
  const id = tool?.id || ""
  const rating = tool?.rating || 0
  const reviewCount = tool?.reviewCount || tool?.review_count || 0
  const pricing = tool?.pricing || "Contact for pricing"

  const isFeatured = Boolean(tool?.isFeatured || tool?.is_featured)
  const isVerified = Boolean(tool?.isVerified || tool?.is_verified)
  const isNew = tool?.isNew || false

  const numericId = typeof id === 'string' ? parseInt(id, 10) : id
  const slug = tool?.slug || tool?.toolSlug || ""

  // تحسين الأداء: تحقق من المصادقة فقط عند الحاجة
  useEffect(() => {
    const checkAuth = async () => {
      if (!supabase || !isInitialized || authChecked) return

      try {
        const { data: { session } } = await supabase.auth.getSession()
        setIsAuthenticated(!!session)
        setAuthChecked(true)

        // لا نحمل بيانات المفضلة والتقييمات إلا عند التفاعل
        // هذا يقلل الاستعلامات بشكل كبير
      } catch (error) {
        console.error('Error checking authentication:', error)
        setAuthChecked(true)
      }
    }

    checkAuth()
  }, [supabase, isInitialized, authChecked])

  // دالة منفصلة لتحميل بيانات المستخدم عند الحاجة فقط
  const loadUserData = async () => {
    if (!isAuthenticated || !supabase || isLoading) return

    try {
      setIsLoading(true)
      const { data: { session } } = await supabase.auth.getSession()
      
      if (session) {
        // تحميل المفضلة والتقييم في استعلام واحد محسن
        const [favResult, ratingResult] = await Promise.allSettled([
          supabase
            .from('favorites')
            .select('id')
            .eq('tool_id', numericId)
            .eq('user_id', session.user.id)
            .maybeSingle(),
          supabase
            .from('reviews')
            .select('rating')
            .eq('tool_id', numericId)
            .eq('user_id', session.user.id)
            .maybeSingle()
        ])

        if (favResult.status === 'fulfilled' && favResult.value.data) {
          setIsFavorite(!!favResult.value.data)
        }

        if (ratingResult.status === 'fulfilled' && ratingResult.value.data) {
          setUserRating(ratingResult.value.data.rating)
        }
      }
    } catch (error) {
      console.warn('Error loading user data:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleFavoriteClick = async (e: React.MouseEvent) => {
    e.preventDefault()
    e.stopPropagation()

    if (!isAuthenticated || !supabase) {
      toast.error("Please log in to add favorites")
      return
    }

    try {
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        toast.error("Please log in to add favorites")
        return
      }

      if (isFavorite) {
        const { error } = await supabase
          .from('favorites')
          .delete()
          .eq('tool_id', numericId)
          .eq('user_id', session.user.id)

        if (error) {
          console.warn('Error removing favorite:', error.message)
          toast.error("Failed to remove from favorites")
          return
        }
        setIsFavorite(false)
        toast.success("Removed from favorites")
      } else {
        const { error } = await supabase
          .from('favorites')
          .insert({
            tool_id: numericId,
            user_id: session.user.id
          })

        if (error) {
          console.warn('Error adding favorite:', error.message)
          toast.error("Failed to add to favorites")
          return
        }
        setIsFavorite(true)
        toast.success("Added to favorites")
      }
    } catch (error: any) {
      console.warn('Error updating favorite status:', error.message)
      toast.error("Failed to update favorite status")
    }
  }

  const handleVisitClick = async (e: React.MouseEvent) => {
    if (!supabase) return

    try {
      await supabase.rpc('increment_tool_click_count', { tool_id: numericId })
      console.log(`Visit clicked for tool: ${name} (ID: ${numericId}), redirecting to: ${url}`)
    } catch (error) {
      console.error('Error incrementing click count:', error)
    }
  }

  const handleRatingClick = async (star: number) => {
    if (!isAuthenticated || !supabase) {
      toast.error("Please log in to rate tools")
      return
    }

    try {
      setIsRatingSubmitting(true)
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        toast.error("Please log in to rate tools")
        return
      }

      const { data: existingRating, error: checkError } = await supabase
        .from('reviews')
        .select('id')
        .eq('tool_id', numericId)
        .eq('user_id', session.user.id)
        .maybeSingle()

      if (checkError && checkError.code !== 'PGRST116') {
        console.warn('Error checking existing rating:', checkError.message)
        toast.error("Failed to check existing rating")
        return
      }

      if (existingRating) {
        const { error: updateError } = await supabase
          .from('reviews')
          .update({
            rating: star,
            updated_at: new Date().toISOString()
          })
          .eq('id', existingRating.id)

        if (updateError) {
          console.warn('Error updating rating:', updateError.message)
          toast.error("Failed to update rating")
          return
        }

        toast.success(`You've updated your rating for ${name}`)
      } else {
        const { error: insertError } = await supabase.from('reviews').insert({
          tool_id: numericId,
          user_id: session.user.id,
          rating: star,
          comment: null
        })

        if (insertError) {
          console.warn('Error inserting rating:', insertError.message)
          toast.error("Failed to submit rating")
          return
        }

        toast.success(`Thank you for rating ${name}!`)
      }

      setUserRating(star)
    } catch (error: any) {
      console.warn('Error submitting rating:', error.message)
      toast.error("Failed to submit your rating. Please try again.")
    } finally {
      setIsRatingSubmitting(false)
    }
  }



  const placeholderImage = `https://via.placeholder.com/80x80/f3f4f6/6b7280?text=${encodeURIComponent(name.charAt(0).toUpperCase())}`
  
  // Always use grid view (list view removed)
  const isListView = false

  return (
    <div
      className={cn(
        "group relative rounded-xl shadow-sm hover:shadow-xl transition-all duration-300 bg-card border overflow-hidden card-hover theme-transition",
        isListView
          ? "flex flex-row items-center px-3 py-1 h-8 hover:translate-x-1 hover:shadow-sm hover:bg-muted/10"
          : "flex flex-col hover:-translate-y-2 p-4 sm:p-5 min-h-[220px] sm:min-h-[240px]",
        isFeatured
          ? "border-amber-400/50 ring-2 ring-amber-400/20 bg-gradient-to-br from-amber-50/40 to-orange-50/40 dark:from-amber-900/20 dark:to-orange-900/20"
          : isVerified
            ? "border-blue-400/50 ring-2 ring-blue-400/20 bg-gradient-to-br from-blue-50/40 to-indigo-50/40 dark:from-blue-900/20 dark:to-indigo-900/20"
            : "border-border hover:border-primary/40 hover:ring-2 hover:ring-primary/10",
        className
      )}
      data-tool-card="true"
      data-tool-name={name}
      data-tool-slug={slug}
    >
      {/* Main Content */}
      <div className={cn(
        "flex flex-1",
        isListView ? "gap-2 items-center" : "gap-3 items-start"
      )}>
        <div className={cn(
          "logo-container shadow-sm border border-gray-200/50 dark:border-gray-700/50",
          isListView ? "h-6 w-6 rounded" : "h-11 w-11 rounded-xl"
        )}>
          {imgError ? (
            <div className="flex items-center justify-center h-full w-full bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-700 dark:to-gray-800 text-gray-500 dark:text-gray-400 font-bold text-sm">
              {name.charAt(0).toUpperCase()}
            </div>
          ) : (
            <img
              src={logo || placeholderImage}
              alt={`${name} logo`}
              className="logo-image"
              onError={() => setImgError(true)}
              onLoad={(e) => {
                const target = e.target as HTMLImageElement;
                target.style.opacity = '1';
                setImgError(false);
              }}
              style={{ opacity: 0, transition: 'opacity 0.3s ease-in-out' }}
              loading="lazy"
            />
          )}
        </div>

        <div className={cn(
          "flex-1 min-w-0 relative",
          isListView && "flex items-center justify-between"
        )}>
          {/* أيقونات الحالة - فقط في Grid View */}
          {!isListView && (
            <div className="absolute -top-1 right-0 flex gap-1 z-10">
              {isFeatured && (
                <div
                  className="relative w-7 h-7 bg-gradient-to-br from-amber-400 via-yellow-500 to-orange-500 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl hover:scale-110 transition-all duration-300 ring-2 ring-white/80 dark:ring-gray-900/80 backdrop-blur-sm"
                  title="Featured Tool"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-yellow-200 to-amber-300 rounded-full opacity-30 animate-pulse"></div>
                  <svg className="w-4 h-4 text-white relative z-10 drop-shadow-md" fill="none" stroke="currentColor" strokeWidth="2" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M11.049 2.927c.3-.921 1.603-.921 1.902 0l1.519 4.674a1 1 0 00.95.69h4.915c.969 0 1.371 1.24.588 1.81l-3.976 2.888a1 1 0 00-.363 1.118l1.518 4.674c.3.922-.755 1.688-1.538 1.118l-3.976-2.888a1 1 0 00-1.176 0l-3.976 2.888c-.783.57-1.838-.197-1.538-1.118l1.518-4.674a1 1 0 00-.363-1.118l-3.976-2.888c-.784-.57-.38-1.81.588-1.81h4.914a1 1 0 00.951-.69l1.519-4.674z"/>
                  </svg>
                  <div className="absolute inset-0 rounded-full bg-gradient-to-t from-transparent to-white/20"></div>
                </div>
              )}
              {isVerified && (
                <div
                  className="relative w-7 h-7 bg-gradient-to-br from-blue-500 via-indigo-600 to-purple-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl hover:scale-110 transition-all duration-300 ring-2 ring-white/80 dark:ring-gray-900/80 backdrop-blur-sm"
                  title="Verified Tool"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-blue-200 to-indigo-300 rounded-full opacity-30 animate-pulse"></div>
                  <svg className="w-4 h-4 text-white relative z-10 drop-shadow-md" fill="none" stroke="currentColor" strokeWidth="2.5" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9 12.75L11.25 15 15 9.75M21 12c0 1.268-.63 2.39-1.593 3.068a3.745 3.745 0 01-1.043 3.296 3.745 3.745 0 01-3.296 1.043A3.745 3.745 0 0112 21c-1.268 0-2.39-.63-3.068-1.593a3.746 3.746 0 01-3.296-1.043 3.745 3.745 0 01-1.043-3.296A3.745 3.745 0 013 12c0-1.268.63-2.39 1.593-3.068a3.745 3.745 0 011.043-3.296 3.746 3.746 0 013.296-1.043A3.746 3.746 0 0112 3c1.268 0 2.39.63 3.068 1.593a3.746 3.746 0 013.296 1.043 3.746 3.746 0 011.043 3.296A3.745 3.745 0 0121 12z"/>
                  </svg>
                  <div className="absolute inset-0 rounded-full bg-gradient-to-t from-transparent to-white/20"></div>
                </div>
              )}
              {isNew && (
                <div
                  className="relative w-7 h-7 bg-gradient-to-br from-emerald-500 via-green-500 to-teal-600 rounded-full flex items-center justify-center shadow-lg hover:shadow-xl hover:scale-110 transition-all duration-300 ring-2 ring-white/80 dark:ring-gray-900/80 backdrop-blur-sm"
                  title="New Tool"
                >
                  <div className="absolute inset-0 bg-gradient-to-br from-emerald-200 to-green-300 rounded-full opacity-30 animate-ping"></div>
                  <svg className="w-4 h-4 text-white relative z-10 drop-shadow-md" fill="none" stroke="currentColor" strokeWidth="2.5" viewBox="0 0 24 24">
                    <path strokeLinecap="round" strokeLinejoin="round" d="M9.813 15.904L9 18.75l-.813-2.846a4.5 4.5 0 00-3.09-3.09L2.25 12l2.846-.813a4.5 4.5 0 003.09-3.09L9 5.25l.813 2.846a4.5 4.5 0 003.09 3.09L15.75 12l-2.846.813a4.5 4.5 0 00-3.09 3.09zM18.259 8.715L18 9.75l-.259-1.035a3.375 3.375 0 00-2.455-2.456L14.25 6l1.036-.259a3.375 3.375 0 002.455-2.456L18 2.25l.259 1.035a3.375 3.375 0 002.456 2.456L21.75 6l-1.035.259a3.375 3.375 0 00-2.456 2.456zM16.894 20.567L16.5 21.75l-.394-1.183a2.25 2.25 0 00-1.423-1.423L13.5 18.75l1.183-.394a2.25 2.25 0 001.423-1.423l.394-1.183.394 1.183a2.25 2.25 0 001.423 1.423l1.183.394-1.183.394a2.25 2.25 0 00-1.423 1.423z"/>
                  </svg>
                  <div className="absolute inset-0 rounded-full bg-gradient-to-t from-transparent to-white/20"></div>
                </div>
              )}
            </div>
          )}

          {/* Content Layout - Simple Row vs Grid */}
          {isListView ? (
            // ULTRA COMPACT ROW - Like table row
            <div className="flex items-center justify-between flex-1 gap-2">
              <div className="flex-1 min-w-0">
                <h3 className="font-medium text-xs text-gray-900 dark:text-white truncate leading-none">
                  {slug ? (
                    <Link href={`/Tool/${slug}`} className="hover:text-primary transition-colors duration-200">
                      {name}
                    </Link>
                  ) : (
                    <span>{name}</span>
                  )}
                </h3>
              </div>

              <div className="flex items-center gap-1 flex-shrink-0">
                {pricing && (
                  <span className={cn(
                    "text-xs px-1.5 py-0.5 rounded text-center min-w-0",
                    pricing.toLowerCase().includes('free')
                      ? "text-emerald-600 bg-emerald-50 dark:text-emerald-400 dark:bg-emerald-900/20"
                      : "text-amber-600 bg-amber-50 dark:text-amber-400 dark:bg-amber-900/20"
                  )}>
                    {pricing.toLowerCase().includes('free') ? 'Free' : 'Paid'}
                  </span>
                )}

                {url && url !== "#" && (
                  <a
                    href={url}
                    target="_blank"
                    rel="noopener noreferrer"
                    onClick={handleVisitClick}
                    className="inline-flex items-center px-1.5 py-0.5 text-xs font-medium text-primary-foreground bg-primary hover:bg-primary/90 rounded transition-colors"
                  >
                    Go
                  </a>
                )}
              </div>
            </div>
          ) : (
            // Grid View Layout (Original)
            <>
              <h3 className="font-bold text-sm sm:text-base text-gray-900 dark:text-white truncate mb-1 pr-6 sm:pr-8 leading-tight">
                {slug ? (
                  <Link href={`/Tool/${slug}`} className="hover:text-primary transition-colors duration-200 touch-manipulation">
                    {name}
                  </Link>
                ) : (
                  <span>{name}</span>
                )}
              </h3>

              <div className="flex flex-col gap-1.5 mt-1">
                <span className="inline-flex items-center gap-1 text-xs text-gray-600 dark:text-gray-400 bg-gradient-to-r from-gray-100 to-gray-50 dark:from-gray-800 dark:to-gray-750 px-2 py-0.5 rounded-lg font-medium border border-gray-200/50 dark:border-gray-700/50 w-fit max-w-full">
                  <div className="w-1.5 h-1.5 bg-gradient-to-r from-primary to-primary/80 rounded-full flex-shrink-0"></div>
                  <span className="truncate">{category}</span>
                </span>
                {pricing && (
                  <span className={`text-xs px-2 py-0.5 rounded-lg font-bold border w-fit ${
                    pricing.toLowerCase().includes('free') || pricing.toLowerCase().includes('مجاني')
                      ? 'bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 text-emerald-700 dark:text-emerald-400 border-emerald-200 dark:border-emerald-700/50'
                      : pricing.toLowerCase().includes('paid') || pricing.toLowerCase().includes('premium')
                      ? 'bg-gradient-to-r from-amber-50 to-yellow-50 dark:from-amber-900/20 dark:to-yellow-900/20 text-amber-700 dark:text-amber-400 border-amber-200 dark:border-amber-700/50'
                      : 'bg-gradient-to-r from-primary/10 to-primary/5 text-primary border-primary/20'
                  }`}>
                    {pricing.toLowerCase().includes('free') || pricing.toLowerCase().includes('مجاني') ? (
                      <span className="flex items-center gap-1.5">
                        <div className="w-2 h-2 bg-emerald-500 rounded-full animate-pulse"></div>
                        <span className="font-semibold text-emerald-700 dark:text-emerald-400">Free</span>
                      </span>
                    ) : pricing.toLowerCase().includes('paid') || pricing.toLowerCase().includes('premium') ? (
                      <span className="flex items-center gap-1.5">
                        <div className="w-2 h-2 bg-amber-500 rounded-full"></div>
                        <span className="font-semibold text-amber-700 dark:text-amber-400">{pricing}</span>
                      </span>
                    ) : (
                      pricing
                    )}
                  </span>
                )}
              </div>
            </>
          )}
        </div>
      </div>



      {/* Grid View Content - Only show for grid view */}
      {!isListView && (
        <>
          <p className="text-sm text-gray-600 dark:text-gray-400 mt-2 sm:mt-2.5 line-clamp-2 leading-relaxed">
            {description}
          </p>

          {/* Rating Section - مضغوط أكثر */}
          <div className="flex items-center gap-2 mt-2 sm:mt-2.5">
            <div className="flex items-center gap-0.5">
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className={cn(
                    "h-4 w-4 sm:h-3.5 sm:w-3.5 cursor-pointer transition-all duration-200 hover:scale-110 touch-manipulation",
                    (hoveredRating !== null ? i < hoveredRating : i < (userRating || rating))
                      ? "text-yellow-400 fill-yellow-400 drop-shadow-sm"
                      : "text-gray-300 hover:text-yellow-400"
                  )}
                  onClick={() => handleRatingClick(i + 1)}
                  onMouseEnter={() => setHoveredRating(i + 1)}
                  onMouseLeave={() => setHoveredRating(null)}
                />
              ))}
            </div>
            <span className="text-xs text-gray-500 dark:text-gray-400 font-medium truncate">
              {rating > 0 ? `${rating.toFixed(1)} (${reviewCount})` : 'No reviews'}
            </span>
          </div>

          {/* Enhanced Action Buttons */}
          <div className="mt-auto pt-3 sm:pt-4 flex gap-2 items-center">
            {slug ? (
              <Link
                href={`/Tool/${slug}`}
                className="flex-1 inline-flex items-center justify-center px-3 sm:px-4 py-3 sm:py-2.5 text-sm font-semibold text-foreground bg-muted hover:bg-muted/80 rounded-lg transition-all duration-200 border border-border hover:border-primary/30 hover:scale-[1.02] group min-h-[44px] touch-manipulation"
              >
                <span>Details</span>
                <ArrowRight className="h-3 w-3 ml-1 transition-transform group-hover:translate-x-0.5" />
              </Link>
            ) : (
              <button
                disabled
                className="flex-1 inline-flex items-center justify-center px-3 sm:px-4 py-3 sm:py-2.5 text-sm font-semibold text-muted-foreground bg-muted/50 rounded-lg border border-border opacity-50 cursor-not-allowed min-h-[44px]"
              >
                <span>No Details</span>
              </button>
            )}

            {/* Enhanced Favorite Button */}
            <button
              onClick={handleFavoriteClick}
              className={cn(
                "p-3 sm:p-2.5 rounded-lg transition-all duration-200 hover:scale-110 shadow-sm hover:shadow-md border min-h-[44px] min-w-[44px] touch-manipulation",
                isFavorite
                  ? "bg-red-50 border-red-200 hover:bg-red-100 dark:bg-red-900/20 dark:border-red-800 dark:hover:bg-red-900/30"
                  : "bg-background border-border hover:bg-muted hover:border-red-200"
              )}
              aria-label={isFavorite ? "Remove from favorites" : "Add to favorites"}
            >
              <Heart className={cn(
                "h-4 w-4 transition-all duration-200",
                isFavorite
                  ? "fill-red-500 text-red-500"
                  : "text-muted-foreground hover:text-red-500"
              )} />
            </button>

            {url && url !== "#" && (
              <a
                href={url}
                target="_blank"
                rel="noopener noreferrer"
                onClick={handleVisitClick}
                className="flex-1 inline-flex items-center justify-center gap-2 px-3 sm:px-4 py-3 sm:py-2.5 text-sm font-semibold text-primary-foreground bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary/80 rounded-lg transition-all duration-200 shadow-md hover:shadow-lg hover:scale-[1.02] border border-primary/20 group min-h-[44px] touch-manipulation"
              >
                <span>Visit</span>
                <ExternalLink className="h-3 w-3 transition-transform group-hover:translate-x-0.5 group-hover:-translate-y-0.5" />
              </a>
            )}
          </div>
        </>
      )}
    </div>
  )
})

export default ToolCard
