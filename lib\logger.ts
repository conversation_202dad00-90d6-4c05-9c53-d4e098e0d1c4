/**
 * Enhanced logging utility for better debugging and error tracking
 */

// Log levels
export enum LogLevel {
  DEBUG = 0,
  INFO = 1,
  WARN = 2,
  ERROR = 3,
  NONE = 4
}

// Current log level - can be adjusted based on environment
const currentLogLevel = process.env.NODE_ENV === 'production' 
  ? LogLevel.ERROR  // Only log errors in production
  : LogLevel.DEBUG; // Log everything in development

// Store logs in memory for debugging
const logHistory: Array<{
  level: LogLevel;
  message: string;
  data?: any;
  timestamp: Date;
  context?: string;
}> = [];

// Maximum number of logs to keep in memory
const MAX_LOG_HISTORY = 100;

/**
 * Log a message at the specified level
 * @param level Log level
 * @param message Log message
 * @param data Additional data to log
 * @param context Context of the log (e.g., component name)
 */
function log(level: LogLevel, message: string, data?: any, context?: string) {
  // Skip if below current log level
  if (level < currentLogLevel) return;

  const logEntry = {
    level,
    message,
    data,
    timestamp: new Date(),
    context
  };

  // Add to history
  logHistory.unshift(logEntry);
  
  // Trim history if needed
  if (logHistory.length > MAX_LOG_HISTORY) {
    logHistory.pop();
  }

  // Format the log message
  const formattedMessage = context 
    ? `[${context}] ${message}`
    : message;

  // Log to console based on level
  switch (level) {
    case LogLevel.DEBUG:
      console.debug(formattedMessage, data || '');
      break;
    case LogLevel.INFO:
      console.info(formattedMessage, data || '');
      break;
    case LogLevel.WARN:
      console.warn(formattedMessage, data || '');
      break;
    case LogLevel.ERROR:
      console.error(formattedMessage, data || '');
      break;
  }
}

/**
 * Log a debug message
 * @param message Log message
 * @param data Additional data to log
 * @param context Context of the log
 */
export function debug(message: string, data?: any, context?: string) {
  log(LogLevel.DEBUG, message, data, context);
}

/**
 * Log an info message
 * @param message Log message
 * @param data Additional data to log
 * @param context Context of the log
 */
export function info(message: string, data?: any, context?: string) {
  log(LogLevel.INFO, message, data, context);
}

/**
 * Log a warning message
 * @param message Log message
 * @param data Additional data to log
 * @param context Context of the log
 */
export function warn(message: string, data?: any, context?: string) {
  log(LogLevel.WARN, message, data, context);
}

/**
 * Log an error message
 * @param message Log message
 * @param error Error object or additional data
 * @param context Context of the log
 */
export function error(message: string, error?: any, context?: string) {
  log(LogLevel.ERROR, message, error, context);
}

/**
 * Get the log history
 * @returns Array of log entries
 */
export function getLogHistory() {
  return [...logHistory];
}

/**
 * Clear the log history
 */
export function clearLogHistory() {
  logHistory.length = 0;
}

/**
 * Log a database query for debugging
 * @param query Query string or object
 * @param params Query parameters
 * @param context Context of the query
 */
export function logQuery(query: string | object, params?: any, context?: string) {
  debug('Database Query', { query, params }, context || 'Database');
}

/**
 * Log a search operation
 * @param term Search term
 * @param filters Search filters
 * @param results Search results
 * @param duration Duration of the search in milliseconds
 */
export function logSearch(term: string, filters?: any, results?: any[], duration?: number) {
  info('Search Operation', {
    term,
    filters,
    resultCount: results?.length || 0,
    duration,
  }, 'Search');
}

/**
 * Log an API request
 * @param method HTTP method
 * @param url Request URL
 * @param status Response status
 * @param duration Duration of the request in milliseconds
 */
export function logApiRequest(method: string, url: string, status?: number, duration?: number) {
  info('API Request', {
    method,
    url,
    status,
    duration,
  }, 'API');
}

// Export a default logger object
export default {
  debug,
  info,
  warn,
  error,
  logQuery,
  logSearch,
  logApiRequest,
  getLogHistory,
  clearLogHistory,
};
