/**
 * Professional Open Graph Configuration
 * Centralized OG settings for consistent social media sharing
 */

export interface OpenGraphConfig {
  title: string
  description: string
  url: string
  type: 'website' | 'article' | 'product'
  images: OpenGraphImage[]
  siteName?: string
  locale?: string
  countryName?: string
  emails?: string[]
  phoneNumbers?: string[]
  alternateLocale?: string[]
  article?: {
    publishedTime?: string
    modifiedTime?: string
    authors?: string[]
    section?: string
    tags?: string[]
  }
}

export interface OpenGraphImage {
  url: string
  secureUrl?: string
  width: number
  height: number
  alt: string
  type: string
}

export interface TwitterConfig {
  card: 'summary' | 'summary_large_image' | 'app' | 'player'
  title: string
  description: string
  images: string[]
  creator?: string
  site?: string
}

// Default Open Graph configuration
export const defaultOGConfig: Partial<OpenGraphConfig> = {
  siteName: 'AiAnyTool.com',
  locale: 'en_US',
  countryName: 'United States',
  emails: ['<EMAIL>'],
  phoneNumbers: [],
  alternateLocale: ['en_GB', 'en_CA'],
}

// Default Twitter configuration
export const defaultTwitterConfig: Partial<TwitterConfig> = {
  card: 'summary_large_image',
  creator: '@aianytool',
  site: '@aianytool',
}

/**
 * Generate professional Open Graph configuration for tools
 */
export function generateToolOG(tool: any, slug: string): OpenGraphConfig {
  const title = `${tool.company_name} - ${tool.primary_task || 'AI Tool'}`
  const description = tool.short_description || `Discover ${tool.company_name}, a powerful AI tool for ${tool.primary_task?.toLowerCase() || 'various tasks'}. Read reviews, compare features, and find pricing information.`
  
  return {
    ...defaultOGConfig,
    title,
    description,
    url: `https://aianytool.com/Tool/${slug}`,
    type: 'article',
    images: generateToolImages(tool),
    article: {
      publishedTime: tool.created_at,
      modifiedTime: tool.updated_at,
      authors: ['https://aianytool.com/about'],
      section: tool.primary_task || 'AI Tools',
      tags: generateToolTags(tool),
    },
  }
}

/**
 * Generate professional Twitter configuration for tools
 */
export function generateToolTwitter(tool: any): TwitterConfig {
  const title = `${tool.company_name} - ${tool.primary_task || 'AI Tool'}`
  const description = tool.short_description || `Discover ${tool.company_name}, a powerful AI tool for ${tool.primary_task?.toLowerCase() || 'various tasks'}.`
  
  return {
    ...defaultTwitterConfig,
    title: title.length > 70 ? title.substring(0, 67) + '...' : title,
    description: description.length > 200 ? description.substring(0, 197) + '...' : description,
    images: tool.featured_image_url ? [
      tool.featured_image_url.startsWith('http') ? tool.featured_image_url : `https://aianytool.com${tool.featured_image_url}`
    ] : ['https://aianytool.com/og-default-tool.jpg'],
  }
}

/**
 * Generate professional Open Graph configuration for categories
 */
export function generateCategoryOG(category: string, toolsCount: number, slug: string): OpenGraphConfig {
  const title = `Best ${category} AI Tools 2024 - ${toolsCount}+ Tools | AiAnyTool.com`
  const description = `Discover the best ${category} AI tools. Compare ${toolsCount}+ ${category} tools with reviews, pricing, and features. Find the perfect AI solution for your ${category.toLowerCase()} needs.`
  
  return {
    ...defaultOGConfig,
    title,
    description,
    url: `https://aianytool.com/category/${slug}`,
    type: 'website',
    images: [
      {
        url: 'https://aianytool.com/og-category.jpg',
        secureUrl: 'https://aianytool.com/og-category.jpg',
        width: 1200,
        height: 630,
        alt: `${category} AI Tools Directory - ${toolsCount}+ Curated Tools | AiAnyTool.com`,
        type: 'image/jpeg',
      },
      {
        url: 'https://aianytool.com/og-category-square.jpg',
        secureUrl: 'https://aianytool.com/og-category-square.jpg',
        width: 1200,
        height: 1200,
        alt: `Best ${category} AI Tools Collection`,
        type: 'image/jpeg',
      }
    ],
  }
}

/**
 * Generate images for tool Open Graph
 */
function generateToolImages(tool: any): OpenGraphImage[] {
  const images: OpenGraphImage[] = []
  
  // Primary image (featured image or default)
  if (tool.featured_image_url) {
    images.push({
      url: tool.featured_image_url.startsWith('http') ? tool.featured_image_url : `https://aianytool.com${tool.featured_image_url}`,
      secureUrl: tool.featured_image_url.startsWith('http') ? tool.featured_image_url : `https://aianytool.com${tool.featured_image_url}`,
      width: 1200,
      height: 630,
      alt: `${tool.company_name} - ${tool.primary_task || 'AI Tool'} Interface Screenshot | Review & Features`,
      type: 'image/jpeg',
    })
  } else {
    images.push({
      url: 'https://aianytool.com/og-default-tool.jpg',
      secureUrl: 'https://aianytool.com/og-default-tool.jpg',
      width: 1200,
      height: 630,
      alt: `${tool.company_name} - AI Tool Review & Analysis | AiAnyTool.com`,
      type: 'image/jpeg',
    })
  }
  
  // Secondary image (logo if available)
  if (tool.logo_url) {
    images.push({
      url: tool.logo_url.startsWith('http') ? tool.logo_url : `https://aianytool.com${tool.logo_url}`,
      secureUrl: tool.logo_url.startsWith('http') ? tool.logo_url : `https://aianytool.com${tool.logo_url}`,
      width: 400,
      height: 400,
      alt: `${tool.company_name} Logo`,
      type: 'image/png',
    })
  }
  
  return images
}

/**
 * Generate tags for tool Open Graph
 */
function generateToolTags(tool: any): string[] {
  const tags = [
    tool.company_name,
    tool.primary_task,
    'AI tool',
    'review',
    'pricing',
    'features',
    'alternatives',
    '2024',
  ]
  
  if (tool.pricing) {
    tags.push(tool.pricing, 'pricing')
  }
  
  if (tool.primary_task) {
    tags.push(tool.primary_task.toLowerCase(), `${tool.primary_task} AI`)
  }
  
  return tags.filter(Boolean)
}

/**
 * Validate Open Graph image requirements
 */
export function validateOGImage(image: OpenGraphImage): boolean {
  // Facebook/Meta requirements
  const minWidth = 200
  const minHeight = 200
  const maxWidth = 8192
  const maxHeight = 8192
  const recommendedRatio = 1.91 // 1200x630
  
  if (image.width < minWidth || image.height < minHeight) {
    console.warn(`OG image too small: ${image.width}x${image.height}`)
    return false
  }
  
  if (image.width > maxWidth || image.height > maxHeight) {
    console.warn(`OG image too large: ${image.width}x${image.height}`)
    return false
  }
  
  const ratio = image.width / image.height
  if (Math.abs(ratio - recommendedRatio) > 0.1) {
    console.warn(`OG image ratio not optimal: ${ratio.toFixed(2)} (recommended: ${recommendedRatio})`)
  }
  
  return true
}

/**
 * Generate structured data for better social sharing
 */
export function generateStructuredData(tool: any) {
  return {
    "@context": "https://schema.org",
    "@type": "SoftwareApplication",
    "name": tool.company_name,
    "description": tool.short_description,
    "url": tool.visit_website_url,
    "applicationCategory": tool.primary_task,
    "operatingSystem": "Web Browser",
    "author": {
      "@type": "Organization",
      "name": "AiAnyTool.com",
      "url": "https://aianytool.com"
    },
    "image": tool.featured_image_url || tool.logo_url,
    "datePublished": tool.created_at,
    "dateModified": tool.updated_at,
    ...(tool.pricing && {
      "offers": {
        "@type": "Offer",
        "price": tool.pricing.toLowerCase().includes('free') ? '0' : 'varies',
        "priceCurrency": "USD"
      }
    })
  }
}
