"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Star, ExternalLink, Heart, Award, TrendingUp, ArrowRight, <PERSON>rkles } from 'lucide-react'
import { createBrowserClient } from '@/lib/supabase/client-utils'
import Link from 'next/link'
import { cn } from '@/lib/utils'

interface FeaturedTool {
  id: string
  company_name: string
  short_description: string
  logo_url?: string
  url?: string
  rating?: number
  pricing?: string
  primary_task?: string
  is_featured: boolean
  is_verified: boolean
  created_at: string
}

interface FeaturedToolsSectionProps {
  limit?: number
  showHeader?: boolean
  className?: string
}

export default function FeaturedToolsSection({
  limit = 6,
  showHeader = true,
  className
}: FeaturedToolsSectionProps) {
  const [featuredTools, setFeaturedTools] = useState<FeaturedTool[]>([])
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchFeaturedTools = async () => {
      try {
        setLoading(true)
        const supabase = createBrowserClient()

        const { data: tools, error } = await supabase
          .from('tools')
          .select('*')
          .eq('is_featured', true)
          .order('rating', { ascending: false })
          .limit(limit)

        if (error) {
          console.error('Error fetching featured tools:', error)
          return
        }

        setFeaturedTools(tools || [])
      } catch (error) {
        console.error('Error fetching featured tools:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchFeaturedTools()
  }, [limit])

  if (loading) {
    return (
      <div className={cn("space-y-6", className)}>
        {showHeader && (
          <div className="text-center space-y-2">
            <div className="h-8 bg-muted rounded w-48 mx-auto animate-pulse"></div>
            <div className="h-4 bg-muted rounded w-64 mx-auto animate-pulse"></div>
          </div>
        )}
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array(limit).fill(0).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="flex items-center gap-3 mb-4">
                  <div className="h-12 w-12 bg-muted rounded-lg"></div>
                  <div className="flex-1">
                    <div className="h-4 bg-muted rounded mb-2"></div>
                    <div className="h-3 bg-muted rounded w-2/3"></div>
                  </div>
                </div>
                <div className="h-3 bg-muted rounded mb-2"></div>
                <div className="h-3 bg-muted rounded w-3/4 mb-4"></div>
                <div className="flex gap-2 mb-4">
                  <div className="h-6 bg-muted rounded w-16"></div>
                  <div className="h-6 bg-muted rounded w-16"></div>
                </div>
                <div className="h-10 bg-muted rounded"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  if (featuredTools.length === 0) {
    return null
  }

  return (
    <div className={cn("space-y-8", className)}>
      {showHeader && (
        <div className="text-center space-y-4">
          <div className="inline-flex items-center gap-2 bg-amber-50 text-amber-700 px-4 py-2 rounded-full text-sm font-medium border border-amber-200">
            <Sparkles className="h-4 w-4" />
            Featured AI Tools
          </div>
          <h2 className="text-3xl md:text-4xl font-bold">
            Handpicked Tools for You
          </h2>
          <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover the most popular and highly-rated AI tools trusted by thousands of users worldwide
          </p>
        </div>
      )}

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {featuredTools.map((tool, index) => (
          <Card
            key={tool.id}
            className="group cursor-pointer transition-all duration-300 hover:shadow-xl hover:-translate-y-2 bg-gradient-to-br from-amber-50/30 to-orange-50/30 border-amber-200/50 relative overflow-hidden"
          >
            {/* Featured Badge */}
            <div className="absolute top-4 right-4 z-10">
              <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 shadow-lg">
                <Award className="h-3 w-3 mr-1" />
                Featured
              </Badge>
            </div>

            <CardContent className="p-6">
              {/* Tool Header */}
              <div className="flex items-start gap-4 mb-4">
                <div className="relative">
                  {tool.logo_url ? (
                    <img
                      src={tool.logo_url}
                      alt={tool.company_name}
                      className="w-12 h-12 rounded-lg object-cover border border-border"
                    />
                  ) : (
                    <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center border border-border">
                      <span className="text-lg font-bold text-primary">
                        {tool.company_name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  {tool.is_verified && (
                    <div className="absolute -bottom-1 -right-1 w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center">
                      <Star className="h-3 w-3 text-white fill-white" />
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <h3 className="font-bold text-lg group-hover:text-primary transition-colors truncate">
                    {tool.company_name}
                  </h3>
                  {tool.primary_task && (
                    <Badge variant="outline" className="text-xs mt-1">
                      {tool.primary_task}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-muted-foreground line-clamp-3 mb-4">
                {tool.short_description}
              </p>

              {/* Stats */}
              <div className="flex items-center gap-4 mb-4">
                {tool.rating && (
                  <div className="flex items-center gap-1">
                    <Star className="h-4 w-4 text-amber-500 fill-amber-500" />
                    <span className="text-sm font-medium">{tool.rating.toFixed(1)}</span>
                  </div>
                )}

                {tool.pricing && (
                  <Badge 
                    variant="secondary" 
                    className={cn(
                      "text-xs",
                      tool.pricing.toLowerCase().includes('free') 
                        ? "bg-green-50 text-green-700 border-green-200"
                        : "bg-blue-50 text-blue-700 border-blue-200"
                    )}
                  >
                    {tool.pricing}
                  </Badge>
                )}

                {/* New Badge */}
                {tool.created_at && new Date(tool.created_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) && (
                  <Badge variant="secondary" className="bg-purple-50 text-purple-700 border-purple-200 text-xs">
                    <TrendingUp className="h-3 w-3 mr-1" />
                    New
                  </Badge>
                )}
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <Link href={`/Tool/${tool.slug || tool.company_name.toLowerCase().replace(/\s+/g, '-')}`} className="flex-1">
                  <Button variant="outline" size="sm" className="w-full group/btn">
                    <span>Learn More</span>
                    <ArrowRight className="h-3 w-3 ml-1 transition-transform group-hover/btn:translate-x-0.5" />
                  </Button>
                </Link>

                {tool.url && tool.url !== "#" && (
                  <Button size="sm" className="px-3" asChild>
                    <a
                      href={tool.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  </Button>
                )}

                <Button variant="ghost" size="sm" className="px-3 hover:text-red-500">
                  <Heart className="h-4 w-4" />
                </Button>
              </div>
            </CardContent>

            {/* Hover Effect Overlay */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
          </Card>
        ))}
      </div>

      {/* View All Button */}
      <div className="text-center">
        <Link href="/tools?sortBy=featured">
          <Button size="lg" variant="outline" className="group">
            View All Featured Tools
            <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>
        </Link>
      </div>
    </div>
  )
}
