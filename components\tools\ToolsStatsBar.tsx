"use client"

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { TrendingUp, Star, Clock, Zap, Users, Award } from 'lucide-react'
import { createBrowserClient } from '@/lib/supabase/client-utils'

interface ToolsStatsBarProps {
  searchQuery?: string
  category?: string
  pricing?: string
  features?: string[]
}

interface Stats {
  totalTools: number
  featuredTools: number
  freeTools: number
  averageRating: number
  totalCategories: number
  recentlyAdded: number
}

export default function ToolsStatsBar({
  searchQuery,
  category,
  pricing,
  features = []
}: ToolsStatsBarProps) {
  const [stats, setStats] = useState<Stats>({
    totalTools: 0,
    featuredTools: 0,
    freeTools: 0,
    averageRating: 0,
    totalCategories: 0,
    recentlyAdded: 0
  })
  const [loading, setLoading] = useState(true)

  useEffect(() => {
    const fetchStats = async () => {
      try {
        setLoading(true)
        const supabase = createBrowserClient()

        // Build query based on filters
        let query = supabase.from('tools').select('*')

        if (searchQuery) {
          query = query.or(
            `company_name.ilike.%${searchQuery}%,short_description.ilike.%${searchQuery}%,full_description.ilike.%${searchQuery}%`
          )
        }

        if (category) {
          query = query.eq('primary_task', category.replace(/-/g, ' '))
        }

        if (pricing) {
          query = query.eq('pricing', pricing)
        }

        if (features.length > 0) {
          features.forEach(feature => {
            query = query.contains('features', [feature.replace(/-/g, ' ')])
          })
        }

        const { data: tools, error } = await query

        if (error) {
          console.error('Error fetching stats:', error)
          return
        }

        if (tools) {
          // Calculate stats
          const totalTools = tools.length
          const featuredTools = tools.filter(tool => tool.is_featured).length
          const freeTools = tools.filter(tool => 
            tool.pricing?.toLowerCase().includes('free') || 
            tool.pricing?.toLowerCase().includes('مجاني')
          ).length
          
          const ratingsSum = tools.reduce((sum, tool) => sum + (tool.rating || 0), 0)
          const averageRating = totalTools > 0 ? ratingsSum / totalTools : 0
          
          const categories = new Set(tools.map(tool => tool.primary_task).filter(Boolean))
          const totalCategories = categories.size

          // Recently added (last 30 days)
          const thirtyDaysAgo = new Date()
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)
          const recentlyAdded = tools.filter(tool => 
            tool.created_at && new Date(tool.created_at) > thirtyDaysAgo
          ).length

          setStats({
            totalTools,
            featuredTools,
            freeTools,
            averageRating,
            totalCategories,
            recentlyAdded
          })
        }
      } catch (error) {
        console.error('Error fetching stats:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchStats()
  }, [searchQuery, category, pricing, features])

  if (loading) {
    return (
      <div className="bg-card rounded-xl border shadow-sm p-6">
        <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
          {Array(6).fill(0).map((_, i) => (
            <div key={i} className="text-center">
              <div className="h-8 w-8 bg-muted rounded-full mx-auto mb-2 animate-pulse"></div>
              <div className="h-4 bg-muted rounded mx-auto mb-1 animate-pulse"></div>
              <div className="h-3 bg-muted rounded w-16 mx-auto animate-pulse"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  const statItems = [
    {
      icon: <Zap className="h-5 w-5 text-blue-500" />,
      value: stats.totalTools.toLocaleString(),
      label: 'Total Tools',
      color: 'bg-blue-50 text-blue-700 border-blue-200'
    },
    {
      icon: <Award className="h-5 w-5 text-amber-500" />,
      value: stats.featuredTools.toLocaleString(),
      label: 'Featured',
      color: 'bg-amber-50 text-amber-700 border-amber-200'
    },
    {
      icon: <TrendingUp className="h-5 w-5 text-green-500" />,
      value: stats.freeTools.toLocaleString(),
      label: 'Free Tools',
      color: 'bg-green-50 text-green-700 border-green-200'
    },
    {
      icon: <Star className="h-5 w-5 text-yellow-500" />,
      value: stats.averageRating.toFixed(1),
      label: 'Avg Rating',
      color: 'bg-yellow-50 text-yellow-700 border-yellow-200'
    },
    {
      icon: <Users className="h-5 w-5 text-purple-500" />,
      value: stats.totalCategories.toLocaleString(),
      label: 'Categories',
      color: 'bg-purple-50 text-purple-700 border-purple-200'
    },
    {
      icon: <Clock className="h-5 w-5 text-indigo-500" />,
      value: stats.recentlyAdded.toLocaleString(),
      label: 'New (30d)',
      color: 'bg-indigo-50 text-indigo-700 border-indigo-200'
    }
  ]

  return (
    <div className="bg-card rounded-xl border shadow-sm p-6 mb-6">
      <div className="grid grid-cols-2 md:grid-cols-3 lg:grid-cols-6 gap-4">
        {statItems.map((item, index) => (
          <div
            key={index}
            className="text-center group hover:scale-105 transition-transform duration-200"
          >
            <div className="flex items-center justify-center mb-2">
              <div className="p-2 rounded-full bg-muted/50 group-hover:bg-muted transition-colors">
                {item.icon}
              </div>
            </div>
            <div className="text-2xl font-bold text-foreground mb-1">
              {item.value}
            </div>
            <Badge variant="outline" className={`text-xs ${item.color}`}>
              {item.label}
            </Badge>
          </div>
        ))}
      </div>
    </div>
  )
}
