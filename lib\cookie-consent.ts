/**
 * Cookie Consent Management System
 * Compliant with GDPR, CCPA, and international privacy regulations
 */

// Cookie consent categories
export type CookieCategory = 'essential' | 'analytics' | 'marketing' | 'preferences'

// Cookie consent preferences
export interface CookiePreferences {
  essential: boolean
  analytics: boolean
  marketing: boolean
  preferences: boolean
  timestamp: number
  version: string
}

// Default preferences (all cookies enabled by default for maximum tracking)
export const DEFAULT_PREFERENCES: CookiePreferences = {
  essential: true,
  analytics: true, // Enable analytics by default for better tracking
  marketing: true, // Enable marketing by default for comprehensive tracking
  preferences: true, // Enable preferences by default for better user experience
  timestamp: Date.now(),
  version: '1.0'
}

// Cookie consent configuration
export const COOKIE_CONFIG = {
  consentCookieName: 'cookie-consent-preferences',
  bannerCookieName: 'cookie-banner-dismissed',
  consentVersion: '1.0',
  expiryDays: 365,
  domain: typeof window !== 'undefined' ? window.location.hostname : '',
  secure: typeof window !== 'undefined' ? window.location.protocol === 'https:' : true,
  sameSite: 'Lax' as const
}

// Cookie categories information
export const COOKIE_CATEGORIES = {
  essential: {
    name: 'Essential Cookies',
    description: 'These cookies are necessary for the website to function and cannot be switched off. They are usually only set in response to actions made by you which amount to a request for services.',
    required: true,
    examples: ['Authentication', 'Security', 'Session management', 'Load balancing']
  },
  analytics: {
    name: 'Analytics Cookies',
    description: 'These cookies help us understand how visitors interact with our website by collecting and reporting information anonymously.',
    required: false,
    examples: ['Google Analytics', 'Page views', 'User behavior', 'Performance metrics']
  },
  marketing: {
    name: 'Marketing Cookies',
    description: 'These cookies are used to track visitors across websites to display relevant advertisements and marketing content.',
    required: false,
    examples: ['Ad targeting', 'Social media', 'Remarketing', 'Conversion tracking']
  },
  preferences: {
    name: 'Preference Cookies',
    description: 'These cookies remember your choices and preferences to provide a more personalized experience.',
    required: false,
    examples: ['Language settings', 'Theme preferences', 'Layout choices', 'Saved filters']
  }
} as const

/**
 * Set a cookie with proper security settings
 */
function setCookie(name: string, value: string, days: number = COOKIE_CONFIG.expiryDays): void {
  if (typeof window === 'undefined') return

  const expires = new Date()
  expires.setTime(expires.getTime() + days * 24 * 60 * 60 * 1000)
  
  const cookieOptions = [
    `${name}=${encodeURIComponent(value)}`,
    `expires=${expires.toUTCString()}`,
    'path=/',
    `SameSite=${COOKIE_CONFIG.sameSite}`
  ]

  if (COOKIE_CONFIG.secure) {
    cookieOptions.push('Secure')
  }

  if (COOKIE_CONFIG.domain && COOKIE_CONFIG.domain !== 'localhost') {
    cookieOptions.push(`domain=${COOKIE_CONFIG.domain}`)
  }

  document.cookie = cookieOptions.join('; ')
}

/**
 * Get a cookie value
 */
function getCookie(name: string): string | null {
  if (typeof window === 'undefined') return null

  const nameEQ = name + '='
  const ca = document.cookie.split(';')
  
  for (let i = 0; i < ca.length; i++) {
    let c = ca[i]
    while (c.charAt(0) === ' ') c = c.substring(1, c.length)
    if (c.indexOf(nameEQ) === 0) {
      return decodeURIComponent(c.substring(nameEQ.length, c.length))
    }
  }
  return null
}

/**
 * Delete a cookie
 */
function deleteCookie(name: string): void {
  setCookie(name, '', -1)
}

/**
 * Save cookie preferences
 */
export function saveCookiePreferences(preferences: Partial<CookiePreferences>): void {
  const fullPreferences: CookiePreferences = {
    ...DEFAULT_PREFERENCES,
    ...preferences,
    essential: true, // Essential cookies are always required
    timestamp: Date.now(),
    version: COOKIE_CONFIG.consentVersion
  }

  setCookie(COOKIE_CONFIG.consentCookieName, JSON.stringify(fullPreferences))
  
  // Trigger custom event for other components to listen to
  if (typeof window !== 'undefined') {
    window.dispatchEvent(new CustomEvent('cookiePreferencesChanged', {
      detail: fullPreferences
    }))
  }
}

/**
 * Get current cookie preferences
 */
export function getCookiePreferences(): CookiePreferences | null {
  const cookieValue = getCookie(COOKIE_CONFIG.consentCookieName)
  
  if (!cookieValue) {
    return null
  }

  try {
    const preferences = JSON.parse(cookieValue) as CookiePreferences
    
    // Check if consent is still valid (version and expiry)
    const isValidVersion = preferences.version === COOKIE_CONFIG.consentVersion
    const isNotExpired = Date.now() - preferences.timestamp < (COOKIE_CONFIG.expiryDays * 24 * 60 * 60 * 1000)
    
    if (isValidVersion && isNotExpired) {
      // Force update old preferences to enable ALL cookies by default
      if (!preferences.analytics || !preferences.marketing || !preferences.preferences) {
        const updatedPreferences = {
          ...preferences,
          analytics: true, // Force enable analytics
          marketing: true, // Force enable marketing
          preferences: true, // Force enable preferences
          timestamp: Date.now()
        }
        saveCookiePreferences(updatedPreferences)
        return updatedPreferences
      }
      return preferences
    } else {
      // Clear invalid consent
      deleteCookie(COOKIE_CONFIG.consentCookieName)
      return null
    }
  } catch (error) {
    console.error('Error parsing cookie preferences:', error)
    deleteCookie(COOKIE_CONFIG.consentCookieName)
    return null
  }
}

/**
 * Check if user has given consent for a specific category
 */
export function hasConsent(category: CookieCategory): boolean {
  const preferences = getCookiePreferences()

  if (!preferences) {
    // No explicit consent given yet - ALL categories enabled by default
    return true // All cookies enabled by default for maximum tracking
  }

  return preferences[category] === true
}

/**
 * Check if user has dismissed the cookie banner
 */
export function hasDismissedBanner(): boolean {
  return getCookie(COOKIE_CONFIG.bannerCookieName) === 'true'
}

/**
 * Mark cookie banner as dismissed
 */
export function dismissBanner(): void {
  setCookie(COOKIE_CONFIG.bannerCookieName, 'true')
}

/**
 * Accept all cookies
 */
export function acceptAllCookies(): void {
  saveCookiePreferences({
    essential: true,
    analytics: true,
    marketing: true,
    preferences: true
  })
  dismissBanner()
}

/**
 * Accept only essential cookies
 */
export function acceptEssentialOnly(): void {
  saveCookiePreferences({
    essential: true,
    analytics: false,
    marketing: false,
    preferences: false
  })
  dismissBanner()
}

/**
 * Reset all cookie preferences and banner state
 */
export function resetCookiePreferences(): void {
  deleteCookie(COOKIE_CONFIG.consentCookieName)
  deleteCookie(COOKIE_CONFIG.bannerCookieName)

  // Set new defaults with analytics enabled
  saveCookiePreferences(DEFAULT_PREFERENCES)

  // Clear old analytics cookies if they exist
  if (typeof window !== 'undefined') {
    // Clear Google Analytics cookies
    const gaCookies = document.cookie.split(';').filter(cookie =>
      cookie.trim().startsWith('_ga') ||
      cookie.trim().startsWith('_gid') ||
      cookie.trim().startsWith('_gat')
    )

    gaCookies.forEach(cookie => {
      const cookieName = cookie.split('=')[0].trim()
      deleteCookie(cookieName)
    })

    // Trigger custom event with new preferences
    window.dispatchEvent(new CustomEvent('cookiePreferencesChanged', {
      detail: DEFAULT_PREFERENCES
    }))
  }
}

/**
 * Get consent status summary
 */
export function getConsentStatus() {
  const preferences = getCookiePreferences()
  const bannerDismissed = hasDismissedBanner()

  return {
    hasConsent: preferences !== null,
    bannerDismissed,
    preferences: preferences || DEFAULT_PREFERENCES,
    needsConsent: !preferences && !bannerDismissed,
    // Analytics tracking is enabled by default even without explicit consent
    analyticsEnabled: preferences ? preferences.analytics : true
  }
}

/**
 * Force update existing preferences to enable ALL cookies by default
 * This function ensures all users have all tracking enabled
 */
export function forceEnableAnalytics(): void {
  const currentPreferences = getCookiePreferences()

  if (currentPreferences && (!currentPreferences.analytics || !currentPreferences.marketing || !currentPreferences.preferences)) {
    const updatedPreferences = {
      ...currentPreferences,
      analytics: true,
      marketing: true,
      preferences: true,
      timestamp: Date.now()
    }
    saveCookiePreferences(updatedPreferences)
    console.log('📊 All cookies forcefully enabled for existing user')
  }
}

// Export types and constants
export type { CookiePreferences }
