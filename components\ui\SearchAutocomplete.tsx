"use client"

import { useState, useEffect, useRef } from "react"
import { Search, Zap, Loader2, ArrowRight } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { motion, AnimatePresence } from "framer-motion"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { enhancedSearch, SearchResult } from "@/lib/search-service"
import logger from "@/lib/logger"

interface SearchAutocompleteProps {
  placeholder?: string
  onSearch?: (term: string) => void
  className?: string
  buttonText?: string
  maxSuggestions?: number
}

// Use the SearchResult interface from search-utils.ts

export default function SearchAutocomplete({
  placeholder = "Search for AI tools...",
  onSearch,
  className = "",
  buttonText = "Search",
  maxSuggestions = 5
}: SearchAutocompleteProps) {
  const [searchTerm, setSearchTerm] = useState("")
  const [suggestions, setSuggestions] = useState<SearchResult[]>([])
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [searchError, setSearchError] = useState<string | null>(null)
  const searchRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  // Fetch suggestions using enhanced search
  const fetchSuggestions = async (query: string) => {
    if (!query || query.length < 2) {
      setSuggestions([])
      setSearchError(null)
      return
    }

    setIsLoading(true)
    setSearchError(null)

    logger.info('Fetching search suggestions', { query, maxSuggestions }, 'SearchAutocomplete');

    try {
      // Use the enhanced search function with improved matching
      const startTime = Date.now();
      const results = await enhancedSearch(query, maxSuggestions);
      const duration = Date.now() - startTime;

      logger.info('Search suggestions fetched', {
        query,
        resultCount: results.length,
        duration
      }, 'SearchAutocomplete');

      if (results.length === 0) {
        // No results found, but don't show error
        setSuggestions([])
        logger.debug('No search suggestions found', { query }, 'SearchAutocomplete');
      } else {
        setSuggestions(results)
      }
    } catch (err) {
      logger.error("Error in fetchSuggestions", err, 'SearchAutocomplete');
      setSearchError("Failed to fetch suggestions. Please try again.")
      setSuggestions([])
    } finally {
      setIsLoading(false)
    }
  }

  // Debounce search input with optimized delay
  useEffect(() => {
    // Clear suggestions if search term is empty
    if (!searchTerm || searchTerm.length < 2) {
      setSuggestions([])
      setIsLoading(false)
      return () => {}
    }

    // For very short queries, use a slightly longer delay to avoid excessive API calls
    const debounceDelay = searchTerm.length <= 3 ? 300 : 200

    // Don't show loading immediately for better UX
    // Only show loading if the query takes longer than 100ms
    const loadingTimer = setTimeout(() => {
      setIsLoading(true)
    }, 100)

    // Actual search debounce
    const searchTimer = setTimeout(() => {
      try {
        fetchSuggestions(searchTerm)
      } catch (err) {
        console.error("Error in debounced search:", err)
        setSuggestions([])
        setIsLoading(false)
      }
    }, debounceDelay)

    return () => {
      clearTimeout(searchTimer)
      clearTimeout(loadingTimer)
    }
  }, [searchTerm])

  // Handle click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        setShowSuggestions(false)
      }
    }

    document.addEventListener("mousedown", handleClickOutside)
    return () => document.removeEventListener("mousedown", handleClickOutside)
  }, [])

  // Handle search submission
  const handleSearch = () => {
    setShowSuggestions(false)
    if (onSearch) {
      onSearch(searchTerm)
    } else if (searchTerm.trim()) {
      router.push(`/search?q=${encodeURIComponent(searchTerm.trim())}`)
    }
  }

  return (
    <div ref={searchRef} className={`relative w-full ${className}`}>
      <div className="relative flex w-full rounded-full border border-slate-200 dark:border-slate-700 bg-white/90 dark:bg-slate-800/90 backdrop-blur-md shadow-sm overflow-hidden group transition-all duration-300 hover:border-primary dark:hover:border-primary hover:shadow-md">
        {isLoading ? (
          <Loader2 className="absolute left-4 top-1/2 -translate-y-1/2 text-primary animate-spin" size={18} />
        ) : (
          <Search className="absolute left-4 top-1/2 -translate-y-1/2 text-slate-400 dark:text-slate-500" size={18} />
        )}
        <Input
          type="text"
          placeholder={placeholder}
          className="flex-1 pl-11 py-6 h-12 border-0 focus-visible:ring-0 bg-transparent text-base min-w-0"
          value={searchTerm}
          onChange={(e) => {
            setSearchTerm(e.target.value)
            setShowSuggestions(true)
          }}
          onFocus={() => setShowSuggestions(true)}
          onKeyDown={(e) => e.key === "Enter" && handleSearch()}
          autoComplete="off"
        />
        <Button
          className="px-5 py-2.5 h-12 relative overflow-hidden group rounded-l-none bg-primary hover:bg-primary/90 text-white font-medium shrink-0"
          onClick={handleSearch}
          variant="default"
        >
          <span className="relative z-10 flex items-center">
            {buttonText}
            <Zap size={16} className="ml-2 transition-all group-hover:rotate-12" />
          </span>
        </Button>
      </div>

      {/* Suggestions dropdown */}
      <AnimatePresence>
        {showSuggestions && (
          <motion.div
            initial={{ opacity: 0, y: -10 }}
            animate={{ opacity: 1, y: 0 }}
            exit={{ opacity: 0, y: -10 }}
            transition={{ duration: 0.2 }}
            className="absolute z-50 mt-2 w-full bg-white/95 dark:bg-slate-800/95 backdrop-blur-md rounded-xl shadow-lg border border-slate-200 dark:border-slate-700 overflow-hidden left-0 right-0"
          >
            {isLoading && suggestions.length === 0 ? (
              <div className="py-4 px-4 text-center">
                <Loader2 className="animate-spin h-5 w-5 mx-auto mb-2 text-primary" />
                <p className="text-sm text-muted-foreground">Searching...</p>
              </div>
            ) : searchError ? (
              <div className="py-4 px-4 text-center">
                <p className="text-sm text-red-500">{searchError}</p>
                <Button
                  variant="ghost"
                  className="mt-2 text-primary hover:text-primary hover:bg-primary/5"
                  onClick={() => handleSearch()}
                >
                  <Search size={14} className="mr-2" />
                  Try full search instead
                </Button>
              </div>
            ) : suggestions.length > 0 ? (
              <ul className="py-2 max-h-72 overflow-auto">
                {suggestions.map((suggestion) => (
                  <li key={suggestion.id} className="px-4 py-2.5 hover:bg-slate-100/80 dark:hover:bg-slate-700/80 cursor-pointer transition-colors duration-150">
                    <Link
                      href={suggestion.slug ? `/Tool/${suggestion.slug}` : `/search?q=${encodeURIComponent(suggestion.company_name)}`}
                      className="flex items-center gap-3"
                      onClick={() => setShowSuggestions(false)}
                    >
                      {suggestion.logo_url ? (
                        <img
                          src={suggestion.logo_url}
                          alt={suggestion.company_name}
                          className="w-8 h-8 rounded-md object-cover border border-slate-200 dark:border-slate-700"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.style.display = 'none'
                          }}
                        />
                      ) : (
                        <div className="w-8 h-8 rounded-md bg-primary/10 flex items-center justify-center text-primary text-sm font-medium border border-primary/20">
                          {suggestion.company_name.charAt(0)}
                        </div>
                      )}
                      <div className="flex-1 min-w-0">
                        <p className="text-sm font-medium text-foreground truncate">{suggestion.company_name}</p>
                        {suggestion.primary_task && (
                          <p className="text-xs text-muted-foreground truncate">{suggestion.primary_task}</p>
                        )}
                        {suggestion.relevance_score && suggestion.relevance_score > 50 && (
                          <span className="inline-flex items-center px-1.5 py-0.5 rounded-full text-xs font-medium bg-green-100 text-green-800 dark:bg-green-800/30 dark:text-green-400">
                            Exact Match
                          </span>
                        )}
                      </div>
                      {suggestion.is_fallback && (
                        <span className="text-xs text-slate-400 dark:text-slate-500">Suggested</span>
                      )}
                    </Link>
                  </li>
                ))}

                {/* Search for exact term button */}
                <li className="px-4 py-2.5 border-t border-slate-100 dark:border-slate-700">
                  <Button
                    variant="ghost"
                    className="w-full justify-between text-primary hover:text-primary hover:bg-primary/5"
                    onClick={() => {
                      handleSearch()
                    }}
                  >
                    <span className="flex items-center">
                      <Search size={14} className="mr-2" />
                      Search for "{searchTerm}"
                    </span>
                    <ArrowRight size={14} />
                  </Button>
                </li>
              </ul>
            ) : searchTerm.length >= 2 ? (
              <div className="py-4 px-4 text-center">
                <p className="text-sm text-muted-foreground">No results found</p>
                <p className="text-xs text-muted-foreground mt-1 mb-2">
                  Try different keywords or check your spelling
                </p>
                <Button
                  variant="ghost"
                  className="mt-2 text-primary hover:text-primary hover:bg-primary/5"
                  onClick={() => handleSearch()}
                >
                  <Search size={14} className="mr-2" />
                  Search for "{searchTerm}"
                </Button>
              </div>
            ) : null}
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  )
}
