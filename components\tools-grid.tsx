import { createServerClient } from "../lib/supabase/server"
import ToolCard from "./tool-card"
import { Button } from "./ui/button"
import Link from "next/link"
import { ChevronLeft, ChevronRight } from "lucide-react"

interface ToolsGridProps {
  query: string
  categories: string[]
  pricing: string[]
  features: string[]
  sort: string
  page: number
}

export default async function ToolsGrid({ query, categories, pricing, features, sort, page }: ToolsGridProps) {
  const pageSize = 12
  const supabase = createServerClient()

  // Build the query
  let supabaseQuery = supabase.from("tools").select("*", { count: "exact" })

  // Apply search filter
  if (query) {
    supabaseQuery = supabaseQuery.or(
      `company_name.ilike.%${query}%,short_description.ilike.%${query}%,full_description.ilike.%${query}%`,
    )
  }

  // Apply category filter
  if (categories.length > 0) {
    supabaseQuery = supabaseQuery.in("primary_task", categories)
  }

  // Apply pricing filter
  if (pricing.length > 0) {
    supabaseQuery = supabaseQuery.in("pricing", pricing)
  }

  // Apply features filter (assuming features are stored in an array column)
  if (features.length > 0) {
    features.forEach((feature) => {
      supabaseQuery = supabaseQuery.contains("features", [feature])
    })
  }

  // Apply sorting
  switch (sort) {
    case "newest":
      supabaseQuery = supabaseQuery.order("created_at", { ascending: false })
      break
    case "popular":
      supabaseQuery = supabaseQuery.order("click_count", { ascending: false })
      break
    case "top-rated":
      supabaseQuery = supabaseQuery.order("average_rating", { ascending: false })
      break
    default: // featured
      supabaseQuery = supabaseQuery
        .order("is_featured", { ascending: false })
        .order("click_count", { ascending: false })
  }

  // Apply pagination
  const from = (page - 1) * pageSize
  const to = from + pageSize - 1

  const { data: tools, error, count } = await supabaseQuery.range(from, to)

  if (error) {
    console.error("Error fetching tools:", error.message)
    return (
      <div className="text-center py-12 bg-white rounded-lg shadow-sm">
        <p className="text-slate-600">Error loading tools. Please try again later.</p>
      </div>
    )
  }

  if (!tools || tools.length === 0) {
    return (
      <div className="text-center py-12 bg-white rounded-lg shadow-sm">
        <h2 className="text-2xl font-bold mb-2">No results found</h2>
        <p className="text-slate-600 mb-4">
          Try adjusting your search or filters to find what you're looking for.
        </p>
        <Button asChild variant="outline">
          <Link href="/tools">Clear all filters</Link>
        </Button>
      </div>
    )
  }

  // Calculate pagination
  const totalPages = count ? Math.ceil(count / pageSize) : 1
  const showPagination = count && count > pageSize

  // Build the pagination URL
  const buildPaginationUrl = (targetPage: number) => {
    const params = new URLSearchParams()
    if (query) params.set("q", query)
    if (categories.length) params.set("categories", categories.join(","))
    if (pricing.length) params.set("pricing", pricing.join(","))
    if (features.length) params.set("features", features.join(","))
    if (sort !== "featured") params.set("sort", sort)
    params.set("page", targetPage.toString())
    return `/tools?${params.toString()}`
  }

  return (
    <div>
      <div className="mb-6 flex justify-between items-center">
        <h2 className="text-xl font-semibold text-gray-800">
          {count} {count === 1 ? "result" : "results"} found
        </h2>
        <p className="text-sm text-gray-500">
          Page {page} of {totalPages}
        </p>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {tools.map((tool) => (
          <div key={tool.id} className="h-full">
            <ToolCard tool={tool} />
          </div>
        ))}
      </div>

      {/* Pagination */}
      {showPagination && (
        <div className="mt-8 flex justify-center">
          <div className="flex items-center space-x-2">
            <Button variant="outline" size="icon" disabled={page <= 1} asChild={page > 1}>
              {page > 1 ? (
                <Link href={buildPaginationUrl(page - 1)}>
                  <ChevronLeft className="h-4 w-4" />
                </Link>
              ) : (
                <ChevronLeft className="h-4 w-4" />
              )}
            </Button>

            <div className="flex items-center space-x-1">
              {Array.from({ length: Math.min(5, totalPages) }, (_, i) => {
                // Show pages around the current page
                let pageNum
                if (totalPages <= 5) {
                  pageNum = i + 1
                } else if (page <= 3) {
                  pageNum = i + 1
                } else if (page >= totalPages - 2) {
                  pageNum = totalPages - 4 + i
                } else {
                  pageNum = page - 2 + i
                }

                return (
                  <Button
                    key={pageNum}
                    variant={pageNum === page ? "default" : "outline"}
                    size="sm"
                    asChild={pageNum !== page}
                  >
                    {pageNum !== page ? <Link href={buildPaginationUrl(pageNum)}>{pageNum}</Link> : pageNum}
                  </Button>
                )
              })}
            </div>

            <Button variant="outline" size="icon" disabled={page >= totalPages} asChild={page < totalPages}>
              {page < totalPages ? (
                <Link href={buildPaginationUrl(page + 1)}>
                  <ChevronRight className="h-4 w-4" />
                </Link>
              ) : (
                <ChevronRight className="h-4 w-4" />
              )}
            </Button>
          </div>
        </div>
      )}
    </div>
  )
}
