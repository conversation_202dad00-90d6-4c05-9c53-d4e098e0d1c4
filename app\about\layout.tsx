import { Metadata } from 'next'

export const metadata: Metadata = {
  title: 'About Us | AiAnyTool.com',
  description: 'Learn about AiAnyTool.com - your ultimate directory for discovering the best AI tools and software solutions. Join our community of innovators and creators building the future with AI.',
  keywords: [
    'AI tools directory',
    'about AiAnyTool',
    'AI tools community',
    'artificial intelligence directory',
    'AI tools mission',
    'AI tools vision',
    'AI innovation',
    'AI tools team'
  ],
  openGraph: {
    title: 'About AiAnyTool.com - Your Trusted AI Tools Directory',
    description: 'Discover our mission to make AI tools accessible to everyone. Join thousands of users who trust our curated directory of 1000+ AI tools.',
    url: 'https://aianytool.com/about',
    type: 'website',
    locale: 'en_US',
    siteName: 'AiAnyTool.com',
    images: [
      {
        url: 'https://aianytool.com/og-about.jpg',
        secureUrl: 'https://aianytool.com/og-about.jpg',
        width: 1200,
        height: 630,
        alt: 'About AiAnyTool.com - Your Trusted AI Tools Directory Team',
        type: 'image/jpeg',
      }
    ],
    countryName: 'United States',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'About AiAnyTool.com - Your Trusted AI Tools Directory',
    description: 'Discover our mission to make AI tools accessible to everyone. Join thousands of users who trust our curated directory.',
    images: ['https://aianytool.com/og-about.jpg'],
    creator: '@aianytool',
    site: '@aianytool',
  },
  alternates: {
    canonical: 'https://aianytool.com/about',
  },
}

export default function AboutLayout({
  children,
}: {
  children: React.ReactNode
}) {
  return children
}
