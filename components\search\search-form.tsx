"use client"

import { useState, useEffect } from "react"
import { useRout<PERSON> } from "next/navigation"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import UniversalSearch from "@/components/search/universal-search"
import { createBrowserClient } from "@/lib/supabase/client-utils"

interface Category {
  id: string
  name: string
}

interface SearchFormProps {
  initialQuery?: string
  initialCategory?: string
  showSuggestions?: boolean
  autoFocus?: boolean
}

export default function SearchForm({
  initialQuery = "",
  initialCategory = "",
  showSuggestions = true,
  autoFocus = false,
}: SearchFormProps) {
  const [category, setCategory] = useState(initialCategory)
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const router = useRouter()

  // Obtener categorías del servidor
  useEffect(() => {
    const fetchCategories = async () => {
      try {
        const supabase = createBrowserClient()

        // Obtener todas las herramientas para obtener categorías
        const { data: tools, error } = await supabase
          .from('tools')
          .select('primary_task')

        if (error) {
          return
        }

        // Obtener categorías únicas
        const uniqueCategories = Array.from(new Set(
          tools
            .map(tool => tool.primary_task)
            .filter(Boolean)
        )).map(name => ({
          id: name.toLowerCase().replace(/\s+/g, '-'),
          name
        })).sort((a, b) => a.name.localeCompare(b.name))

        setCategories(uniqueCategories)
      } catch (error) {
        setCategories([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchCategories()
  }, [])

  // Handle search with category
  const handleSearch = (query: string) => {
    const params = new URLSearchParams()

    if (query.trim()) {
      params.set("q", query.trim())
    }

    if (category && category !== "all") {
      params.set("category", category)
    }

    router.push(`/search?${params.toString()}`)
  }

  // Handle form submission
  const handleSubmit = (e: React.FormEvent) => {
    e.preventDefault()
    // This will be handled by the UnifiedSearch component
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-4">
      <div className="flex flex-col md:flex-row gap-4">
        <div className="flex-grow">
          <UniversalSearch
            mode="navigation"
            context="general"
            variant="hero"
            size="lg"
            placeholder="Search AI tools by name, category, or description..."
            showInstantResults={true}
            rounded="xl"
            autoFocus={autoFocus}
            onSearch={handleSearch}
            fullWidth={true}
            showInstantResults={true}
            maxResults={8}
            showSearchButton={true}
          />
        </div>

        <div className="w-full md:w-64">
          <Select value={category} onValueChange={setCategory}>
            <SelectTrigger className="h-12 rounded-xl border-2 border-border/50 focus:border-primary/50">
              <SelectValue placeholder="All Categories" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">All Categories</SelectItem>
              {categories.map((cat) => (
                <SelectItem key={cat.id} value={cat.id}>
                  {cat.name}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        <Button
          type="submit"
          className="md:w-auto h-12 px-8 rounded-xl bg-primary hover:bg-primary/90 text-primary-foreground font-medium theme-transition hover-button"
        >
          Search
        </Button>
      </div>
    </form>
  )
}
