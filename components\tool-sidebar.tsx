"use client"

import { useState } from "react"
import Link from "next/link"
import Image from "next/image"
import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { ExternalLink, Star, Calendar, Clock, ArrowRight } from "lucide-react"
import { format } from "date-fns"

interface Tool {
  id: number
  company_name: string
  short_description?: string
  logo_url?: string
  primary_task?: string
  pricing?: string
  created_at?: string
  updated_at?: string
  visit_website_url?: string
  slug?: string
}

interface RelatedTool {
  id: number
  company_name: string
  short_description?: string
  logo_url?: string
  primary_task?: string
  slug?: string
}

interface ToolSidebarProps {
  tool: Tool
  relatedTools?: RelatedTool[]
}

export default function ToolSidebar({ tool, relatedTools = [] }: ToolSidebarProps) {
  return (
    <div className="space-y-6">
      {/* Enhanced Quick Info */}
      <Card className="bg-gradient-to-br from-blue-50/80 via-purple-50/60 to-pink-50/80 dark:from-blue-950/30 dark:via-purple-950/20 dark:to-pink-950/30 border border-border/60 shadow-xl shadow-primary/5 dark:shadow-primary/10 backdrop-blur-sm rounded-2xl overflow-hidden">
        <CardHeader className="pb-4 bg-gradient-to-r from-primary/5 to-accent/5 border-b border-border/30">
          <CardTitle className="text-lg flex items-center gap-3">
            <div className="p-2.5 bg-primary/10 dark:bg-primary/20 rounded-xl">
              <span className="text-lg">ℹ️</span>
            </div>
            <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent font-bold">
              Quick Info
            </span>
          </CardTitle>
        </CardHeader>
        <CardContent className="p-6">
          <ul className="space-y-3 text-sm">
            {tool.primary_task && (
              <li className="flex justify-between items-center p-4 bg-background/60 dark:bg-card/60 rounded-xl border border-border/40 hover:border-primary/30 transition-all duration-200 group">
                <span className="text-muted-foreground flex items-center gap-2.5 group-hover:text-foreground transition-colors">
                  <span className="text-base">🏷️</span>
                  <span className="font-medium">Category</span>
                </span>
                <Badge variant="outline" className="font-medium bg-primary/5 border-primary/20 text-primary hover:bg-primary/10 transition-colors">
                  {tool.primary_task}
                </Badge>
              </li>
            )}

            {tool.pricing && (
              <li className="flex justify-between items-center p-4 bg-background/60 dark:bg-card/60 rounded-xl border border-border/40 hover:border-green-500/30 transition-all duration-200 group">
                <span className="text-muted-foreground flex items-center gap-2.5 group-hover:text-foreground transition-colors">
                  <span className="text-base">💰</span>
                  <span className="font-medium">Pricing</span>
                </span>
                <Badge variant="outline" className="font-medium bg-green-50 dark:bg-green-900/20 border-green-200 dark:border-green-800 text-green-700 dark:text-green-300 hover:bg-green-100 dark:hover:bg-green-900/30 transition-colors">
                  {tool.pricing}
                </Badge>
              </li>
            )}

            {tool.created_at && (
              <li className="flex justify-between items-center p-4 bg-background/60 dark:bg-card/60 rounded-xl border border-border/40 hover:border-blue-500/30 transition-all duration-200 group">
                <span className="text-muted-foreground flex items-center gap-2.5 group-hover:text-foreground transition-colors">
                  <Calendar className="h-4 w-4 text-blue-500" />
                  <span className="font-medium">Added</span>
                </span>
                <span className="font-medium text-foreground">
                  {format(new Date(tool.created_at), "MMM d, yyyy")}
                </span>
              </li>
            )}

            {tool.updated_at && (
              <li className="flex justify-between items-center p-4 bg-background/60 dark:bg-card/60 rounded-xl border border-border/40 hover:border-purple-500/30 transition-all duration-200 group">
                <span className="text-muted-foreground flex items-center gap-2.5 group-hover:text-foreground transition-colors">
                  <Clock className="h-4 w-4 text-purple-500" />
                  <span className="font-medium">Updated</span>
                </span>
                <span className="font-medium text-foreground">
                  {format(new Date(tool.updated_at), "MMM d, yyyy")}
                </span>
              </li>
            )}
          </ul>
        </CardContent>
        {tool.visit_website_url && (
          <CardFooter>
            <Button asChild className="w-full">
              <a
                href={tool.visit_website_url}
                target="_blank"
                rel="noopener noreferrer"
                className="flex items-center justify-center gap-2"
              >
                <span>Visit Website</span>
                <ExternalLink className="h-4 w-4" />
              </a>
            </Button>
          </CardFooter>
        )}
      </Card>

      {/* Enhanced Related Tools */}
      {relatedTools && relatedTools.length > 0 ? (
        <Card className="bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-pink-50/50 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20 border border-border/60 shadow-xl shadow-primary/5 dark:shadow-primary/10 backdrop-blur-sm rounded-2xl overflow-hidden">
          <CardHeader className="pb-4 bg-gradient-to-r from-primary/5 to-accent/5 border-b border-border/30">
            <CardTitle className="text-lg flex items-center gap-3">
              <div className="p-2.5 bg-primary/10 dark:bg-primary/20 rounded-xl">
                <span className="text-lg">🔗</span>
              </div>
              <span className="bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent font-bold">
                Similar Tools
              </span>
            </CardTitle>
            <CardDescription className="text-sm text-muted-foreground">
              Discover alternatives in {tool.primary_task || "this category"}
            </CardDescription>
          </CardHeader>
          <CardContent className="p-4">
            <div className="space-y-3">
              {relatedTools.slice(0, 4).map((relatedTool) => (
                <Link
                  key={relatedTool.id}
                  href={`/Tool/${relatedTool.slug || relatedTool.id}`}
                  className="group block p-3 bg-background/60 dark:bg-card/60 rounded-xl border border-border/40 hover:border-primary/30 hover:shadow-lg transition-all duration-300 hover:-translate-y-1"
                >
                  <div className="flex items-center gap-3">
                    <div className="h-10 w-10 rounded-lg border border-border/40 overflow-hidden flex-shrink-0 bg-background group-hover:border-primary/30 transition-all duration-300">
                      {relatedTool.logo_url ? (
                        <Image
                          src={relatedTool.logo_url}
                          alt={relatedTool.company_name}
                          width={40}
                          height={40}
                          className="h-full w-full object-cover group-hover:scale-110 transition-transform duration-300"
                        />
                      ) : (
                        <div className="h-full w-full flex items-center justify-center text-muted-foreground bg-gradient-to-br from-primary/10 to-accent/10 font-semibold text-sm">
                          {relatedTool.company_name.charAt(0)}
                        </div>
                      )}
                    </div>
                    <div className="flex-1 min-w-0">
                      <h4 className="font-medium text-foreground group-hover:text-primary transition-colors duration-200 truncate">
                        {relatedTool.company_name}
                      </h4>
                      {relatedTool.short_description && (
                        <p className="text-xs text-muted-foreground line-clamp-1 mt-0.5">
                          {relatedTool.short_description}
                        </p>
                      )}
                    </div>
                    <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary transition-colors duration-200" />
                  </div>
                </Link>
              ))}
            </div>
          </CardContent>
          <CardFooter className="pt-0">
            <Button variant="outline" size="sm" asChild className="w-full group">
              <Link href={`/tools?category=${encodeURIComponent(tool.primary_task || '')}`}>
                <span>View All Similar Tools</span>
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
          </CardFooter>
        </Card>
      ) : (
        <Card className="bg-gradient-to-br from-gray-50/50 via-slate-50/30 to-gray-50/50 dark:from-gray-950/20 dark:via-slate-950/10 dark:to-gray-950/20 border border-border/60 shadow-xl shadow-primary/5 dark:shadow-primary/10 backdrop-blur-sm rounded-2xl overflow-hidden">
          <CardContent className="p-6 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-primary/10 to-accent/10 rounded-full flex items-center justify-center">
              <span className="text-2xl">🔍</span>
            </div>
            <h4 className="font-semibold text-foreground mb-2">Similar Tools Coming Soon!</h4>
            <p className="text-sm text-muted-foreground mb-4">
              We're analyzing tools to find the best alternatives for you.
            </p>
            <div className="inline-flex items-center gap-2 px-3 py-1 bg-primary/10 text-primary rounded-full text-xs font-medium">
              <span className="w-1.5 h-1.5 bg-primary rounded-full animate-pulse"></span>
              Analysis in progress
            </div>
          </CardContent>
        </Card>
      )}

      {/* Share Card */}
      <Card>
        <CardHeader className="pb-2">
          <CardTitle className="text-lg">Share</CardTitle>
        </CardHeader>
        <CardContent>
          <div className="flex gap-2">
            <Button variant="outline" size="sm" className="flex-1" onClick={() => {
              navigator.clipboard.writeText(window.location.href);
              alert("Link copied to clipboard!");
            }}>
              Copy Link
            </Button>
            <Button variant="outline" size="sm" className="flex-1" onClick={() => {
              window.open(`https://twitter.com/intent/tweet?url=${encodeURIComponent(window.location.href)}&text=Check out ${tool.company_name} on AI Tools Directory`, '_blank');
            }}>
              Twitter
            </Button>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
