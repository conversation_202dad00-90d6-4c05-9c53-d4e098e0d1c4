import { createBrowserClient } from "@/lib/supabase/client-utils"

interface Tool {
  id: string
  name: string
  description: string
  website_url: string
  logo_url?: string
  primary_task: string
  pricing: string
  verified?: boolean
  featured?: boolean
  rating?: number
  user_count?: number
}

export async function getSimilarTools(
  query: string = "",
  categories: string[] = [],
  limit: number = 6
): Promise<Tool[]> {
  const supabase = createBrowserClient()
  
  try {
    let queryBuilder = supabase
      .from('tools')
      .select(`
        id,
        name,
        description,
        website_url,
        logo_url,
        primary_task,
        pricing,
        verified,
        featured,
        rating,
        user_count
      `)
      .eq('status', 'published')
      .order('featured', { ascending: false })
      .order('verified', { ascending: false })
      .order('rating', { ascending: false })
      .limit(limit)

    // If we have categories, prioritize tools from those categories
    if (categories.length > 0) {
      queryBuilder = queryBuilder.in('primary_task', categories)
    } else if (query.trim()) {
      // If no categories but we have a query, find tools with similar descriptions
      const keywords = extractKeywords(query)
      if (keywords.length > 0) {
        const searchPattern = keywords.join('|')
        queryBuilder = queryBuilder.or(`
          description.ilike.%${keywords[0]}%,
          primary_task.ilike.%${keywords[0]}%,
          tags.ilike.%${keywords[0]}%
        `)
      }
    }

    const { data, error } = await queryBuilder

    if (error) {
      console.error('Get similar tools error:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Get similar tools error:', error)
    return []
  }
}

export async function getPopularTools(limit: number = 8): Promise<Tool[]> {
  const supabase = createBrowserClient()
  
  try {
    const { data, error } = await supabase
      .from('tools')
      .select(`
        id,
        name,
        description,
        website_url,
        logo_url,
        primary_task,
        pricing,
        verified,
        featured,
        rating,
        user_count
      `)
      .eq('status', 'published')
      .order('featured', { ascending: false })
      .order('verified', { ascending: false })
      .order('user_count', { ascending: false })
      .order('rating', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Get popular tools error:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Get popular tools error:', error)
    return []
  }
}

export async function getTrendingTools(limit: number = 6): Promise<Tool[]> {
  const supabase = createBrowserClient()
  
  try {
    // Get tools that are recently added or updated and have good ratings
    const { data, error } = await supabase
      .from('tools')
      .select(`
        id,
        name,
        description,
        website_url,
        logo_url,
        primary_task,
        pricing,
        verified,
        featured,
        rating,
        user_count,
        created_at
      `)
      .eq('status', 'published')
      .gte('rating', 4.0)
      .order('created_at', { ascending: false })
      .order('rating', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Get trending tools error:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Get trending tools error:', error)
    return []
  }
}

export async function getToolsByCategory(
  category: string,
  limit: number = 6
): Promise<Tool[]> {
  const supabase = createBrowserClient()
  
  try {
    const { data, error } = await supabase
      .from('tools')
      .select(`
        id,
        name,
        description,
        website_url,
        logo_url,
        primary_task,
        pricing,
        verified,
        featured,
        rating,
        user_count
      `)
      .eq('status', 'published')
      .eq('primary_task', category)
      .order('featured', { ascending: false })
      .order('verified', { ascending: false })
      .order('rating', { ascending: false })
      .limit(limit)

    if (error) {
      console.error('Get tools by category error:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Get tools by category error:', error)
    return []
  }
}

export async function getAlternativeSearchSuggestions(
  query: string
): Promise<{
  relatedQueries: string[]
  relatedCategories: string[]
  popularInCategory: Tool[]
}> {
  const supabase = createBrowserClient()
  
  try {
    const keywords = extractKeywords(query)
    const relatedQueries: string[] = []
    const relatedCategories: string[] = []
    let popularInCategory: Tool[] = []

    if (keywords.length > 0) {
      // Get related tool names and categories
      const { data, error } = await supabase
        .from('tools')
        .select('name, primary_task, description')
        .eq('status', 'published')
        .or(`
          name.ilike.%${keywords[0]}%,
          description.ilike.%${keywords[0]}%,
          primary_task.ilike.%${keywords[0]}%
        `)
        .limit(20)

      if (!error && data) {
        const categories = new Set<string>()
        const queries = new Set<string>()

        data.forEach(tool => {
          // Add related categories
          if (tool.primary_task) {
            categories.add(tool.primary_task)
          }

          // Add related tool names as query suggestions
          if (tool.name.toLowerCase().includes(keywords[0].toLowerCase())) {
            queries.add(tool.name)
          }

          // Extract related terms from descriptions
          const descWords = tool.description.toLowerCase().split(/\s+/)
          keywords.forEach(keyword => {
            descWords.forEach(word => {
              if (word.includes(keyword.toLowerCase()) && word.length > 3) {
                queries.add(word)
              }
            })
          })
        })

        relatedQueries.push(...Array.from(queries).slice(0, 5))
        relatedCategories.push(...Array.from(categories).slice(0, 5))

        // Get popular tools from the most relevant category
        if (relatedCategories.length > 0) {
          popularInCategory = await getToolsByCategory(relatedCategories[0], 4)
        }
      }
    }

    return {
      relatedQueries,
      relatedCategories,
      popularInCategory
    }
  } catch (error) {
    console.error('Get alternative suggestions error:', error)
    return {
      relatedQueries: [],
      relatedCategories: [],
      popularInCategory: []
    }
  }
}

// Helper function to extract keywords from query
function extractKeywords(query: string): string[] {
  if (!query.trim()) return []
  
  // Remove common stop words and extract meaningful keywords
  const stopWords = new Set([
    'the', 'a', 'an', 'and', 'or', 'but', 'in', 'on', 'at', 'to', 'for', 
    'of', 'with', 'by', 'is', 'are', 'was', 'were', 'be', 'been', 'have', 
    'has', 'had', 'do', 'does', 'did', 'will', 'would', 'could', 'should',
    'ai', 'tool', 'tools', 'app', 'application', 'software', 'platform'
  ])
  
  return query
    .toLowerCase()
    .split(/\s+/)
    .filter(word => word.length > 2 && !stopWords.has(word))
    .slice(0, 3) // Take first 3 meaningful keywords
}

export async function getSearchHistory(): Promise<string[]> {
  // This could be implemented with local storage or user preferences
  // For now, return some common search terms
  return [
    "ChatGPT",
    "Image generation",
    "Text to speech",
    "Code assistant",
    "Writing assistant",
    "Video editing",
    "Data analysis",
    "Translation"
  ]
}

export async function getRecommendedTools(
  userInterests: string[] = [],
  limit: number = 6
): Promise<Tool[]> {
  const supabase = createBrowserClient()
  
  try {
    let queryBuilder = supabase
      .from('tools')
      .select(`
        id,
        name,
        description,
        website_url,
        logo_url,
        primary_task,
        pricing,
        verified,
        featured,
        rating,
        user_count
      `)
      .eq('status', 'published')
      .order('featured', { ascending: false })
      .order('verified', { ascending: false })
      .order('rating', { ascending: false })
      .limit(limit)

    // If user has interests, prioritize those categories
    if (userInterests.length > 0) {
      queryBuilder = queryBuilder.in('primary_task', userInterests)
    }

    const { data, error } = await queryBuilder

    if (error) {
      console.error('Get recommended tools error:', error)
      return []
    }

    return data || []
  } catch (error) {
    console.error('Get recommended tools error:', error)
    return []
  }
}
