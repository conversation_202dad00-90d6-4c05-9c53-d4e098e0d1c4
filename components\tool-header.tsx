"use client"

import Image from "next/image"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import { ExternalLink, Share2, CheckCircle } from "lucide-react"
import FavoriteButton from "@/components/favorite-button"
import { toast } from "sonner"
import { useState } from "react"
import { createBrowserClient } from "@/lib/supabase/client-utils"

interface ToolHeaderProps {
  tool: {
    id: number
    company_name?: string
    short_description?: string
    logo_url?: string
    visit_website_url?: string
    is_verified?: boolean
    primary_task?: string
    pricing?: string
    is_featured?: boolean
    click_count?: number
    average_rating?: number
    review_count?: number
    created_at?: string
    featured_image_url?: string
  }
}

export default function ToolHeader({ tool }: ToolHeaderProps) {
  const [isLoading, setIsLoading] = useState(false)

  const handleShare = async () => {
    if (navigator.share) {
      try {
        await navigator.share({
          title: tool.company_name,
          text: tool.short_description,
          url: window.location.href,
        })
      } catch (error) {
        console.error("Error sharing:", error)
      }
    } else {
      // Fallback to copying to clipboard
      navigator.clipboard.writeText(window.location.href)
      toast.success("Link copied to clipboard")
    }
  }

  const handleVisitWebsite = async () => {
    // Track outbound click
    try {
      setIsLoading(true)
      const supabase = createBrowserClient()
      await supabase
        .from('tools')
        .update({ click_count: (tool.click_count || 0) + 1 })
        .eq('id', tool.id)
    } catch (error) {
      console.error("Error tracking click:", error)
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <div className="relative">
      {/* Enhanced Glass Card */}
      <div className="relative bg-background/95 dark:bg-card/95 backdrop-blur-xl rounded-3xl border border-border/60 shadow-2xl shadow-primary/5 dark:shadow-primary/10 p-6 sm:p-8 lg:p-10">
        {/* Subtle gradient overlay */}
        <div className="absolute inset-0 bg-gradient-to-br from-primary/5 via-transparent to-accent/5 rounded-3xl pointer-events-none" />

        <div className="relative flex flex-col lg:flex-row gap-6 lg:gap-8">
          {/* Logo and Basic Info */}
          <div className="flex flex-col sm:flex-row gap-6 lg:flex-1">
            {/* Enhanced Logo */}
            <div className="relative group flex-shrink-0">
              {/* Animated glow effect */}
              <div className="absolute inset-0 bg-gradient-to-br from-blue-400 via-purple-500 to-pink-500 rounded-3xl blur-xl opacity-20 group-hover:opacity-40 transition-all duration-500 animate-pulse" />
              <div className="relative w-20 h-20 sm:w-24 sm:h-24 lg:w-28 lg:h-28 rounded-3xl overflow-hidden bg-white dark:bg-card shadow-xl border-2 border-white dark:border-border/50 group-hover:scale-105 transition-all duration-300">
                {tool.logo_url ? (
                  <Image
                    src={tool.logo_url}
                    alt={tool.company_name || "Tool logo"}
                    fill
                    className="object-cover group-hover:scale-110 transition-transform duration-300"
                    sizes="(max-width: 640px) 80px, (max-width: 768px) 96px, 112px"
                    priority
                  />
                ) : (
                  <div className="w-full h-full bg-gradient-to-br from-blue-500 via-purple-600 to-pink-600 flex items-center justify-center group-hover:from-blue-600 group-hover:via-purple-700 group-hover:to-pink-700 transition-all duration-300">
                    <span className="text-white font-bold text-xl sm:text-2xl lg:text-3xl">
                      {tool.company_name?.charAt(0)?.toUpperCase() || "T"}
                    </span>
                  </div>
                )}
              </div>
            </div>

            {/* Enhanced Title and Description */}
            <div className="flex-1 space-y-4 min-w-0">
              <div className="space-y-3">
                <div className="flex items-start gap-3 flex-wrap">
                  <h1 className="text-2xl sm:text-3xl lg:text-4xl xl:text-5xl font-bold bg-gradient-to-r from-gray-900 via-blue-800 to-purple-800 dark:from-white dark:via-blue-200 dark:to-purple-200 bg-clip-text text-transparent leading-tight">
                    {tool.company_name}
                  </h1>

                  {/* Enhanced Verification Badge */}
                  {tool.is_verified && (
                    <div className="flex items-center gap-1.5 bg-gradient-to-r from-blue-100 to-blue-50 dark:from-blue-900/40 dark:to-blue-800/30 text-blue-700 dark:text-blue-300 px-3 py-1.5 rounded-full text-sm font-medium border border-blue-200 dark:border-blue-700/50 shadow-sm">
                      <CheckCircle className="h-4 w-4" />
                      <span>Verified</span>
                    </div>
                  )}

                  {/* Featured Badge */}
                  {tool.is_featured && (
                    <div className="flex items-center gap-1.5 bg-gradient-to-r from-amber-100 to-yellow-50 dark:from-amber-900/40 dark:to-yellow-800/30 text-amber-700 dark:text-amber-300 px-3 py-1.5 rounded-full text-sm font-medium border border-amber-200 dark:border-amber-700/50 shadow-sm">
                      <span className="text-amber-500">⭐</span>
                      <span>Featured</span>
                    </div>
                  )}
                </div>

                {/* Category and Pricing */}
                <div className="flex items-center gap-3 flex-wrap">
                  {tool.primary_task && (
                    <Badge variant="outline" className="bg-gray-50 dark:bg-gray-800/50 text-gray-700 dark:text-gray-300 border-gray-200 dark:border-gray-700">
                      {tool.primary_task}
                    </Badge>
                  )}

                  {tool.pricing && (
                    <Badge variant="outline" className="bg-emerald-50 dark:bg-emerald-900/30 text-emerald-700 dark:text-emerald-300 border-emerald-200 dark:border-emerald-700">
                      {tool.pricing}
                    </Badge>
                  )}

                  {tool.is_featured && (
                    <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white shadow-lg">
                      ✨ Featured
                    </Badge>
                  )}
                </div>
              </div>

              {/* Description */}
              {tool.short_description && (
                <p className="text-lg text-muted-foreground leading-relaxed max-w-2xl">
                  {tool.short_description}
                </p>
              )}

              {/* Enhanced Description */}
              <div className="text-base sm:text-lg text-muted-foreground leading-relaxed">
                {tool.short_description && (
                  <p className="line-clamp-3">{tool.short_description}</p>
                )}
              </div>
            </div>
          </div>

          {/* Enhanced Action Buttons */}
          <div className="flex flex-col gap-4 lg:w-72 xl:w-80">
            {/* Primary Visit Website Button */}
            {tool.visit_website_url && (
              <Button
                asChild
                size="lg"
                className="relative overflow-hidden bg-gradient-to-r from-blue-600 via-purple-600 to-blue-700 hover:from-blue-700 hover:via-purple-700 hover:to-blue-800 text-white shadow-xl hover:shadow-2xl transition-all duration-300 group h-12 text-base font-semibold"
                disabled={isLoading}
                onClick={handleVisitWebsite}
              >
                <a href={tool.visit_website_url} target="_blank" rel="noopener noreferrer" className="flex items-center justify-center w-full">
                  {/* Animated background */}
                  <div className="absolute inset-0 bg-gradient-to-r from-white/0 via-white/20 to-white/0 -translate-x-full group-hover:translate-x-full transition-transform duration-700" />

                  <span className="relative">Visit Website</span>
                  <ExternalLink className="relative h-5 w-5 ml-2 group-hover:translate-x-1 group-hover:scale-110 transition-all duration-200" />
                </a>
              </Button>
            )}

            {/* Enhanced Secondary Actions */}
            <div className="flex gap-3">
              <div className="flex-1">
                <FavoriteButton toolId={tool.id} />
              </div>

              <Button
                variant="outline"
                size="lg"
                onClick={handleShare}
                className="group hover:bg-primary/5 hover:border-primary/30 dark:hover:bg-primary/10 transition-all duration-200 h-12 px-4 border-border/60"
              >
                <Share2 className="h-5 w-5 group-hover:scale-110 transition-transform duration-200" />
                <span className="sr-only">Share</span>
              </Button>
            </div>

            {/* Enhanced Stats Display */}
            <div className="flex items-center justify-between p-4 bg-secondary/30 dark:bg-secondary/20 rounded-2xl border border-border/40">
              {tool.average_rating && (
                <div className="flex items-center gap-2">
                  <div className="flex">
                    {Array.from({ length: 5 }).map((_, i) => (
                      <div
                        key={i}
                        className={`w-4 h-4 ${
                          i < Math.floor(tool.average_rating || 0)
                            ? "text-yellow-400 fill-current"
                            : "text-gray-300 dark:text-gray-600"
                        }`}
                      >
                        ⭐
                      </div>
                    ))}
                  </div>
                  <span className="font-semibold text-foreground text-sm">
                    {tool.average_rating}
                    {tool.review_count && (
                      <span className="text-muted-foreground ml-1">
                        ({tool.review_count} review{tool.review_count !== 1 ? 's' : ''})
                      </span>
                    )}
                  </span>
                </div>
              )}

              {tool.click_count && (
                <div className="flex items-center gap-1 text-sm">
                  <span className="font-semibold text-foreground">{tool.click_count.toLocaleString()}</span>
                  <span className="text-muted-foreground">visits</span>
                </div>
              )}
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
