# Supabase Configuration Guide

## إعدادات مهمة لمنع التسجيل المكرر

### 1. إعدادات Authentication في Supabase Dashboard

#### أ) Site URL Configuration
- اذهب إلى: **Authentication → Settings**
- **Site URL**: `https://aianytool.com`
- **Redirect URLs**: 
  ```
  https://aianytool.com/auth/callback
  https://aianytool.com/**
  ```

#### ب) Email Settings
- **Enable email confirmations**: ✅ مفعل
- **Secure email change**: ✅ مفعل  
- **Double confirm email changes**: ✅ مفعل

#### ج) Security Settings
- **Enable phone confirmations**: حسب الحاجة
- **Enable manual linking**: ❌ غير مفعل (لمنع ربط حسابات متعددة)

### 2. SMTP Configuration (اختياري لتخصيص الرسائل)

#### استخدام Gmail SMTP:
```
SMTP Host: smtp.gmail.com
SMTP Port: 587
SMTP User: <EMAIL>
SMTP Pass: your-app-password
Sender Name: AI Any Tool
Sender Email: <EMAIL>
```

#### استخدام خدمة احترافية (موصى به للإنتاج):
- **SendGrid**: أسهل في الإعداد
- **Mailgun**: مرونة أكثر
- **AWS SES**: أرخص للكميات الكبيرة

### 3. Database Policies (RLS)

تأكد من وجود هذه السياسات في قاعدة البيانات:

```sql
-- منع إنشاء ملفات تعريف متعددة لنفس المستخدم
CREATE POLICY "Users can only create one profile" ON profiles
FOR INSERT WITH CHECK (auth.uid() = user_id);

-- منع تعديل ملفات التعريف من قبل مستخدمين آخرين
CREATE POLICY "Users can only update own profile" ON profiles
FOR UPDATE USING (auth.uid() = user_id);
```

### 4. Environment Variables

تأكد من وجود هذه المتغيرات في `.env.local`:

```env
NEXT_PUBLIC_SUPABASE_URL=https://your-project.supabase.co
NEXT_PUBLIC_SUPABASE_ANON_KEY=your-anon-key
NEXT_PUBLIC_SITE_URL=https://aianytool.com
NEXT_PUBLIC_AUTH_PROVIDER=supabase
```

### 5. معالجة الأخطاء المحسنة

الكود الحالي يتضمن:

- ✅ **تحقق من وجود المستخدم مسبقاً**
- ✅ **رسائل خطأ واضحة ومفيدة**
- ✅ **اقتراحات للمستخدم عند الخطأ**
- ✅ **تنظيف النموذج بعد التسجيل الناجح**
- ✅ **معالجة حالات البريد غير المؤكد**

### 6. اختبار النظام

للتأكد من عمل النظام بشكل صحيح:

1. **اختبار التسجيل العادي**:
   - سجل بإيميل جديد
   - تحقق من وصول رسالة التأكيد
   - أكد البريد الإلكتروني

2. **اختبار منع التسجيل المكرر**:
   - حاول التسجيل بنفس الإيميل مرة أخرى
   - يجب أن تظهر رسالة: "This email is already registered"
   - يجب أن تظهر اقتراح: "Try signing in instead"

3. **اختبار البريد غير المؤكد**:
   - سجل بإيميل جديد ولا تؤكده
   - حاول التسجيل بنفس الإيميل مرة أخرى
   - يجب أن تظهر رسالة مناسبة عن البريد غير المؤكد

### 7. مراقبة الأخطاء

راقب هذه الأخطاء في Supabase Dashboard:

- **Authentication → Logs**
- **Database → Logs** 
- **Edge Functions → Logs** (إذا كنت تستخدمها)

### 8. نصائح إضافية

- **استخدم Rate Limiting**: لمنع محاولات التسجيل المتكررة
- **فعل Captcha**: للحماية من البوتات (اختياري)
- **راقب الإحصائيات**: عدد المستخدمين الجدد يومياً
- **نسخ احتياطية منتظمة**: لقاعدة البيانات

## استكشاف الأخطاء

### المشكلة: لا تزال تسمح بالتسجيل المكرر
**الحل**:
1. تحقق من إعدادات Authentication في Supabase
2. تأكد من تحديث الكود وإعادة النشر
3. امسح cache المتصفح
4. استخدم صفحة التشخيص: `/debug/email` للاختبار

### المشكلة: لا تصل رسائل البريد الإلكتروني
**الحلول المحتملة**:

#### 1. تحقق من إعدادات SMTP في Supabase:
- اذهب إلى Authentication → Settings → SMTP Settings
- تأكد من أن "Enable custom SMTP" مفعل
- تحقق من صحة إعدادات Brevo:
  ```
  Host: smtp-relay.brevo.com
  Port: 587
  Username: <EMAIL>
  Password: [كلمة المرور الصحيحة]
  ```

#### 2. تحقق من إعدادات Brevo:
- تأكد من أن حسابك في Brevo مفعل
- تحقق من حدود الإرسال اليومية
- تأكد من أن البريد المرسل (<EMAIL>) مؤكد في Brevo

#### 3. تحقق من إعدادات النطاق:
- تأكد من إعداد SPF records لنطاقك
- أضف DKIM records إذا كانت متوفرة
- تحقق من DMARC policy

#### 4. اختبر الإعدادات:
- استخدم صفحة `/debug/email` لاختبار الإعدادات
- تحقق من logs في Supabase Dashboard
- اختبر إرسال بريد من Brevo مباشرة

### المشكلة: رسائل البريد تأتي من "Supabase"
**الحل**:
1. اذهب إلى Authentication → Settings → SMTP Settings
2. فعل Custom SMTP
3. أدخل إعدادات خدمة البريد الخاصة بك
4. تأكد من ملء حقل "Sender Name" بـ "AiAnyTool"

### المشكلة: روابط التأكيد تؤدي إلى localhost
**الحل**:
1. تحقق من Site URL في Supabase Dashboard
2. تأكد من أن NEXT_PUBLIC_SITE_URL صحيح
3. أعد نشر الموقع

### المشكلة: خطأ "User already registered" لا يظهر
**الحل**:
1. تحقق من أن الكود محدث
2. امسح cache المتصفح
3. تحقق من console للأخطاء
4. استخدم صفحة التشخيص للاختبار

### أدوات التشخيص:

#### صفحة التشخيص: `/debug/email`
- اختبار إعدادات SMTP
- اختبار عملية التسجيل
- عرض حالة الإعدادات
- سجلات مفصلة للأخطاء

#### فحص Console المتصفح:
```javascript
// في console المتصفح، شغل:
checkSupabaseConfig()
```

#### فحص Supabase Logs:
1. اذهب إلى Supabase Dashboard
2. اختر مشروعك
3. اذهب إلى Logs → Authentication
4. ابحث عن أخطاء SMTP أو التسجيل
