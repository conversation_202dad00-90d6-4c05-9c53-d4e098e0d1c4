/**
 * Ser<PERSON><PERSON> de búsqueda optimizado para Next.js
 * Basado en el proyecto original pero con mejoras para rendimiento y fiabilidad
 */

import { createBrowserClient } from '@/lib/supabase/client-utils';
import { createServerClient } from '@/lib/supabase/server';
import { Database } from '@/types/supabase';
import { QUERY_TIMEOUTS, withQueryTimeout } from '@/lib/query-timeout-config';

// Definición de tipos para los resultados de búsqueda
export interface SearchResult {
  id: string | number;
  company_name: string;
  slug?: string | null;
  logo_url?: string | null;
  primary_task?: string | null;
  is_featured?: boolean;
  is_verified?: boolean;
  is_new?: boolean;
  pricing?: string;
  rating?: number;
  short_description?: string | null;
  full_description?: string | null;
  click_count?: number;
  created_at?: string;
  updated_at?: string;
  visit_website_url?: string | null;
  detail_url?: string | null;
  categories?: string[];
  is_fallback?: boolean;
  result_type?: 'tool' | 'category' | 'subcategory' | 'company' | 'feature' | 'article' | 'blog';
  category_name?: string;
  category_description?: string;
  subcategory_name?: string;
  parent_category?: string;
  feature_name?: string;
  article_title?: string;
  article_excerpt?: string;
}

// Opciones de búsqueda
export interface SearchOptions {
  query?: string;
  category?: string;
  pricing?: string;
  sortBy?: 'newest' | 'popular' | 'top-rated' | 'featured';
  features?: string[];
  limit?: number;
}

// Herramientas populares para resultados de respaldo
const POPULAR_TOOLS = [
  "ChatGPT", "Midjourney", "DALL-E", "Stable Diffusion", "Claude",
  "Anthropic", "Jasper", "Synthesia", "RunwayML", "Hugging Face",
  "Notion AI", "Copy.ai", "Otter.ai", "Replicate", "Runway", "Gemini",
  "Bard", "Bing AI", "Perplexity", "Leonardo AI", "Poe", "Kaiber",
  "Firefly", "Canva AI", "Grammarly", "Caktus AI", "Writesonic"
];

// Caché de búsqueda para mejorar el rendimiento
const searchCache = new Map<string, { data: SearchResult[], timestamp: number }>();
const CACHE_TTL = 2 * 60 * 1000; // 2 minutos - reducido para mejor rendimiento

/**
 * Normaliza una consulta de búsqueda
 */
function normalizeQuery(query: string): string {
  return query
    .trim()
    .toLowerCase()
    .replace(/[^\w\s-]/g, ' ') // Keep hyphens for tool names like "GPT-4"
    .replace(/\s+/g, ' ');
}

/**
 * Create multiple search variations for better matching
 */
function createSearchVariations(query: string): string[] {
  const normalized = normalizeQuery(query);
  const variations = [
    normalized, // Original normalized
    normalized.replace(/\s+/g, ''), // No spaces
    normalized.replace(/\s+/g, '-'), // Spaces to hyphens
    normalized.replace(/-/g, ' '), // Hyphens to spaces
    normalized.replace(/-/g, ''), // No hyphens
  ];

  // Add common variations for popular tools
  const commonVariations: { [key: string]: string[] } = {
    'chatgpt': ['chat gpt', 'gpt', 'openai'],
    'gpt': ['chatgpt', 'chat gpt', 'openai'],
    'claude': ['anthropic'],
    'midjourney': ['mid journey'],
    'dall e': ['dalle', 'dall-e'],
    'dalle': ['dall e', 'dall-e'],
  };

  const lowerQuery = normalized.toLowerCase();
  if (commonVariations[lowerQuery]) {
    variations.push(...commonVariations[lowerQuery]);
  }

  // Remove duplicates
  return [...new Set(variations)].filter(v => v.length > 0);
}

/**
 * Crea una clave para el caché
 */
function createCacheKey(options: SearchOptions): string {
  const { query = '', category = '', pricing = '', sortBy = 'featured', features = [], limit = 50 } = options;
  return `${normalizeQuery(query)}:${category}:${pricing}:${sortBy}:${features.join(',')}:${limit}`;
}

/**
 * Obtiene resultados de respaldo cuando no hay coincidencias
 */
function getFallbackResults(query: string, limit: number): SearchResult[] {
  const normalizedQuery = normalizeQuery(query);

  return POPULAR_TOOLS
    .filter(tool =>
      normalizeQuery(tool).includes(normalizedQuery) ||
      normalizedQuery.includes(normalizeQuery(tool))
    )
    .slice(0, limit)
    .map((name, index) => ({
      id: `fallback-${index}`,
      company_name: name,
      slug: null,
      logo_url: null,
      primary_task: null,
      is_fallback: true
    }));
}

/**
 * Búsqueda principal - funciona tanto en el servidor como en el cliente
 */
export async function search(options: SearchOptions = {}): Promise<{ data: SearchResult[]; error: any }> {
  const {
    query = '',
    category = '',
    pricing = '',
    sortBy = 'featured',
    features = [],
    limit = 50
  } = options;

  // Si no hay criterios de búsqueda, devolver lista vacía
  if (!query && !category && !pricing && features.length === 0) {
    return { data: [], error: null };
  }

  // Verificar caché primero
  const cacheKey = createCacheKey(options);
  const cachedResult = searchCache.get(cacheKey);

  if (cachedResult && (Date.now() - cachedResult.timestamp) < CACHE_TTL) {
    return { data: cachedResult.data, error: null };
  }

  try {
    // Obtener cliente Supabase
    const client = typeof window !== 'undefined'
      ? createBrowserClient()
      : await createServerClient();

    if (!client) {
      throw new Error('Failed to create Supabase client');
    }

    // Iniciar consulta
    let supabaseQuery = client.from('tools').select('*');

    // Aplicar filtros
    if (query) {
      // Buscar en company_name, short_description y full_description
      supabaseQuery = supabaseQuery.or(
        `company_name.ilike.%${query}%,short_description.ilike.%${query}%,full_description.ilike.%${query}%,primary_task.ilike.%${query}%`
      );
    }

    if (category) {
      const isSlug = category.includes('-');

      if (isSlug) {
        const formattedCategory = category
          .split('-')
          .map(word => word.charAt(0).toUpperCase() + word.slice(1))
          .join(' ');

        supabaseQuery = supabaseQuery.eq('primary_task', formattedCategory);
      } else {
        supabaseQuery = supabaseQuery.eq('primary_task', category);
      }
    }

    if (pricing) {
      supabaseQuery = supabaseQuery.eq('pricing', pricing);
    }

    if (features && features.length > 0) {
      // Aplicar filtros de características
      features.forEach(feature => {
        if (feature) {
          const keyword = feature.replace(/-/g, ' ');
          supabaseQuery = supabaseQuery.or(
            `short_description.ilike.%${keyword}%`
          );
        }
      });
    }

    // Aplicar ordenamiento
    switch (sortBy) {
      case 'newest':
        supabaseQuery = supabaseQuery.order('created_at', { ascending: false });
        break;
      case 'popular':
        supabaseQuery = supabaseQuery.order('click_count', { ascending: false });
        break;
      case 'top-rated':
        supabaseQuery = supabaseQuery
          .order('is_featured', { ascending: false })
          .order('click_count', { ascending: false });
        break;
      case 'featured':
      default:
        supabaseQuery = supabaseQuery
          .order('is_featured', { ascending: false })
          .order('is_verified', { ascending: false })
          .order('company_name', { ascending: true });
    }

    // Aplicar límite
    supabaseQuery = supabaseQuery.limit(limit);

    // Ejecutar consulta con timeout
    const { data, error } = await withQueryTimeout(supabaseQuery, QUERY_TIMEOUTS.SEARCH);

    if (error) {
      console.error('Search error:', error);

      // Si hay error y hay consulta, devolver resultados de respaldo
      if (query) {
        const fallbackResults = getFallbackResults(query, limit);
        return { data: fallbackResults, error };
      }

      return { data: [], error };
    }

    // Si no hay resultados y hay consulta, devolver resultados de respaldo
    if ((!data || data.length === 0) && query) {
      const fallbackResults = getFallbackResults(query, limit);

      // Guardar en caché
      searchCache.set(cacheKey, {
        data: fallbackResults,
        timestamp: Date.now()
      });

      return { data: fallbackResults, error: null };
    }

    // Guardar en caché
    searchCache.set(cacheKey, {
      data: data || [],
      timestamp: Date.now()
    });

    return { data: data || [], error: null };
  } catch (err: any) {
    console.error('Exception in search:', err);

    // If it's a timeout error, provide user-friendly message
    if (err.message?.includes('timeout')) {
      return {
        data: [],
        error: new Error('Search is taking longer than expected. Please try a more specific search term.')
      };
    }

    // En caso de excepción, si hay consulta, devolver resultados de respaldo
    if (query) {
      const fallbackResults = getFallbackResults(query, limit);
      return { data: fallbackResults, error: err };
    }

    return { data: [], error: err };
  }
}

/**
 * Búsqueda en el servidor
 */
export async function serverSearch(
  query: string = '',
  category: string = '',
  limit: number = 50
): Promise<{ data: SearchResult[]; error: any }> {
  return search({ query, category, limit });
}

/**
 * Búsqueda en el cliente
 */
export async function clientSearch(
  query: string = '',
  category: string = '',
  limit: number = 10
): Promise<{ data: SearchResult[]; error: any }> {
  return search({ query, category, limit });
}

/**
 * Búsqueda para sugerencias (optimizada para velocidad)
 */
export async function suggestionsSearch(
  query: string = '',
  limit: number = 5
): Promise<{ data: SearchResult[]; error: any }> {
  return search({ query, limit, sortBy: 'featured' });
}

/**
 * Optimized quick search for search results page - simplified and faster
 */
export async function quickSearch(
  query: string = '',
  limit: number = 24,
  filters?: {
    category?: string
    pricing?: string
    features?: string[]
    sortBy?: string
  }
): Promise<{ data: SearchResult[]; error: any }> {
  if (!query || query.length < 2) {
    return { data: [], error: null };
  }

  // Check cache first for better performance
  const cacheKey = `quick_${query}_${JSON.stringify(filters)}_${limit}`;
  const cachedResult = searchCache.get(cacheKey);

  if (cachedResult && (Date.now() - cachedResult.timestamp) < CACHE_TTL) {
    return { data: cachedResult.data, error: null };
  }

  try {
    const client = typeof window !== 'undefined'
      ? createBrowserClient()
      : await createServerClient();

    if (!client) {
      throw new Error('Failed to create Supabase client');
    }

    // Simplified query with essential fields only
    let supabaseQuery = client
      .from('tools')
      .select(`
        id,
        company_name,
        slug,
        logo_url,
        primary_task,
        is_verified,
        is_featured,
        pricing,
        short_description,
        click_count,
        visit_website_url,
        full_description
      `)

    // Apply search term - simplified for better performance
    const searchTerm = query.trim();
    supabaseQuery = supabaseQuery.or(
      `company_name.ilike.%${searchTerm}%,short_description.ilike.%${searchTerm}%,primary_task.ilike.%${searchTerm}%`
    );

    // Apply filters
    if (filters?.category && filters.category !== 'all') {
      supabaseQuery = supabaseQuery.eq('primary_task', filters.category)
    }

    if (filters?.pricing && filters.pricing !== 'all') {
      supabaseQuery = supabaseQuery.eq('pricing', filters.pricing)
    }

    // Apply feature filters
    if (filters?.features && filters.features.length > 0) {
      if (filters.features.includes('verified')) {
        supabaseQuery = supabaseQuery.eq('is_verified', true)
      }
      if (filters.features.includes('featured')) {
        supabaseQuery = supabaseQuery.eq('is_featured', true)
      }
      if (filters.features.includes('free')) {
        supabaseQuery = supabaseQuery.in('pricing', ['Free', 'Freemium'])
      }
    }

    // Simplified ordering for better performance
    const finalQuery = supabaseQuery
      .order('is_featured', { ascending: false })
      .order('is_verified', { ascending: false })
      .order('click_count', { ascending: false })
      .limit(limit)

    // Execute query with simplified error handling
    const { data, error } = await finalQuery;

    if (error) {
      console.error('Quick search error:', error);
      return { data: [], error };
    }

    // Cache successful results
    if (data) {
      searchCache.set(cacheKey, {
        data: data,
        timestamp: Date.now()
      });
    }

    return { data: data || [], error: null };
  } catch (err) {
    console.error('Exception in quick search:', err);
    return { data: [], error: err };
  }
}

/**
 * Enhanced search across all data types (tools, categories, articles, companies)
 */
export async function enhancedQuickSearch(
  query: string,
  limit: number = 10,
  filters?: { category?: string; pricing?: string; features?: string[] }
): Promise<{ data: SearchResult[]; error: any }> {
  if (!query || query.trim().length < 2) {
    return { data: [], error: null };
  }

  const normalizedQuery = normalizeQuery(query);
  const client = createBrowserClient();
  const results: SearchResult[] = [];

  try {
    // Use primary search term for main query (simpler and more reliable)
    const searchTerm = query.trim();

    // Search in tools (primary results - 70% of limit) with timeout
    const toolsQuery = client
      .from('tools')
      .select('id, company_name, slug, logo_url, primary_task, is_verified, is_featured, is_new, pricing, rating, short_description, click_count, visit_website_url')
      .or(`company_name.ilike.%${searchTerm}%,short_description.ilike.%${searchTerm}%,primary_task.ilike.%${searchTerm}%`)
      .order('is_featured', { ascending: false })
      .order('is_verified', { ascending: false })
      .order('click_count', { ascending: false })
      .limit(Math.floor(limit * 0.7)); // 70% for tools

    const { data: toolsData, error: toolsError } = await withQueryTimeout(toolsQuery, QUERY_TIMEOUTS.SEARCH);

    if (!toolsError && toolsData) {
      results.push(...toolsData.map(tool => ({
        ...tool,
        result_type: 'tool' as const
      })));
    }



    // Generate category results from existing tools data (15% of limit) with timeout
    try {
      const categoryQuery = client
        .from('tools')
        .select('primary_task')
        .not('primary_task', 'is', null)
        .ilike('primary_task', `%${searchTerm}%`)
        .limit(Math.floor(limit * 0.15));

      const { data: categoryData, error: categoryError } = await withQueryTimeout(categoryQuery, QUERY_TIMEOUTS.FAST);

      if (!categoryError && categoryData) {
        // Get unique categories
        const uniqueCategories = [...new Set(categoryData.map(item => item.primary_task).filter(Boolean))];

        results.push(...uniqueCategories.slice(0, Math.floor(limit * 0.15)).map((categoryName, index) => ({
          id: `category-${index}`,
          company_name: categoryName,
          category_name: categoryName,
          short_description: `Browse ${categoryName} tools`,
          primary_task: categoryName,
          result_type: 'category' as const,
          is_featured: false,
          is_verified: true,
          is_new: false
        })));
      }
    } catch (err) {
      console.warn('Categories search failed:', err);
    }

    // Enhanced sorting by relevance and type priority with exact match emphasis
    const sortedResults = results
      .map(result => {
        const searchLower = normalizedQuery.toLowerCase();
        let score = 0;

        // Enhanced name matching with exact match priority
        const name = (result.company_name || '').toLowerCase();
        if (name.includes(searchLower)) {
          // Exact match gets maximum score
          if (name === searchLower) {
            score += 1000;
          }
          // Starts with search term gets very high score
          else if (name.startsWith(searchLower)) {
            score += 500;
          }
          // Contains search term gets high score
          else {
            score += 200;
          }
        }

        // Enhanced description match
        const description = (result.short_description || '').toLowerCase();
        if (description.includes(searchLower)) {
          score += 50;
        }

        // Enhanced category/task match
        const category = (result.primary_task || '').toLowerCase();
        if (category.includes(searchLower)) {
          if (category === searchLower) {
            score += 300;
          } else if (category.startsWith(searchLower)) {
            score += 150;
          } else {
            score += 75;
          }
        }

        // Type-specific scoring with enhanced priorities
        switch (result.result_type) {
          case 'tool':
            score += 50; // Tools get highest priority
            if (result.is_featured) score += 20;
            if (result.is_verified) score += 15;
            if (result.is_new) score += 10;
            break;
          case 'category':
            score += 30; // Categories second priority
            break;
          case 'blog':
          case 'article':
            score += 20; // Articles third priority
            break;
          case 'subcategory':
            score += 15;
            break;
          case 'feature':
            score += 10;
            break;
          default:
            score += 5;
        }

        return { ...result, relevanceScore: score };
      })
      .sort((a, b) => (b as any).relevanceScore - (a as any).relevanceScore)
      .slice(0, limit);

    return { data: sortedResults, error: null };
  } catch (error: any) {
    console.error('Enhanced search exception:', error);

    // If it's a timeout error, provide user-friendly message
    if (error.message?.includes('timeout')) {
      return {
        data: [],
        error: new Error('Search is taking longer than expected. Please try a more specific search term or try again later.')
      };
    }

    return { data: [], error };
  }
}
