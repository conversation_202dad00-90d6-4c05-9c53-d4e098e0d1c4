import { createServerClient } from './server'

export async function fetchCategories(limit: number = 20, cacheKey?: string) {
  try {
    const supabase = await createServerClient()
    
    const { data, error } = await supabase
      .from('tools')
      .select('primary_task')
      .not('primary_task', 'is', null)
      .limit(500)
    
    if (error) {
      console.error('Error fetching categories:', error)
      return []
    }
    
    // Count occurrences of each category
    const categoryCount: Record<string, number> = {}
    data?.forEach((tool: any) => {
      if (tool.primary_task) {
        categoryCount[tool.primary_task] = (categoryCount[tool.primary_task] || 0) + 1
      }
    })
    
    // Convert to array and sort by count
    const categories = Object.entries(categoryCount)
      .map(([name, count]) => ({ 
        id: name.toLowerCase().replace(/\s+/g, '-'), 
        name, 
        count 
      }))
      .sort((a, b) => b.count - a.count)
      .slice(0, limit)
    
    return categories
  } catch (error) {
    console.error('Error in fetchCategories:', error)
    return []
  }
}

export async function fetchPricingOptions(cacheKey?: string) {
  try {
    const supabase = await createServerClient()
    
    const { data, error } = await supabase
      .from('tools')
      .select('pricing')
      .not('pricing', 'is', null)
    
    if (error) {
      console.error('Error fetching pricing options:', error)
      return []
    }
    
    // Get unique pricing options
    const pricingSet = new Set(data?.map((tool: any) => tool.pricing).filter(Boolean))
    return Array.from(pricingSet).sort()
  } catch (error) {
    console.error('Error in fetchPricingOptions:', error)
    return []
  }
}

export async function fetchToolCount(cacheKey?: string) {
  try {
    const supabase = await createServerClient()
    
    const { count, error } = await supabase
      .from('tools')
      .select('*', { count: 'exact', head: true })
    
    if (error) {
      console.error('Error fetching tool count:', error)
      return 0
    }
    
    return count || 0
  } catch (error) {
    console.error('Error in fetchToolCount:', error)
    return 0
  }
}

export async function fetchReviewCount(cacheKey?: string) {
  try {
    const supabase = await createServerClient()
    
    const { count, error } = await supabase
      .from('reviews')
      .select('*', { count: 'exact', head: true })
    
    if (error) {
      console.error('Error fetching review count:', error)
      return 0
    }
    
    return count || 0
  } catch (error) {
    console.error('Error in fetchReviewCount:', error)
    return 0
  }
}
