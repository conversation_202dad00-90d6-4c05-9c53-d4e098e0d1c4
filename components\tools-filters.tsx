"use client"

import { useState, useEffect } from "react"
import { useRouter } from "next/navigation"
import { 
  Search, 
  SlidersHorizontal, 
  Check, 
  ChevronDown, 
  ChevronUp, 
  Star, 
  ArrowUpDown, 
  Clock, 
  X 
} from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { 
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { Checkbox } from "@/components/ui/checkbox"
import { Label } from "@/components/ui/label"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"

// Common categories
const categoryOptions = [
  "Text Generation",
  "Image Generation",
  "Audio Generation",
  "Code",
  "Marketing",
  "Productivity",
  "Design",
  "Video",
  "Business",
  "Finance",
  "Education",
  "Healthcare",
]

// Pricing options
const pricingOptions = [
  "Free",
  "Freemium",
  "Paid",
  "Subscription",
  "One-time Purchase",
]

// Features options
const featureOptions = [
  "API Access",
  "Mobile App",
  "Chrome Extension",
  "Custom Training",
  "Multi-language Support",
  "Offline Mode",
]

interface ToolsFiltersProps {
  selectedCategories: string[]
  selectedPricing: string[]
  selectedFeatures: string[]
  query: string
  sort: string
}

export default function ToolsFilters({
  selectedCategories,
  selectedPricing,
  selectedFeatures,
  query,
  sort,
}: ToolsFiltersProps) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState(query)
  const [categories, setCategories] = useState<string[]>(selectedCategories)
  const [pricing, setPricing] = useState<string[]>(selectedPricing)
  const [features, setFeatures] = useState<string[]>(selectedFeatures)
  const [selectedSort, setSelectedSort] = useState(sort)
  const [isMobileFiltersOpen, setIsMobileFiltersOpen] = useState(false)
  
  // Apply filters when form is submitted
  const applyFilters = () => {
    const params = new URLSearchParams()
    
    if (searchQuery) {
      params.set('q', searchQuery)
    }
    
    if (categories.length > 0) {
      params.set('categories', categories.join(','))
    }
    
    if (pricing.length > 0) {
      params.set('pricing', pricing.join(','))
    }
    
    if (features.length > 0) {
      params.set('features', features.join(','))
    }
    
    if (selectedSort) {
      params.set('sort', selectedSort)
    }
    
    // Reset to page 1 when filters change
    params.set('page', '1')
    
    router.push(`/tools?${params.toString()}`)
  }
  
  // Reset all filters
  const resetFilters = () => {
    setSearchQuery('')
    setCategories([])
    setPricing([])
    setFeatures([])
    setSelectedSort('popular')
    router.push('/tools')
  }
  
  // Toggle category selection
  const toggleCategory = (category: string) => {
    setCategories(prev => 
      prev.includes(category)
        ? prev.filter(c => c !== category)
        : [...prev, category]
    )
  }
  
  // Toggle pricing selection
  const togglePricing = (price: string) => {
    setPricing(prev => 
      prev.includes(price)
        ? prev.filter(p => p !== price)
        : [...prev, price]
    )
  }
  
  // Toggle feature selection
  const toggleFeature = (feature: string) => {
    setFeatures(prev => 
      prev.includes(feature)
        ? prev.filter(f => f !== feature)
        : [...prev, feature]
    )
  }

  return (
    <div className="bg-white dark:bg-slate-800 rounded-xl shadow-sm border border-slate-200 dark:border-slate-700 p-4 sticky top-24">
      <div className="flex items-center justify-between mb-4">
        <h2 className="text-lg font-bold text-slate-900 dark:text-white">Filters</h2>
        <Button
          variant="ghost"
          size="sm"
          onClick={resetFilters}
          className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-white"
        >
          <X className="h-4 w-4 mr-1" />
          Reset
        </Button>
      </div>
      
      {/* Search */}
      <div className="mb-4">
        <div className="relative">
          <Search className="absolute left-3 top-1/2 -translate-y-1/2 h-4 w-4 text-slate-400" />
          <Input
            type="text"
            placeholder="Search tools..."
            value={searchQuery}
            onChange={(e) => setSearchQuery(e.target.value)}
            className="pl-9 bg-slate-50 dark:bg-slate-900"
          />
        </div>
      </div>
      
      {/* Sorting */}
      <div className="mb-4">
        <Label htmlFor="sort-select" className="text-sm font-medium text-slate-700 dark:text-slate-300 mb-1.5 block">
          Sort By
        </Label>
        <Select value={selectedSort} onValueChange={setSelectedSort}>
          <SelectTrigger id="sort-select" className="w-full bg-slate-50 dark:bg-slate-900">
            <SelectValue placeholder="Sort by..." />
          </SelectTrigger>
          <SelectContent>
            <SelectItem value="popular">
              <div className="flex items-center">
                <Star className="h-4 w-4 mr-2 text-amber-500" />
                Most Popular
              </div>
            </SelectItem>
            <SelectItem value="rating">
              <div className="flex items-center">
                <Star className="h-4 w-4 mr-2 text-amber-500" />
                Highest Rated
              </div>
            </SelectItem>
            <SelectItem value="newest">
              <div className="flex items-center">
                <Clock className="h-4 w-4 mr-2 text-blue-500" />
                Newest First
              </div>
            </SelectItem>
            <SelectItem value="a-z">
              <div className="flex items-center">
                <ArrowUpDown className="h-4 w-4 mr-2 text-slate-500" />
                A to Z
              </div>
            </SelectItem>
            <SelectItem value="z-a">
              <div className="flex items-center">
                <ArrowUpDown className="h-4 w-4 mr-2 text-slate-500" />
                Z to A
              </div>
            </SelectItem>
          </SelectContent>
        </Select>
      </div>
      
      {/* Filter groups */}
      <Accordion type="multiple" defaultValue={["categories", "pricing"]}>
        {/* Categories */}
        <AccordionItem value="categories">
          <AccordionTrigger className="text-sm font-medium text-slate-700 dark:text-slate-300">
            Categories
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 pt-1">
              {categoryOptions.map(category => (
                <div key={category} className="flex items-center">
                  <Checkbox 
                    id={`category-${category}`}
                    checked={categories.includes(category.toLowerCase())}
                    onCheckedChange={() => toggleCategory(category.toLowerCase())}
                  />
                  <Label 
                    htmlFor={`category-${category}`}
                    className="ml-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {category}
                  </Label>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
        
        {/* Pricing */}
        <AccordionItem value="pricing">
          <AccordionTrigger className="text-sm font-medium text-slate-700 dark:text-slate-300">
            Pricing
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 pt-1">
              {pricingOptions.map(price => (
                <div key={price} className="flex items-center">
                  <Checkbox 
                    id={`price-${price}`}
                    checked={pricing.includes(price.toLowerCase())}
                    onCheckedChange={() => togglePricing(price.toLowerCase())}
                  />
                  <Label 
                    htmlFor={`price-${price}`}
                    className="ml-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {price}
                  </Label>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
        
        {/* Features */}
        <AccordionItem value="features">
          <AccordionTrigger className="text-sm font-medium text-slate-700 dark:text-slate-300">
            Features
          </AccordionTrigger>
          <AccordionContent>
            <div className="space-y-2 pt-1">
              {featureOptions.map(feature => (
                <div key={feature} className="flex items-center">
                  <Checkbox 
                    id={`feature-${feature}`}
                    checked={features.includes(feature.toLowerCase())}
                    onCheckedChange={() => toggleFeature(feature.toLowerCase())}
                  />
                  <Label 
                    htmlFor={`feature-${feature}`}
                    className="ml-2 text-sm font-medium leading-none peer-disabled:cursor-not-allowed peer-disabled:opacity-70"
                  >
                    {feature}
                  </Label>
                </div>
              ))}
            </div>
          </AccordionContent>
        </AccordionItem>
      </Accordion>
      
      <Button 
        onClick={applyFilters}
        className="w-full mt-4 bg-blue-600 hover:bg-blue-700 text-white"
      >
        Apply Filters
      </Button>
    </div>
  )
} 