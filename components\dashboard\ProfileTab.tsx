'use client'

import { useAuth } from '@/hooks/use-auth'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Shield, User, Mail, Calendar } from 'lucide-react'

export function ProfileTab() {
  const [user, setUser] = useState<any>(null)
  const [isAdmin, setIsAdmin] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Safe date formatting
  const formatMemberSince = (dateString: string) => {
    try {
      return new Date(dateString).toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'long',
        day: 'numeric'
      })
    } catch {
      return 'Unknown'
    }
  }

  useEffect(() => {
    const fetchUser = async () => {
      try {
        setIsLoading(true)
        const supabase = createBrowserClient()

        if (!supabase) {
          // Use mock data for development
          setUser({
            id: 'dev-user-123',
            full_name: 'Development User',
            email: '<EMAIL>',
            created_at: '2024-01-15T10:00:00.000Z',
            role: 'user'
          })
          setIsAdmin(false)
          return
        }

        const { data: { user: authUser }, error } = await supabase.auth.getUser()

        if (error || !authUser) {
          // Use mock data for development
          setUser({
            id: 'dev-user-123',
            full_name: 'Development User',
            email: '<EMAIL>',
            created_at: '2024-01-15T10:00:00.000Z',
            role: 'user'
          })
          setIsAdmin(false)
          return
        }

        // Get user profile
        const { data: profile, error: profileError } = await supabase
          .from('profiles')
          .select('*')
          .eq('id', authUser.id)
          .single()

        if (profileError) {
          console.error('Error fetching profile:', profileError)
          // Use auth user data as fallback
          setUser({
            id: authUser.id,
            full_name: authUser.user_metadata?.full_name || 'User',
            email: authUser.email,
            created_at: authUser.created_at,
            role: 'user'
          })
          setIsAdmin(false)
        } else {
          setUser({
            id: profile.id,
            full_name: profile.full_name || authUser.user_metadata?.full_name || 'User',
            email: authUser.email,
            created_at: profile.created_at || authUser.created_at,
            role: profile.role || 'user',
            bio: profile.bio,
            avatar_url: profile.avatar_url
          })
          setIsAdmin(profile.role === 'admin' || profile.role === 'super_admin')
        }

      } catch (error) {
        console.error('Error fetching user:', error)
        // Use mock data for development
        setUser({
          id: 'dev-user-123',
          full_name: 'Development User',
          email: '<EMAIL>',
          created_at: '2024-01-15T10:00:00.000Z',
          role: 'user'
        })
        setIsAdmin(false)
      } finally {
        setIsLoading(false)
      }
    }

    fetchUser()
  }, [])

  if (isLoading) {
    return (
      <Card>
        <CardContent className="flex items-center justify-center py-12">
          <div className="text-center">
            <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
            <p className="text-sm text-muted-foreground">Loading profile...</p>
          </div>
        </CardContent>
      </Card>
    )
  }

  return (
    <Card>
      <CardHeader>
        <CardTitle className="flex items-center gap-2">
          <User className="h-5 w-5" />
          Profile Settings
        </CardTitle>
        <CardDescription>
          Manage your account settings and preferences
        </CardDescription>
      </CardHeader>
      <CardContent>
        <div className="space-y-6">
          <div>
            <h3 className="text-lg font-medium mb-4">Account Information</h3>
            <div className="space-y-4">
              {user?.full_name && (
                <div>
                  <label className="text-sm font-medium flex items-center gap-2">
                    <User className="h-4 w-4" />
                    Full Name
                  </label>
                  <div className="mt-1 p-3 border rounded bg-muted/30">
                    {user.full_name}
                  </div>
                </div>
              )}

              <div>
                <label className="text-sm font-medium flex items-center gap-2">
                  <Mail className="h-4 w-4" />
                  Email
                </label>
                <div className="mt-1 p-3 border rounded bg-muted/30">
                  {user?.email || 'Not provided'}
                </div>
              </div>

              <div>
                <label className="text-sm font-medium flex items-center gap-2">
                  <Shield className="h-4 w-4" />
                  Role
                </label>
                <div className="mt-1">
                  {isAdmin ? (
                    <Badge variant="secondary" className="text-purple-600 border-purple-600">
                      <Shield className="h-3 w-3 mr-1" />
                      Administrator
                    </Badge>
                  ) : (
                    <Badge variant="outline">
                      <User className="h-3 w-3 mr-1" />
                      User
                    </Badge>
                  )}
                </div>
              </div>

              {user?.created_at && (
                <div>
                  <label className="text-sm font-medium flex items-center gap-2">
                    <Calendar className="h-4 w-4" />
                    Member Since
                  </label>
                  <div className="mt-1 p-3 border rounded bg-muted/30">
                    {formatMemberSince(user.created_at)}
                  </div>
                </div>
              )}
            </div>
          </div>

          <div>
            <h3 className="text-lg font-medium mb-4">Account Actions</h3>
            <div className="space-y-4">
              <div className="flex flex-col sm:flex-row gap-2">
                <Button
                  variant="outline"
                  className="w-full sm:w-auto"
                  disabled
                >
                  Update Profile
                </Button>
                <Button
                  variant="outline"
                  className="w-full sm:w-auto"
                  disabled
                >
                  Change Password
                </Button>
              </div>
              <p className="text-xs text-muted-foreground">
                Profile management is handled through Logto dashboard
              </p>
            </div>
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
