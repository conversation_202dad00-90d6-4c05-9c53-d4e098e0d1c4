"use client"

import { useEffect } from "react"
import Link from "next/link"
import { But<PERSON> } from "@/components/ui/button"
import { AlertTriangle, RefreshCw } from "lucide-react"

export default function Error({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log the error to an error reporting service
    console.error("Application error:", error)
  }, [error])

  return (
    <div className="flex min-h-screen flex-col items-center justify-center py-12 px-4 sm:px-6 lg:px-8">
      <div className="text-center">
        <div className="flex justify-center mb-4">
          <AlertTriangle className="h-16 w-16 text-destructive" />
        </div>
        
        <h1 className="text-3xl font-bold mb-2">Something went wrong!</h1>
        <p className="text-lg text-muted-foreground mb-8">
          We're sorry, but we encountered an unexpected error.
        </p>
        
        <div className="flex flex-col sm:flex-row items-center justify-center gap-4">
          <Button onClick={reset}>
            <RefreshCw className="mr-2 h-4 w-4" />
            Try Again
          </Button>
          
          <Button variant="outline" asChild>
            <Link href="/">
              Go to Home Page
            </Link>
          </Button>
        </div>
        
        {process.env.NODE_ENV === "development" && (
          <div className="mt-8 p-4 bg-destructive/10 rounded-lg text-left overflow-auto max-w-2xl mx-auto">
            <h2 className="text-lg font-semibold mb-2">Error Details:</h2>
            <p className="font-mono text-sm">{error.message}</p>
            {error.stack && (
              <pre className="mt-2 text-xs overflow-auto p-2 bg-background rounded">
                {error.stack}
              </pre>
            )}
          </div>
        )}
      </div>
    </div>
  )
}
