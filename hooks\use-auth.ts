'use client'

import { useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { useAuthProvider, getProviderInfo } from '@/lib/auth/auth-factory'
import type { AuthUser } from '@/lib/auth/types'

export function useAuth() {
  const [user, setUser] = useState<AuthUser | null>(null)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const authProvider = useAuthProvider()
  const router = useRouter()
  const providerInfo = getProviderInfo()

  useEffect(() => {
    if (!authProvider) {
      setIsLoading(false)
      return
    }

    let mounted = true

    // الاستماع لتغييرات حالة التوثيق
    const unsubscribe = authProvider.onAuthStateChange?.(async (authUser) => {
      if (mounted) {
        setUser(authUser)
        setIsLoading(false)
      }
    })

    // تحميل المستخدم الحالي
    const loadUser = async () => {
      if (!mounted) return
      try {
        const currentUser = await authProvider.getUser()
        if (mounted) {
          setUser(currentUser)
          setError(null)
          setIsLoading(false)
        }
      } catch (err: any) {
        if (mounted) {
          console.warn('Error loading user:', err.message)
          setUser(null)
          setError(err.message)
          setIsLoading(false)
        }
      }
    }

    loadUser()

    return () => {
      mounted = false
      if (unsubscribe) {
        unsubscribe()
      }
    }
  }, [authProvider])



  // دالة مساعدة لتحديث المستخدم
  const refreshUser = async () => {
    await loadCurrentUser()
  }

  const signIn = async (email: string, password: string) => {
    if (!authProvider) return { error: 'Auth provider not initialized' }

    try {
      setIsLoading(true)
      setError(null)

      const result = await authProvider.signIn(email, password)

      if (result.error) {
        setError(result.error)
        return { error: result.error }
      }

      // في حالة Kinde، قد يكون هناك redirect
      if (result.data === 'redirect_initiated') {
        return { data: result.data }
      }

      // onAuthStateChange سيتعامل مع تحديث المستخدم تلقائياً
      return { data: result.data }
    } catch (err: any) {
      setError(err.message)
      return { error: err.message }
    } finally {
      setIsLoading(false)
    }
  }

  const signUp = async (email: string, password: string, fullName?: string) => {
    if (!authProvider) return { error: 'Auth provider not initialized' }

    try {
      setIsLoading(true)
      setError(null)

      const result = await authProvider.signUp(email, password, fullName)

      if (result.error) {
        setError(result.error)
        return { error: result.error }
      }

      // في حالة Kinde، قد يكون هناك redirect
      if (result.data === 'redirect_initiated') {
        return { data: result.data }
      }

      return { data: result.data }
    } catch (err: any) {
      setError(err.message)
      return { error: err.message }
    } finally {
      setIsLoading(false)
    }
  }

  const signOut = async () => {
    if (!authProvider) return

    try {
      // تحديث الحالة فوراً لتحسين UX
      setUser(null)
      setError(null)

      // تنفيذ signOut في الخلفية
      authProvider.signOut().catch(err => {
        console.warn('Sign out error:', err)
      })

      // التوجيه فوراً
      router.push('/')
    } catch (err: any) {
      setError(err.message)
    }
  }

  // دوال OAuth إضافية
  const signInWithGoogle = async () => {
    if (!authProvider?.signInWithGoogle) {
      return { error: 'Google sign in not supported by current provider' }
    }

    try {
      setIsLoading(true)
      setError(null)

      const result = await authProvider.signInWithGoogle()

      if (result.error) {
        setError(result.error)
        return { error: result.error }
      }

      return { data: result.data }
    } catch (err: any) {
      setError(err.message)
      return { error: err.message }
    } finally {
      setIsLoading(false)
    }
  }

  const signInWithGitHub = async () => {
    if (!authProvider?.signInWithGitHub) {
      return { error: 'GitHub sign in not supported by current provider' }
    }

    try {
      setIsLoading(true)
      setError(null)

      const result = await authProvider.signInWithGitHub()

      if (result.error) {
        setError(result.error)
        return { error: result.error }
      }

      return { data: result.data }
    } catch (err: any) {
      setError(err.message)
      return { error: err.message }
    } finally {
      setIsLoading(false)
    }
  }

  const isAuthenticated = !!user

  return {
    user,
    isLoading,
    isAuthenticated,
    error,
    signIn,
    signUp,
    signOut,
    signInWithGoogle,
    signInWithGitHub,
    refreshUser,
    // معلومات المزود الحالي
    provider: providerInfo
  }
}


