"use client"

import { Component, ErrorInfo, ReactNode } from "react"
import { <PERSON><PERSON><PERSON><PERSON>gle, <PERSON>fresh<PERSON><PERSON>, Home, Bug } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Alert, AlertDescription } from "@/components/ui/alert"

interface Props {
  children: ReactNode
  fallback?: ReactNode
  onError?: (error: Error, errorInfo: ErrorInfo) => void
}

interface State {
  hasError: boolean
  error?: Error
  errorInfo?: ErrorInfo
}

export class ErrorBoundary extends Component<Props, State> {
  constructor(props: Props) {
    super(props)
    this.state = { hasError: false }
  }

  static getDerivedStateFromError(error: Error): State {
    return { hasError: true, error }
  }

  componentDidCatch(error: Error, errorInfo: ErrorInfo) {
    console.error('<PERSON><PERSON>rBoundary caught an error:', error, errorInfo)
    this.props.onError?.(error, errorInfo)
    this.setState({ errorInfo })
  }

  render() {
    if (this.state.hasError) {
      if (this.props.fallback) {
        return this.props.fallback
      }

      return (
        <div className="min-h-[400px] flex items-center justify-center p-4">
          <Card className="w-full max-w-md">
            <CardHeader className="text-center">
              <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
                <AlertTriangle className="h-6 w-6 text-destructive" />
              </div>
              <CardTitle className="text-xl">Something went wrong</CardTitle>
              <CardDescription>
                We encountered an unexpected error. Please try again.
              </CardDescription>
            </CardHeader>
            
            <CardContent>
              {process.env.NODE_ENV === 'development' && this.state.error && (
                <Alert className="mb-4">
                  <Bug className="h-4 w-4" />
                  <AlertDescription className="text-xs font-mono">
                    {this.state.error.message}
                  </AlertDescription>
                </Alert>
              )}
            </CardContent>

            <CardFooter className="flex gap-2">
              <Button
                variant="outline"
                onClick={() => this.setState({ hasError: false, error: undefined, errorInfo: undefined })}
                className="flex-1"
              >
                <RefreshCw className="mr-2 h-4 w-4" />
                Try Again
              </Button>
              <Button
                variant="default"
                onClick={() => window.location.href = '/'}
                className="flex-1"
              >
                <Home className="mr-2 h-4 w-4" />
                Go Home
              </Button>
            </CardFooter>
          </Card>
        </div>
      )
    }

    return this.props.children
  }
}

// Hook-based error boundary for functional components
export function useErrorHandler() {
  return (error: Error, errorInfo?: ErrorInfo) => {
    console.error('Error caught by error handler:', error, errorInfo)
    // You can send error to logging service here
  }
}

// Simple error display component
interface ErrorDisplayProps {
  error?: Error | string
  title?: string
  description?: string
  onRetry?: () => void
  onGoHome?: () => void
  className?: string
}

export function ErrorDisplay({
  error,
  title = "Something went wrong",
  description = "We encountered an unexpected error. Please try again.",
  onRetry,
  onGoHome,
  className
}: ErrorDisplayProps) {
  const errorMessage = typeof error === 'string' ? error : error?.message

  return (
    <div className={`flex items-center justify-center p-8 ${className}`}>
      <Card className="w-full max-w-md text-center">
        <CardHeader>
          <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-destructive/10">
            <AlertTriangle className="h-6 w-6 text-destructive" />
          </div>
          <CardTitle>{title}</CardTitle>
          <CardDescription>{description}</CardDescription>
        </CardHeader>

        {errorMessage && process.env.NODE_ENV === 'development' && (
          <CardContent>
            <Alert>
              <Bug className="h-4 w-4" />
              <AlertDescription className="text-xs font-mono text-left">
                {errorMessage}
              </AlertDescription>
            </Alert>
          </CardContent>
        )}

        <CardFooter className="flex gap-2">
          {onRetry && (
            <Button variant="outline" onClick={onRetry} className="flex-1">
              <RefreshCw className="mr-2 h-4 w-4" />
              Try Again
            </Button>
          )}
          {onGoHome && (
            <Button variant="default" onClick={onGoHome} className="flex-1">
              <Home className="mr-2 h-4 w-4" />
              Go Home
            </Button>
          )}
        </CardFooter>
      </Card>
    </div>
  )
}

// Network error component
export function NetworkError({ onRetry }: { onRetry?: () => void }) {
  return (
    <ErrorDisplay
      title="Connection Error"
      description="Unable to connect to our servers. Please check your internet connection and try again."
      onRetry={onRetry}
      onGoHome={() => window.location.reload()}
    />
  )
}

// Not found error component
export function NotFoundError({ 
  title = "Page Not Found",
  description = "The page you're looking for doesn't exist or has been moved.",
  onGoHome
}: {
  title?: string
  description?: string
  onGoHome?: () => void
}) {
  return (
    <ErrorDisplay
      title={title}
      description={description}
      onGoHome={onGoHome || (() => window.location.href = '/')}
    />
  )
}

export default ErrorBoundary
