import { Metadata } from 'next'
import { redirect } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import SubmitToolForm from '@/components/submit/SubmitToolForm'

export const dynamic = 'force-dynamic'

export const metadata: Metadata = {
  title: 'Submit an AI Tool - AI Tools Directory',
  description: 'Submit your AI tool to our directory and get discovered by thousands of users looking for AI solutions.',
}

export default async function SubmitToolPage() {
  const supabase = await createServerClient()

  // Check if user is authenticated
  const { data: { session } } = await supabase.auth.getSession()

  if (!session) {
    // Redirect to login page if not authenticated
    redirect('/auth?returnUrl=/submit')
  }

  // Fetch categories for the form
  const { data: tools } = await supabase
    .from('tools')
    .select('primary_task')
    .not('primary_task', 'is', null)

  // Extract unique categories
  const categories = Array.from(new Set(
    tools?.map(tool => tool.primary_task).filter(Boolean) || []
  )).sort()

  return (
    <div className="min-h-screen bg-background pt-24 pb-16">
      <div className="container mx-auto px-4">
        <div className="max-w-3xl mx-auto">
          <h1 className="text-3xl font-bold mb-2">Submit an AI Tool</h1>
          <p className="text-muted-foreground mb-8">
            Share your AI tool with our community and get discovered by thousands of users.
          </p>

          <SubmitToolForm
            user={session.user}
            categories={categories}
          />
        </div>
      </div>
    </div>
  )
}
