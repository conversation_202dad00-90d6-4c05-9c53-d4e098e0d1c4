'use client'

import React, { Suspense, useState, useEffect } from 'react'
import { useRouter } from 'next/navigation'
import ToolsFiltersServer from '@/components/tools/ToolsFiltersServer'
import ToolsStatsBar from '@/components/tools/ToolsStatsBar'
import PopularCategories from '@/components/tools/PopularCategories'
import ActiveFilters from '@/components/tools/ActiveFilters'
import CategoriesShowcase from '@/components/tools/CategoriesShowcase'
import UniversalSearch from '@/components/search/universal-search'
import FeaturedToolsSection from '@/components/tools/FeaturedToolsSection'
import LiveStats from '@/components/tools/LiveStats'
import QuickFilters from '@/components/tools/QuickFilters'
import SearchResults from '@/components/tools/SearchResults'
import { LoadingGrid } from '@/components/ui/loading-grid'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Grid3X3, List, LayoutGrid, Filter, Search, TrendingUp, Star, Clock, Sparkles, Map, Layers } from 'lucide-react'
import { cn } from '@/lib/utils'
import EnhancedToolGrid from './EnhancedToolGrid'
import EnhancedToolList from './EnhancedToolList'

interface EnhancedToolsPageProps {
  searchParams: {
    category?: string
    pricing?: string
    sortBy?: string
    search?: string
    features?: string
  }
  categories: any[]
  pricingOptions: string[]
}

export default function EnhancedToolsPage({
  searchParams,
  categories,
  pricingOptions
}: EnhancedToolsPageProps) {
  const router = useRouter()
  const [viewMode, setViewMode] = useState<'grid' | 'list' | 'categories'>('grid')
  const [totalTools, setTotalTools] = useState(0)

  // Convert searchParams to a regular object to avoid Next.js warnings
  const params = {
    category: searchParams?.category || '',
    pricing: searchParams?.pricing || '',
    sortBy: searchParams?.sortBy || 'featured',
    search: searchParams?.search || '',
    features: searchParams?.features || ''
  }

  // Use the params object to avoid Next.js warnings
  const { category, pricing, sortBy, search, features } = params
  const featuresList = features ? features.split(',') : []

  // Get page title based on filters
  const getPageTitle = () => {
    if (category) {
      const categoryName = category
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')

      return `${categoryName} AI Tools`
    }

    if (search) {
      return `Search results for "${search}"`
    }

    return "Discover AI Tools"
  }

  // Get page description
  const getPageDescription = () => {
    if (search) {
      return `Found ${totalTools} tools matching "${search}"`
    }
    if (category) {
      const categoryName = category
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
      return `Explore ${totalTools} ${categoryName.toLowerCase()} tools to enhance your workflow`
    }
    return `Browse ${totalTools} cutting-edge AI tools to boost your productivity`
  }

  // Get sort icon
  const getSortIcon = () => {
    switch (sortBy) {
      case 'featured':
        return <Sparkles className="h-4 w-4" />
      case 'newest':
        return <Clock className="h-4 w-4" />
      case 'popular':
        return <TrendingUp className="h-4 w-4" />
      case 'rating':
        return <Star className="h-4 w-4" />
      default:
        return <Sparkles className="h-4 w-4" />
    }
  }

  // Get active filters count
  const getActiveFiltersCount = () => {
    let count = 0
    if (category) count++
    if (pricing) count++
    if (search) count++
    if (featuresList.length > 0) count += featuresList.length
    return count
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-gray-50 via-white to-blue-50/30 dark:from-gray-900 dark:via-gray-900 dark:to-blue-900/10">
      {/* Hero Section - Same as homepage */}
      <div className="relative overflow-hidden pt-24 pb-12">
        <div className="container mx-auto px-4 relative">
          <div className="max-w-6xl mx-auto text-center">
            {/* Live Stats */}
            <LiveStats compact={true} className="justify-center mb-6" />

            {/* Main Title - Same style as homepage */}
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-4">
              <span className="bg-gradient-to-r from-blue-600 via-purple-600 to-blue-800 bg-clip-text text-transparent">
                {getPageTitle()}
              </span>
            </h1>

            <p className="text-lg md:text-xl text-muted-foreground mb-8 max-w-3xl mx-auto">
              {getPageDescription()}
            </p>

            {/* Search Bar - Same as homepage */}
            <div className="max-w-3xl mx-auto mb-8">
              <div className="relative bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-slate-700/30">
                <div className="flex items-center p-3">
                  <div className="flex-1">
                    <UniversalSearch
                      mode="instant-filter"
                      context="tools"
                      variant="hero"
                      size="lg"
                      placeholder="Search for AI tools, features, or categories..."
                      className="w-full"
                      rounded="full"
                      glass={false}
                      showKeyboardShortcut={false}
                      fullWidth={true}
                      showInstantResults={false}
                      showSearchButton={false}
                      maxResults={8}
                      initialValue={search}
                      onInstantFilter={(query: string) => {
                        // Update URL with search parameter for instant filtering
                        const params = new URLSearchParams(window.location.search)
                        if (query.trim()) {
                          params.set('search', query.trim())
                        } else {
                          params.delete('search')
                        }
                        router.push(`/tools?${params.toString()}`)
                      }}
                      onSearch={(query: string) => {
                        // Navigate to search results page for full search
                        if (query.trim()) {
                          router.push(`/search?q=${encodeURIComponent(query.trim())}`)
                        } else {
                          router.push('/tools')
                        }
                      }}
                    />
                  </div>

                  {/* Search Button */}
                  <div className="flex-shrink-0 ml-3">
                    <button
                      type="button"
                      onClick={() => {
                        if (search?.trim()) {
                          router.push(`/search?q=${encodeURIComponent(search.trim())}`)
                        }
                      }}
                      className="h-10 px-4 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary active:from-primary/80 active:to-primary/80 text-white font-semibold transition-all duration-200 hover:shadow-lg hover:scale-105 active:scale-95 flex items-center gap-2 rounded-full touch-manipulation"
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      <span className="hidden sm:inline">Search</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Quick Filters */}
            <QuickFilters />
          </div>
        </div>
      </div>

      <div className="container mx-auto px-4 py-8">
        {/* View Mode Tabs */}
        <div className="bg-card rounded-xl border shadow-sm p-2 mb-8">
          <div className="flex items-center gap-2 overflow-x-auto">
            <Button
              variant={viewMode === 'grid' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('grid')}
              className="flex items-center gap-2 whitespace-nowrap"
            >
              <Grid3X3 className="h-4 w-4" />
              Grid View
            </Button>

            <Button
              variant={viewMode === 'list' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('list')}
              className="flex items-center gap-2 whitespace-nowrap"
            >
              <List className="h-4 w-4" />
              List View
            </Button>

            <Button
              variant={viewMode === 'categories' ? 'default' : 'ghost'}
              size="sm"
              onClick={() => setViewMode('categories')}
              className="flex items-center gap-2 whitespace-nowrap"
            >
              <Layers className="h-4 w-4" />
              Categories
            </Button>

            <div className="ml-auto flex items-center gap-2 text-sm text-muted-foreground">
              <span>View:</span>
              <Badge variant="outline">{totalTools} tools</Badge>
            </div>
          </div>
        </div>

        {/* Categories View */}
        {viewMode === 'categories' && (
          <CategoriesShowcase showAll={true} />
        )}

        {/* Grid and List Views */}
        {(viewMode === 'grid' || viewMode === 'list') && (
          <div className="flex flex-col lg:flex-row gap-8">
            {/* Enhanced Sidebar for desktop */}
            <div className="hidden lg:block w-80 shrink-0">
              <div className="sticky top-24 space-y-6">
                {/* Filters Header */}
                <div className="bg-card rounded-xl border shadow-sm p-6">
                  <div className="flex items-center justify-between mb-4">
                    <h2 className="text-xl font-bold flex items-center gap-2">
                      <Filter className="h-5 w-5 text-primary" />
                      Filters
                    </h2>
                    {getActiveFiltersCount() > 0 && (
                      <Badge variant="secondary" className="bg-primary/10 text-primary">
                        {getActiveFiltersCount()} active
                      </Badge>
                    )}
                  </div>

                  <ToolsFiltersServer
                    categories={categories}
                    pricingOptions={pricingOptions}
                  />
                </div>

                {/* Quick Stats */}
                <div className="bg-card rounded-xl border shadow-sm p-6">
                  <h3 className="font-semibold mb-4 flex items-center gap-2">
                    <TrendingUp className="h-4 w-4 text-primary" />
                    Quick Stats
                  </h3>
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Total Tools</span>
                      <Badge variant="outline">{totalTools}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Categories</span>
                      <Badge variant="outline">{categories.length}</Badge>
                    </div>
                    <div className="flex justify-between items-center">
                      <span className="text-sm text-muted-foreground">Featured Tools</span>
                      <Badge variant="outline" className="bg-amber-50 text-amber-700 border-amber-200">
                        {Math.floor(totalTools * 0.15)}
                      </Badge>
                    </div>
                  </div>
                </div>

                {/* Popular Categories */}
                <PopularCategories
                  categories={categories}
                  currentCategory={category}
                />
              </div>
            </div>

            {/* Main Content Area */}
            <div className="flex-1 min-w-0">
              {/* Active Filters */}
              <ActiveFilters
                searchQuery={search}
                category={category}
                pricing={pricing}
                features={featuresList}
                sortBy={sortBy}
              />

              {/* Search Results (when searching) */}
              {search ? (
                <div className="mb-8">
                  <SearchResults
                    searchQuery={search}
                    onResultsChange={setTotalTools}
                  />
                </div>
              ) : (
                /* Tools Display with Grid/List Toggle */
                <div className="space-y-6">
                  {viewMode === 'grid' ? (
                    <EnhancedToolGrid
                      searchQuery={search}
                      category={category}
                      pricing={pricing}
                      sortBy={sortBy}
                      features={featuresList}
                      onResultsChange={setTotalTools}
                    />
                  ) : (
                    <EnhancedToolList
                      searchQuery={search}
                      category={category}
                      pricing={pricing}
                      sortBy={sortBy}
                      features={featuresList}
                      onResultsChange={setTotalTools}
                    />
                  )}
                </div>
              )}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}