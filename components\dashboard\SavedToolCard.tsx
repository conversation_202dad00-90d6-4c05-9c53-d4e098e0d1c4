'use client'

import { useState } from 'react'
import Link from 'next/link'
import { Card, CardContent, CardFooter, CardHeader } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Avatar, AvatarFallback, AvatarImage } from '@/components/ui/avatar'
import { 
  AlertDialog,
  AlertDialogAction,
  AlertDialogCancel,
  AlertDialogContent,
  AlertDialogDescription,
  AlertDialogFooter,
  AlertDialogHeader,
  AlertDialogTitle,
  AlertDialogTrigger,
} from '@/components/ui/alert-dialog'
import { ExternalLink, Trash2, Star, Calendar } from 'lucide-react'

interface SavedTool {
  id: string
  tool_id: number
  created_at: string
  tool: {
    id: number
    name: string
    description: string
    logo_url: string
    website_url: string
    category: string
    pricing_type: string
    rating: number
    slug: string
  }
}

interface SavedToolCardProps {
  savedTool: SavedTool
  onRemove: () => void
}

export function SavedToolCard({ savedTool, onRemove }: SavedToolCardProps) {
  const [isRemoving, setIsRemoving] = useState(false)
  const { tool } = savedTool

  const handleRemove = async () => {
    setIsRemoving(true)
    try {
      await onRemove()
    } finally {
      setIsRemoving(false)
    }
  }

  const getPricingColor = (pricingType: string) => {
    switch (pricingType.toLowerCase()) {
      case 'free':
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-300'
      case 'freemium':
        return 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-300'
      case 'paid':
        return 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-300'
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-900 dark:text-gray-300'
    }
  }

  return (
    <Card className="group hover:shadow-md transition-shadow">
      <CardHeader className="pb-3">
        <div className="flex items-start justify-between">
          <div className="flex items-center gap-3">
            <Avatar className="h-10 w-10">
              <AvatarImage src={tool.logo_url} alt={tool.name} />
              <AvatarFallback>
                {tool.name.charAt(0).toUpperCase()}
              </AvatarFallback>
            </Avatar>
            <div className="min-w-0 flex-1">
              <h3 className="font-semibold text-sm leading-tight truncate">
                {tool.name}
              </h3>
              <div className="flex items-center gap-2 mt-1">
                <Badge variant="secondary" className="text-xs">
                  {tool.category}
                </Badge>
                <Badge 
                  variant="outline" 
                  className={`text-xs ${getPricingColor(tool.pricing_type)}`}
                >
                  {tool.pricing_type}
                </Badge>
              </div>
            </div>
          </div>
          
          <AlertDialog>
            <AlertDialogTrigger asChild>
              <Button
                variant="ghost"
                size="sm"
                className="opacity-0 group-hover:opacity-100 transition-opacity"
                disabled={isRemoving}
              >
                <Trash2 className="h-4 w-4 text-destructive" />
              </Button>
            </AlertDialogTrigger>
            <AlertDialogContent>
              <AlertDialogHeader>
                <AlertDialogTitle>Remove from saved tools?</AlertDialogTitle>
                <AlertDialogDescription>
                  This will remove "{tool.name}" from your saved tools list. You can always save it again later.
                </AlertDialogDescription>
              </AlertDialogHeader>
              <AlertDialogFooter>
                <AlertDialogCancel>Cancel</AlertDialogCancel>
                <AlertDialogAction
                  onClick={handleRemove}
                  className="bg-destructive text-destructive-foreground hover:bg-destructive/90"
                >
                  Remove
                </AlertDialogAction>
              </AlertDialogFooter>
            </AlertDialogContent>
          </AlertDialog>
        </div>
      </CardHeader>

      <CardContent className="py-3">
        <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
          {tool.description}
        </p>
        
        <div className="flex items-center justify-between text-xs text-muted-foreground">
          <div className="flex items-center gap-1">
            <Star className="h-3 w-3 fill-yellow-400 text-yellow-400" />
            <span>{tool.rating.toFixed(1)}</span>
          </div>
          <div className="flex items-center gap-1">
            <Calendar className="h-3 w-3" />
            <span>Saved {new Date(savedTool.created_at).toLocaleDateString()}</span>
          </div>
        </div>
      </CardContent>

      <CardFooter className="pt-3 gap-2">
        <Button asChild variant="outline" size="sm" className="flex-1">
          <Link href={`/Tool/${tool.slug}`}>
            View Details
          </Link>
        </Button>
        <Button asChild size="sm" className="flex-1">
          <a 
            href={tool.website_url} 
            target="_blank" 
            rel="noopener noreferrer"
            className="flex items-center gap-1"
          >
            <ExternalLink className="h-3 w-3" />
            Visit Tool
          </a>
        </Button>
      </CardFooter>
    </Card>
  )
}
