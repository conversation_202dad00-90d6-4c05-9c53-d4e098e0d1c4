"use client"

import { forwardRef } from "react"
import { Button, ButtonProps } from "@/components/ui/button"
import { cn } from "@/lib/utils"

interface EnhancedButtonProps extends ButtonProps {
  glow?: boolean
  float?: boolean
  ripple?: boolean
  gradient?: boolean
}

const EnhancedButton = forwardRef<HTMLButtonElement, EnhancedButtonProps>(
  ({ className, glow, float, ripple, gradient, children, ...props }, ref) => {
    return (
      <Button
        ref={ref}
        className={cn(
          "theme-transition",
          glow && "animate-glow",
          float && "animate-float",
          gradient && "bg-gradient-to-r from-primary to-accent hover:from-primary/90 hover:to-accent/90",
          ripple && "relative overflow-hidden",
          className
        )}
        {...props}
      >
        {ripple && (
          <div className="absolute inset-0 rounded-full opacity-0 group-active:opacity-100 bg-current transition-opacity duration-150 animate-ping" 
               style={{ animationDuration: '0.3s', animationIterationCount: 1 }} />
        )}
        <span className="relative z-10">{children}</span>
      </Button>
    )
  }
)

EnhancedButton.displayName = "EnhancedButton"

export { EnhancedButton }
