"use client"

import type React from "react"

import { useState } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { useToast } from "@/components/ui/use-toast"
import { supabase } from "@/lib/supabase/client"
import { PlusCircle, X } from "lucide-react"

export default function ToolSubmissionForm() {
  const [isLoading, setIsLoading] = useState(false)
  const [features, setFeatures] = useState<string[]>([])
  const [newFeature, setNewFeature] = useState("")
  const { toast } = useToast()

  const handleAddFeature = () => {
    if (newFeature.trim()) {
      setFeatures([...features, newFeature.trim()])
      setNewFeature("")
    }
  }

  const handleRemoveFeature = (index: number) => {
    setFeatures(features.filter((_, i) => i !== index))
  }

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()
    const formData = new FormData(e.currentTarget)

    const toolData = {
      tool_name: formData.get("tool-name") as string,
      description: formData.get("description") as string,
      category: formData.get("category") as string,
      pricing: formData.get("pricing") as string,
      website_url: formData.get("website-url") as string,
      contact_email: formData.get("contact-email") as string,
      additional_info: formData.get("additional-info") as string,
      features: features,
    }

    setIsLoading(true)

    try {
      const { data, error } = await supabase.from("tool_submissions").insert([toolData])

      if (error) throw error

      toast({
        title: "Tool submitted successfully",
        description: "Your tool has been submitted for review.",
        variant: "default",
      })

      // Reset form
      ;(e.target as HTMLFormElement).reset()
      setFeatures([])
    } catch (error) {
      console.error("Error submitting tool:", error)
      toast({
        title: "Submission failed",
        description: "There was an error submitting your tool. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <form onSubmit={handleSubmit} className="space-y-6">
      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label htmlFor="tool-name" className="block text-sm font-medium">
            Tool Name <span className="text-red-500">*</span>
          </label>
          <Input id="tool-name" name="tool-name" placeholder="Enter the name of your AI tool" required />
        </div>

        <div className="space-y-2">
          <label htmlFor="category" className="block text-sm font-medium">
            Category <span className="text-red-500">*</span>
          </label>
          <Select name="category" required>
            <SelectTrigger id="category">
              <SelectValue placeholder="Select a category" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="text-generation">Text Generation</SelectItem>
              <SelectItem value="image-generation">Image Generation</SelectItem>
              <SelectItem value="audio-generation">Audio Generation</SelectItem>
              <SelectItem value="video-generation">Video Generation</SelectItem>
              <SelectItem value="data-analysis">Data Analysis</SelectItem>
              <SelectItem value="chatbots">Chatbots</SelectItem>
              <SelectItem value="code-generation">Code Generation</SelectItem>
              <SelectItem value="analytics">Analytics</SelectItem>
              <SelectItem value="research">Research</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      <div className="space-y-2">
        <label htmlFor="description" className="block text-sm font-medium">
          Description <span className="text-red-500">*</span>
        </label>
        <Textarea
          id="description"
          name="description"
          placeholder="Provide a detailed description of your AI tool"
          required
          rows={4}
        />
      </div>

      <div className="space-y-2">
        <label htmlFor="website-url" className="block text-sm font-medium">
          Website URL <span className="text-red-500">*</span>
        </label>
        <Input id="website-url" name="website-url" type="url" placeholder="https://example.com" required />
      </div>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        <div className="space-y-2">
          <label htmlFor="pricing" className="block text-sm font-medium">
            Pricing <span className="text-red-500">*</span>
          </label>
          <Select name="pricing" required>
            <SelectTrigger id="pricing">
              <SelectValue placeholder="Select pricing model" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="free">Free</SelectItem>
              <SelectItem value="freemium">Freemium</SelectItem>
              <SelectItem value="paid">Paid</SelectItem>
              <SelectItem value="subscription">Subscription</SelectItem>
              <SelectItem value="contact-for-pricing">Contact for Pricing</SelectItem>
            </SelectContent>
          </Select>
        </div>

        <div className="space-y-2">
          <label htmlFor="contact-email" className="block text-sm font-medium">
            Contact Email <span className="text-red-500">*</span>
          </label>
          <Input id="contact-email" name="contact-email" type="email" placeholder="<EMAIL>" required />
        </div>
      </div>

      <div className="space-y-2">
        <label className="block text-sm font-medium">Key Features</label>
        <div className="flex items-center gap-2 mb-2">
          <Input placeholder="Add a feature" value={newFeature} onChange={(e) => setNewFeature(e.target.value)} />
          <Button type="button" variant="outline" size="icon" onClick={handleAddFeature}>
            <PlusCircle className="h-4 w-4" />
          </Button>
        </div>

        {features.length > 0 && (
          <div className="flex flex-wrap gap-2 mt-2">
            {features.map((feature, index) => (
              <div
                key={index}
                className="flex items-center gap-1 bg-slate-100 dark:bg-slate-800 px-3 py-1 rounded-full"
              >
                <span className="text-sm">{feature}</span>
                <button
                  type="button"
                  onClick={() => handleRemoveFeature(index)}
                  className="text-slate-500 hover:text-slate-700 dark:text-slate-400 dark:hover:text-slate-200"
                >
                  <X className="h-3 w-3" />
                </button>
              </div>
            ))}
          </div>
        )}
      </div>

      <div className="space-y-2">
        <label htmlFor="additional-info" className="block text-sm font-medium">
          Additional Information
        </label>
        <Textarea
          id="additional-info"
          name="additional-info"
          placeholder="Any other details you'd like to share about your tool"
          rows={3}
        />
      </div>

      <Button type="submit" disabled={isLoading} className="w-full">
        {isLoading ? "Submitting..." : "Submit Tool for Review"}
      </Button>
    </form>
  )
}
