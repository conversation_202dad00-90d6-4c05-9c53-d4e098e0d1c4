"use client"

import { useState, useEffect } from "react"
import { <PERSON>rk<PERSON> } from "lucide-react"
import Link from "next/link"
import { motion } from "framer-motion"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import HeroSearch from "./HeroSearch"
import TextCycler from "@/components/ui/TextCycler"
import DynamicToolsBadge from "@/components/ui/DynamicToolsBadge"

interface Category {
  id: string
  name: string
  count: number
}

export default function Hero() {
  const [categories, setCategories] = useState<Category[]>([])
  const [pricingOptions, setPricingOptions] = useState<string[]>([])
  const [statistics, setStatistics] = useState({
    toolCount: 0,
    categoryCount: 0,
    reviewCount: 0
  })
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        const supabase = createBrowserClient()

        // Fetch categories with counts
        const { data: categoryData, error: categoryError } = await supabase
          .from("tools")
          .select("primary_task")

        if (categoryError) {
          console.error("Error fetching categories:", categoryError)
          return
        }

        // Count categories
        const categoryCounts: Record<string, number> = {}
        categoryData.forEach(tool => {
          if (tool.primary_task) {
            categoryCounts[tool.primary_task] = (categoryCounts[tool.primary_task] || 0) + 1
          }
        })

        const sortedCategories: Category[] = Object.entries(categoryCounts)
          .filter(([name]) => name)
          .map(([name, count]) => ({
            id: name.toLowerCase().replace(/\s+/g, '-'),
            name,
            count
          }))
          .sort((a, b) => b.count - a.count)

        setCategories(sortedCategories)

        // Fetch pricing options
        const { data: pricingData, error: pricingError } = await supabase
          .from("tools")
          .select("pricing")

        if (pricingError) {
          console.error("Error fetching pricing options:", pricingError)
          return
        }

        const uniquePricing = Array.from(new Set(
          pricingData.map(tool => tool.pricing).filter(Boolean)
        ))

        setPricingOptions(uniquePricing)

        // Fetch statistics
        const { count: toolCount, error: toolError } = await supabase
          .from("tools")
          .select("*", { count: "exact", head: true })

        if (toolError) {
          console.error("Error fetching tool count:", toolError)
        }

        const uniqueCategories = new Set()
        categoryData?.forEach(tool => {
          if (tool.primary_task) uniqueCategories.add(tool.primary_task)
        })

        const { count: reviewCount, error: reviewError } = await supabase
          .from("reviews")
          .select("*", { count: "exact", head: true })

        if (reviewError) {
          console.error("Error fetching review count:", reviewError)
        }

        setStatistics({
          toolCount: toolCount || 0,
          categoryCount: uniqueCategories.size || 0,
          reviewCount: reviewCount || 0
        })
      } catch (err) {
        console.error("Error fetching data:", err)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const popularCategories = categories.slice(0, 5)

  // Project types for text cycling
  const projectTypes = [
    "business",
    "content",
    "marketing",
    "design",
    "coding",
    "writing"
  ]

  return (
    <div className="relative pt-20 pb-12 sm:pt-28 sm:pb-16 md:pt-40 md:pb-24 overflow-hidden">
      {/* Background with gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-violet-50 dark:from-slate-950 dark:via-indigo-950/20 dark:to-slate-900 -z-10"></div>

      {/* Decorative elements */}
      <div className="absolute -top-12 -left-12 sm:-top-24 sm:-left-24 w-48 h-48 sm:w-72 sm:h-72 md:w-96 md:h-96 bg-blue-200 dark:bg-blue-900/20 rounded-full filter blur-3xl opacity-30 animate-pulse"></div>
      <div className="absolute -bottom-12 -right-12 sm:-bottom-24 sm:-right-24 w-48 h-48 sm:w-72 sm:h-72 md:w-96 md:h-96 bg-indigo-200 dark:bg-indigo-900/20 rounded-full filter blur-3xl opacity-30 animate-pulse" style={{ animationDelay: '2s' }}></div>

      <div className="container mx-auto px-3 sm:px-4 lg:px-6 relative z-10">
        <motion.div
          className="text-center max-w-4xl mx-auto"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <DynamicToolsBadge
            className="inline-block rounded-full bg-blue-100 dark:bg-blue-900/30 px-3 sm:px-4 py-1 sm:py-1.5 text-xs sm:text-sm font-medium text-blue-600 dark:text-blue-400"
            iconSize={14}
          />

          <h1 className="mt-4 sm:mt-6 text-3xl sm:text-4xl md:text-5xl lg:text-6xl xl:text-7xl font-extrabold tracking-tight text-center leading-tight">
            <span className="bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 dark:from-blue-400 dark:via-indigo-400 dark:to-purple-400">AI tools</span> for{" "}
            <TextCycler
              texts={projectTypes}
              interval={2500}
            />
            {" "}projects
          </h1>

          <p className="mt-4 sm:mt-6 text-base sm:text-lg md:text-xl text-slate-700 dark:text-slate-300 max-w-2xl mx-auto px-2">
            Discover, compare, and choose the best AI-powered tools for your specific needs
          </p>
        </motion.div>

        <HeroSearch categories={categories} pricingOptions={pricingOptions} />

        <motion.div
          className="mt-6 sm:mt-8 flex flex-wrap justify-center gap-2 sm:gap-3 px-2"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <span className="text-xs sm:text-sm text-slate-500 dark:text-slate-400">Popular:</span>
          {popularCategories.map((category) => (
            <Link
              key={category.id}
              href={`/tools?category=${category.id}`}
              className="rounded-full bg-slate-100/80 dark:bg-slate-800/50 backdrop-blur-sm px-2 sm:px-3 py-1 text-xs sm:text-sm text-slate-700 dark:text-slate-300 hover:bg-slate-200/80 dark:hover:bg-slate-700/50 transition-colors hover:shadow-sm group"
            >
              <span className="relative">
                {category.name} ({category.count})
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-blue-500 transition-all duration-300 group-hover:w-full"></span>
              </span>
            </Link>
          ))}
        </motion.div>

        <motion.div
          className="mt-12 sm:mt-16 grid grid-cols-2 gap-3 sm:gap-4 lg:grid-cols-4"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.4 }}
        >
          <div className="text-center">
            <div className="text-2xl sm:text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-br from-blue-500 to-indigo-500">
              {statistics.toolCount || "300"}+
            </div>
            <div className="text-slate-500 dark:text-slate-400 mt-1 text-xs sm:text-sm">AI Tools</div>
          </div>
          <div className="text-center">
            <div className="text-2xl sm:text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-br from-indigo-500 to-purple-500">
              {statistics.categoryCount || "18"}
            </div>
            <div className="text-slate-500 dark:text-slate-400 mt-1 text-xs sm:text-sm">Categories</div>
          </div>
          <div className="text-center">
            <div className="text-2xl sm:text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-br from-purple-500 to-pink-500">
              {statistics.reviewCount || "1000"}+
            </div>
            <div className="text-slate-500 dark:text-slate-400 mt-1 text-xs sm:text-sm">Reviews</div>
          </div>
          <div className="text-center">
            <div className="text-2xl sm:text-3xl md:text-4xl font-bold bg-clip-text text-transparent bg-gradient-to-br from-pink-500 to-blue-500">
              <span className="animate-pulse inline-block h-2 w-2 sm:h-3 sm:w-3 rounded-full bg-blue-500 mr-1 sm:mr-2"></span>
              Weekly
            </div>
            <div className="text-slate-500 dark:text-slate-400 mt-1 text-xs sm:text-sm">Updates</div>
          </div>
        </motion.div>
      </div>
    </div>
  )
}
