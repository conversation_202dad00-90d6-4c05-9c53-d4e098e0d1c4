"use client"

import { useState, useEffect, useRef } from "react"
import { cn } from "@/lib/utils"
import { Activity, Star, Zap } from "lucide-react"

interface AnimatedStatProps {
  value: number
  label: string
  suffix?: string
  prefix?: string
  valueClassName?: string
  labelClassName?: string
  disableAnimation?: boolean
  duration?: number
  icon?: "star" | "zap" | "activity"
  iconColor?: string
}

export default function AnimatedStatClient({
  value,
  label,
  suffix = "",
  prefix = "",
  valueClassName = "",
  labelClassName = "",
  disableAnimation = false,
  duration = 2000,
  icon,
  iconColor = "text-primary",
}: AnimatedStatProps) {
  const [displayValue, setDisplayValue] = useState(0)
  const startTimeRef = useRef<number | null>(null)
  const animationFrameRef = useRef<number | null>(null)
  const isAnimatingRef = useRef(false)

  useEffect(() => {
    if (disableAnimation) {
      setDisplayValue(value)
      return
    }

    const animate = (timestamp: number) => {
      if (!startTimeRef.current) {
        startTimeRef.current = timestamp
      }

      const elapsed = timestamp - startTimeRef.current
      const progress = Math.min(elapsed / duration, 1)
      
      // Easing function for smoother animation
      const easedProgress = 1 - Math.pow(1 - progress, 3)
      
      setDisplayValue(Math.floor(easedProgress * value))

      if (progress < 1) {
        animationFrameRef.current = requestAnimationFrame(animate)
      } else {
        isAnimatingRef.current = false
        setDisplayValue(value)
      }
    }

    if (!isAnimatingRef.current) {
      isAnimatingRef.current = true
      startTimeRef.current = null
      animationFrameRef.current = requestAnimationFrame(animate)
    }

    return () => {
      if (animationFrameRef.current) {
        cancelAnimationFrame(animationFrameRef.current)
      }
    }
  }, [value, disableAnimation, duration])

  const renderIcon = () => {
    if (!icon) return null

    const iconProps = {
      className: cn("h-5 w-5 mr-2", iconColor),
    }

    switch (icon) {
      case "star":
        return <Star {...iconProps} />
      case "zap":
        return <Zap {...iconProps} />
      case "activity":
        return <Activity {...iconProps} />
      default:
        return null
    }
  }

  return (
    <div className="text-center">
      <div className={cn("text-3xl md:text-4xl font-bold flex items-center justify-center", valueClassName)}>
        {renderIcon()}
        {prefix}
        {displayValue.toLocaleString()}
        {suffix}
      </div>
      <div className={cn("text-muted-foreground mt-1", labelClassName)}>{label}</div>
    </div>
  )
}
