"use client"

import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"

interface TextCyclerProps {
  texts: string[]
  interval?: number
  className?: string
}

export default function TextCyclerClient({
  texts,
  interval = 3000,
  className,
}: TextCyclerProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isVisible, setIsVisible] = useState(true)

  useEffect(() => {
    if (texts.length <= 1) return

    const timer = setInterval(() => {
      setIsVisible(false)

      setTimeout(() => {
        setCurrentIndex((prevIndex) => (prevIndex + 1) % texts.length)
        setIsVisible(true)
      }, 150) // Half of transition duration
    }, interval)

    return () => clearInterval(timer)
  }, [texts.length, interval])

  return (
    <span
      className={cn(
        "transition-opacity duration-300 text-[rgb(182,71,209)]",
        isVisible ? "opacity-100" : "opacity-0",
        className
      )}
    >
      {texts[currentIndex]}
    </span>
  )
}
