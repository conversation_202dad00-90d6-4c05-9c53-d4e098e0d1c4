"use client"

import React, { useEffect, useRef, useState } from "react"
import { cn } from "@/lib/utils"

interface MotionWrapperProps {
  children: React.ReactNode
  className?: string
  animation?:
    | "fadeIn"
    | "slideUp"
    | "slideDown"
    | "slideInRight"
    | "scaleIn"
    | "none"
  delay?: "delay-100" | "delay-200" | "delay-300" | "delay-400" | "delay-500" | "none"
  threshold?: number
  once?: boolean
}

function MotionWrapper({
  children,
  className,
  animation = "fadeIn",
  delay = "none",
  threshold = 0.1,
  once = true,
}: MotionWrapperProps) {
  // For SSR, start with a simple non-animated state
  const [isVisible, setIsVisible] = useState(false)
  const [isMounted, setIsMounted] = useState(false)
  const ref = useRef<HTMLDivElement>(null)

  // First useEffect just to mark when component is mounted
  useEffect(() => {
    setIsMounted(true)

    // Set isVisible to true immediately for all animations to prevent disappearing elements
    setIsVisible(true)
  }, [])

  // Second useEffect to handle intersection observer, only runs client-side
  useEffect(() => {
    // Skip if not mounted yet or if animation is "none"
    if (!isMounted || animation === "none") {
      return
    }

    const current = ref.current
    if (!current) return

    // Set a timeout to ensure visibility even if intersection observer fails
    const visibilityTimeout = setTimeout(() => {
      setIsVisible(true)
    }, 500)

    const observer = new IntersectionObserver(
      ([entry]) => {
        if (entry.isIntersecting) {
          setIsVisible(true)
          if (once) observer.unobserve(current)
        } else if (!once && threshold > 0.5) {
          // Only hide elements if they're significantly out of view and not set to once
          setIsVisible(false)
        }
      },
      { threshold, rootMargin: '10px' }
    )

    observer.observe(current)

    return () => {
      if (current) {
        observer.unobserve(current)
      }
      clearTimeout(visibilityTimeout)
    }
  }, [once, threshold, isMounted, animation])

  // Define animation classes - moved outside of render to avoid recalculation
  const getAnimationClass = () => {
    if (animation === "none") return ""

    switch (animation) {
      case "fadeIn":
        return "animate-fadeIn"
      case "slideUp":
        return "animate-slideUp"
      case "slideDown":
        return "animate-slideDown"
      case "slideInRight":
        return "animate-slideInRight"
      case "scaleIn":
        return "animate-scaleIn"
      default:
        return "animate-fadeIn"
    }
  }

  // For SSR or when animation is disabled, render without animation classes
  if (!isMounted || animation === "none") {
    return <div className={className}>{children}</div>
  }

  // For client-side with animations
  return (
    <div
      ref={ref}
      className={cn(
        // Always include the base className
        className,
        // Only add animation classes when mounted on client
        isMounted ? (
          isVisible ? "opacity-100" : "opacity-0"
        ) : "",
        isMounted && isVisible && getAnimationClass(),
        isMounted && isVisible && (delay === "none" ? "" : delay)
      )}
    >
      {children}
    </div>
  )
}

export default MotionWrapper
export { MotionWrapper }
