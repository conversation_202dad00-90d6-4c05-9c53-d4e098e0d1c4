import { Metadata } from 'next'
import { createServerClient } from '@/lib/supabase/server'
import CategoryClient from '@/components/category/CategoryClient'
import { generateCategoryStructuredData, generateBreadcrumbData, combineStructuredData } from '@/lib/seo/structured-data'

export const dynamic = 'force-dynamic'

interface Props {
  params: {
    slug: string
  }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  // Get the slug parameter safely
  const slug = String(params?.slug || '');
  // Decode the category from URL
  const categoryFromUrl = decodeURIComponent(slug).replace(/\+/g, ' ')

  try {
    const supabase = await createServerClient()

    // Get tools count for this category
    const { count } = await supabase
      .from('tools')
      .select('*', { count: 'exact', head: true })
      .eq('primary_task', categoryFromUrl)

    const toolsCount = count || 0

    const title = `Best ${categoryFromUrl} AI Tools 2024 - ${toolsCount}+ Tools | AiAnyTool.com`
    const description = `Discover the best ${categoryFromUrl} AI tools. Compare ${toolsCount}+ ${categoryFromUrl} tools with reviews, pricing, and features. Find the perfect AI solution for your ${categoryFromUrl.toLowerCase()} needs.`

    return {
      title,
      description,
      keywords: [
        categoryFromUrl,
        `${categoryFromUrl} AI tools`,
        `best ${categoryFromUrl} tools`,
        `${categoryFromUrl} software`,
        'AI tools',
        'comparison',
        'reviews',
        '2024'
      ],
      openGraph: {
        title,
        description,
        url: `https://aianytool.com/category/${slug}`,
        type: 'website',
        locale: 'en_US',
        siteName: 'AiAnyTool.com',
        images: [
          {
            url: `https://aianytool.com/og-category.jpg`,
            secureUrl: `https://aianytool.com/og-category.jpg`,
            width: 1200,
            height: 630,
            alt: `${categoryFromUrl} AI Tools Directory - ${toolsCount}+ Curated Tools | AiAnyTool.com`,
            type: 'image/jpeg',
          },
          {
            url: `https://aianytool.com/og-category-square.jpg`,
            secureUrl: `https://aianytool.com/og-category-square.jpg`,
            width: 1200,
            height: 1200,
            alt: `Best ${categoryFromUrl} AI Tools Collection`,
            type: 'image/jpeg',
          }
        ],
        countryName: 'United States',
        // Add category-specific metadata
        ...(toolsCount > 0 && {
          article: {
            section: categoryFromUrl,
            tags: [categoryFromUrl, `${categoryFromUrl} AI tools`, 'AI directory', 'tool comparison'],
          }
        }),
      },
      twitter: {
        card: 'summary_large_image',
        title: title.length > 70 ? title.substring(0, 67) + '...' : title,
        description: description.length > 200 ? description.substring(0, 197) + '...' : description,
        images: [`https://aianytool.com/og-category.jpg`],
        creator: '@aianytool',
        site: '@aianytool',
      },
      alternates: {
        canonical: `https://aianytool.com/category/${slug}`,
      },
    }
  } catch (error) {
    console.error('Error generating category metadata:', error)
    return {
      title: `${categoryFromUrl} AI Tools | AiAnyTool.com`,
      description: `Discover AI tools for ${categoryFromUrl.toLowerCase()}.`,
    }
  }
}

export default async function CategoryPage({ params }: Props) {
  const supabase = await createServerClient()

  // Get the slug parameter safely
  const slug = String(params?.slug || '');
  console.log(`Category page: Processing slug "${slug}"`);

  // Decode the category from URL
  const categoryFromUrl = decodeURIComponent(slug).replace(/\+/g, ' ');
  console.log(`Category page: Decoded category "${categoryFromUrl}"`);

  // Try to find the exact category
  const { data: allTasks, error: tasksError } = await supabase
    .from("tools")
    .select("primary_task")
    .not("primary_task", "is", null);

  if (tasksError) {
    console.error(`Error fetching tasks: ${tasksError.message}`);
    return (
      <div className="min-h-screen bg-background pt-24 pb-16">
        <div className="container mx-auto px-4">
          <h1 className="text-2xl font-bold mb-4">Error Loading Category</h1>
          <p>There was an error loading the category data. Please try again later.</p>
        </div>
      </div>
    );
  }

  // Extract unique primary_task values
  const uniqueTasks = Array.from(new Set(
    allTasks?.map(item => item.primary_task).filter(Boolean) || []
  ));

  console.log(`Category page: Found ${uniqueTasks.length} unique tasks`);

  // Find exact matches (case insensitive)
  const exactMatches = uniqueTasks.filter(task => {
    if (!task) return false;
    return task.toLowerCase() === categoryFromUrl.toLowerCase();
  });

  // Use the first exact match from database for perfect query
  const exactMatch = exactMatches.length > 0 ? exactMatches[0] : categoryFromUrl

  // Fetch tools for this category
  const { data: tools } = await supabase
    .from("tools")
    .select("*")
    .eq("primary_task", exactMatch)
    .order("company_name")

  // If no exact match found, try ILIKE search
  let toolsData = tools

  if (!tools || tools.length === 0) {
    const { data: likeTools } = await supabase
      .from("tools")
      .select("*")
      .ilike("primary_task", `%${categoryFromUrl}%`)
      .order("company_name")

    toolsData = likeTools
  }

  // If still no tools found, try searching in tags
  if (!toolsData || toolsData.length === 0) {
    const { data: tagTools } = await supabase
      .from("tools")
      .select("*")
      .contains("tags", [categoryFromUrl])
      .order("company_name")

    toolsData = tagTools
  }

  // If still no tools found, try broader search with % wildcards
  if (!toolsData || toolsData.length === 0) {
    const { data: wildcardTools } = await supabase
      .from("tools")
      .select("*")
      .ilike("primary_task", `%${categoryFromUrl}%`)
      .order("company_name")
      .limit(20)

    toolsData = wildcardTools

    // If still no tools found, search in descriptions and name
    if (!wildcardTools || wildcardTools.length === 0) {
      const { data: contentTools } = await supabase
        .from("tools")
        .select("*")
        .or(`company_name.ilike.%${categoryFromUrl}%,description.ilike.%${categoryFromUrl}%`)
        .limit(20)
        .order("rating", { ascending: false })

      toolsData = contentTools

      // As a last resort, show some popular tools
      if (!contentTools || contentTools.length === 0) {
        const { data: popularTools } = await supabase
          .from("tools")
          .select("*")
          .limit(12)
          .order("rating", { ascending: false })

        toolsData = popularTools
      }
    }
  }

  console.log(`Category page: Found ${toolsData?.length || 0} tools for category "${categoryFromUrl}"${exactMatch !== categoryFromUrl ? ` (exact match: "${exactMatch}")` : ''}`)

  // Generate structured data
  const finalCategory = exactMatch || categoryFromUrl
  const toolsCount = toolsData?.length || 0
  const categoryStructuredData = generateCategoryStructuredData(finalCategory, toolsCount, toolsData || [])
  const breadcrumbData = generateBreadcrumbData([
    { name: 'Home', url: 'https://aianytool.com' },
    { name: 'Categories', url: 'https://aianytool.com/categories' },
    { name: finalCategory, url: `https://aianytool.com/category/${slug}` }
  ])

  const structuredDataScript = combineStructuredData([categoryStructuredData, breadcrumbData])

  return (
    <div className="min-h-screen bg-background pt-24 pb-16">
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: structuredDataScript }}
      />
      <CategoryClient
        category={finalCategory}
        tools={toolsData || []}
      />
    </div>
  )
}
