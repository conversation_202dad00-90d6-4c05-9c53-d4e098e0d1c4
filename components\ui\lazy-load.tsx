"use client"

import { useState, useEffect, Suspense, memo } from 'react'
import { useInView } from 'react-intersection-observer'

interface LazyLoadProps {
  children: React.ReactNode
  fallback?: React.ReactNode
  threshold?: number
  rootMargin?: string
  triggerOnce?: boolean
  height?: string | number
  width?: string | number
  priority?: boolean
  id?: string
  className?: string
  skipHydration?: boolean
}

// استخدام memo لمنع إعادة التصيير غير الضرورية
export const LazyLoad = memo(function LazyLoad({
  children,
  fallback = <div className="h-32 w-full animate-pulse bg-muted/30 rounded-md"></div>,
  threshold = 0.1,
  rootMargin = '300px 0px', // زيادة هامش التحميل المسبق
  triggerOnce = true,
  height,
  width,
  priority = false,
  id,
  className = '',
  skipHydration = false,
}: LazyLoadProps) {
  const [isClient, setIsClient] = useState(false)
  const [isVisible, setIsVisible] = useState(false)

  // استخدام IntersectionObserver للتحميل البطيء
  const { ref, inView } = useInView({
    threshold,
    rootMargin,
    triggerOnce,
    skip: priority, // تخطي IntersectionObserver إذا كانت ذات أولوية
  })

  // تجنب أخطاء عدم تطابق الترميز بين الخادم والعميل
  useEffect(() => {
    setIsClient(true)

    // إذا كانت ذات أولوية، اجعلها مرئية فورًا
    if (priority) {
      setIsVisible(true)
    }
  }, [priority])

  // تحديث الرؤية عند تغيير inView
  useEffect(() => {
    if (inView) {
      setIsVisible(true)
    }
  }, [inView])

  // إذا لم يكن في العميل وتم تخطي الترطيب، عرض المحتوى مباشرة
  if (!isClient && skipHydration) {
    return <>{children}</>
  }

  // تحديد ما إذا كان يجب عرض المحتوى
  const shouldRenderContent = priority || isVisible;

  return (
    <div
      ref={!priority ? ref : undefined}
      className={`w-full ${className}`}
      id={id}
      style={{
        height: !shouldRenderContent && height ? height : undefined,
        width: width,
        minHeight: !shouldRenderContent && height ? height : undefined,
      }}
    >
      {shouldRenderContent ? (
        <Suspense fallback={fallback}>
          {children}
        </Suspense>
      ) : (
        fallback
      )}
    </div>
  )
});
