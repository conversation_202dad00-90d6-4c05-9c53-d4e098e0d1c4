"use client"

import { useState, useEffect } from 'react'
import { toast } from 'sonner'

export interface FavoriteTool {
  id: string
  name: string
  logo_url?: string
  category?: string
  pricing?: string
}

export const useFavorites = () => {
  const [favorites, setFavorites] = useState<FavoriteTool[]>([])
  const [isLoading, setIsLoading] = useState(true)

  // Load favorites from localStorage on mount
  useEffect(() => {
    try {
      const saved = localStorage.getItem('favoriteTools')
      if (saved) {
        const parsedFavorites = JSON.parse(saved)
        setFavorites(Array.isArray(parsedFavorites) ? parsedFavorites : [])
      }
    } catch (error) {
      console.warn('Error loading favorites from localStorage:', error)
      setFavorites([])
    } finally {
      setIsLoading(false)
    }
  }, [])

  // Save favorites to localStorage whenever favorites change
  useEffect(() => {
    if (!isLoading) {
      try {
        localStorage.setItem('favoriteTools', JSON.stringify(favorites))
      } catch (error) {
        console.warn('Error saving favorites to localStorage:', error)
      }
    }
  }, [favorites, isLoading])

  const addToFavorites = (tool: FavoriteTool) => {
    if (favorites.find(fav => fav.id === tool.id)) {
      toast.info(`${tool.name} is already in your favorites`)
      return false
    }

    setFavorites(prev => [...prev, tool])
    toast.success(`${tool.name} added to favorites`)
    return true
  }

  const removeFromFavorites = (toolId: string) => {
    const tool = favorites.find(fav => fav.id === toolId)
    if (!tool) return false

    setFavorites(prev => prev.filter(fav => fav.id !== toolId))
    toast.success(`${tool.name} removed from favorites`)
    return true
  }

  const toggleFavorite = (tool: FavoriteTool) => {
    const isFavorite = favorites.some(fav => fav.id === tool.id)
    
    if (isFavorite) {
      return removeFromFavorites(tool.id)
    } else {
      return addToFavorites(tool)
    }
  }

  const isFavorite = (toolId: string) => {
    return favorites.some(fav => fav.id === toolId)
  }

  const clearAllFavorites = () => {
    setFavorites([])
    toast.success('All favorites cleared')
  }

  const getFavoritesByCategory = (category?: string) => {
    if (!category || category === 'all') return favorites
    return favorites.filter(fav => fav.category === category)
  }

  const getFavoriteCount = () => favorites.length

  const exportFavorites = () => {
    try {
      const dataStr = JSON.stringify(favorites, null, 2)
      const dataBlob = new Blob([dataStr], { type: 'application/json' })
      const url = URL.createObjectURL(dataBlob)
      
      const link = document.createElement('a')
      link.href = url
      link.download = `ai-tools-favorites-${new Date().toISOString().split('T')[0]}.json`
      document.body.appendChild(link)
      link.click()
      document.body.removeChild(link)
      URL.revokeObjectURL(url)
      
      toast.success('Favorites exported successfully')
    } catch (error) {
      console.error('Error exporting favorites:', error)
      toast.error('Failed to export favorites')
    }
  }

  const importFavorites = (file: File) => {
    return new Promise<void>((resolve, reject) => {
      const reader = new FileReader()
      
      reader.onload = (e) => {
        try {
          const content = e.target?.result as string
          const importedFavorites = JSON.parse(content)
          
          if (!Array.isArray(importedFavorites)) {
            throw new Error('Invalid file format')
          }
          
          // Validate structure
          const validFavorites = importedFavorites.filter(fav => 
            fav && typeof fav.id === 'string' && typeof fav.name === 'string'
          )
          
          if (validFavorites.length === 0) {
            throw new Error('No valid favorites found in file')
          }
          
          // Merge with existing favorites (avoid duplicates)
          const existingIds = new Set(favorites.map(fav => fav.id))
          const newFavorites = validFavorites.filter(fav => !existingIds.has(fav.id))
          
          if (newFavorites.length > 0) {
            setFavorites(prev => [...prev, ...newFavorites])
            toast.success(`Imported ${newFavorites.length} new favorites`)
          } else {
            toast.info('No new favorites to import')
          }
          
          resolve()
        } catch (error) {
          console.error('Error importing favorites:', error)
          toast.error('Failed to import favorites: Invalid file format')
          reject(error)
        }
      }
      
      reader.onerror = () => {
        toast.error('Failed to read file')
        reject(new Error('File read error'))
      }
      
      reader.readAsText(file)
    })
  }

  return {
    favorites,
    isLoading,
    addToFavorites,
    removeFromFavorites,
    toggleFavorite,
    isFavorite,
    clearAllFavorites,
    getFavoritesByCategory,
    getFavoriteCount,
    exportFavorites,
    importFavorites
  }
}
