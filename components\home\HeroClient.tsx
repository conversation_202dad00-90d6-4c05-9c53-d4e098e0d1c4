"use client"

import { useState, useEffect } from "react"
import { <PERSON>rk<PERSON> } from "lucide-react"
import Link from "next/link"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import HeroSearch from "./HeroSearch"
import TextCycler<PERSON>lient from "@/components/ui/TextCyclerClient"
import AnimatedStatClient from "@/components/ui/AnimatedStatClient"
import GradientBackground from "@/components/ui/GradientBackground"
import MotionWrapper from "@/components/ui/MotionWrapper"
import DynamicToolsBadge from "@/components/ui/DynamicToolsBadge"

interface Category {
  id: string
  name: string
  count: number
}

// Project types for text cycling
const projectTypes = [
  "business",
  "content",
  "marketing",
  "design",
  "coding",
  "writing"
]

export default function HeroClient() {
  const [toolCount, setToolCount] = useState<number | null>(null)
  const [categories, setCategories] = useState<Category[]>([])
  const [uniqueCategories, setUniqueCategories] = useState<Set<string>>(new Set())
  const [reviewCount, setReviewCount] = useState<number | null>(null)
  const [pricingOptions, setPricingOptions] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const fetchData = async () => {
      try {
        setIsLoading(true)
        const supabase = createBrowserClient()

        // Fetch tool count
        const { count: toolsCount, error: toolsError } = await supabase
          .from("tools")
          .select("*", { count: "exact", head: true })

        if (toolsError) throw toolsError
        setToolCount(toolsCount)

        // Fetch categories
        const { data: categoryData, error: categoryError } = await supabase
          .from("tools")
          .select("primary_task")

        if (categoryError) throw categoryError

        // Count categories
        const categoryCounts: Record<string, number> = {}
        const uniqueCats = new Set<string>()

        categoryData?.forEach(tool => {
          if (tool.primary_task) {
            categoryCounts[tool.primary_task] = (categoryCounts[tool.primary_task] || 0) + 1
            uniqueCats.add(tool.primary_task)
          }
        })

        setUniqueCategories(uniqueCats)

        const formattedCategories: Category[] = Object.entries(categoryCounts)
          .filter(([name]) => name)
          .map(([name, count]) => ({
            id: name.toLowerCase().replace(/\s+/g, '-'),
            name,
            count
          }))
          .sort((a, b) => b.count - a.count)

        setCategories(formattedCategories)

        // Fetch review count
        const { count: reviews, error: reviewsError } = await supabase
          .from("reviews")
          .select("*", { count: "exact", head: true })

        if (reviewsError) throw reviewsError
        setReviewCount(reviews)

        // Fetch pricing options
        const { data: pricingData, error: pricingError } = await supabase
          .from("tools")
          .select("pricing")

        if (pricingError) throw pricingError

        const uniquePricing = Array.from(new Set(
          pricingData?.map(tool => tool.pricing).filter(Boolean) || []
        ))

        setPricingOptions(uniquePricing)
      } catch (error) {
        console.error("Error fetching hero data:", error)
      } finally {
        setIsLoading(false)
      }
    }

    fetchData()
  }, [])

  const popularCategories = categories.slice(0, 5)

  return (
    <GradientBackground variant="primary" className="pt-32 pb-16 md:pt-40 md:pb-24">
      <div className="container-tight relative z-10">
        <MotionWrapper animation="fadeIn" className="text-center">
          <DynamicToolsBadge />

          <h1 className="mt-6 text-4xl sm:text-5xl md:text-6xl lg:text-7xl font-bold tracking-tight">
            <span className="text-gradient">AI tools</span> for{" "}
            <TextCyclerClient
              texts={projectTypes}
              interval={2500}
              className="inline-block"
            />
            {" "}projects
          </h1>

          <p className="mt-6 text-lg md:text-xl text-muted-foreground max-w-2xl mx-auto">
            Discover, compare, and choose the best AI-powered tools for your specific needs
          </p>
        </MotionWrapper>

        <HeroSearch categories={categories} pricingOptions={pricingOptions} />

        <MotionWrapper animation="fadeIn" delay="delay-300" className="mt-8">
          <div className="flex flex-wrap justify-center gap-3">
            <span className="text-sm text-muted-foreground">Popular:</span>
            {popularCategories.map((category) => (
              <Link
                key={category.id}
                href={`/tools?category=${category.id}`}
                className="rounded-full bg-secondary/50 backdrop-blur-sm px-3 py-1 text-sm text-foreground/70 hover:bg-secondary/80 hover:text-foreground transition-colors hover:shadow-sm group"
              >
                <span className="relative">
                  {category.name} ({category.count})
                  <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
                </span>
              </Link>
            ))}
          </div>
        </MotionWrapper>

        <MotionWrapper animation="fadeIn" delay="delay-400" className="mt-16">
          <div className="grid grid-cols-2 gap-4 sm:grid-cols-4">
            <AnimatedStatClient
              value={toolCount || 300}
              label="AI Tools"
              suffix="+"
              valueClassName="bg-gradient-to-br from-primary to-accent bg-clip-text text-transparent"
              disableAnimation={false}
              duration={3000}
              icon="zap"
              iconColor="text-primary"
            />
            <AnimatedStatClient
              value={uniqueCategories.size || 18}
              label="Categories"
              valueClassName="bg-gradient-to-br from-accent to-primary bg-clip-text text-transparent"
              disableAnimation={false}
              duration={2500}
              icon="activity"
              iconColor="text-accent"
            />
            <AnimatedStatClient
              value={reviewCount || 1000}
              label="Reviews"
              suffix="+"
              valueClassName="bg-gradient-to-br from-primary to-accent bg-clip-text text-transparent"
              disableAnimation={false}
              duration={2800}
              icon="star"
              iconColor="text-primary"
            />
            <div className="text-center">
              <div className="text-3xl md:text-4xl font-bold flex items-center justify-center bg-gradient-to-br from-accent to-primary bg-clip-text text-transparent">
                <span className="animate-pulse inline-block h-3 w-3 rounded-full bg-primary mr-2"></span>
                Weekly
              </div>
              <div className="text-muted-foreground mt-1">Updates</div>
            </div>
          </div>
        </MotionWrapper>
      </div>
    </GradientBackground>
  )
}
