"use client"

import Link from "next/link"
import { <PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON>, <PERSON>, Star } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import GradientBackground from "@/components/ui/GradientBackground"
import MotionWrapper from "@/components/ui/MotionWrapper"
import GlassCard from "@/components/ui/GlassCard"

export default function CallToAction() {
  return (
    <div className="relative">
      <GradientBackground variant="primary" className="py-12 md:py-16" intensity="light">
        <div className="container-tight">
          <MotionWrapper animation="fadeIn">
            <GlassCard
              className="bg-background/80 backdrop-blur-lg border border-border dark:bg-background/30 p-6 md:p-10 text-center"
              glowEffect
              hoverEffect
            >
              <div className="relative mb-5">
                <Sparkles size={36} className="mx-auto text-primary" />
              </div>

              <h2 className="text-xl sm:text-2xl font-bold">
                Ready to find your <span className="text-primary">perfect AI tool?</span>
              </h2>
              <p className="mt-3 text-muted-foreground max-w-2xl mx-auto text-sm">
                Browse our curated collection of the best AI tools to boost your productivity, creativity, and efficiency.
              </p>
              <div className="mt-6 flex flex-col sm:flex-row gap-3 justify-center">
                <Button
                  className="rounded-lg px-5 py-2 font-medium relative overflow-hidden group"
                  variant="default"
                  asChild
                >
                  <Link href="/tools">
                    <span className="relative z-10 flex items-center">
                      <Search size={16} className="mr-2" />
                      Explore Tools
                      <ArrowRight size={16} className="ml-2 transition-transform group-hover:translate-x-1" />
                    </span>
                  </Link>
                </Button>
                <Button
                  variant="outline"
                  className="rounded-lg px-5 py-2 font-medium"
                  asChild
                >
                  <Link href="/categories" className="flex items-center">
                    <Star size={16} className="mr-2" />
                    Browse Categories
                  </Link>
                </Button>
              </div>
            </GlassCard>
          </MotionWrapper>
        </div>
      </GradientBackground>
    </div>
  )
}
