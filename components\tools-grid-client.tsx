"use client"

import { useState, useEffect } from "react"
import { useRouter, useSearchParams } from "next/navigation"
import { supabase } from "@/lib/supabase/client"
import ToolCard from "@/components/tool-card"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "./ui/select"
import { Loader2, SlidersHorizontal, Check } from "lucide-react"
import { Button } from "./ui/button"
import { motion, AnimatePresence } from "framer-motion"

interface Tool {
  id: number
  name: string
  company_name: string
  description: string
  logo_url?: string
  website_url?: string
  primary_task?: string
  slug?: string
  rating?: number
  pricing_type?: string
}

export default function ToolsGridClient({
  category = '', 
  initialTools = [], 
  showFilters = true,
  title = 'AI Tools',
  subtitle = 'Explore our selection of AI tools'
}) {
  const [tools, setTools] = useState<Tool[]>(initialTools)
  const [loading, setLoading] = useState(true)
  const [sortOption, setSortOption] = useState('popular')
  const [filterOption, setFilterOption] = useState('all')
  const [showFiltersMenu, setShowFiltersMenu] = useState(false)
  
  const router = useRouter()
  const searchParams = useSearchParams()
  const searchQuery = searchParams ? searchParams.get('q') : null
  
  // Load tools from Supabase
  useEffect(() => {
    const fetchTools = async () => {
      setLoading(true)
      try {
        let query = supabase
          .from('tools')
          .select('*')
        
        // Apply category filter if provided
        if (category) {
          query = query.ilike('primary_task', `%${category}%`)
        }
        
        // Apply search filter if provided
        if (searchQuery) {
          query = query.or(`company_name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%`)
        }
        
        // Apply additional filters
        if (filterOption !== 'all') {
          query = query.eq('pricing_type', filterOption)
        }
        
        // Sorting
        switch (sortOption) {
          case 'popular':
            query = query.order('rating', { ascending: false })
            break
          case 'newest':
            query = query.order('created_at', { ascending: false })
            break
          case 'name':
            query = query.order('company_name', { ascending: true })
            break
          default:
            query = query.order('rating', { ascending: false })
        }
        
        // Execute query and limit results
        const { data, error } = await query.limit(100)
        
        if (error) {
          console.error('Error fetching tools:', error)
          return
        }
        
        if (data) {
          setTools(data)
        }
      } catch (error) {
        console.error('Error in tools fetch:', error)
      } finally {
        setLoading(false)
      }
    }
    
    fetchTools()
  }, [category, searchQuery, sortOption, filterOption])

  return (
    <div className="w-full">
      <div className="mb-8">
        <h2 className="text-3xl font-bold mb-2">{title}</h2>
        <p className="text-slate-600 dark:text-slate-400">{subtitle}</p>
      </div>
      
      {showFilters && (
        <div className="mb-6">
          <div className="flex flex-wrap items-center justify-between gap-4">
            <div className="flex items-center gap-2">
              <Select value={sortOption} onValueChange={setSortOption}>
                <SelectTrigger className="w-[180px]">
                  <SelectValue placeholder="Sort by" />
                </SelectTrigger>
                <SelectContent>
                  <SelectItem value="popular">Most Popular</SelectItem>
                  <SelectItem value="newest">Newest First</SelectItem>
                  <SelectItem value="name">Alphabetical</SelectItem>
                </SelectContent>
              </Select>
              
              <Button 
                variant="outline" 
                size="icon"
                onClick={() => setShowFiltersMenu(!showFiltersMenu)}
                className={showFiltersMenu ? "bg-blue-50 text-blue-600 border-blue-200" : ""}
              >
                <SlidersHorizontal className="h-4 w-4" />
              </Button>
            </div>
            
            <div className="text-sm text-slate-500 dark:text-slate-400">
              {!loading && `Showing ${tools.length} tools`}
            </div>
          </div>
          
          <AnimatePresence>
            {showFiltersMenu && (
              <motion.div 
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: 'auto', opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                className="mt-3 bg-white dark:bg-slate-800 p-4 rounded-lg border border-slate-200 dark:border-slate-700 shadow-sm"
              >
                <div className="grid grid-cols-1 sm:grid-cols-3 gap-4">
    <div>
                    <label className="block text-sm font-medium mb-1">Pricing</label>
                    <Select value={filterOption} onValueChange={setFilterOption}>
                      <SelectTrigger>
                        <SelectValue placeholder="Filter by pricing" />
                      </SelectTrigger>
                      <SelectContent>
                        <SelectItem value="all">All Pricing</SelectItem>
                        <SelectItem value="free">Free</SelectItem>
                        <SelectItem value="freemium">Freemium</SelectItem>
                        <SelectItem value="paid">Paid</SelectItem>
                        <SelectItem value="subscription">Subscription</SelectItem>
                      </SelectContent>
                    </Select>
                  </div>
                  
                  <div className="flex items-end">
                    <Button 
                      variant="ghost" 
                      size="sm" 
                      onClick={() => {
                        setSortOption('popular')
                        setFilterOption('all')
                      }}
                      className="text-slate-500"
                    >
                      Reset Filters
          </Button>
                  </div>
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      )}
      
      {loading ? (
        <div className="flex justify-center items-center py-20">
          <Loader2 className="h-8 w-8 animate-spin text-slate-400" />
        </div>
      ) : tools.length > 0 ? (
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-5">
          {tools.map((tool) => (
            <ToolCard key={tool.id} tool={tool} />
          ))}
        </div>
      ) : (
        <div className="text-center py-16 bg-slate-50 dark:bg-slate-800/50 rounded-lg">
          <div className="mx-auto w-16 h-16 rounded-full bg-slate-100 dark:bg-slate-700 flex items-center justify-center mb-4">
            <Check className="h-8 w-8 text-slate-400" />
          </div>
          <h3 className="text-xl font-semibold mb-2">No tools found</h3>
          <p className="text-slate-500 dark:text-slate-400 max-w-md mx-auto">
            We couldn't find any tools matching your criteria. Try adjusting your filters or search terms.
          </p>
        </div>
      )}
    </div>
  )
}
