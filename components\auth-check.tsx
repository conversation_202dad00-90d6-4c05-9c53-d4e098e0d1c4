"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { But<PERSON> } from "@/components/ui/button"
import { Loader2 } from "lucide-react"
import Link from "next/link"

interface AuthCheckProps {
  children: React.ReactNode
  fallbackMessage?: string
  redirectTo?: string
}

export default function AuthCheck({
  children,
  fallbackMessage = "You need to sign in to access this feature.",
  redirectTo = "/auth"
}: AuthCheckProps) {
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const checkAuth = async () => {
      try {
        const supabase = createBrowserClient()

        // Get current session
        const { data: { session }, error: sessionError } = await supabase.auth.getSession()

        if (sessionError) {
          console.error('Session error:', sessionError)
          setError('Failed to verify authentication')
          setIsAuthenticated(false)
          setIsLoading(false)
          return
        }

        if (session?.user) {
          // User is authenticated
          setIsAuthenticated(true)
          setError(null)
        } else {
          // No session found
          setIsAuthenticated(false)
          setError(null)
        }
      } catch (err) {
        console.error('Auth check failed:', err)
        setError('Authentication check failed')
        setIsAuthenticated(false)
      } finally {
        setIsLoading(false)
      }
    }

    checkAuth()

    // Listen for auth state changes
    const supabase = createBrowserClient()
    const { data: { subscription } } = supabase.auth.onAuthStateChange(
      async (event, session) => {
        console.log('Auth state changed:', event, session?.user?.id)
        setIsAuthenticated(!!session?.user)
        setError(null)
      }
    )

    return () => {
      subscription.unsubscribe()
    }
  }, [])

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-12">
        <div className="flex items-center gap-2 text-muted-foreground">
          <Loader2 className="h-5 w-5 animate-spin" />
          <span>Checking authentication...</span>
        </div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="bg-destructive/10 border border-destructive/20 rounded-lg p-8 text-center">
        <h2 className="text-xl font-semibold text-destructive mb-2">Authentication Error</h2>
        <p className="text-muted-foreground mb-4">{error}</p>
        <Button onClick={() => window.location.reload()} variant="outline">
          Try Again
        </Button>
      </div>
    )
  }

  if (!isAuthenticated) {
    return (
      <div className="bg-card border rounded-lg shadow-sm p-8 text-center">
        <h2 className="text-2xl font-bold mb-4">Sign in Required</h2>
        <p className="text-muted-foreground mb-6">
          {fallbackMessage}
        </p>
        <div className="flex flex-col sm:flex-row gap-4 justify-center">
          <Button asChild size="lg">
            <Link href={redirectTo}>Sign In</Link>
          </Button>
          <Button asChild variant="outline" size="lg">
            <Link href="/auth?mode=signup">Create Account</Link>
          </Button>
        </div>
      </div>
    )
  }

  return <>{children}</>
}
