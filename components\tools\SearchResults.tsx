"use client"

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Star, ExternalLink, Heart, Award, TrendingUp, ArrowRight } from 'lucide-react'
import { createBrowserClient } from '@/lib/supabase/client-utils'
import Link from 'next/link'
import { cn } from '@/lib/utils'

interface Tool {
  id: string
  company_name: string
  short_description: string
  logo_url?: string
  url?: string
  rating?: number
  pricing?: string
  primary_task?: string
  is_featured: boolean
  is_verified: boolean
  created_at: string
}

interface SearchResultsProps {
  searchQuery: string
  onResultsChange?: (count: number) => void
  className?: string
}

export default function SearchResults({
  searchQuery,
  onResultsChange,
  className
}: SearchResultsProps) {
  const [results, setResults] = useState<Tool[]>([])
  const [loading, setLoading] = useState(false)
  const [showAll, setShowAll] = useState(false)

  useEffect(() => {
    const fetchResults = async () => {
      if (!searchQuery.trim()) {
        setResults([])
        onResultsChange?.(0)
        return
      }

      setLoading(true)
      try {
        const supabase = createBrowserClient()

        const { data, error } = await supabase
          .from('tools')
          .select('*')
          .or(
            `company_name.ilike.%${searchQuery}%,short_description.ilike.%${searchQuery}%,full_description.ilike.%${searchQuery}%,primary_task.ilike.%${searchQuery}%`
          )
          .order('is_featured', { ascending: false })
          .order('rating', { ascending: false })
          .limit(50)

        if (error) {
          console.error('Error fetching search results:', error)
          return
        }

        setResults(data || [])
        onResultsChange?.(data?.length || 0)
      } catch (error) {
        console.error('Error fetching search results:', error)
      } finally {
        setLoading(false)
      }
    }

    const debounceTimer = setTimeout(fetchResults, 300)
    return () => clearTimeout(debounceTimer)
  }, [searchQuery, onResultsChange])

  if (!searchQuery.trim()) {
    return null
  }

  if (loading) {
    return (
      <div className={cn("space-y-4", className)}>
        <div className="text-center py-8">
          <div className="animate-spin h-8 w-8 border-2 border-primary border-t-transparent rounded-full mx-auto mb-4"></div>
          <p className="text-muted-foreground">Searching for "{searchQuery}"...</p>
        </div>
      </div>
    )
  }

  if (results.length === 0) {
    return (
      <div className={cn("space-y-4", className)}>
        <Card className="border-dashed">
          <CardContent className="p-8 text-center">
            <div className="w-16 h-16 mx-auto mb-4 bg-muted rounded-full flex items-center justify-center">
              <svg className="w-8 h-8 text-muted-foreground" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
              </svg>
            </div>
            <h3 className="text-lg font-semibold mb-2">No results found</h3>
            <p className="text-muted-foreground mb-4">
              No tools found for "{searchQuery}". Try different keywords or browse categories.
            </p>
            <Link href="/tools">
              <Button variant="outline">
                Browse All Tools
              </Button>
            </Link>
          </CardContent>
        </Card>
      </div>
    )
  }

  const displayedResults = showAll ? results : results.slice(0, 6)

  return (
    <div className={cn("space-y-6", className)}>
      {/* Results Header */}
      <div className="flex items-center justify-between">
        <div>
          <h2 className="text-2xl font-bold">
            Search Results for "{searchQuery}"
          </h2>
          <p className="text-muted-foreground">
            Found {results.length} tools matching your search
          </p>
        </div>
        
        {results.length > 6 && (
          <Button
            variant="outline"
            onClick={() => setShowAll(!showAll)}
          >
            {showAll ? 'Show Less' : `Show All ${results.length} Results`}
          </Button>
        )}
      </div>

      {/* Results Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
        {displayedResults.map((tool, index) => (
          <Card
            key={tool.id}
            className="group cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1 relative overflow-hidden"
          >
            {/* Featured Badge */}
            {tool.is_featured && (
              <div className="absolute top-3 right-3 z-10">
                <Badge className="bg-gradient-to-r from-amber-500 to-orange-500 text-white border-0 shadow-md text-xs">
                  <Star className="h-3 w-3 mr-1" />
                  Featured
                </Badge>
              </div>
            )}

            <CardContent className="p-5">
              {/* Tool Header */}
              <div className="flex items-start gap-3 mb-3">
                <div className="relative">
                  {tool.logo_url ? (
                    <img
                      src={tool.logo_url}
                      alt={tool.company_name}
                      className="w-10 h-10 rounded-lg object-cover border border-border"
                    />
                  ) : (
                    <div className="w-10 h-10 rounded-lg bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center border border-border">
                      <span className="text-sm font-bold text-primary">
                        {tool.company_name.charAt(0).toUpperCase()}
                      </span>
                    </div>
                  )}
                  {tool.is_verified && (
                    <div className="absolute -bottom-1 -right-1 w-4 h-4 bg-blue-500 rounded-full flex items-center justify-center">
                      <Star className="h-2 w-2 text-white fill-white" />
                    </div>
                  )}
                </div>

                <div className="flex-1 min-w-0">
                  <h3 className="font-semibold text-base group-hover:text-primary transition-colors truncate">
                    {tool.company_name}
                  </h3>
                  {tool.primary_task && (
                    <Badge variant="outline" className="text-xs mt-1">
                      {tool.primary_task}
                    </Badge>
                  )}
                </div>
              </div>

              {/* Description */}
              <p className="text-sm text-muted-foreground line-clamp-2 mb-3">
                {tool.short_description}
              </p>

              {/* Stats */}
              <div className="flex items-center gap-3 mb-4">
                {tool.rating && (
                  <div className="flex items-center gap-1">
                    <Star className="h-3 w-3 text-amber-500 fill-amber-500" />
                    <span className="text-xs font-medium">{tool.rating.toFixed(1)}</span>
                  </div>
                )}

                {tool.pricing && (
                  <Badge 
                    variant="secondary" 
                    className={cn(
                      "text-xs",
                      tool.pricing.toLowerCase().includes('free') 
                        ? "bg-green-50 text-green-700 border-green-200"
                        : "bg-blue-50 text-blue-700 border-blue-200"
                    )}
                  >
                    {tool.pricing}
                  </Badge>
                )}

                {/* New Badge */}
                {tool.created_at && new Date(tool.created_at) > new Date(Date.now() - 30 * 24 * 60 * 60 * 1000) && (
                  <Badge variant="secondary" className="bg-purple-50 text-purple-700 border-purple-200 text-xs">
                    <TrendingUp className="h-2 w-2 mr-1" />
                    New
                  </Badge>
                )}
              </div>

              {/* Actions */}
              <div className="flex gap-2">
                <Link href={`/Tool/${tool.slug || tool.company_name.toLowerCase().replace(/\s+/g, '-')}`} className="flex-1">
                  <Button variant="outline" size="sm" className="w-full text-xs group/btn">
                    <span>View Details</span>
                    <ArrowRight className="h-3 w-3 ml-1 transition-transform group-hover/btn:translate-x-0.5" />
                  </Button>
                </Link>

                {tool.url && tool.url !== "#" && (
                  <Button size="sm" className="px-2" asChild>
                    <a
                      href={tool.url}
                      target="_blank"
                      rel="noopener noreferrer"
                      className="flex items-center gap-1"
                    >
                      <ExternalLink className="h-3 w-3" />
                    </a>
                  </Button>
                )}

                <Button variant="ghost" size="sm" className="px-2 hover:text-red-500">
                  <Heart className="h-3 w-3" />
                </Button>
              </div>
            </CardContent>

            {/* Hover Effect */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/5 to-accent/5 opacity-0 group-hover:opacity-100 transition-opacity duration-300 pointer-events-none" />
          </Card>
        ))}
      </div>

      {/* View All Link */}
      {!showAll && results.length > 6 && (
        <div className="text-center">
          <Link href={`/tools?search=${encodeURIComponent(searchQuery)}`}>
            <Button size="lg" variant="outline" className="group">
              View All {results.length} Results
              <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
            </Button>
          </Link>
        </div>
      )}
    </div>
  )
}
