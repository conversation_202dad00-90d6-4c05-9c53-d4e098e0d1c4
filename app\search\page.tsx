import { Metada<PERSON> } from "next"
import HeroSearchResults from "@/components/search/hero-search-results"

export const dynamic = 'force-dynamic'

interface SearchPageProps {
  searchParams: Promise<{
    q?: string
    categories?: string
    pricing?: string
    verified?: string
    featured?: string
    free?: string
  }>
}

export async function generateMetadata({ searchParams }: SearchPageProps): Promise<Metadata> {
  const params = await searchParams
  const { q } = params

  const title = q ? `Search Results for "${q}"` : "Search AI Tools"
  const description = q
    ? `Find AI tools matching "${q}" in our comprehensive directory.`
    : "Search our comprehensive collection of AI tools for various tasks and needs."

  return {
    title,
    description,
    openGraph: { title, description, type: 'website' },
    twitter: { card: 'summary_large_image', title, description },
  }
}

export default async function SearchPage({ searchParams }: SearchPageProps) {
  const params = await searchParams

  return (
    <div className="min-h-screen bg-gradient-to-br from-slate-50 via-white to-slate-100 dark:from-slate-950 dark:via-slate-900 dark:to-slate-800 pt-16 sm:pt-20 lg:pt-24 pb-8 sm:pb-12 lg:pb-16">
      <div className="container mx-auto px-3 sm:px-4 lg:px-6">
        <div className="max-w-6xl mx-auto">
          <HeroSearchResults searchParams={params} />
        </div>
      </div>
    </div>
  )
}
