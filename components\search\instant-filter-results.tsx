"use client"

import React, { useEffect, useState } from "react"
import { createPortal } from "react-dom"
import { useRouter } from "next/navigation"
import { ArrowR<PERSON>, Filter, Layers, Folder, Wrench, Star, Clock } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import SearchLoadingSkeleton from "./search-loading-skeleton"

interface InstantFilterResultsProps {
  results: any[]
  isLoading: boolean
  isOpen: boolean
  selectedIndex: number
  query: string
  context: 'tools' | 'categories' | 'general'
  onResultSelect: (result: any) => void
  onViewAll?: () => void
  className?: string
  containerRef?: React.RefObject<HTMLElement>
}

// Helper function to get icon and styling for different contexts
function getContextInfo(context: string, result?: any) {
  switch (context) {
    case 'tools':
      return {
        icon: Wrench,
        label: 'Tool',
        color: 'text-blue-600 dark:text-blue-400',
        bgColor: 'bg-blue-50 dark:bg-blue-900/30',
        borderColor: 'border-blue-200 dark:border-blue-800',
        emoji: '🔧'
      }
    case 'categories':
      return {
        icon: Folder,
        label: 'Category',
        color: 'text-green-600 dark:text-green-400',
        bgColor: 'bg-green-50 dark:bg-green-900/30',
        borderColor: 'border-green-200 dark:border-green-800',
        emoji: '📁'
      }
    default:
      return {
        icon: Layers,
        label: 'Result',
        color: 'text-purple-600 dark:text-purple-400',
        bgColor: 'bg-purple-50 dark:bg-purple-900/30',
        borderColor: 'border-purple-200 dark:border-purple-800',
        emoji: '📋'
      }
  }
}

export default function InstantFilterResults({
  results,
  isLoading,
  isOpen,
  selectedIndex,
  query,
  context,
  onResultSelect,
  onViewAll,
  className,
  containerRef
}: InstantFilterResultsProps) {
  const router = useRouter()
  const [mounted, setMounted] = useState(false)
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null)
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 })

  useEffect(() => {
    setMounted(true)

    // Create or get portal container
    let container = document.getElementById('instant-filter-portal')
    if (!container) {
      container = document.createElement('div')
      container.id = 'instant-filter-portal'
      container.style.position = 'fixed'
      container.style.top = '0'
      container.style.left = '0'
      container.style.zIndex = '999999'
      container.style.pointerEvents = 'none'
      document.body.appendChild(container)
    }
    setPortalContainer(container)

    return () => {
      // Clean up portal container when component unmounts
      const container = document.getElementById('instant-filter-portal')
      if (container && container.children.length === 0) {
        document.body.removeChild(container)
      }
    }
  }, [])

  useEffect(() => {
    if (containerRef?.current && isOpen) {
      const rect = containerRef.current.getBoundingClientRect()
      setPosition({
        top: rect.bottom + window.scrollY,
        left: rect.left + window.scrollX,
        width: rect.width
      })
    }
  }, [containerRef, isOpen])

  if (!mounted || !isOpen || !query || query.trim().length === 0) return null

  // Show loading skeleton
  if (isLoading) {
    return portalContainer ? createPortal(
      <div
        className="fixed"
        style={{
          top: position.top,
          left: position.left,
          width: position.width,
          zIndex: 999999,
          pointerEvents: 'auto'
        }}
      >
        <SearchLoadingSkeleton className={className} itemCount={3} />
      </div>,
      portalContainer
    ) : null
  }

  const contextInfo = getContextInfo(context)
  const ContextIcon = contextInfo.icon

  const resultsContent = (
    <div
      className="fixed"
      style={{
        top: position.top,
        left: position.left,
        width: position.width,
        zIndex: 999999,
        pointerEvents: 'auto'
      }}
    >
      <Card
        className="max-h-80 sm:max-h-96 overflow-hidden shadow-2xl border border-border/50 bg-background/98 backdrop-blur-xl rounded-xl animate-in fade-in-0 zoom-in-95 duration-200"
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'
        }}
      >
        <CardContent className="p-0">
          {/* Results */}
          {results.length > 0 && (
            <>
              <div className="px-4 py-2 text-xs font-medium text-muted-foreground bg-muted/20 border-b border-border/30 flex items-center gap-2">
                <Filter className="h-3 w-3" />
                Filtered {results.length} {context === 'tools' ? 'tool' : context === 'categories' ? 'categor' : 'result'}{results.length !== 1 ? (context === 'categories' ? 'ies' : 's') : (context === 'categories' ? 'y' : '')}
              </div>
              <div className="max-h-48 sm:max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent">
                {results.map((result, index) => {
                  return (
                    <button
                      key={result.id || index}
                      onClick={() => onResultSelect(result)}
                      className={cn(
                        "w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-muted/50 transition-all duration-150 border-b border-border/20 last:border-b-0 focus:outline-none focus:bg-muted/50 focus:ring-2 focus:ring-primary/20 focus:ring-inset",
                        selectedIndex === index && "bg-muted/50"
                      )}
                    >
                      {/* Logo/Icon */}
                      <div className="relative w-10 h-10 rounded-lg bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 flex items-center justify-center flex-shrink-0 shadow-sm">
                        {result.logo_url ? (
                          <img
                            src={result.logo_url}
                            alt={result.company_name || result.name || 'Item'}
                            className="w-8 h-8 rounded-md object-cover shadow-sm"
                            onError={(e) => {
                              const target = e.target as HTMLImageElement
                              target.style.display = 'none'
                              target.nextElementSibling?.classList.remove('hidden')
                            }}
                          />
                        ) : null}
                        <ContextIcon className={cn("h-5 w-5", contextInfo.color, result.logo_url && "hidden")} />

                        {/* Context indicator badge */}
                        <div className={cn(
                          "absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-[10px] font-medium shadow-sm border-2 border-white dark:border-slate-900",
                          contextInfo.bgColor,
                          contextInfo.borderColor
                        )}>
                          <span className="text-[8px]">{contextInfo.emoji}</span>
                        </div>
                      </div>

                      {/* Content */}
                      <div className="flex-1 min-w-0">
                        <div className="flex items-center gap-2">
                          <h4 className="font-medium text-sm truncate">
                            {result.company_name || result.name || 'Unnamed Item'}
                          </h4>
                          
                          {/* Context badge */}
                          <span className={cn(
                            "inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-[9px] font-medium border",
                            contextInfo.bgColor,
                            contextInfo.color,
                            contextInfo.borderColor
                          )}>
                            <span className="text-[8px]">{contextInfo.emoji}</span>
                            {contextInfo.label}
                          </span>
                          
                          {result.is_verified && (
                            <Badge variant="outline" className="h-5 px-1 bg-blue-500/10 text-blue-600 border-blue-200 dark:border-blue-900 dark:bg-blue-500/20">
                              <Star className="h-3 w-3 mr-0.5 fill-current" />
                              <span className="text-[10px] font-medium">Verified</span>
                            </Badge>
                          )}
                        </div>
                        <p className="text-xs text-muted-foreground line-clamp-1">
                          {result.short_description || result.description || 'No description available'}
                        </p>
                        
                        {/* Additional info for tools */}
                        {context === 'tools' && (
                          <div className="flex items-center mt-1 gap-1.5 flex-wrap">
                            {/* Category */}
                            {result.primary_task && (
                              <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded-md text-[10px] font-medium bg-slate-50 text-slate-700 dark:bg-slate-800/50 dark:text-slate-300 border border-slate-200 dark:border-slate-700">
                                <Folder className="h-2.5 w-2.5" />
                                {result.primary_task}
                              </span>
                            )}

                            {/* Pricing */}
                            {result.pricing && (
                              <span className={cn(
                                "inline-flex items-center gap-1 px-2 py-0.5 rounded-md text-[10px] font-medium border",
                                result.pricing.toLowerCase().includes('free')
                                  ? "bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-800"
                                  : "bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-800"
                              )}>
                                <span className="text-[8px]">
                                  {result.pricing.toLowerCase().includes('free') ? '🆓' : '💰'}
                                </span>
                                {result.pricing}
                              </span>
                            )}
                          </div>
                        )}

                        {/* Additional info for categories */}
                        {context === 'categories' && result.tool_count && (
                          <div className="flex items-center mt-1 gap-1.5">
                            <span className="inline-flex items-center gap-1 text-[10px] text-muted-foreground bg-slate-50 dark:bg-slate-800/50 px-2 py-0.5 rounded-md border border-slate-200 dark:border-slate-700">
                              <Wrench className="h-2.5 w-2.5" />
                              {result.tool_count} tools
                            </span>
                          </div>
                        )}
                      </div>

                      <ArrowRight className="h-4 w-4 text-muted-foreground" />
                    </button>
                  )
                })}
              </div>

              {/* View All Button */}
              {onViewAll && (
                <div className="p-3 border-t border-border/30 bg-muted/10">
                  <Button
                    variant="ghost"
                    size="sm"
                    className="w-full text-xs justify-between hover:bg-primary/10 hover:text-primary transition-colors"
                    onClick={onViewAll}
                  >
                    View all filtered results
                    <ArrowRight className="h-3 w-3 ml-1" />
                  </Button>
                </div>
              )}
            </>
          )}

          {/* No Results */}
          {results.length === 0 && query && query.trim().length > 0 && (
            <div className="py-6 px-4 text-center">
              <div className="mb-3 flex justify-center">
                <div className="rounded-full bg-muted/50 p-3">
                  <ContextIcon className="h-5 w-5 text-muted-foreground" />
                </div>
              </div>
              <h3 className="font-medium mb-1 text-sm">No {context} found</h3>
              <p className="text-xs text-muted-foreground mb-4">
                No {context} match "{query.length > 20 ? query.substring(0, 20) + '...' : query}"
              </p>
              {onViewAll && (
                <Button
                  variant="outline"
                  size="sm"
                  className="text-xs hover:bg-primary/10 hover:text-primary hover:border-primary/50"
                  onClick={onViewAll}
                >
                  Browse all {context}
                  <ArrowRight className="h-3 w-3 ml-1" />
                </Button>
              )}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )

  return portalContainer ? createPortal(resultsContent, portalContainer) : null
}
