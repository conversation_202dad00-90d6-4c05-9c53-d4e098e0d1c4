import { Inter, Roboto_Mono } from 'next/font/google'
import localFont from 'next/font/local'

// Optimize font loading from Google Fonts
export const inter = Inter({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-inter',
  preload: true,
  fallback: ['system-ui', 'sans-serif'],
  adjustFontFallback: true,
})

export const robotoMono = Roboto_Mono({
  subsets: ['latin'],
  display: 'swap',
  variable: '--font-roboto-mono',
  preload: true,
  fallback: ['monospace'],
  adjustFontFallback: true,
})

// Use system font for headings to improve performance
// Local font disabled because file doesn't exist
// export const headingFont = localFont({
//   src: '../public/fonts/heading-font.woff2',
//   display: 'swap',
//   variable: '--font-heading',
//   preload: true,
//   fallback: ['system-ui', 'sans-serif'],
//   adjustFontFallback: true,
// })

// Use system font instead
export const headingFont = {
  variable: '--font-heading',
  // Use the same variables as the inter font
  className: inter.className,
  style: inter.style,
}
