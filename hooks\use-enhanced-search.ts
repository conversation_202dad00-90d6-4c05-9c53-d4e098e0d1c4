import { useState, useEffect, useCallback } from 'react'
import { useDebounce } from './use-debounce'
import { quickSearch, enhancedQuickSearch, SearchResult } from '@/lib/search/search-service'

// Use SearchResult from search service

interface UseEnhancedSearchProps {
  minQueryLength?: number
  debounceMs?: number
  maxResults?: number
  enhancedSearch?: boolean
  initialValue?: string
  filters?: {
    category?: string
    pricing?: string
    features?: string[]
  }
}

export function useEnhancedSearch({
  minQueryLength = 2,
  debounceMs = 200, // Optimized for better performance and reduced API calls
  maxResults = 8,   // Increased for better user experience
  enhancedSearch = false,
  initialValue = "",
  filters
}: UseEnhancedSearchProps = {}) {
  const [query, setQuery] = useState(initialValue)
  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [isOpen, setIsOpen] = useState(false)
  const [selectedIndex, setSelectedIndex] = useState(-1)

  const debouncedQuery = useDebounce(query, debounceMs)

  // Search function using real database
  const searchTools = useCallback(async (searchQuery: string) => {
    if (!searchQuery || searchQuery.length < minQueryLength) {
      setResults([])
      setIsLoading(false)
      return
    }

    setIsLoading(true)

    try {
      // Use quick search for all queries to avoid timeout issues
      // Enhanced search causes timeouts with longer queries
      console.log(`🔧 Using quick search for query: "${searchQuery}"`);

      const { data: searchResults, error } = await quickSearch(searchQuery, maxResults, filters)

      console.log(`📊 Search completed. Results: ${searchResults?.length || 0}, Error: ${error ? 'Yes' : 'No'}`);

      if (error) {
        console.warn('Search error:', error)

        // If it's a timeout error, show a user-friendly message
        if (error.message?.includes('timeout') || error.message?.includes('longer than expected')) {
          console.warn('Search timeout detected, showing empty results')
          setResults([])
          // Could optionally show a toast notification here
          return
        }

        // For other errors, just return empty results silently
        setResults([])
        return
      }

      // Transform results to match our interface and add enhanced relevance scoring
      const transformedResults = (searchResults || [])
        .map(tool => {
          const searchLower = searchQuery.toLowerCase().trim()
          const searchOriginal = searchQuery.trim()
          let score = 0

          // Name match gets highest score with exact match priority
          const name = (tool.company_name || '').toLowerCase()
          if (name.includes(searchLower)) {
            // Exact match gets maximum score
            if (name === searchLower) {
              score += 1000
            }
            // Starts with search term gets very high score
            else if (name.startsWith(searchLower)) {
              score += 500
            }
            // Contains search term gets high score
            else {
              score += 200
            }
          }

          // Description match
          const description = (tool.short_description || tool.full_description || '').toLowerCase()
          if (description.includes(searchLower)) {
            // Exact phrase match in description
            if (description.includes(searchLower)) {
              score += 50
            }
          }

          // Category match
          const category = (tool.primary_task || '').toLowerCase()
          if (category.includes(searchLower)) {
            // Exact category match
            if (category === searchLower) {
              score += 300
            }
            // Starts with search term
            else if (category.startsWith(searchLower)) {
              score += 150
            }
            // Contains search term
            else {
              score += 75
            }
          }

          // Boost verified, featured, and new tools (but less than exact matches)
          if (tool.is_verified) score += 15
          if (tool.is_featured) score += 12
          if (tool.is_new) score += 8

          // Boost popular tools slightly
          if (tool.click_count && tool.click_count > 100) score += 5

          return {
            ...tool,
            relevanceScore: score
          }
        })
        .sort((a, b) => (b as any).relevanceScore - (a as any).relevanceScore)
        .slice(0, maxResults)

      setResults(transformedResults)
    } catch (error) {
      console.error('Search error:', error)
      setResults([])
    } finally {
      setIsLoading(false)
    }
  }, [minQueryLength, maxResults, enhancedSearch, filters])

  // Effect for debounced search
  useEffect(() => {
    searchTools(debouncedQuery)
  }, [debouncedQuery, searchTools])

  // Handle input change
  const handleInputChange = useCallback((value: string) => {
    setQuery(value)
    setSelectedIndex(-1)

    if (value.length >= minQueryLength) {
      setIsOpen(true)
    } else {
      setIsOpen(false)
      setResults([])
    }
  }, [minQueryLength])

  // Handle keyboard navigation - Fixed Enter key handling
  const handleKeyDown = useCallback((e: React.KeyboardEvent) => {
    switch (e.key) {
      case 'ArrowDown':
        if (isOpen && results.length > 0) {
          e.preventDefault()
          setSelectedIndex(prev =>
            prev < results.length - 1 ? prev + 1 : prev
          )
        }
        break
      case 'ArrowUp':
        if (isOpen && results.length > 0) {
          e.preventDefault()
          setSelectedIndex(prev => prev > 0 ? prev - 1 : -1)
        }
        break
      case 'Enter':
        // Only prevent default if we're selecting from dropdown
        if (isOpen && results.length > 0 && selectedIndex >= 0 && results[selectedIndex]) {
          e.preventDefault()
          return results[selectedIndex]
        }
        // Otherwise, let the form submission happen naturally
        break
      case 'Escape':
        if (isOpen) {
          e.preventDefault()
          setIsOpen(false)
          setSelectedIndex(-1)
        }
        break
    }
  }, [isOpen, results, selectedIndex])

  // Close dropdown
  const closeDropdown = useCallback(() => {
    setIsOpen(false)
    setSelectedIndex(-1)
  }, [])

  // Clear search
  const clearSearch = useCallback(() => {
    setQuery('')
    setResults([])
    setIsOpen(false)
    setSelectedIndex(-1)
  }, [])

  return {
    query,
    results,
    isLoading,
    isOpen,
    selectedIndex,
    setQuery,
    handleInputChange,
    handleKeyDown,
    closeDropdown,
    clearSearch,
    setIsOpen
  }
}
