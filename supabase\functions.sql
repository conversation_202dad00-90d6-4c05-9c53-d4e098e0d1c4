-- Create function to get user profile by ID
CREATE OR REPLACE FUNCTION get_profile_by_id(user_id UUID)
RETURNS TABLE (
  id UUID,
  role TEXT,
  created_at TIMESTAMPTZ,
  updated_at TIMESTAMPTZ
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    p.id,
    p.role,
    p.created_at,
    p.updated_at
  FROM profiles p
  WHERE p.id = user_id;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to create a new profile
CREATE OR REPLACE FUNCTION create_new_profile(user_id UUID, user_role TEXT DEFAULT 'user')
RETURNS VOID AS $$
BEGIN
  INSERT INTO profiles (id, role, created_at, updated_at)
  VALUES (user_id, user_role, NOW(), NOW())
  ON CONFLICT (id) DO NOTHING;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to promote user to admin
CREATE OR REPLACE FUNCTION promote_to_admin(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  updated_rows INTEGER;
BEGIN
  UPDATE profiles 
  SET role = 'admin', updated_at = NOW()
  WHERE id = user_id;
  
  GET DIAGNOSTICS updated_rows = ROW_COUNT;
  RETURN updated_rows > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to demote admin to user
CREATE OR REPLACE FUNCTION demote_from_admin(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  updated_rows INTEGER;
BEGIN
  UPDATE profiles 
  SET role = 'user', updated_at = NOW()
  WHERE id = user_id;
  
  GET DIAGNOSTICS updated_rows = ROW_COUNT;
  RETURN updated_rows > 0;
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to check if user is admin
CREATE OR REPLACE FUNCTION is_admin(user_id UUID)
RETURNS BOOLEAN AS $$
DECLARE
  user_role TEXT;
BEGIN
  SELECT role INTO user_role
  FROM profiles
  WHERE id = user_id;
  
  RETURN COALESCE(user_role = 'admin', FALSE);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get user statistics
CREATE OR REPLACE FUNCTION get_user_stats(user_id UUID)
RETURNS TABLE (
  favorites_count BIGINT,
  reviews_count BIGINT,
  submissions_count BIGINT
) AS $$
BEGIN
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*) FROM favorites WHERE favorites.user_id = get_user_stats.user_id),
    (SELECT COUNT(*) FROM reviews WHERE reviews.user_id = get_user_stats.user_id),
    (SELECT COUNT(*) FROM tool_submissions WHERE tool_submissions.user_id = get_user_stats.user_id);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Create function to get admin dashboard stats
CREATE OR REPLACE FUNCTION get_admin_dashboard_stats()
RETURNS TABLE (
  total_users BIGINT,
  total_tools BIGINT,
  total_reviews BIGINT,
  total_favorites BIGINT,
  pending_submissions BIGINT,
  new_users_this_month BIGINT,
  new_tools_this_month BIGINT
) AS $$
DECLARE
  start_of_month DATE;
BEGIN
  start_of_month := DATE_TRUNC('month', CURRENT_DATE);
  
  RETURN QUERY
  SELECT 
    (SELECT COUNT(*) FROM profiles),
    (SELECT COUNT(*) FROM tools),
    (SELECT COUNT(*) FROM reviews),
    (SELECT COUNT(*) FROM favorites),
    (SELECT COUNT(*) FROM tool_submissions WHERE status = 'pending'),
    (SELECT COUNT(*) FROM profiles WHERE created_at >= start_of_month),
    (SELECT COUNT(*) FROM tools WHERE created_at >= start_of_month);
END;
$$ LANGUAGE plpgsql SECURITY DEFINER;

-- Grant execute permissions to authenticated users
GRANT EXECUTE ON FUNCTION get_profile_by_id(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION create_new_profile(UUID, TEXT) TO authenticated;
GRANT EXECUTE ON FUNCTION promote_to_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION demote_from_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION is_admin(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_user_stats(UUID) TO authenticated;
GRANT EXECUTE ON FUNCTION get_admin_dashboard_stats() TO authenticated;
