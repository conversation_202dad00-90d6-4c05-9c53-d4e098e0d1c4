import { createServerClient } from "@/lib/supabase/server"
import Tool<PERSON>ard from "@/components/tool-card"

export default async function ToolGrid({ category }: { category: string }) {
  const supabase = await createServerClient()
  const { data: tools, error } = await supabase
    .from("tools")
    .select("*")
    .eq("primary_task", category)
    .order("is_featured", { ascending: false })
    .order("click_count", { ascending: false })

  if (error) {
    console.error("Error fetching tools by category:", error.message)
    return (
      <div className="text-center py-12 bg-white dark:bg-slate-800 rounded-lg shadow-sm">
        <p className="text-slate-600 dark:text-slate-400">Error loading tools. Please try again later.</p>
      </div>
    )
  }

  if (!tools || tools.length === 0) {
    return (
      <div className="text-center py-12 bg-white dark:bg-slate-800 rounded-lg shadow-sm">
        <p className="text-slate-600 dark:text-slate-400">No tools available in this category yet.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {tools.map((tool) => (
        <ToolCard key={tool.id} tool={tool} />
      ))}
    </div>
  )
}
