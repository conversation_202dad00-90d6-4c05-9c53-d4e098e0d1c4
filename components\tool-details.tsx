"use client"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON> } from "@/components/ui/tabs"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"
import { Card, CardContent } from "@/components/ui/card"
import { Avatar, AvatarFallback } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { Star, CheckCircle, CheckSquare, XCircle, Calendar } from "lucide-react"
import Image from "next/image"
import { useState } from "react"
import { format } from "date-fns"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { toast } from "sonner"

interface Review {
  id: string
  rating: number
  comment?: string
  created_at: string
  user_id: string
  user_email?: string
}

interface ToolDetailsProps {
  tool: any
  reviews?: Review[]
}

export default function ToolDetails({ tool, reviews = [] }: ToolDetailsProps) {
  const [newReview, setNewReview] = useState({ rating: 5, comment: "" })
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [userReviews, setUserReviews] = useState<Review[]>(reviews)

  const handleReviewSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    try {
      setIsSubmitting(true)
      const supabase = createBrowserClient()

      // Check if user is authenticated
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        toast.error("Please sign in to submit a review")
        return
      }

      // Submit review
      const { data, error } = await supabase
        .from('reviews')
        .insert({
          tool_id: tool.id,
          rating: newReview.rating,
          comment: newReview.comment,
          user_id: session.user.id,
          user_email: session.user.email,
          created_at: new Date().toISOString(),
          updated_at: new Date().toISOString()
        })
        .select()

      if (error) throw error

      // Update reviews list
      setUserReviews([...(data || []), ...userReviews])
      setNewReview({ rating: 5, comment: "" })
      toast.success("Review submitted successfully")
    } catch (error: any) {
      console.error("Error submitting review:", error)
      toast.error(error.message || "Failed to submit review")
    } finally {
      setIsSubmitting(false)
    }
  }

  return (
    <div className="bg-background/60 dark:bg-card/60 backdrop-blur-xl border border-border/60 shadow-2xl shadow-primary/5 dark:shadow-primary/10 rounded-3xl overflow-hidden">
      <Tabs defaultValue="overview">
        {/* Simplified Tab Navigation */}
        <TabsList className="w-full border-b border-border/30 rounded-none p-0 bg-gradient-to-r from-secondary/20 via-secondary/10 to-secondary/20 dark:from-secondary/10 dark:via-secondary/5 dark:to-secondary/10 h-auto">
          <TabsTrigger
            value="overview"
            className="flex-1 rounded-none py-4 px-6 data-[state=active]:bg-background/90 dark:data-[state=active]:bg-card/90 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary font-medium transition-all duration-300 hover:bg-background/50 dark:hover:bg-card/50 text-sm sm:text-base"
          >
            <span className="hidden sm:inline">📋 Overview</span>
            <span className="sm:hidden">📋</span>
          </TabsTrigger>
          <TabsTrigger
            value="pros-cons"
            className="flex-1 rounded-none py-4 px-6 data-[state=active]:bg-background/90 dark:data-[state=active]:bg-card/90 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary font-medium transition-all duration-300 hover:bg-background/50 dark:hover:bg-card/50 text-sm sm:text-base"
          >
            <span className="hidden sm:inline">⚖️ Pros & Cons</span>
            <span className="sm:hidden">⚖️</span>
          </TabsTrigger>
          <TabsTrigger
            value="reviews"
            className="flex-1 rounded-none py-4 px-6 data-[state=active]:bg-background/90 dark:data-[state=active]:bg-card/90 data-[state=active]:border-b-2 data-[state=active]:border-primary data-[state=active]:text-primary font-medium transition-all duration-300 hover:bg-background/50 dark:hover:bg-card/50 text-sm sm:text-base"
          >
            <span className="hidden sm:inline">⭐ Reviews ({userReviews?.length || 0})</span>
            <span className="sm:hidden">⭐ {userReviews?.length || 0}</span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="overview" className="p-6 sm:p-8 lg:p-10">
          {/* Featured Image */}
          {tool.featured_image_url && (
            <div className="relative aspect-video w-full mb-8 rounded-3xl overflow-hidden shadow-2xl group">
              <Image
                src={tool.featured_image_url}
                alt={tool.company_name || "Featured image"}
                fill
                sizes="(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw"
                className="object-cover group-hover:scale-105 transition-transform duration-500"
                priority
              />
              <div className="absolute inset-0 bg-gradient-to-t from-black/30 via-transparent to-transparent" />
              <div className="absolute bottom-4 left-4 right-4">
                <div className="bg-background/90 dark:bg-card/90 backdrop-blur-sm rounded-2xl p-4 border border-border/50">
                  <h3 className="font-semibold text-foreground">{tool.company_name}</h3>
                  <p className="text-sm text-muted-foreground mt-1">Featured Preview</p>
                </div>
              </div>
            </div>
          )}

          {/* Detailed Description */}
          <div className="prose dark:prose-invert max-w-none prose-lg prose-headings:text-foreground prose-p:text-muted-foreground prose-strong:text-foreground prose-a:text-primary hover:prose-a:text-primary/80">
            <div dangerouslySetInnerHTML={{
              __html: tool.full_description ||
              '<div class="text-center py-16 px-8 bg-gradient-to-br from-blue-50/50 via-purple-50/30 to-pink-50/50 dark:from-blue-950/20 dark:via-purple-950/10 dark:to-pink-950/20 rounded-3xl border border-border/30 backdrop-blur-sm"><div class="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-primary/20 to-accent/20 rounded-full flex items-center justify-center shadow-lg"><span class="text-3xl">🚀</span></div><h3 class="text-xl font-bold text-foreground mb-3">Detailed Information Coming Soon!</h3><p class="text-muted-foreground mb-4 max-w-md mx-auto">We\'re working hard to bring you comprehensive details about this amazing tool. Stay tuned for updates!</p><div class="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium"><span class="w-2 h-2 bg-primary rounded-full animate-pulse"></span>Updates in progress</div></div>'
            }} />
          </div>
        </TabsContent>

        {/* New Pros & Cons Tab */}
        <TabsContent value="pros-cons" className="p-6 sm:p-8 lg:p-10">
          <div className="grid grid-cols-1 lg:grid-cols-2 gap-8">
            {/* Pros Section */}
            <div className="space-y-6">
              <div className="text-center lg:text-left">
                <h3 className="text-2xl font-bold mb-2 flex items-center gap-3 text-emerald-700 dark:text-emerald-300">
                  <div className="p-3 bg-emerald-100 dark:bg-emerald-800 rounded-xl">
                    ✅
                  </div>
                  Advantages
                </h3>
                <p className="text-muted-foreground">What makes this tool great</p>
              </div>

              {tool.pros && tool.pros.length > 0 ? (
                <div className="space-y-4">
                  {tool.pros.map((pro: string, index: number) => (
                    <div key={index} className="group p-4 bg-emerald-50 dark:bg-emerald-900/20 rounded-xl border border-emerald-200 dark:border-emerald-800 hover:shadow-lg transition-all duration-200">
                      <div className="flex items-start gap-3">
                        <CheckCircle className="h-5 w-5 text-emerald-500 mt-0.5 flex-shrink-0 group-hover:scale-110 transition-transform" />
                        <span className="text-gray-700 dark:text-gray-300 font-medium">{pro}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 px-6 bg-emerald-50/50 dark:bg-emerald-900/10 rounded-2xl border border-emerald-200/50 dark:border-emerald-800/50">
                  <div className="w-16 h-16 mx-auto mb-4 bg-emerald-100 dark:bg-emerald-800 rounded-full flex items-center justify-center">
                    <span className="text-2xl">✨</span>
                  </div>
                  <h4 className="font-semibold text-foreground mb-2">Pros Coming Soon!</h4>
                  <p className="text-sm text-muted-foreground">We're analyzing this tool to bring you detailed advantages.</p>
                </div>
              )}
            </div>

            {/* Cons Section */}
            <div className="space-y-6">
              <div className="text-center lg:text-left">
                <h3 className="text-2xl font-bold mb-2 flex items-center gap-3 text-red-700 dark:text-red-300">
                  <div className="p-3 bg-red-100 dark:bg-red-800 rounded-xl">
                    ❌
                  </div>
                  Limitations
                </h3>
                <p className="text-muted-foreground">Areas for improvement</p>
              </div>

              {tool.cons && tool.cons.length > 0 ? (
                <div className="space-y-4">
                  {tool.cons.map((con: string, index: number) => (
                    <div key={index} className="group p-4 bg-red-50 dark:bg-red-900/20 rounded-xl border border-red-200 dark:border-red-800 hover:shadow-lg transition-all duration-200">
                      <div className="flex items-start gap-3">
                        <XCircle className="h-5 w-5 text-red-500 mt-0.5 flex-shrink-0 group-hover:scale-110 transition-transform" />
                        <span className="text-gray-700 dark:text-gray-300 font-medium">{con}</span>
                      </div>
                    </div>
                  ))}
                </div>
              ) : (
                <div className="text-center py-12 px-6 bg-red-50/50 dark:bg-red-900/10 rounded-2xl border border-red-200/50 dark:border-red-800/50">
                  <div className="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-800 rounded-full flex items-center justify-center">
                    <span className="text-2xl">🔍</span>
                  </div>
                  <h4 className="font-semibold text-foreground mb-2">Limitations Analysis Pending</h4>
                  <p className="text-sm text-muted-foreground">We're conducting thorough research to provide balanced insights.</p>
                </div>
              )}
            </div>
          </div>
        </TabsContent>

        <TabsContent value="reviews" className="p-6 sm:p-8 lg:p-10">
          <div className="text-center mb-8">
            <h3 className="text-2xl font-bold mb-2 flex items-center justify-center gap-3 text-yellow-600 dark:text-yellow-400">
              <div className="p-3 bg-yellow-100 dark:bg-yellow-800 rounded-xl">
                ⭐
              </div>
              User Reviews
            </h3>
            <p className="text-muted-foreground">Share your experience and read what others think</p>
          </div>

          {/* Enhanced Review form */}
          <Card className="mb-8 border-yellow-200 dark:border-yellow-800 bg-yellow-50/50 dark:bg-yellow-900/10">
            <CardContent className="pt-6">
              <form onSubmit={handleReviewSubmit}>
                <div className="mb-6">
                  <label className="block text-sm font-medium mb-3">Rating</label>
                  <div className="flex gap-2 justify-center sm:justify-start">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        onClick={() => setNewReview({ ...newReview, rating: star })}
                        className="focus:outline-none hover:scale-110 transition-transform"
                      >
                        <Star
                          className={`h-8 w-8 ${
                            star <= newReview.rating ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground hover:text-yellow-300"
                          }`}
                        />
                      </button>
                    ))}
                  </div>
                </div>

                <div className="mb-6">
                  <label className="block text-sm font-medium mb-3">Your Review</label>
                  <Textarea
                    value={newReview.comment}
                    onChange={(e) => setNewReview({ ...newReview, comment: e.target.value })}
                    placeholder="Share your experience with this tool... What did you like? What could be improved?"
                    rows={4}
                    className="resize-none"
                  />
                </div>

                <Button type="submit" disabled={isSubmitting} className="w-full sm:w-auto">
                  {isSubmitting ? "Submitting..." : "Submit Review"}
                </Button>
              </form>
            </CardContent>
          </Card>

          {/* Enhanced Reviews list */}
          {userReviews && userReviews.length > 0 ? (
            <div className="space-y-6">
              {userReviews.map((review) => (
                <Card key={review.id} className="hover:shadow-lg transition-shadow duration-200">
                  <CardContent className="pt-6">
                    <div className="flex items-start gap-4">
                      <Avatar className="ring-2 ring-primary/20">
                        <AvatarFallback className="bg-gradient-to-br from-primary/20 to-accent/20 text-primary font-semibold">
                          {review.user_email?.charAt(0).toUpperCase() || "U"}
                        </AvatarFallback>
                      </Avatar>

                      <div className="flex-1">
                        <div className="flex items-center justify-between mb-3">
                          <div>
                            <div className="font-medium text-foreground">{review.user_email || "Anonymous User"}</div>
                            <div className="flex items-center mt-2">
                              {Array.from({ length: 5 }).map((_, i) => (
                                <Star
                                  key={i}
                                  className={`h-4 w-4 ${
                                    i < review.rating ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"
                                  }`}
                                />
                              ))}
                              <span className="ml-2 text-sm text-muted-foreground">
                                {review.rating}/5
                              </span>
                            </div>
                          </div>

                          <div className="flex items-center text-xs text-muted-foreground bg-secondary/50 px-3 py-1 rounded-full">
                            <Calendar className="h-3 w-3 mr-1" />
                            {format(new Date(review.created_at), "MMM d, yyyy")}
                          </div>
                        </div>

                        <p className="text-sm leading-relaxed text-muted-foreground bg-background/50 p-4 rounded-lg border border-border/50">
                          {review.comment}
                        </p>
                      </div>
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : (
            <div className="text-center py-16 px-8 bg-gradient-to-br from-yellow-50/50 via-orange-50/30 to-red-50/50 dark:from-yellow-950/20 dark:via-orange-950/10 dark:to-red-950/20 rounded-3xl border border-border/30">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-yellow-100 to-orange-100 dark:from-yellow-800 dark:to-orange-800 rounded-full flex items-center justify-center shadow-lg">
                <span className="text-3xl">💭</span>
              </div>
              <h4 className="text-xl font-bold text-foreground mb-3">No Reviews Yet!</h4>
              <p className="text-muted-foreground mb-4 max-w-md mx-auto">
                Be the first to share your experience with this tool. Your review helps others make informed decisions.
              </p>
              <div className="inline-flex items-center gap-2 px-4 py-2 bg-yellow-100 dark:bg-yellow-800 text-yellow-700 dark:text-yellow-300 rounded-full text-sm font-medium">
                <span className="w-2 h-2 bg-yellow-500 rounded-full animate-pulse"></span>
                Waiting for your review
              </div>
            </div>
          )}
        </TabsContent>
      </Tabs>
    </div>
  )
}
