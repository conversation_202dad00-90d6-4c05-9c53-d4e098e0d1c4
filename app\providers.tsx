"use client";

import { ThemeProvider } from "@/components/theme-provider";
import { Toaster } from "@/components/ui/toaster";
import { Toaster as SonnerToaster } from "@/components/ui/sonner";

import { memo } from "react";

// تحسين الأداء: استخدام memo لتجنب إعادة الرسم غير الضرورية
export const Providers = memo(function Providers({ children }: { children: React.ReactNode }) {
  return (
    <ThemeProvider
      attribute="class"
      defaultTheme="system"
      enableSystem
      disableTransitionOnChange={false} // تحسين: تمكين الانتقالات للحصول على تجربة أفضل
      storageKey="theme-preference" // تحسين: إضافة مفتاح تخزين مخصص
    >
      {children}
      <Toaster />
      <SonnerToaster
        position="bottom-right"
        toastOptions={{
          duration: 3000, // تحسين: تقليل مدة عرض الإشعارات
          style: {
            background: 'hsl(var(--background))',
            color: 'hsl(var(--foreground))',
            border: '1px solid hsl(var(--border))',
          },
        }}
      />
    </ThemeProvider>
  );
});
