"use client"

import { useRouter } from 'next/navigation'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { TrendingUp, ArrowRight } from 'lucide-react'

interface Category {
  id: string
  name: string
  count: number
}

interface PopularCategoriesProps {
  categories: Category[]
  currentCategory?: string
}

export default function PopularCategories({ 
  categories, 
  currentCategory 
}: PopularCategoriesProps) {
  const router = useRouter()

  // Get top 8 categories by count
  const topCategories = categories
    .sort((a, b) => b.count - a.count)
    .slice(0, 8)

  const handleCategoryClick = (categoryId: string) => {
    if (categoryId === currentCategory) {
      router.push('/tools')
    } else {
      router.push(`/tools?category=${categoryId}`)
    }
  }

  return (
    <div className="bg-card rounded-xl border shadow-sm p-6">
      <h3 className="font-semibold mb-4 flex items-center gap-2">
        <TrendingUp className="h-4 w-4 text-primary" />
        Popular Categories
      </h3>
      
      <div className="space-y-2">
        {topCategories.map((category) => (
          <Button
            key={category.id}
            variant="ghost"
            size="sm"
            className={`w-full justify-between text-left h-auto p-3 transition-all duration-200 ${
              currentCategory === category.id
                ? 'bg-primary/10 text-primary border border-primary/20'
                : 'hover:bg-muted/50'
            }`}
            onClick={() => handleCategoryClick(category.id)}
          >
            <div className="flex flex-col items-start">
              <span className="font-medium text-sm">{category.name}</span>
              <span className="text-xs text-muted-foreground">
                {category.count} tools
              </span>
            </div>
            <ArrowRight className="h-3 w-3 opacity-50" />
          </Button>
        ))}
      </div>

      <div className="mt-4 pt-4 border-t border-border/50">
        <Button
          variant="outline"
          size="sm"
          className="w-full"
          onClick={() => router.push('/tools')}
        >
          View All Categories
        </Button>
      </div>
    </div>
  )
}
