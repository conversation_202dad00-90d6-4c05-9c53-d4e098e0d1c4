import { createServerClient } from "@/lib/supabase/server"
import ToolCard from "@/components/tool-card"
import Link from "next/link"
import { ChevronRight } from "lucide-react"

export default async function TopRatedTools() {
  const supabase = createServerClient()
  const { data: tools, error } = await supabase
    .from("tools")
    .select("*")
    .order("average_rating", { ascending: false })
    .limit(6)

  if (error) {
    console.error("Error fetching top rated tools:", error.message)
    return (
      <div className="text-center py-12">
        <p className="text-slate-600 dark:text-slate-400">Error loading top rated tools. Please try again later.</p>
      </div>
    )
  }

  if (!tools || tools.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-600 dark:text-slate-400">No tools available at the moment.</p>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Top Rated Tools</h2>
        <Link href="/top-rated" className="text-blue-600 dark:text-blue-400 flex items-center hover:underline">
          View all <ChevronRight className="h-4 w-4 ml-1" />
        </Link>
      </div>
      <p className="text-slate-600 dark:text-slate-400 mb-8">Explore the highest-rated AI tools by our community</p>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {tools.map((tool) => (
          <ToolCard key={tool.id} tool={tool} />
        ))}
      </div>
    </div>
  )
}
