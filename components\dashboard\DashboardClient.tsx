"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import Image from "next/image"
import Link from "next/link"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { 
  User, Settings, Heart, Bookmark, LogOut, 
  Edit, Trash2, ExternalLink, Star, Clock, 
  CheckCircle, XCircle, AlertTriangle
} from "lucide-react"
import { toast } from "sonner"
import { Button } from "@/components/ui/button"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON>bsList, TabsTrigger } from "@/components/ui/tabs"
import { Card, CardContent, CardDescription, CardFooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { 
  Dialog, 
  DialogContent, 
  DialogDescription, 
  DialogFooter, 
  DialogHeader, 
  DialogTitle, 
  DialogTrigger 
} from "@/components/ui/dialog"
import { format } from "date-fns"

interface Tool {
  id: number
  name: string
  description: string
  logo_url?: string
  website_url?: string
  category?: string
  pricing?: string
  created_at?: string
  slug?: string
}

interface Review {
  id: string
  rating: number
  comment?: string
  created_at: string
  tools: {
    id: number
    company_name: string
    slug: string
    logo_url?: string
  }
}

interface ToolSubmission {
  id: number
  tool_name: string
  description: string
  website_url: string
  category: string
  pricing: string
  status: "pending" | "approved" | "rejected"
  created_at: string
}

interface DashboardClientProps {
  user: any
  savedTools: Tool[]
  reviews: Review[]
  submissions: ToolSubmission[]
}

export default function DashboardClient({ 
  user, 
  savedTools, 
  reviews, 
  submissions 
}: DashboardClientProps) {
  const router = useRouter()
  const [deleteDialogOpen, setDeleteDialogOpen] = useState(false)
  const [itemToDelete, setItemToDelete] = useState<{ id: number, type: 'saved' | 'review' | 'submission' } | null>(null)
  const [isDeleting, setIsDeleting] = useState(false)
  
  const handleSignOut = async () => {
    try {
      const supabase = createBrowserClient()
      await supabase.auth.signOut()
      router.push('/')
      toast.success("Signed out successfully")
    } catch (error) {
      console.error('Error signing out:', error)
      toast.error("Sign out failed")
    }
  }
  
  const handleDeleteItem = async () => {
    if (!itemToDelete) return
    
    setIsDeleting(true)
    
    try {
      const supabase = createBrowserClient()
      
      if (itemToDelete.type === 'saved') {
        const { error } = await supabase
          .from('favorites')
          .delete()
          .eq('user_id', user.id)
          .eq('tool_id', itemToDelete.id)
        
        if (error) throw error
        
        toast.success("Tool removed from favorites")
        router.refresh()
      } 
      else if (itemToDelete.type === 'review') {
        const { error } = await supabase
          .from('reviews')
          .delete()
          .eq('id', itemToDelete.id)
          .eq('user_id', user.id)
        
        if (error) throw error
        
        toast.success("Review deleted successfully")
        router.refresh()
      }
      else if (itemToDelete.type === 'submission') {
        const { error } = await supabase
          .from('tool_submissions')
          .delete()
          .eq('id', itemToDelete.id)
          .eq('user_id', user.id)
        
        if (error) throw error
        
        toast.success("Submission deleted successfully")
        router.refresh()
      }
      
    } catch (error) {
      console.error('Error deleting item:', error)
      toast.error("Delete failed")
    } finally {
      setIsDeleting(false)
      setDeleteDialogOpen(false)
      setItemToDelete(null)
    }
  }
  
  const confirmDelete = (id: number, type: 'saved' | 'review' | 'submission') => {
    setItemToDelete({ id, type })
    setDeleteDialogOpen(true)
  }
  
  return (
    <>
      <div className="flex flex-col md:flex-row md:items-center justify-between gap-4 mb-8">
        <div>
          <h1 className="text-3xl font-bold">Dashboard</h1>
          <p className="text-muted-foreground">
            Welcome back, {user.email}
          </p>
        </div>
        
        <div className="flex items-center gap-3">
          <Button variant="outline" onClick={handleSignOut} className="flex items-center gap-2">
            <LogOut size={16} />
            Sign Out
          </Button>
          <Button variant="outline" onClick={() => router.push('/profile')} className="flex items-center gap-2">
            <Settings size={16} />
            Profile
          </Button>
        </div>
      </div>

      <Tabs defaultValue="saved" className="w-full">
        <TabsList className="grid grid-cols-3 mb-8">
          <TabsTrigger value="saved" className="flex items-center gap-2">
            <Bookmark size={16} />
            <span className="hidden sm:inline">Saved Tools</span>
            <span className="sm:hidden">Saved</span>
            <span className="ml-1 text-xs bg-muted rounded-full px-2 py-0.5">
              {savedTools.length}
            </span>
          </TabsTrigger>
          <TabsTrigger value="reviews" className="flex items-center gap-2">
            <Star size={16} />
            <span className="hidden sm:inline">My Reviews</span>
            <span className="sm:hidden">Reviews</span>
            <span className="ml-1 text-xs bg-muted rounded-full px-2 py-0.5">
              {reviews.length}
            </span>
          </TabsTrigger>
          <TabsTrigger value="submissions" className="flex items-center gap-2">
            <User size={16} />
            <span className="hidden sm:inline">My Submissions</span>
            <span className="sm:hidden">Submissions</span>
            <span className="ml-1 text-xs bg-muted rounded-full px-2 py-0.5">
              {submissions.length}
            </span>
          </TabsTrigger>
        </TabsList>

        <TabsContent value="saved">
          {savedTools.length === 0 ? (
            <div className="text-center py-12 bg-card rounded-lg border">
              <Bookmark className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No saved tools yet</h3>
              <p className="text-muted-foreground max-w-md mx-auto mb-6">
                You haven't saved any tools yet. Browse our directory and save tools you're interested in.
              </p>
              <Button asChild>
                <Link href="/tools">Browse Tools</Link>
              </Button>
            </div>
          ) : (
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {savedTools.map((tool) => (
                <Card key={tool.id} className="overflow-hidden">
                  <CardHeader className="pb-2">
                    <div className="flex items-start gap-3">
                      <div className="h-12 w-12 rounded-lg border overflow-hidden flex-shrink-0 bg-muted">
                        {tool.logo_url ? (
                          <Image 
                            src={tool.logo_url} 
                            alt={tool.name} 
                            width={48}
                            height={48}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center text-muted-foreground">
                            {tool.name.charAt(0)}
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <CardTitle className="text-lg">{tool.name}</CardTitle>
                        <CardDescription className="line-clamp-1">
                          {tool.category || "AI Tool"}
                        </CardDescription>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground line-clamp-3">
                      {tool.description}
                    </p>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => confirmDelete(tool.id, 'saved')}
                      className="text-destructive hover:text-destructive hover:bg-destructive/10"
                    >
                      <Trash2 size={16} className="mr-1" />
                      Remove
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      asChild
                    >
                      <Link href={`/Tool/${tool.slug || tool.id}`}>
                        <ExternalLink size={16} className="mr-1" />
                        View
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="reviews">
          {reviews.length === 0 ? (
            <div className="text-center py-12 bg-card rounded-lg border">
              <Star className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No reviews yet</h3>
              <p className="text-muted-foreground max-w-md mx-auto mb-6">
                You haven't reviewed any tools yet. Browse our directory and share your thoughts on tools you've used.
              </p>
              <Button asChild>
                <Link href="/tools">Browse Tools</Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {reviews.map((review) => (
                <Card key={review.id}>
                  <CardHeader>
                    <div className="flex items-start gap-3">
                      <div className="h-12 w-12 rounded-lg border overflow-hidden flex-shrink-0 bg-muted">
                        {review.tools.logo_url ? (
                          <Image 
                            src={review.tools.logo_url} 
                            alt={review.tools.company_name} 
                            width={48}
                            height={48}
                            className="h-full w-full object-cover"
                          />
                        ) : (
                          <div className="h-full w-full flex items-center justify-center text-muted-foreground">
                            {review.tools.company_name.charAt(0)}
                          </div>
                        )}
                      </div>
                      <div className="flex-1">
                        <div className="flex items-center justify-between">
                          <CardTitle className="text-lg">{review.tools.company_name}</CardTitle>
                          <div className="text-xs text-muted-foreground">
                            {format(new Date(review.created_at), "MMM d, yyyy")}
                          </div>
                        </div>
                        <div className="flex mt-1">
                          {Array.from({ length: 5 }).map((_, i) => (
                            <Star
                              key={i}
                              className={`h-4 w-4 ${
                                i < review.rating ? "fill-yellow-400 text-yellow-400" : "text-muted-foreground"
                              }`}
                            />
                          ))}
                        </div>
                      </div>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <p className="text-sm text-muted-foreground">
                      {review.comment || "No comment provided."}
                    </p>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    <Button 
                      variant="outline" 
                      size="sm"
                      onClick={() => confirmDelete(Number(review.id), 'review')}
                      className="text-destructive hover:text-destructive hover:bg-destructive/10"
                    >
                      <Trash2 size={16} className="mr-1" />
                      Delete Review
                    </Button>
                    <Button 
                      variant="outline" 
                      size="sm"
                      asChild
                    >
                      <Link href={`/Tool/${review.tools.slug || review.tools.id}`}>
                        <ExternalLink size={16} className="mr-1" />
                        View Tool
                      </Link>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>

        <TabsContent value="submissions">
          {submissions.length === 0 ? (
            <div className="text-center py-12 bg-card rounded-lg border">
              <User className="h-12 w-12 mx-auto text-muted-foreground mb-4" />
              <h3 className="text-lg font-medium mb-2">No submissions yet</h3>
              <p className="text-muted-foreground max-w-md mx-auto mb-6">
                You haven't submitted any tools yet. Submit your AI tool to get featured on our platform.
              </p>
              <Button asChild>
                <Link href="/submit">Submit a Tool</Link>
              </Button>
            </div>
          ) : (
            <div className="space-y-6">
              {submissions.map((submission) => (
                <Card key={submission.id}>
                  <CardHeader>
                    <div className="flex items-center justify-between">
                      <div>
                        <CardTitle>{submission.tool_name}</CardTitle>
                        <CardDescription>
                          Submitted on {format(new Date(submission.created_at), "MMM d, yyyy")}
                        </CardDescription>
                      </div>
                      <Badge
                        className={
                          submission.status === "approved"
                            ? "bg-green-100 text-green-800 hover:bg-green-100"
                            : submission.status === "rejected"
                            ? "bg-red-100 text-red-800 hover:bg-red-100"
                            : "bg-yellow-100 text-yellow-800 hover:bg-yellow-100"
                        }
                      >
                        {submission.status === "approved" ? (
                          <CheckCircle className="h-3 w-3 mr-1" />
                        ) : submission.status === "rejected" ? (
                          <XCircle className="h-3 w-3 mr-1" />
                        ) : (
                          <Clock className="h-3 w-3 mr-1" />
                        )}
                        {submission.status.charAt(0).toUpperCase() + submission.status.slice(1)}
                      </Badge>
                    </div>
                  </CardHeader>
                  <CardContent>
                    <div className="space-y-4">
                      <div>
                        <h4 className="text-sm font-medium mb-1">Description</h4>
                        <p className="text-sm text-muted-foreground">{submission.description}</p>
                      </div>
                      <div className="grid grid-cols-2 gap-4">
                        <div>
                          <h4 className="text-sm font-medium mb-1">Category</h4>
                          <p className="text-sm text-muted-foreground">{submission.category}</p>
                        </div>
                        <div>
                          <h4 className="text-sm font-medium mb-1">Pricing</h4>
                          <p className="text-sm text-muted-foreground">{submission.pricing}</p>
                        </div>
                      </div>
                    </div>
                  </CardContent>
                  <CardFooter className="flex justify-between">
                    {submission.status === "pending" && (
                      <Button 
                        variant="outline" 
                        size="sm"
                        onClick={() => confirmDelete(submission.id, 'submission')}
                        className="text-destructive hover:text-destructive hover:bg-destructive/10"
                      >
                        <Trash2 size={16} className="mr-1" />
                        Delete Submission
                      </Button>
                    )}
                    {submission.status === "rejected" && (
                      <div className="flex items-center text-sm text-destructive">
                        <AlertTriangle className="h-4 w-4 mr-1" />
                        Submission rejected
                      </div>
                    )}
                    {submission.status === "approved" && (
                      <div className="flex items-center text-sm text-green-600">
                        <CheckCircle className="h-4 w-4 mr-1" />
                        Submission approved
                      </div>
                    )}
                    <Button 
                      variant="outline" 
                      size="sm"
                      asChild
                    >
                      <a href={submission.website_url} target="_blank" rel="noopener noreferrer">
                        <ExternalLink size={16} className="mr-1" />
                        Visit Website
                      </a>
                    </Button>
                  </CardFooter>
                </Card>
              ))}
            </div>
          )}
        </TabsContent>
      </Tabs>
      
      <Dialog open={deleteDialogOpen} onOpenChange={setDeleteDialogOpen}>
        <DialogContent>
          <DialogHeader>
            <DialogTitle>Confirm Deletion</DialogTitle>
            <DialogDescription>
              Are you sure you want to delete this item? This action cannot be undone.
            </DialogDescription>
          </DialogHeader>
          <DialogFooter>
            <Button variant="outline" onClick={() => setDeleteDialogOpen(false)} disabled={isDeleting}>
              Cancel
            </Button>
            <Button 
              variant="destructive" 
              onClick={handleDeleteItem}
              disabled={isDeleting}
            >
              {isDeleting ? (
                <>
                  <span className="mr-2">Deleting...</span>
                  <span className="animate-spin">⏳</span>
                </>
              ) : (
                "Delete"
              )}
            </Button>
          </DialogFooter>
        </DialogContent>
      </Dialog>
    </>
  )
}
