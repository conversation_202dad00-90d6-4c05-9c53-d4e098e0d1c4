"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import UniversalSearch from "@/components/search/universal-search"
import SearchContainer from "@/components/search/search-container"
import SearchButton from "@/components/search/search-button"

interface HeroSearchProps {
  onSearch?: (query: string) => void
  initialValue?: string
  placeholder?: string
}

export default function HeroSearch({
  onSearch,
  initialValue = "",
  placeholder = "Search for AI tools, categories, features, or companies..."
}: HeroSearchProps) {
  const router = useRouter()
  const [currentQuery, setCurrentQuery] = useState(initialValue)

  // Handle custom search if provided, otherwise use default navigation
  const handleSearch = (query: string) => {
    const trimmedQuery = query.trim()
    if (!trimmedQuery) return

    if (onSearch) {
      onSearch(trimmedQuery)
    } else {
      router.push(`/search?q=${encodeURIComponent(trimmedQuery)}`)
    }
  }

  // Handle search button click - use the current query from UniversalSearch
  const handleSearchButtonClick = () => {
    if (currentQuery.trim()) {
      handleSearch(currentQuery.trim())
    }
  }

  return (
    <div className="mt-6">
      {/* Search Container - Using UniversalSearch with navigation mode */}
      <SearchContainer maxWidth="3xl">
        <div className="flex items-center p-3">
          <div className="flex-1">
            <UniversalSearch
              mode="navigation"
              context="general"
              variant="hero"
              size="lg"
              placeholder={placeholder}
              className="w-full"
              rounded="full"
              glass={false}
              showKeyboardShortcut={false}
              fullWidth={true}
              showInstantResults={true}
              maxResults={8}
              autoFocus={false}
              showSearchButton={false}
              initialValue={initialValue}
              onSearch={handleSearch}
              onInstantFilter={(query) => setCurrentQuery(query)}
            />
          </div>

          {/* Search Button */}
          <div className="flex-shrink-0 ml-3">
            <SearchButton
              onClick={handleSearchButtonClick}
              size="md"
              showText={true}
              text="Search"
            />
          </div>
        </div>
      </SearchContainer>
    </div>
  )
}
