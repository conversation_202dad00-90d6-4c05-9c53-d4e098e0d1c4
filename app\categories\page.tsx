import { Suspense } from 'react'
import { Metada<PERSON> } from 'next'
import CategoriesPageClient from '@/components/categories/CategoriesPageClient'

export const dynamic = 'force-dynamic'

export const metadata: Metadata = {
  title: 'AI Tool Categories | AiAnyTool.com',
  description: 'Explore AI tools organized by categories. Find the perfect AI solution for your specific needs across various industries and use cases. Browse 50+ categories with 1000+ tools.',
  keywords: [
    'AI tool categories',
    'AI tools by category',
    'AI directory categories',
    'AI software categories',
    'artificial intelligence tools',
    'AI tools classification',
    'AI tools organization',
    'find AI tools by category'
  ],
  openGraph: {
    title: 'AI Tool Categories - Organized Directory | AiAnyTool.com',
    description: 'Explore AI tools organized by categories. Find the perfect AI solution for your specific needs across various industries and use cases. Browse 50+ categories with 1000+ tools.',
    url: 'https://aianytool.com/categories',
    type: 'website',
    locale: 'en_US',
    siteName: 'AiAnyTool.com',
    images: [
      {
        url: 'https://aianytool.com/og-categories.jpg',
        secureUrl: 'https://aianytool.com/og-categories.jpg',
        width: 1200,
        height: 630,
        alt: 'AI Tool Categories Directory - 50+ Categories | AiAnyTool.com',
        type: 'image/jpeg',
      }
    ],
    countryName: 'United States',
  },
  twitter: {
    card: 'summary_large_image',
    title: 'AI Tool Categories - Organized Directory',
    description: 'Explore AI tools organized by categories. Find the perfect AI solution across 50+ categories with 1000+ tools.',
    images: ['https://aianytool.com/og-categories.jpg'],
    creator: '@aianytool',
    site: '@aianytool',
  },
  alternates: {
    canonical: 'https://aianytool.com/categories',
  },
}

interface CategoriesPageProps {
  searchParams: {
    search?: string
    category?: string
  }
}

// Loading component for Suspense
function CategoriesLoading() {
  return (
    <div className="min-h-screen bg-background pt-24 pb-16">
      <div className="container mx-auto px-4">
        <div className="text-center mb-12">
          <div className="h-8 bg-muted rounded-lg mb-4 animate-pulse"></div>
          <div className="h-4 bg-muted rounded-lg max-w-2xl mx-auto animate-pulse"></div>
        </div>
        <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
          {Array.from({ length: 12 }).map((_, index) => (
            <div key={index} className="h-full border border-slate-200 dark:border-slate-800 overflow-hidden animate-pulse rounded-lg">
              <div className="p-4">
                <div className="flex items-center justify-between mb-4">
                  <div className="h-5 w-24 bg-slate-200 dark:bg-slate-700 rounded"></div>
                  <div className="h-5 w-8 bg-slate-200 dark:bg-slate-700 rounded-full"></div>
                </div>
                <div className="space-y-2">
                  {Array.from({ length: 4 }).map((_, i) => (
                    <div key={i} className="flex items-center gap-2">
                      <div className="w-4 h-4 rounded-full bg-slate-200 dark:bg-slate-700"></div>
                      <div className="h-3 bg-slate-200 dark:bg-slate-700 rounded w-full"></div>
                    </div>
                  ))}
                </div>
              </div>
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}

export default function CategoriesPage({ searchParams }: CategoriesPageProps) {
  return (
    <Suspense fallback={<CategoriesLoading />}>
      <CategoriesPageClient
        initialSearch={searchParams.search}
        initialCategory={searchParams.category}
      />
    </Suspense>
  )
}