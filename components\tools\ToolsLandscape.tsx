"use client"

import { useState, useEffect } from 'react'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { TrendingUp, Star, Clock, Zap, Users, Award, ArrowRight, Filter, Grid3X3 } from 'lucide-react'
import { createBrowserClient } from '@/lib/supabase/client-utils'
import Link from 'next/link'
import { cn } from '@/lib/utils'

interface Category {
  id: string
  name: string
  count: number
  description?: string
  trending?: boolean
  featured?: boolean
  growth?: number
}

interface ToolsLandscapeProps {
  onCategorySelect?: (category: string) => void
  selectedCategory?: string
}

export default function ToolsLandscape({ 
  onCategorySelect, 
  selectedCategory 
}: ToolsLandscapeProps) {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState<'grid' | 'landscape'>('landscape')
  const [totalTools, setTotalTools] = useState(0)

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setLoading(true)
        const supabase = createBrowserClient()

        const { data: tools, error } = await supabase
          .from('tools')
          .select('primary_task, created_at, is_featured')

        if (error) {
          console.error('Error fetching categories:', error)
          return
        }

        if (tools) {
          setTotalTools(tools.length)
          
          // Count tools by category
          const categoryCounts: Record<string, { count: number; recent: number; featured: number }> = {}
          
          const thirtyDaysAgo = new Date()
          thirtyDaysAgo.setDate(thirtyDaysAgo.getDate() - 30)

          tools.forEach(tool => {
            if (tool.primary_task) {
              if (!categoryCounts[tool.primary_task]) {
                categoryCounts[tool.primary_task] = { count: 0, recent: 0, featured: 0 }
              }
              categoryCounts[tool.primary_task].count++
              
              if (tool.created_at && new Date(tool.created_at) > thirtyDaysAgo) {
                categoryCounts[tool.primary_task].recent++
              }
              
              if (tool.is_featured) {
                categoryCounts[tool.primary_task].featured++
              }
            }
          })

          // Format categories with additional data
          const formattedCategories = Object.entries(categoryCounts)
            .map(([name, data]) => ({
              id: name.toLowerCase().replace(/\s+/g, '-'),
              name,
              count: data.count,
              description: getCategoryDescription(name),
              trending: data.recent > 0,
              featured: data.featured > 0,
              growth: data.recent
            }))
            .sort((a, b) => b.count - a.count)

          setCategories(formattedCategories)
        }
      } catch (error) {
        console.error('Error fetching categories:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchCategories()
  }, [])

  const getCategoryDescription = (categoryName: string): string => {
    const descriptions: Record<string, string> = {
      'AI Writing': 'AI-powered writing assistants and content generation tools',
      'Productivity': 'Tools to boost efficiency and automate daily tasks',
      'Image Generation': 'AI tools for creating and editing images',
      'Video Creation': 'AI-powered video generation and editing platforms',
      'Code Assistant': 'AI coding companions and development tools',
      'Customer Service': 'AI chatbots and customer support automation',
      'Data Analysis': 'AI tools for data processing and insights',
      'Marketing': 'AI-powered marketing and advertising solutions',
      'Research': 'AI research assistants and information gathering tools',
      'Voice AI': 'Speech recognition and voice synthesis tools'
    }
    return descriptions[categoryName] || `AI tools and solutions for ${categoryName.toLowerCase()}`
  }

  const getTopCategories = () => {
    return categories.slice(0, 12)
  }

  const getCategoryIcon = (categoryName: string) => {
    const icons: Record<string, React.ReactNode> = {
      'AI Writing': <span className="text-2xl">✍️</span>,
      'Productivity': <span className="text-2xl">⚡</span>,
      'Image Generation': <span className="text-2xl">🎨</span>,
      'Video Creation': <span className="text-2xl">🎬</span>,
      'Code Assistant': <span className="text-2xl">💻</span>,
      'Customer Service': <span className="text-2xl">🎧</span>,
      'Data Analysis': <span className="text-2xl">📊</span>,
      'Marketing': <span className="text-2xl">📈</span>,
      'Research': <span className="text-2xl">🔍</span>,
      'Voice AI': <span className="text-2xl">🎤</span>
    }
    return icons[categoryName] || <span className="text-2xl">🤖</span>
  }

  if (loading) {
    return (
      <div className="space-y-6">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
          {Array(12).fill(0).map((_, i) => (
            <Card key={i} className="animate-pulse">
              <CardContent className="p-6">
                <div className="h-12 w-12 bg-muted rounded-lg mb-4"></div>
                <div className="h-4 bg-muted rounded mb-2"></div>
                <div className="h-3 bg-muted rounded w-2/3 mb-4"></div>
                <div className="h-6 bg-muted rounded w-16"></div>
              </CardContent>
            </Card>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Header */}
      <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4">
        <div>
          <h2 className="text-3xl font-bold mb-2">AI Tools Landscape</h2>
          <p className="text-muted-foreground">
            Interactive ecosystem map of AI tools and assistants • {totalTools} tools across {categories.length} categories
          </p>
        </div>
        
        <div className="flex items-center gap-2">
          <Button
            variant={viewMode === 'landscape' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('landscape')}
          >
            <Grid3X3 className="h-4 w-4 mr-2" />
            Landscape
          </Button>
          <Button
            variant={viewMode === 'grid' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setViewMode('grid')}
          >
            <Filter className="h-4 w-4 mr-2" />
            Grid
          </Button>
        </div>
      </div>

      {/* Quick Stats */}
      <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-primary">{totalTools}</div>
            <div className="text-sm text-muted-foreground">Total Tools</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-green-600">{categories.length}</div>
            <div className="text-sm text-muted-foreground">Categories</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-amber-600">
              {categories.filter(c => c.featured).length}
            </div>
            <div className="text-sm text-muted-foreground">Featured</div>
          </CardContent>
        </Card>
        <Card>
          <CardContent className="p-4 text-center">
            <div className="text-2xl font-bold text-blue-600">
              {categories.reduce((acc, c) => acc + c.growth, 0)}
            </div>
            <div className="text-sm text-muted-foreground">New (30d)</div>
          </CardContent>
        </Card>
      </div>

      {/* Categories Grid */}
      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {getTopCategories().map((category, index) => (
          <Card
            key={category.id}
            className={cn(
              "group cursor-pointer transition-all duration-300 hover:shadow-lg hover:-translate-y-1",
              selectedCategory === category.id && "ring-2 ring-primary",
              index < 3 && "border-amber-200 bg-gradient-to-br from-amber-50/50 to-orange-50/50"
            )}
            onClick={() => onCategorySelect?.(category.id)}
          >
            <CardContent className="p-6">
              {/* Medal for top 3 */}
              {index < 3 && (
                <div className="absolute -top-2 -right-2">
                  <div className={cn(
                    "w-8 h-8 rounded-full flex items-center justify-center text-white font-bold text-sm",
                    index === 0 && "bg-yellow-500",
                    index === 1 && "bg-gray-400", 
                    index === 2 && "bg-amber-600"
                  )}>
                    {index + 1}
                  </div>
                </div>
              )}

              {/* Category Icon */}
              <div className="flex items-center justify-between mb-4">
                <div className="p-3 rounded-xl bg-muted/50 group-hover:bg-primary/10 transition-colors">
                  {getCategoryIcon(category.name)}
                </div>
                
                <div className="flex items-center gap-1">
                  {category.trending && (
                    <Badge variant="secondary" className="bg-green-50 text-green-700 border-green-200 text-xs">
                      +{category.growth}
                    </Badge>
                  )}
                  {category.featured && (
                    <Badge variant="secondary" className="bg-amber-50 text-amber-700 border-amber-200 text-xs">
                      <Star className="h-3 w-3 mr-1" />
                      Featured
                    </Badge>
                  )}
                </div>
              </div>

              {/* Category Info */}
              <div className="space-y-3">
                <div>
                  <h3 className="font-semibold text-lg group-hover:text-primary transition-colors">
                    {category.name}
                  </h3>
                  <p className="text-sm text-muted-foreground line-clamp-2">
                    {category.description}
                  </p>
                </div>

                <div className="flex items-center justify-between">
                  <Badge variant="outline" className="font-medium">
                    {category.count} tools
                  </Badge>
                  
                  <ArrowRight className="h-4 w-4 text-muted-foreground group-hover:text-primary group-hover:translate-x-1 transition-all" />
                </div>
              </div>
            </CardContent>
          </Card>
        ))}
      </div>

      {/* View All Categories */}
      <div className="text-center">
        <Link href="/tools">
          <Button size="lg" className="group">
            Explore All Categories
            <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>
        </Link>
      </div>
    </div>
  )
}
