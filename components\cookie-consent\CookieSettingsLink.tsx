'use client'

import { useState } from 'react'
import { Button } from '@/components/ui/button'
import { Settings } from 'lucide-react'
import { CookieSettingsModal } from './CookieSettingsModal'

interface CookieSettingsLinkProps {
  variant?: 'link' | 'button' | 'ghost'
  size?: 'sm' | 'default' | 'lg'
  className?: string
  children?: React.ReactNode
}

export function CookieSettingsLink({ 
  variant = 'link',
  size = 'sm',
  className,
  children
}: CookieSettingsLinkProps) {
  const [showSettings, setShowSettings] = useState(false)

  const handleOpenSettings = () => {
    setShowSettings(true)
  }

  const handleCloseSettings = () => {
    setShowSettings(false)
  }

  return (
    <>
      <Button
        variant={variant === 'link' ? 'link' : variant === 'button' ? 'default' : 'ghost'}
        size={size}
        onClick={handleOpenSettings}
        className={className}
      >
        {children || (
          <>
            <Settings className="h-4 w-4 mr-1" />
            <PERSON><PERSON>
          </>
        )}
      </Button>

      <CookieSettingsModal
        open={showSettings}
        onOpenChange={setShowSettings}
        onClose={handleCloseSettings}
      />
    </>
  )
}

export default CookieSettingsLink
