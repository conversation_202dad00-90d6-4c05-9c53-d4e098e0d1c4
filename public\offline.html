<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Offline - AiAnyTool</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
            color: white;
            text-align: center;
            padding: 20px;
        }
        
        .container {
            max-width: 500px;
            background: rgba(255, 255, 255, 0.1);
            backdrop-filter: blur(10px);
            border-radius: 20px;
            padding: 40px;
            border: 1px solid rgba(255, 255, 255, 0.2);
        }
        
        .icon {
            font-size: 4rem;
            margin-bottom: 20px;
            opacity: 0.8;
        }
        
        h1 {
            font-size: 2rem;
            margin-bottom: 15px;
            font-weight: 600;
        }
        
        p {
            font-size: 1.1rem;
            line-height: 1.6;
            margin-bottom: 30px;
            opacity: 0.9;
        }
        
        .retry-btn {
            background: rgba(255, 255, 255, 0.2);
            border: 2px solid rgba(255, 255, 255, 0.3);
            color: white;
            padding: 12px 30px;
            border-radius: 50px;
            font-size: 1rem;
            cursor: pointer;
            transition: all 0.3s ease;
            text-decoration: none;
            display: inline-block;
        }
        
        .retry-btn:hover {
            background: rgba(255, 255, 255, 0.3);
            border-color: rgba(255, 255, 255, 0.5);
            transform: translateY(-2px);
        }
        
        .features {
            margin-top: 30px;
            text-align: left;
        }
        
        .feature {
            margin-bottom: 10px;
            opacity: 0.8;
        }
        
        .feature::before {
            content: "✓ ";
            color: #4ade80;
            font-weight: bold;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="icon">📱</div>
        <h1>You're Offline</h1>
        <p>It looks like you're not connected to the internet. Don't worry, you can still browse some cached content!</p>
        
        <button class="retry-btn" onclick="window.location.reload()">
            Try Again
        </button>
        
        <div class="features">
            <div class="feature">Browse cached AI tools</div>
            <div class="feature">View saved categories</div>
            <div class="feature">Access offline content</div>
        </div>
    </div>

    <script>
        // تحقق من حالة الاتصال
        function checkConnection() {
            if (navigator.onLine) {
                window.location.reload()
            }
        }
        
        // استمع لأحداث الاتصال
        window.addEventListener('online', checkConnection)
        
        // تحقق دوري من الاتصال
        setInterval(checkConnection, 5000)
    </script>
</body>
</html>
