"use client"

import { Search } from "lucide-react"
import { cn } from "@/lib/utils"

interface SearchButtonProps {
  onClick?: () => void
  disabled?: boolean
  className?: string
  size?: 'sm' | 'md' | 'lg'
  showText?: boolean
  text?: string
}

const sizeClasses = {
  sm: 'h-8 px-3 text-sm',
  md: 'h-10 px-4',
  lg: 'h-12 px-6 text-lg'
}

const iconSizeClasses = {
  sm: 'h-3 w-3',
  md: 'h-4 w-4', 
  lg: 'h-5 w-5'
}

export default function SearchButton({
  onClick,
  disabled = false,
  className,
  size = 'md',
  showText = true,
  text = "Search"
}: SearchButtonProps) {
  return (
    <button
      type="button"
      onClick={onClick}
      disabled={disabled}
      className={cn(
        sizeClasses[size],
        "bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary active:from-primary/80 active:to-primary/80",
        "text-white font-semibold transition-all duration-200",
        "hover:shadow-lg hover:scale-105 active:scale-95",
        "flex items-center gap-2 rounded-full touch-manipulation",
        "disabled:opacity-50 disabled:cursor-not-allowed disabled:hover:scale-100",
        className
      )}
    >
      <Search className={iconSizeClasses[size]} />
      {showText && (
        <span className="hidden sm:inline">{text}</span>
      )}
    </button>
  )
}
