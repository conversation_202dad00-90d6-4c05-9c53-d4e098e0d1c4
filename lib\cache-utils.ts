/**
 * Cache utilities for optimizing data fetching
 */

import localforage from 'localforage';

// تكوين مخزن التخزين المؤقت
localforage.config({
  name: 'ai-tools-cache',
  description: 'Cache storage for AI tools directory',
});

// تعريف فترات التخزين المؤقت الافتراضية
export const CACHE_DURATION = {
  SHORT: 60 * 1000, // دقيقة واحدة
  MEDIUM: 5 * 60 * 1000, // 5 دقائق
  LONG: 30 * 60 * 1000, // 30 دقيقة
  PERMANENT: 24 * 60 * 60 * 1000, // يوم كامل
};

// In-memory cache for quick access
const DEFAULT_CACHE_DURATION = CACHE_DURATION.MEDIUM;
const memoryCache = new Map<string, { data: any; timestamp: number; ttl: number }>();

// معدل الاستخدام للتخزين المؤقت الذكي
const cacheHitRates = new Map<string, { hits: number; misses: number }>();

/**
 * Get data from cache if it exists and is not expired
 * @param key Cache key
 * @returns Cached data or null if not found or expired
 */
export async function getFromCache<T>(key: string): Promise<T | null> {
  // أولاً، البحث في التخزين المؤقت في الذاكرة للوصول السريع
  const cachedInMemory = memoryCache.get(key);
  if (cachedInMemory) {
    const now = Date.now();
    const ttl = cachedInMemory.ttl || DEFAULT_CACHE_DURATION;
    
    if (now - cachedInMemory.timestamp > ttl) {
      // انتهت صلاحية التخزين المؤقت
      memoryCache.delete(key);
      updateCacheStats(key, false);
      return null;
    }
    
    updateCacheStats(key, true);
    return cachedInMemory.data as T;
  }

  // محاولة الحصول من التخزين المستمر
  try {
    const persistedData = await localforage.getItem<{
      data: T;
      timestamp: number;
      ttl: number;
    }>(key);

    if (!persistedData) {
      updateCacheStats(key, false);
      return null;
    }

    const now = Date.now();
    const ttl = persistedData.ttl || DEFAULT_CACHE_DURATION;
    
    if (now - persistedData.timestamp > ttl) {
      // انتهت صلاحية التخزين المؤقت
      await localforage.removeItem(key);
      updateCacheStats(key, false);
      return null;
    }
    
    // إضافة القيمة إلى ذاكرة التخزين المؤقت أيضًا
    memoryCache.set(key, persistedData);
    updateCacheStats(key, true);
    return persistedData.data;
  } catch (error) {
    console.warn(`Error retrieving from persistent cache for key ${key}:`, error);
    updateCacheStats(key, false);
    return null;
  }
}

/**
 * Store data in cache
 * @param key Cache key
 * @param data Data to cache
 * @param ttl Time to live in milliseconds
 * @param persistToStorage Whether to persist to local storage
 */
export async function setCache<T>(
  key: string, 
  data: T, 
  ttl: number = DEFAULT_CACHE_DURATION,
  persistToStorage: boolean = true
): Promise<void> {
  const cacheItem = {
    data,
    timestamp: Date.now(),
    ttl
  };
  
  // تخزين في ذاكرة التخزين المؤقت
  memoryCache.set(key, cacheItem);
  
  // تخزين في التخزين المستمر إذا لزم الأمر
  if (persistToStorage && typeof window !== 'undefined') {
    try {
      await localforage.setItem(key, cacheItem);
    } catch (error) {
      console.warn(`Error persisting to cache for key ${key}:`, error);
    }
  }
}

/**
 * Clear all cache or specific key
 * @param key Optional specific key to clear
 */
export async function clearCache(key?: string): Promise<void> {
  if (key) {
    memoryCache.delete(key);
    if (typeof window !== 'undefined') {
      await localforage.removeItem(key);
    }
  } else {
    memoryCache.clear();
    if (typeof window !== 'undefined') {
      await localforage.clear();
    }
  }
}

/**
 * تحديث إحصائيات استخدام التخزين المؤقت
 */
function updateCacheStats(key: string, isHit: boolean): void {
  const stats = cacheHitRates.get(key) || { hits: 0, misses: 0 };
  
  if (isHit) {
    stats.hits++;
  } else {
    stats.misses++;
  }
  
  cacheHitRates.set(key, stats);
}

/**
 * الحصول على معدل استخدام التخزين المؤقت لمفتاح معين
 */
export function getCacheHitRate(key: string): number {
  const stats = cacheHitRates.get(key);
  if (!stats) return 0;
  
  const total = stats.hits + stats.misses;
  return total > 0 ? stats.hits / total : 0;
}

/**
 * Generate a cache key from query parameters
 * @param baseKey Base key name
 * @param params Query parameters
 * @returns Cache key string
 */
export function generateCacheKey(baseKey: string, params?: Record<string, any>): string {
  if (!params) return baseKey;
  return `${baseKey}:${JSON.stringify(params)}`;
}

/**
 * Cached fetch function with stale-while-revalidate pattern
 * @param key Cache key
 * @param fetchFn Function to fetch data if cache miss
 * @param ttl Optional cache TTL in milliseconds
 * @param bypassCache Whether to bypass cache and force fresh data
 * @returns Promise with data
 */
export async function cachedFetch<T>(
  key: string,
  fetchFn: () => Promise<T>,
  ttl: number = DEFAULT_CACHE_DURATION,
  bypassCache: boolean = false
): Promise<T> {
  // إذا كان تجاهل التخزين المؤقت نشطًا، نتخطى التحقق من التخزين المؤقت
  if (!bypassCache) {
    // محاولة الحصول من التخزين المؤقت أولاً
    const cached = await getFromCache<T>(key);
    if (cached) {
      return cached;
    }
  }

  // إذا لم يكن في التخزين المؤقت أو تم تفعيل تجاهل التخزين المؤقت، نجلب بيانات جديدة
  try {
    const data = await fetchFn();
    
    // تخزين البيانات في ذاكرة التخزين المؤقت إلا إذا تم تفعيل تجاهل التخزين المؤقت
    if (!bypassCache) {
      await setCache(key, data, ttl);
    }
    
    return data;
  } catch (error) {
    console.error(`Error fetching data for key ${key}:`, error);
    throw error;
  }
}

/**
 * تحسين التخزين المؤقت: تنظيف دوري للتخزين المؤقت المنتهي الصلاحية
 */
export function setupCacheCleanup(interval: number = 5 * 60 * 1000) { // كل 5 دقائق افتراضيًا
  // تنظيف التخزين المؤقت كل فترة محددة
  setInterval(async () => {
    const now = Date.now();
    let expiredCount = 0;
    
    // تنظيف التخزين المؤقت في الذاكرة
    memoryCache.forEach((value, key) => {
      const ttl = value.ttl || DEFAULT_CACHE_DURATION;
      if (now - value.timestamp > ttl) {
        memoryCache.delete(key);
        expiredCount++;
      }
    });
    
    // تنظيف التخزين المستمر إذا كان متاحًا
    if (typeof window !== 'undefined') {
      try {
        const keys = await localforage.keys();
        
        for (const key of keys) {
          const item = await localforage.getItem<{
            timestamp: number;
            ttl: number;
          }>(key);
          
          if (item && now - item.timestamp > (item.ttl || DEFAULT_CACHE_DURATION)) {
            await localforage.removeItem(key);
            expiredCount++;
          }
        }
      } catch (error) {
        console.warn('Error cleaning persistent cache:', error);
      }
    }
    
    if (expiredCount > 0) {
      console.log(`Cache cleanup: removed ${expiredCount} expired items`);
    }
  }, interval);
}

// تحميل التخزين المؤقت المستمر إلى الذاكرة عند بدء التشغيل
export async function preloadCache(): Promise<void> {
  if (typeof window !== 'undefined') {
    try {
      const keys = await localforage.keys();
      let loadedCount = 0;
      
      for (const key of keys) {
        const item = await localforage.getItem(key);
        if (item) {
          memoryCache.set(key, item);
          loadedCount++;
        }
      }
      
      if (loadedCount > 0) {
        console.log(`Preloaded ${loadedCount} items from persistent cache`);
      }
    } catch (error) {
      console.warn('Error preloading cache:', error);
    }
  }
}

// تفعيل تنظيف التخزين المؤقت وتحميل البيانات تلقائيًا عند استخدام الوحدة في المتصفح
if (typeof window !== 'undefined') {
  setupCacheCleanup();
  preloadCache();
}
