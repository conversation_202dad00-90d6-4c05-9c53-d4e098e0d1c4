'use client'

import { useState } from 'react'
import { But<PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Alert, AlertDescription } from '@/components/ui/alert'
import { Loader2, CheckCircle, XCircle, RefreshCw } from 'lucide-react'
import { createRatingUpdater } from '@/lib/supabase/rating-updater'

export default function UpdateRatingsPage() {
  const [isUpdating, setIsUpdating] = useState(false)
  const [result, setResult] = useState<{ updated: number; errors: number } | null>(null)
  const [error, setError] = useState<string | null>(null)

  const handleUpdateRatings = async () => {
    setIsUpdating(true)
    setError(null)
    setResult(null)

    try {
      const updater = createRatingUpdater()
      const updateResult = await updater.updateAllToolRatings()
      setResult(updateResult)
    } catch (err) {
      console.error('Error updating ratings:', err)
      setError(err instanceof Error ? err.message : 'Unknown error occurred')
    } finally {
      setIsUpdating(false)
    }
  }

  return (
    <div className="container mx-auto py-8 px-4">
      <div className="max-w-2xl mx-auto">
        <Card>
          <CardHeader>
            <CardTitle className="flex items-center gap-2">
              <RefreshCw className="h-5 w-5" />
              Update Tool Ratings
            </CardTitle>
            <CardDescription>
              Calculate and update average ratings for all tools based on user reviews.
              This process will fetch all reviews from the database and update the rating field in the tools table.
            </CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            {error && (
              <Alert variant="destructive">
                <XCircle className="h-4 w-4" />
                <AlertDescription>{error}</AlertDescription>
              </Alert>
            )}

            {result && (
              <Alert variant={result.errors > 0 ? "destructive" : "default"}>
                <CheckCircle className="h-4 w-4" />
                <AlertDescription>
                  Rating update completed: {result.updated} tools updated
                  {result.errors > 0 && `, ${result.errors} errors occurred`}
                </AlertDescription>
              </Alert>
            )}

            <div className="space-y-4">
              <div className="bg-muted p-4 rounded-lg">
                <h3 className="font-medium mb-2">What this process does:</h3>
                <ul className="text-sm text-muted-foreground space-y-1">
                  <li>• Fetches all reviews from the reviews table</li>
                  <li>• Calculates average rating for each tool</li>
                  <li>• Updates the rating field in the tools table</li>
                  <li>• Rounds ratings to 1 decimal place (e.g., 4.3)</li>
                </ul>
              </div>

              <Button 
                onClick={handleUpdateRatings} 
                disabled={isUpdating}
                className="w-full"
                size="lg"
              >
                {isUpdating ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Updating Ratings...
                  </>
                ) : (
                  <>
                    <RefreshCw className="mr-2 h-4 w-4" />
                    Update All Tool Ratings
                  </>
                )}
              </Button>
            </div>

            <div className="text-sm text-muted-foreground">
              <p><strong>Note:</strong> This process may take a few moments depending on the number of tools and reviews in your database.</p>
            </div>
          </CardContent>
        </Card>
      </div>
    </div>
  )
}
