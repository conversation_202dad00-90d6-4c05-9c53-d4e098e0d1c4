import { createServerClient } from "@/lib/supabase/server"
import Tool<PERSON>ard from "@/components/tool-card"

export default async function LatestTools() {
  const supabase = await createServerClient()
  const { data: tools, error } = await supabase
    .from("tools")
    .select("*")
    .order("created_at", { ascending: false })
    .limit(6)

  if (error) {
    console.error("Error fetching latest tools:", error.message)
    return (
      <div className="text-center py-12">
        <p className="text-slate-600 dark:text-slate-400">Error loading latest tools. Please try again later.</p>
      </div>
    )
  }

  if (!tools || tools.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-600 dark:text-slate-400">No tools available at the moment.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {tools.map((tool) => (
        <ToolCard key={tool.id} tool={tool} />
      ))}
    </div>
  )
}
