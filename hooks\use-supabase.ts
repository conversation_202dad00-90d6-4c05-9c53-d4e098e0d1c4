'use client'

import { useState, useEffect } from 'react'
import { createBrowserClient } from '@/lib/supabase/client-utils'

/**
 * Custom hook for managing Supabase client
 * Solves SSR issues by initializing client only on the client side
 */
export function useSupabase() {
  const [supabase, setSupabase] = useState<any>(null)
  const [isInitialized, setIsInitialized] = useState(false)

  useEffect(() => {
    // Initialize Supabase client only on client side
    const client = createBrowserClient()
    setSupabase(client)
    setIsInitialized(true)
  }, [])

  return { 
    supabase, 
    isInitialized 
  }
}
