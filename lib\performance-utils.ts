/**
 * أدوات تحسين الأداء للتطبيق
 */

// تحسين تكوين التخزين المؤقت للأداء السريع
const CACHE_DURATION = {
  SHORT: 30 * 1000,      // 30 ثانية - تحسين للاستجابة السريعة
  MEDIUM: 2 * 60 * 1000, // دقيقتان - تحسين
  LONG: 10 * 60 * 1000,  // 10 دقائق - تحسين
  VERY_LONG: 60 * 60 * 1000, // ساعة واحدة - تحسين
};

// التخزين المؤقت في الذاكرة
const memoryCache = new Map<string, { data: any; timestamp: number; ttl: number }>();

/**
 * تخزين البيانات في الذاكرة المؤقتة
 * @param key مفتاح التخزين
 * @param data البيانات المراد تخزينها
 * @param ttl مدة صلاحية التخزين بالمللي ثانية
 */
export function setMemoryCache<T>(key: string, data: T, ttl = CACHE_DURATION.MEDIUM): void {
  memoryCache.set(key, {
    data,
    timestamp: Date.now(),
    ttl,
  });
}

/**
 * الحصول على البيانات من الذاكرة المؤقتة
 * @param key مفتاح التخزين
 * @returns البيانات المخزنة أو null إذا لم تكن موجودة أو منتهية الصلاحية
 */
export function getMemoryCache<T>(key: string): T | null {
  const cached = memoryCache.get(key);
  if (!cached) return null;

  const now = Date.now();
  if (now - cached.timestamp > cached.ttl) {
    // انتهت صلاحية التخزين المؤقت
    memoryCache.delete(key);
    return null;
  }

  return cached.data as T;
}

/**
 * مسح التخزين المؤقت
 * @param key مفتاح التخزين (اختياري، إذا لم يتم تحديده سيتم مسح كل التخزين المؤقت)
 */
export function clearMemoryCache(key?: string): void {
  if (key) {
    memoryCache.delete(key);
  } else {
    memoryCache.clear();
  }
}

/**
 * إنشاء مفتاح تخزين مؤقت من المعلمات
 * @param baseKey المفتاح الأساسي
 * @param params المعلمات الإضافية
 * @returns مفتاح التخزين المؤقت
 */
export function createCacheKey(baseKey: string, params?: Record<string, any>): string {
  if (!params) return baseKey;
  return `${baseKey}:${JSON.stringify(params)}`;
}

/**
 * تنفيذ استدعاء مع التخزين المؤقت
 * @param key مفتاح التخزين المؤقت
 * @param fn الدالة المراد تنفيذها إذا لم تكن البيانات مخزنة
 * @param ttl مدة صلاحية التخزين بالمللي ثانية
 * @returns نتيجة الاستدعاء
 */
export async function withCache<T>(
  key: string,
  fn: () => Promise<T>,
  ttl = CACHE_DURATION.MEDIUM
): Promise<T> {
  // محاولة الحصول على البيانات من الذاكرة المؤقتة أولاً
  const cached = getMemoryCache<T>(key);
  if (cached !== null) {
    return cached;
  }

  // إذا لم تكن البيانات مخزنة، قم بتنفيذ الدالة
  try {
    const data = await fn();
    setMemoryCache(key, data, ttl);
    return data;
  } catch (error) {
    console.error(`Error in withCache for key ${key}:`, error);
    throw error;
  }
}

/**
 * تنفيذ استدعاء مع التخزين المؤقت في جلسة المتصفح
 * @param key مفتاح التخزين المؤقت
 * @param fn الدالة المراد تنفيذها إذا لم تكن البيانات مخزنة
 * @param ttl مدة صلاحية التخزين بالمللي ثانية
 * @returns نتيجة الاستدعاء
 */
export async function withSessionCache<T>(
  key: string,
  fn: () => Promise<T>,
  ttl = CACHE_DURATION.MEDIUM
): Promise<T> {
  // التحقق من وجود sessionStorage
  if (typeof window === 'undefined' || !window.sessionStorage) {
    return fn();
  }

  try {
    // محاولة الحصول على البيانات من sessionStorage
    const cachedData = sessionStorage.getItem(key);
    if (cachedData) {
      const { data, timestamp } = JSON.parse(cachedData);
      if (Date.now() - timestamp < ttl) {
        return data as T;
      }
    }
  } catch (e) {
    console.warn('Error reading from sessionStorage:', e);
  }

  // إذا لم تكن البيانات مخزنة أو منتهية الصلاحية، قم بتنفيذ الدالة
  try {
    const data = await fn();
    try {
      sessionStorage.setItem(
        key,
        JSON.stringify({
          data,
          timestamp: Date.now(),
        })
      );
    } catch (e) {
      console.warn('Error writing to sessionStorage:', e);
    }
    return data;
  } catch (error) {
    console.error(`Error in withSessionCache for key ${key}:`, error);
    throw error;
  }
}

/**
 * تأخير التنفيذ لفترة محددة
 * @param ms وقت التأخير بالمللي ثانية
 * @returns وعد يتم حله بعد انتهاء فترة التأخير
 */
export function delay(ms: number): Promise<void> {
  return new Promise(resolve => setTimeout(resolve, ms));
}

/**
 * قياس وقت تنفيذ دالة
 * @param fn الدالة المراد قياس وقت تنفيذها
 * @param name اسم الدالة (للتسجيل)
 * @returns نتيجة تنفيذ الدالة
 */
export async function measurePerformance<T>(
  fn: () => Promise<T>,
  name: string
): Promise<T> {
  const start = performance.now();
  try {
    const result = await fn();
    const end = performance.now();
    console.log(`Performance [${name}]: ${Math.round(end - start)}ms`);
    return result;
  } catch (error) {
    const end = performance.now();
    console.error(`Performance [${name}] Error: ${Math.round(end - start)}ms`, error);
    throw error;
  }
}

// تصدير الثوابت
export { CACHE_DURATION };
