'use client'

// React imports
import { useState, useEffect, useCallback, memo } from 'react'
import { useRouter } from 'next/navigation'
import Link from 'next/link'
import dynamic from 'next/dynamic'

// UI Components
import { But<PERSON> } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { Tabs, TabsContent, TabsList, TabsTrigger } from '@/components/ui/tabs'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Dialog, DialogContent, DialogDescription, DialogFooter, DialogHeader, DialogTitle, DialogTrigger } from '@/components/ui/dialog'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { MotionWrapper } from '@/components/ui/MotionWrapper'
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from '@/components/ui/collapsible'

// Icons
import {
  ChevronLeft, ExternalLink, Star, User, Calendar, Tag, Clock, Share2,
  Bookmark, Copy, Check, AlertTriangle, TrendingUp, Award, Zap, Heart,
  ThumbsUp, ThumbsDown, CheckCircle, Edit, Trash2, Loader2, Globe, Wrench,
  ChevronDown, ChevronUp, HelpCircle, Eye, Settings, DollarSign,
  Layers, MessageSquare, Target, Info
} from 'lucide-react'

// Services
import { createBrowserClient } from '@/lib/supabase/client-utils'

// Dynamic imports for performance
const StickyToolHeader = dynamic(() => import('@/components/tools/StickyToolHeader').then(mod => ({ default: mod.StickyToolHeader })), {
  ssr: false,
  loading: () => <div className="h-16 bg-background/95 border-b" />
})

const RelatedToolsSection = dynamic(() => import('@/components/tools/RelatedToolsSection').then(mod => ({ default: mod.RelatedToolsSection })), {
  ssr: false,
  loading: () => (
    <div className="space-y-4">
      <div className="h-6 bg-muted animate-pulse rounded" />
      <div className="space-y-3">
        {Array.from({ length: 3 }).map((_, i) => (
          <div key={i} className="h-20 bg-muted animate-pulse rounded" />
        ))}
      </div>
    </div>
  )
})

const FeaturedToolsSidebar = dynamic(() => import('@/components/tools/FeaturedToolsSidebar').then(mod => ({ default: mod.FeaturedToolsSidebar })), {
  ssr: false,
  loading: () => (
    <div className="space-y-4">
      <div className="h-6 bg-muted animate-pulse rounded" />
      <div className="space-y-3">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="h-16 bg-muted animate-pulse rounded" />
        ))}
      </div>
    </div>
  )
})

// Supabase client - using safe client with better error handling
const getSupabase = () => {
  try {
    const client = createBrowserClient()
    if (!client) {
      console.error('❌ Supabase client not available - check environment variables')
      throw new Error('Supabase client not available - check environment variables')
    }
    return client
  } catch (error) {
    console.error('❌ Failed to create Supabase client:', error)
    throw new Error('Failed to initialize database connection')
  }
}

// Types and Interfaces
interface Tool {
  id: number
  company_name: string
  short_description?: string
  full_description?: string
  logo_url?: string
  featured_image_url?: string
  primary_task?: string
  pricing?: string
  slug?: string
  visit_website_url?: string
  detail_url?: string
  pros?: string[]
  cons?: string[]
  faqs?: Record<string, string>
  applicable_tasks?: string[]
  created_at?: string
  updated_at?: string
}

interface Review {
  id: string
  rating: number
  comment: string | null
  created_at: string
  user_id: string
  user_email?: string
}

interface ToolPageClientProps {
  slug: string
  initialTool?: Tool | null
}

// Main Component
export default function ToolPageClient({ slug, initialTool }: ToolPageClientProps) {
  const router = useRouter()

  // Core state - Initialize with server data if available
  const [tool, setTool] = useState<Tool | null>(initialTool || null)
  const [loading, setLoading] = useState(!initialTool) // Don't show loading if we have initial data
  const [notFound, setNotFound] = useState(false)

  // Reviews state
  const [reviews, setReviews] = useState<Review[]>([])
  const [averageRating, setAverageRating] = useState<number | null>(null)
  const [reviewsLoading, setReviewsLoading] = useState(false)
  const [userHasReviewed, setUserHasReviewed] = useState(false)

  // UI state
  const [isBookmarked, setIsBookmarked] = useState(false)
  const [copySuccess, setCopySuccess] = useState(false)
  const [shareMenuOpen, setShareMenuOpen] = useState(false)
  const [activeTab, setActiveTab] = useState('overview')
  const [isHeaderScrolled, setIsHeaderScrolled] = useState(false)

  // Related tools state
  const [relatedTools, setRelatedTools] = useState<Tool[]>([])
  const [suggestedTools, setSuggestedTools] = useState<Tool[]>([])
  const [loadingRelated, setLoadingRelated] = useState(false)

  // Review form state
  const [reviewDialogOpen, setReviewDialogOpen] = useState(false)
  const [userReview, setUserReview] = useState('')
  const [userReviewTitle, setUserReviewTitle] = useState('')
  const [userRating, setUserRating] = useState(5)
  const [submitting, setSubmitting] = useState(false)
  const [editingReviewId, setEditingReviewId] = useState<string | null>(null)

  // FAQ state
  const [openFaqItems, setOpenFaqItems] = useState<Set<number>>(new Set())

  // Stats state
  const [toolStats, setToolStats] = useState({
    views: 0,
    clicks: 0,
    bookmarks: 0,
    popularity: 0
  })

  // Utility Functions
  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      // Calculate offset to account for sticky headers
      const offset = 220 // Adjusted for better positioning
      const elementPosition = element.offsetTop - offset

      window.scrollTo({
        top: Math.max(0, elementPosition), // Ensure we don't scroll to negative position
        behavior: 'smooth'
      })

      // Set active tab immediately for better UX
      setActiveTab(sectionId)
    }
  }

  // FAQ toggle function
  const toggleFaqItem = (index: number) => {
    setOpenFaqItems(prev => {
      const newSet = new Set(prev)
      if (newSet.has(index)) {
        newSet.delete(index)
      } else {
        newSet.add(index)
      }
      return newSet
    })
  }

  // Effects
  // Header scroll effect
  useEffect(() => {
    const handleHeaderScroll = () => {
      const scrollPosition = window.scrollY
      setIsHeaderScrolled(scrollPosition > 200) // تفعيل التأثير بعد 200px من السحب
    }

    window.addEventListener('scroll', handleHeaderScroll, { passive: true })
    return () => window.removeEventListener('scroll', handleHeaderScroll)
  }, [])

  // Scroll-based active section tracking
  useEffect(() => {
    if (!tool) return

    const handleScroll = () => {
      const sections = ['overview', 'features', 'pricing', 'alternatives', 'reviews']
      const scrollPosition = window.scrollY + 300 // Offset for headers

      let currentSection = 'overview'
      let minDistance = Infinity

      // Find the section closest to the scroll position
      for (const sectionId of sections) {
        const element = document.getElementById(sectionId)
        if (element) {
          const elementTop = element.offsetTop
          const distance = Math.abs(scrollPosition - elementTop)

          if (distance < minDistance && scrollPosition >= elementTop - 100) {
            minDistance = distance
            currentSection = sectionId
          }
        }
      }

      setActiveTab(currentSection)
    }

    // Debounce scroll handler for better performance
    let timeoutId: NodeJS.Timeout
    const debouncedHandleScroll = () => {
      clearTimeout(timeoutId)
      timeoutId = setTimeout(handleScroll, 10)
    }

    // Initial check after a short delay
    setTimeout(handleScroll, 500)

    // Add scroll listener
    window.addEventListener('scroll', debouncedHandleScroll, { passive: true })

    return () => {
      clearTimeout(timeoutId)
      window.removeEventListener('scroll', debouncedHandleScroll)
    }
  }, [tool])

  // Close share menu when clicking outside or pressing escape
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      const target = event.target as Element
      if (shareMenuOpen && !target.closest('.share-menu-container')) {
        setShareMenuOpen(false)
      }
    }

    const handleEscapeKey = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && shareMenuOpen) {
        setShareMenuOpen(false)
      }
    }

    if (shareMenuOpen) {
      document.addEventListener('mousedown', handleClickOutside)
      document.addEventListener('keydown', handleEscapeKey)
    }

    return () => {
      document.removeEventListener('mousedown', handleClickOutside)
      document.removeEventListener('keydown', handleEscapeKey)
    }
  }, [shareMenuOpen])

  // Main data fetching effect - only fetch if no initial data
  useEffect(() => {
    const fetchTool = async () => {
      // If we already have initial tool data, skip API call and load additional data
      if (initialTool) {
        console.log('✅ Using initial tool data:', initialTool.company_name)
        console.log('🔍 Tool data structure:', {
          applicable_tasks: initialTool.applicable_tasks,
          pros: initialTool.pros,
          cons: initialTool.cons,
          features: initialTool.features
        })
        setTool(initialTool)
        setLoading(false)

        // Load additional data in background
        if (initialTool.id) {
          setTimeout(() => {
            fetchReviews(initialTool.id)
            fetchRelatedTools(initialTool)
          }, 0)
        }
        return
      }

      if (!slug) {
        setNotFound(true)
        setLoading(false)
        return
      }

      setLoading(true)
      setNotFound(false)

      try {
        console.log('🔍 Fetching tool via API with slug:', slug)

        const response = await fetch(`/api/Tool/${slug}`)

        if (!response.ok) {
          if (response.status === 404) {
            console.error('❌ Tool not found via API:', slug)
            setNotFound(true)
            return
          }
          throw new Error(`API error: ${response.status}`)
        }

        const data = await response.json()
        const tool = data.tool

        if (!tool) {
          console.error('❌ No tool data returned from API')
          setNotFound(true)
          return
        }

        console.log('✅ Tool found via API:', tool.company_name)
        setTool(tool)
        setLoading(false)

        // Load other data in background
        if (tool.id) {
          setTimeout(() => {
            fetchReviews(tool.id)
            fetchRelatedTools(tool)
          }, 0)
        }
      } catch (error: any) {
        console.error('❌ Error fetching tool via API:', error)
        console.error('🚨 Error details:', {
          message: error.message,
          stack: error.stack,
          name: error.name
        })
        setNotFound(true)
      } finally {
        setLoading(false)
      }
    }

    fetchTool()
  }, [slug, initialTool])

  // Data fetching functions
  const fetchRelatedTools = async (currentTool: Tool) => {
    if (!currentTool) return

    setLoadingRelated(true)
    try {
      const supabase = getSupabase()
      // Only select necessary fields for better performance
      const selectFields = 'id, company_name, short_description, logo_url, slug, primary_task, pricing'

      // Add timeout for related tools queries
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Related tools query timeout')), 8000) // 8 second timeout
      })

      // Get tools with same category (limit to 4)
      const relatedQueryPromise = supabase
        .from('tools')
        .select(selectFields)
        .eq('primary_task', currentTool.primary_task)
        .neq('id', currentTool.id)
        .limit(4)

      const { data: relatedData, error: relatedError } = await Promise.race([relatedQueryPromise, timeoutPromise]) as any

      if (relatedError) {
        console.warn('Error fetching related tools:', relatedError)
      }

      // Get popular tools if not enough related tools (limit to 6)
      const popularQueryPromise = supabase
        .from('tools')
        .select(selectFields)
        .neq('id', currentTool.id)
        .order('created_at', { ascending: false })
        .limit(6)

      const { data: popularData, error: popularError } = await Promise.race([popularQueryPromise, timeoutPromise]) as any

      if (popularError) {
        console.warn('Error fetching popular tools:', popularError)
      }

      setRelatedTools(relatedData || [])
      setSuggestedTools(popularData || [])
    } catch (error) {
      console.error('Error fetching related tools:', error)
      // Set empty arrays on error to prevent UI issues
      setRelatedTools([])
      setSuggestedTools([])
    } finally {
      setLoadingRelated(false)
    }
  }

  const fetchReviews = async (toolId: number) => {
    if (!toolId) return

    setReviewsLoading(true)
    try {
      const supabase = getSupabase()

      // Add timeout for reviews query
      const timeoutPromise = new Promise((_, reject) => {
        setTimeout(() => reject(new Error('Reviews query timeout')), 6000) // 6 second timeout
      })

      // Limit reviews to improve performance
      const reviewsQueryPromise = supabase
        .from('reviews')
        .select('id, rating, comment, created_at, user_id')
        .eq('tool_id', toolId)
        .order('created_at', { ascending: false })
        .limit(20) // Limit to 20 most recent reviews

      const { data: reviewsData, error: reviewsError } = await Promise.race([reviewsQueryPromise, timeoutPromise]) as any

      if (reviewsError) {
        console.warn('Error fetching reviews:', reviewsError)
        setReviews([])
        setAverageRating(null)
        setUserHasReviewed(false)
        return
      }

      const formattedReviews = (reviewsData || []).map(review => ({
        id: review.id,
        rating: review.rating,
        comment: review.comment,
        created_at: new Date(review.created_at).toLocaleDateString(),
        user_id: review.user_id,
        user_email: `User-${review.user_id.substring(0, 8)}`
      }))

      setReviews(formattedReviews)

      if (formattedReviews.length > 0) {
        const total = formattedReviews.reduce((sum, review) => sum + review.rating, 0)
        setAverageRating(Number((total / formattedReviews.length).toFixed(1)))
      } else {
        setAverageRating(null)
      }

      // Check if current user has reviewed
      const currentUserId = localStorage.getItem('currentUserId')
      if (currentUserId) {
        const userReview = formattedReviews.find(review => review.user_id === currentUserId)
        setUserHasReviewed(!!userReview)
      }
    } catch (error) {
      console.error('Error fetching reviews:', error)
      // Set safe defaults on error
      setReviews([])
      setAverageRating(null)
      setUserHasReviewed(false)
    } finally {
      setReviewsLoading(false)
    }
  }

  // Event Handlers
  const handleVisitWebsite = async () => {
    if (!tool) return

    try {
      // Validate URL before opening
      const url = tool.visit_website_url || tool.detail_url
      if (!url || url === '#') {
        console.warn('No valid URL found for tool:', tool.company_name)
        return
      }

      // Sanitize URL - improved validation
      let validUrl: string
      try {
        // Check if URL already has protocol
        if (url.startsWith('http://') || url.startsWith('https://')) {
          // Validate existing URL
          const urlObj = new URL(url)
          validUrl = urlObj.href
        } else {
          // Add https protocol and validate
          const urlObj = new URL(`https://${url}`)
          validUrl = urlObj.href
        }

        // Track click (non-blocking) - Fixed supabase RPC call
        try {
          const supabase = getSupabase()
          await supabase.rpc('increment_tool_click_count', { tool_id: tool.id })
        } catch (trackError) {
          console.warn('Failed to track click:', trackError)
        }

        // Open in new tab (not window) with enhanced security
        const newTab = window.open(validUrl, '_blank', 'noopener,noreferrer')

        // Additional check to ensure new tab opened
        if (newTab) {
          newTab.focus() // Focus on the new tab
        } else {
          // If popup was blocked, try alternative method
          console.warn('Popup blocked, trying alternative method')
          // Create a temporary link and click it
          const link = document.createElement('a')
          link.href = validUrl
          link.target = '_blank'
          link.rel = 'noopener noreferrer nofollow'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }
      } catch (urlError) {
        console.error('Invalid URL format:', url, urlError)
        // Try to open anyway with basic sanitization in new tab
        const fallbackUrl = url.startsWith('http') ? url : `https://${url}`
        const newTab = window.open(fallbackUrl, '_blank', 'noopener,noreferrer')

        if (!newTab) {
          // Final fallback: create link element
          const link = document.createElement('a')
          link.href = fallbackUrl
          link.target = '_blank'
          link.rel = 'noopener noreferrer nofollow'
          document.body.appendChild(link)
          link.click()
          document.body.removeChild(link)
        }
      }
    } catch (error) {
      console.error('Error in handleVisitWebsite:', error)
    }
  }

  // Share functionality
  const handleShare = useCallback(async () => {
    if (!tool) return

    const shareData = {
      title: `${tool.company_name} - AI Tool Review`,
      text: `Check out ${tool.company_name}: ${tool.short_description}`,
      url: window.location.href,
    }

    try {
      if (navigator.share && navigator.canShare?.(shareData)) {
        await navigator.share(shareData)
      } else {
        // Fallback to copy URL
        await navigator.clipboard.writeText(window.location.href)
        setCopySuccess(true)
        setTimeout(() => setCopySuccess(false), 2000)
      }
    } catch (error) {
      console.error('Error sharing:', error)
      // Fallback to copy URL
      try {
        await navigator.clipboard.writeText(window.location.href)
        setCopySuccess(true)
        setTimeout(() => setCopySuccess(false), 2000)
      } catch (copyError) {
        console.error('Error copying to clipboard:', copyError)
      }
    }
  }, [tool])

  // Bookmark functionality
  const handleBookmark = useCallback(() => {
    if (!tool) return

    try {
      const bookmarks = JSON.parse(localStorage.getItem('bookmarkedTools') || '[]')
      const isCurrentlyBookmarked = bookmarks.includes(tool.id)

      if (isCurrentlyBookmarked) {
        const updatedBookmarks = bookmarks.filter((id: number) => id !== tool.id)
        localStorage.setItem('bookmarkedTools', JSON.stringify(updatedBookmarks))
        setIsBookmarked(false)
      } else {
        bookmarks.push(tool.id)
        localStorage.setItem('bookmarkedTools', JSON.stringify(bookmarks))
        setIsBookmarked(true)
      }
    } catch (error) {
      console.error('Error managing bookmarks:', error)
    }
  }, [tool])

  // Check bookmark status on tool load
  useEffect(() => {
    if (tool) {
      try {
        const bookmarks = JSON.parse(localStorage.getItem('bookmarkedTools') || '[]')
        setIsBookmarked(bookmarks.includes(tool.id))
      } catch (error) {
        console.error('Error reading bookmarks:', error)
        setIsBookmarked(false)
      }
    }
  }, [tool])

  // Review submission handler
  const handleReviewSubmit = async () => {
    if (!tool) return

    try {
      setSubmitting(true)
      const supabase = getSupabase()

      // Simulate user session - in real app, get from auth context
      let currentUserId = localStorage.getItem('currentUserId')
      if (!currentUserId) {
        currentUserId = `user_${Date.now()}_${Math.random().toString(36).substr(2, 9)}`
        localStorage.setItem('currentUserId', currentUserId)
      }

      if (editingReviewId) {
        const { error } = await supabase.from('reviews')
          .update({
            rating: userRating,
            comment: userReview,
            updated_at: new Date().toISOString()
          })
          .eq('id', editingReviewId)

        if (error) throw error
      } else {
        const { error } = await supabase.from('reviews').insert({
          tool_id: tool.id,
          user_id: currentUserId,
          rating: userRating,
          comment: userReview
        })

        if (error) throw error
      }

      setUserReview('')
      setUserReviewTitle('')
      setUserRating(5)
      setReviewDialogOpen(false)
      setEditingReviewId(null)

      fetchReviews(tool.id)
    } catch (error) {
      console.error('Error submitting review:', error)
    } finally {
      setSubmitting(false)
    }
  }

  // Delete review handler
  const handleDeleteReview = async (reviewId: string) => {
    if (!confirm("Are you sure you want to delete this review?")) return

    try {
      const supabase = getSupabase()
      const { error } = await supabase
        .from('reviews')
        .delete()
        .eq('id', reviewId)

      if (error) throw error

      fetchReviews(tool!.id)
    } catch (error) {
      console.error('Error deleting review:', error)
    }
  }

  // UI Components
  const StarRating = () => {
    return (
      <div className="flex gap-1">
        {[1, 2, 3, 4, 5].map((star) => (
          <button
            key={star}
            type="button"
            className="focus:outline-none transition-colors"
            onClick={() => setUserRating(star)}
          >
            <Star
              size={20}
              className={`${
                star <= userRating
                  ? "fill-yellow-400 text-yellow-400"
                  : "text-muted-foreground/40 hover:text-yellow-400"
              }`}
            />
          </button>
        ))}
      </div>
    )
  }

  const RatingStars = ({ rating, size = 16 }: { rating: number; size?: number }) => {
    return (
      <div className="flex gap-0.5">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            size={size}
            className={`${
              star <= rating
                ? "fill-yellow-400 text-yellow-400"
                : "text-muted-foreground/30"
            }`}
          />
        ))}
      </div>
    )
  }

  // Tool Card Component for related tools (memoized for performance)
  const ToolCard = memo(({ tool: relatedTool }: { tool: Tool }) => (
    <div className="group relative bg-card border rounded-xl p-4 hover:shadow-xl hover:shadow-primary/10 transition-all duration-300 hover:-translate-y-2 hover:scale-105 cursor-pointer overflow-hidden"
         onClick={() => router.push(`/Tool/${relatedTool.slug || relatedTool.id}`)}>
      <div className="flex items-start gap-3">
        <div className="w-12 h-12 rounded-lg overflow-hidden bg-secondary/50 flex-shrink-0">
          <img
            src={relatedTool.logo_url || "https://via.placeholder.com/48?text=AI"}
            alt={`${relatedTool.company_name} logo`}
            className="w-full h-full object-cover"
            onError={(e) => {
              (e.target as HTMLImageElement).src = "https://via.placeholder.com/48?text=AI"
            }}
            loading="lazy"
          />
        </div>
        <div className="flex-1 min-w-0">
          <h3 className="font-semibold text-sm group-hover:text-primary transition-colors line-clamp-1">
            {relatedTool.company_name}
          </h3>
          <p className="text-xs text-muted-foreground mt-1 line-clamp-2">
            {relatedTool.short_description}
          </p>
          {relatedTool.primary_task && (
            <Badge variant="secondary" className="text-xs mt-2">
              {relatedTool.primary_task}
            </Badge>
          )}
        </div>
      </div>
    </div>
  ))

  // Reviews List Component
  const ReviewsList = () => {
    if (reviewsLoading) {
      return (
        <div className="flex justify-center py-8">
          <Loader2 className="h-6 w-6 animate-spin text-primary" />
        </div>
      )
    }

    // Calculate rating distribution
    const ratingDistribution = [5, 4, 3, 2, 1].map(rating => {
      const count = reviews.filter(review => review.rating === rating).length
      const percentage = reviews.length > 0 ? (count / reviews.length) * 100 : 0
      return { rating, count, percentage }
    })

    return (
      <div className="space-y-8">
        {/* Reviews Header */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-3">
            <div className="flex items-center gap-3">
              <div className="w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                <Star className="h-5 w-5 text-white" />
              </div>
              <h3 className="text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Reviews & Ratings</h3>
            </div>
            <div className="flex items-center gap-2">
              <RatingStars rating={Math.round(averageRating || 0)} size={16} />
              <span className="font-semibold">{averageRating || '0.0'}</span>
              <span className="text-muted-foreground">({reviews.length})</span>
            </div>
          </div>
          <div className="flex gap-2">
            <Button variant="outline" size="sm">
              Most Recent
            </Button>
            <Button variant="outline" size="sm">
              Cancel
            </Button>
          </div>
        </div>

        <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
          {/* Left Column - Review Form */}
          <div className="lg:col-span-1">
            <Card>
              <CardContent className="p-6">
                <h4 className="font-semibold mb-4">Share Your Experience</h4>

                {/* Star Rating Input */}
                <div className="mb-4">
                  <label className="text-sm font-medium text-muted-foreground mb-2 block">
                    Rate Chatvolt (required)
                  </label>
                  <div className="flex gap-1 mb-2">
                    {[1, 2, 3, 4, 5].map((star) => (
                      <button
                        key={star}
                        type="button"
                        className="focus:outline-none transition-colors"
                        onClick={() => setUserRating(star)}
                      >
                        <Star
                          size={24}
                          className={`${
                            star <= userRating
                              ? "fill-yellow-400 text-yellow-400"
                              : "text-muted-foreground/40 hover:text-yellow-400"
                          }`}
                        />
                      </button>
                    ))}
                  </div>
                  <p className="text-xs text-muted-foreground">Please select a star rating</p>
                </div>

                {/* Review Title */}
                <div className="mb-4">
                  <label className="text-sm font-medium text-muted-foreground mb-2 block">
                    Review Title (required)
                  </label>
                  <input
                    type="text"
                    placeholder="Summarize your experience"
                    className="w-full px-3 py-2 border rounded-md text-sm"
                    maxLength={100}
                    value={userReviewTitle}
                    onChange={(e) => setUserReviewTitle(e.target.value)}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {100 - userReviewTitle.length} characters remaining
                  </p>
                </div>

                {/* Review Text */}
                <div className="mb-6">
                  <label className="text-sm font-medium text-muted-foreground mb-2 block">
                    Your Review (minimum 20 characters)
                  </label>
                  <Textarea
                    placeholder="What did you like or dislike? How was your experience with this AI agent?"
                    rows={4}
                    value={userReview}
                    onChange={(e) => setUserReview(e.target.value)}
                    className="resize-none text-sm"
                    maxLength={2000}
                  />
                  <p className="text-xs text-muted-foreground mt-1">
                    {userReview.length}/2000 characters (minimum)
                  </p>
                  <p className="text-xs text-muted-foreground">2000 characters remaining</p>
                </div>

                {/* Submit Button */}
                <Button
                  onClick={handleReviewSubmit}
                  disabled={submitting || userRating === 0 || userReview.length < 20 || userReviewTitle.length < 5}
                  className="w-full bg-green-600 hover:bg-green-700"
                >
                  {submitting ? (
                    <>
                      <Loader2 className="h-4 w-4 animate-spin mr-2" />
                      Submitting...
                    </>
                  ) : (
                    'Login to Submit Review'
                  )}
                </Button>

                {/* Login Notice */}
                <div className="mt-4 p-3 bg-blue-50 dark:bg-blue-950/20 rounded-lg">
                  <div className="flex items-start gap-2">
                    <div className="w-5 h-5 bg-blue-500 rounded-full flex items-center justify-center flex-shrink-0 mt-0.5">
                      <span className="text-white text-xs">i</span>
                    </div>
                    <p className="text-sm text-blue-700 dark:text-blue-300">
                      You'll need to sign in to submit a review. Your draft will be saved.
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Right Column - Rating Summary & Reviews */}
          <div className="lg:col-span-2 space-y-6">
            {/* Rating Summary */}
            <Card>
              <CardContent className="p-6">
                <div className="flex items-center gap-8">
                  {/* Overall Rating */}
                  <div className="text-center">
                    <div className="text-5xl font-bold mb-2">{averageRating || '5.0'}</div>
                    <RatingStars rating={Math.round(averageRating || 5)} size={20} />
                    <p className="text-sm text-muted-foreground mt-1">
                      Based on {reviews.length} review{reviews.length !== 1 ? 's' : ''}
                    </p>
                  </div>

                  {/* Rating Distribution */}
                  <div className="flex-1">
                    {ratingDistribution.map(({ rating, count, percentage }) => (
                      <div key={rating} className="flex items-center gap-3 mb-2">
                        <span className="text-sm font-medium w-2">{rating}</span>
                        <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
                        <div className="flex-1 bg-muted rounded-full h-2">
                          <div
                            className="bg-yellow-400 h-2 rounded-full transition-all duration-300"
                            style={{ width: `${percentage}%` }}
                          />
                        </div>
                        <span className="text-sm text-muted-foreground w-8">{count}</span>
                      </div>
                    ))}
                  </div>
                </div>
              </CardContent>
            </Card>

            {/* Individual Reviews */}
            {reviews.length > 0 ? (
              <div className="space-y-4">
                {reviews.map(review => {
                  const currentUserId = localStorage.getItem('currentUserId')
                  const isUserReview = currentUserId && review.user_id === currentUserId

                  return (
                    <Card key={review.id} className="hover:shadow-sm transition-shadow">
                      <CardContent className="p-6">
                        <div className="flex items-start gap-4">
                          {/* User Avatar */}
                          <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900/30 rounded-full flex items-center justify-center flex-shrink-0">
                            <span className="text-sm font-semibold text-blue-600 dark:text-blue-400">
                              {review.user_email?.charAt(0).toUpperCase() || 'U'}
                            </span>
                          </div>

                          <div className="flex-1">
                            {/* User Info & Rating */}
                            <div className="flex items-center justify-between mb-3">
                              <div>
                                <div className="font-semibold text-sm">{review.user_email}</div>
                                <div className="text-xs text-muted-foreground">{review.created_at}</div>
                              </div>
                              <div className="flex items-center gap-1">
                                <RatingStars rating={review.rating} size={16} />
                                <span className="font-semibold ml-1">{review.rating}</span>
                              </div>
                            </div>

                            {/* Review Content */}
                            {review.comment && (
                              <div className="mb-4">
                                <p className="text-sm leading-relaxed text-foreground">
                                  {review.comment}
                                </p>
                                {review.comment.length > 200 && (
                                  <button className="text-blue-600 hover:text-blue-700 text-sm mt-2">
                                    Read more
                                  </button>
                                )}
                              </div>
                            )}

                            {/* Review Actions */}
                            <div className="flex items-center justify-between pt-3 border-t border-muted">
                              <div className="flex items-center gap-4">
                                <Button variant="ghost" size="sm" className="gap-2 h-8 px-3">
                                  <ThumbsUp className="h-3 w-3" />
                                  <span className="text-xs">Helpful (0)</span>
                                </Button>
                                <Button variant="ghost" size="sm" className="gap-2 h-8 px-3">
                                  <span className="text-xs">Report</span>
                                </Button>
                              </div>

                              {/* Edit/Delete for user's own review */}
                              {isUserReview && (
                                <div className="flex gap-2">
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => {
                                      setEditingReviewId(review.id)
                                      setUserRating(review.rating)
                                      setUserReview(review.comment || '')
                                      setReviewDialogOpen(true)
                                    }}
                                    className="gap-1 h-8 px-3"
                                  >
                                    <Edit className="h-3 w-3" />
                                    <span className="text-xs">Edit</span>
                                  </Button>
                                  <Button
                                    variant="ghost"
                                    size="sm"
                                    onClick={() => handleDeleteReview(review.id)}
                                    className="gap-1 h-8 px-3 text-destructive hover:text-destructive"
                                  >
                                    <Trash2 className="h-3 w-3" />
                                    <span className="text-xs">Delete</span>
                                  </Button>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </CardContent>
                    </Card>
                  )
                })}
              </div>
            ) : (
              <Card className="border-dashed">
                <CardContent className="text-center py-12">
                  <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                    <User className="h-8 w-8 text-muted-foreground" />
                  </div>
                  <h3 className="text-lg font-semibold mb-2">No reviews yet</h3>
                  <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                    Be the first to review this tool and help others make informed decisions.
                  </p>
                </CardContent>
              </Card>
            )}
          </div>
        </div>
      </div>
    )
  }

  // Stats Card Component
  const StatsCard = ({ icon: Icon, label, value, color = "primary" }: {
    icon: any,
    label: string,
    value: string | number,
    color?: string
  }) => (
    <div className="bg-card border rounded-lg p-4 hover-lift hover-glow">
      <div className="flex items-center gap-3">
        <div className={`p-2 rounded-lg bg-${color}/10`}>
          <Icon className={`h-4 w-4 text-${color}`} />
        </div>
        <div>
          <p className="text-sm text-muted-foreground">{label}</p>
          <p className="text-lg font-bold">{value}</p>
        </div>
      </div>
    </div>
  )

  // Loading and Error States
  if (loading) {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1 flex items-center justify-center py-24" role="main" aria-label="Loading tool details">
          <div className="text-center space-y-4">
            <div className="animate-spin rounded-full h-12 w-12 border-b-2 border-primary mx-auto" aria-hidden="true"></div>
            <p className="text-muted-foreground animate-pulse">Loading tool details...</p>
          </div>
        </main>
      </div>
    )
  }

  if (notFound || !tool) {
    return (
      <div className="flex min-h-screen flex-col">
        <main className="flex-1 py-24" role="main">
          <div className="container-wide">
            <MotionWrapper animation="fadeIn">
              <div className="text-center py-12 max-w-md mx-auto">
                <div className="mb-6">
                  <AlertTriangle className="h-16 w-16 mx-auto text-muted-foreground/50" />
                </div>
                <h1 className="text-3xl font-bold mb-4">Tool Not Found</h1>
                <p className="text-muted-foreground mb-8 leading-relaxed">
                  The AI tool you're looking for doesn't exist or may have been removed.
                  Try searching for similar tools or browse our collection.
                </p>
                <div className="space-y-3">
                  <Button onClick={() => router.push('/tools')} className="w-full sm:w-auto">
                    <ChevronLeft className="mr-2 h-4 w-4" />
                    Browse All Tools
                  </Button>
                  <Button
                    variant="outline"
                    onClick={() => router.push('/')}
                    className="w-full sm:w-auto sm:ml-2"
                  >
                    Go to Homepage
                  </Button>
                </div>
              </div>
            </MotionWrapper>
          </div>
        </main>
      </div>
    )
  }

  // Generate comprehensive JSON-LD structured data for maximum SEO
  const generateStructuredData = () => {
    if (!tool) return null

    try {
      const baseUrl = typeof window !== 'undefined' ? window.location.origin : 'https://aianytool.com'
      const currentUrl = typeof window !== 'undefined' ? window.location.href : `${baseUrl}/Tool/${slug}`

      // Main SoftwareApplication schema
      const mainSchema = {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "@id": currentUrl,
        "name": tool.company_name || "AI Tool",
        "alternateName": tool.company_name ? [tool.company_name, `${tool.company_name} AI Tool`] : undefined,
        "description": tool.full_description || tool.short_description || `${tool.company_name} is a powerful AI tool that helps users with ${tool.primary_task || 'various AI tasks'}. Discover features, pricing, and reviews on AiAnyTool.com - your ultimate AI tools directory.`,
        "image": [
          tool.logo_url || `${baseUrl}/default-logo.png`,
          tool.featured_image_url || tool.logo_url || `${baseUrl}/default-featured.png`
        ].filter(Boolean),
        "screenshot": tool.featured_image_url || tool.logo_url,
        "url": currentUrl,
        "sameAs": tool.visit_website_url ? [tool.visit_website_url] : undefined,
        "applicationCategory": [
          "AI Tool",
          tool.primary_task || "Artificial Intelligence",
          "Software Application",
          "Business Software"
        ],
        "applicationSubCategory": tool.primary_task || "AI Tool",
        "operatingSystem": ["Web Browser", "Cross-platform", "Online"],
        "softwareVersion": "Latest",
        "releaseNotes": tool.updated_at ? `Last updated: ${new Date(tool.updated_at).toLocaleDateString()}` : undefined,
        "dateCreated": tool.created_at || new Date().toISOString(),
        "dateModified": tool.updated_at || tool.created_at || new Date().toISOString(),
        "datePublished": tool.created_at || new Date().toISOString(),
        "publisher": {
          "@type": "Organization",
          "name": "AiAnyTool.com",
          "url": baseUrl,
          "logo": {
            "@type": "ImageObject",
            "url": `${baseUrl}/logo.png`,
            "width": 200,
            "height": 60
          }
        },
        "provider": {
          "@type": "Organization",
          "name": "AiAnyTool.com",
          "url": baseUrl
        },
        "mainEntityOfPage": {
          "@type": "WebPage",
          "@id": currentUrl
        },
        "keywords": [
          tool.company_name,
          tool.primary_task,
          "AI Tool",
          "Artificial Intelligence",
          "AI Software",
          tool.pricing?.includes('Free') ? "Free AI Tool" : "AI Tool",
          ...(tool.applicable_tasks || [])
        ].filter(Boolean).join(", "),
        "featureList": tool.applicable_tasks || [],
        "offers": tool.pricing ? {
          "@type": "Offer",
          "price": tool.pricing.toLowerCase().includes('free') ? "0" : "varies",
          "priceCurrency": "USD",
          "availability": "https://schema.org/InStock",
          "priceValidUntil": new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString(),
          "seller": {
            "@type": "Organization",
            "name": tool.company_name || "AI Tool Provider"
          }
        } : undefined,
        "aggregateRating": averageRating && reviews.length > 0 ? {
          "@type": "AggregateRating",
          "ratingValue": averageRating,
          "ratingCount": reviews.length,
          "reviewCount": reviews.length,
          "bestRating": 5,
          "worstRating": 1
        } : undefined,
        "review": reviews.length > 0 ? reviews.slice(0, 10).map(review => ({
          "@type": "Review",
          "reviewRating": {
            "@type": "Rating",
            "ratingValue": review.rating || 5,
            "bestRating": 5,
            "worstRating": 1
          },
          "author": {
            "@type": "Person",
            "name": review.user_email || "Verified User"
          },
          "reviewBody": review.comment || `Great AI tool with ${review.rating} star rating`,
          "datePublished": review.created_at || new Date().toISOString()
        })) : undefined
      }

      // Additional schemas for better SEO
      const breadcrumbSchema = {
        "@context": "https://schema.org",
        "@type": "BreadcrumbList",
        "itemListElement": [
          {
            "@type": "ListItem",
            "position": 1,
            "name": "Home",
            "item": baseUrl
          },
          {
            "@type": "ListItem",
            "position": 2,
            "name": "AI Tools",
            "item": `${baseUrl}/tools`
          },
          {
            "@type": "ListItem",
            "position": 3,
            "name": tool.primary_task || "AI Tools",
            "item": `${baseUrl}/category/${tool.primary_task?.toLowerCase().replace(/\s+/g, '-') || 'ai-tools'}`
          },
          {
            "@type": "ListItem",
            "position": 4,
            "name": tool.company_name,
            "item": currentUrl
          }
        ]
      }

      const faqSchema = tool.faqs && Object.keys(tool.faqs).length > 0 ? {
        "@context": "https://schema.org",
        "@type": "FAQPage",
        "mainEntity": Array.isArray(tool.faqs)
          ? tool.faqs.map((faq: any) => ({
              "@type": "Question",
              "name": faq.question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": faq.answer
              }
            }))
          : Object.entries(tool.faqs).map(([question, answer]) => ({
              "@type": "Question",
              "name": question,
              "acceptedAnswer": {
                "@type": "Answer",
                "text": answer as string
              }
            }))
      } : undefined

      // Return array of schemas
      return [mainSchema, breadcrumbSchema, faqSchema].filter(Boolean)

    } catch (error) {
      console.error('Error generating structured data:', error)
      return {
        "@context": "https://schema.org",
        "@type": "SoftwareApplication",
        "name": tool.company_name || "AI Tool",
        "description": tool.short_description || "AI Tool"
      }
    }
  }

  // Main Render
  return (
    <TooltipProvider>
      <div className="flex min-h-screen flex-col bg-background">
        {/* JSON-LD Structured Data */}
        {tool && (() => {
          const schemas = generateStructuredData()
          return Array.isArray(schemas)
            ? schemas.map((schema, index) => (
                <script
                  key={index}
                  type="application/ld+json"
                  dangerouslySetInnerHTML={{
                    __html: JSON.stringify(schema)
                  }}
                />
              ))
            : (
                <script
                  type="application/ld+json"
                  dangerouslySetInnerHTML={{
                    __html: JSON.stringify(schemas)
                  }}
                />
              )
        })()}

        <main className="flex-1 relative" role="main" itemScope itemType="https://schema.org/SoftwareApplication">
          <div className="container max-w-7xl mx-auto px-3 sm:px-4 lg:px-6 py-2 sm:py-3 lg:py-4">
            <MotionWrapper animation="fadeIn">
              {tool && (
                <>
                  {/* Main Content Grid */}
                  <div className="grid grid-cols-1 xl:grid-cols-4 gap-3 sm:gap-4 lg:gap-6">
                    {/* Main Content - Left Column */}
                    <div className="xl:col-span-3 space-y-3 sm:space-y-4 lg:space-y-6">
                      {/* Featured Image */}
                      {tool.featured_image_url && (
                        <Card className="overflow-hidden">
                          <CardContent className="p-0">
                            <div className="aspect-video bg-muted/30 relative overflow-hidden">
                              <img
                                src={tool.featured_image_url}
                                alt={`${tool.company_name} featured screenshot`}
                                className="w-full h-full object-cover transition-all hover:scale-105 duration-300 relative z-10"
                                loading="lazy"
                                onLoad={(e) => {
                                  const target = e.target as HTMLImageElement
                                  target.style.opacity = '1'
                                  // Hide loading placeholder
                                  const placeholder = target.parentElement?.querySelector('.loading-placeholder') as HTMLElement
                                  if (placeholder) placeholder.style.display = 'none'
                                }}
                                onError={(e) => {
                                  const target = e.target as HTMLImageElement
                                  target.src = 'https://via.placeholder.com/800x450/f3f4f6/9ca3af?text=Screenshot+Not+Available'
                                  target.style.opacity = '1'
                                  // Hide loading placeholder
                                  const placeholder = target.parentElement?.querySelector('.loading-placeholder') as HTMLElement
                                  if (placeholder) placeholder.style.display = 'none'
                                }}
                                style={{ opacity: '0', transition: 'opacity 0.3s ease' }}
                              />
                              {/* Loading placeholder */}
                              <div className="loading-placeholder absolute inset-0 flex items-center justify-center bg-muted/50">
                                <div className="text-center">
                                  <div className="w-8 h-8 border-2 border-primary border-t-transparent rounded-full animate-spin mx-auto mb-2"></div>
                                  <p className="text-sm text-muted-foreground">Loading screenshot...</p>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      )}

                      {/* Sticky Tool Header + Navigation Tabs */}
                      <div className={`sticky top-[56px] z-10 bg-background/95 backdrop-blur-sm border-b mb-3 sm:mb-4 lg:mb-6 -mx-3 sm:mx-0 px-3 sm:px-0 transition-all duration-300 ${
                        isHeaderScrolled ? 'shadow-lg' : ''
                      }`}>
                        {/* Tool Header - Compact Version */}
                        <div className={`border-b border-border/30 transition-all duration-300 ${
                          isHeaderScrolled ? 'py-2 sm:py-2.5' : 'py-3 sm:py-4'
                        }`}>
                          <div className={`flex items-center transition-all duration-300 ${
                            isHeaderScrolled ? 'gap-1.5 sm:gap-2 lg:gap-3' : 'gap-2 sm:gap-3 lg:gap-4'
                          }`}>
                            {/* Logo */}
                            <div className={`rounded-lg overflow-hidden border border-border/30 bg-background shadow-sm flex-shrink-0 transition-all duration-300 ${
                              isHeaderScrolled
                                ? 'w-8 h-8 sm:w-10 sm:h-10 lg:w-12 lg:h-12'
                                : 'w-12 h-12 sm:w-16 sm:h-16 lg:w-18 lg:h-18'
                            }`}>
                              <img
                                src={tool.logo_url || "https://via.placeholder.com/72?text=AI"}
                                alt={`${tool.company_name} logo`}
                                className="w-full h-full object-cover"
                              />
                            </div>

                            {/* Tool Name + Rating + Date Info */}
                            <div className="flex-1 min-w-0">
                              <div className={`flex items-center flex-wrap transition-all duration-300 ${
                                isHeaderScrolled ? 'gap-1.5 sm:gap-2 mb-0.5' : 'gap-2 sm:gap-3 mb-1'
                              }`}>
                                <h1 className={`font-bold text-primary tracking-tight leading-tight truncate transition-all duration-300 ${
                                  isHeaderScrolled
                                    ? 'text-sm sm:text-base md:text-lg lg:text-xl'
                                    : 'text-lg sm:text-xl md:text-2xl lg:text-3xl'
                                }`} itemProp="name">
                                  {tool.company_name}
                                </h1>
                                {/* Rating - Next to name */}
                                <div className={`flex items-center gap-1 bg-amber-50 dark:bg-amber-950/30 rounded-md border border-amber-200 dark:border-amber-800 flex-shrink-0 transition-all duration-300 ${
                                  isHeaderScrolled
                                    ? 'px-1 sm:px-1.5 py-0.5'
                                    : 'px-1.5 sm:px-2 py-0.5 sm:py-1'
                                }`}>
                                  <span className={`font-semibold text-amber-700 dark:text-amber-400 transition-all duration-300 ${
                                    isHeaderScrolled ? 'text-xs' : 'text-xs'
                                  }`}>
                                    {averageRating ? averageRating.toFixed(1) : '0.0'}
                                  </span>
                                  <div className="flex">
                                    {[1, 2, 3, 4, 5].map((star) => (
                                      <Star
                                        key={star}
                                        className={`transition-all duration-300 ${
                                          isHeaderScrolled
                                            ? 'h-1.5 w-1.5 sm:h-2 sm:w-2'
                                            : 'h-2 w-2 sm:h-2.5 sm:w-2.5 lg:h-3 lg:w-3'
                                        } ${
                                          star <= Math.round(averageRating || 0)
                                            ? 'fill-amber-400 text-amber-400'
                                            : 'text-amber-200 dark:text-amber-800'
                                        }`}
                                      />
                                    ))}
                                  </div>
                                  <span className={`text-amber-600 dark:text-amber-500 font-medium transition-all duration-300 ${
                                    isHeaderScrolled ? 'text-xs' : 'text-xs'
                                  }`}>({reviews.length})</span>
                                </div>
                              </div>
                              {!isHeaderScrolled && (
                                <div className="flex items-center gap-2 sm:gap-3 text-xs flex-wrap transition-all duration-300">
                                  {tool.created_at && (
                                    <div className="flex items-center gap-1 text-blue-600 dark:text-blue-400">
                                      <Calendar className="h-3 w-3 flex-shrink-0" />
                                      <span className="font-medium whitespace-nowrap">Added {new Date(tool.created_at).toLocaleDateString('en-US', {
                                        month: 'short',
                                        day: 'numeric',
                                        year: 'numeric'
                                      })}</span>
                                    </div>
                                  )}
                                  {tool.updated_at && tool.updated_at !== tool.created_at && (
                                    <div className="flex items-center gap-1 text-emerald-600 dark:text-emerald-400">
                                      <Clock className="h-3 w-3 flex-shrink-0" />
                                      <span className="font-medium whitespace-nowrap">Updated {new Date(tool.updated_at).toLocaleDateString('en-US', {
                                        month: 'short',
                                        day: 'numeric',
                                        year: 'numeric'
                                      })}</span>
                                    </div>
                                  )}
                                </div>
                              )}
                            </div>

                            {/* Buttons */}
                            <div className={`flex flex-shrink-0 transition-all duration-300 ${
                              isHeaderScrolled ? 'gap-1' : 'gap-1.5 sm:gap-2'
                            }`}>
                              <Button
                                size={isHeaderScrolled ? "sm" : "sm"}
                                className={`bg-emerald-600 hover:bg-emerald-700 text-white font-medium shadow-sm border-0 transition-all duration-300 ${
                                  isHeaderScrolled
                                    ? 'px-2 py-1.5 text-xs'
                                    : 'gap-1 sm:gap-1.5 px-2 sm:px-3 lg:px-4 py-1.5 sm:py-2 text-xs sm:text-sm'
                                }`}
                                onClick={handleVisitWebsite}
                                title="Opens in new tab"
                              >
                                <ExternalLink className={`transition-all duration-300 ${
                                  isHeaderScrolled ? 'h-3 w-3' : 'h-3 w-3 sm:h-4 sm:w-4'
                                }`} />
                                {!isHeaderScrolled && (
                                  <>
                                    <span className="hidden sm:inline">Visit Website</span>
                                    <span className="sm:hidden">Visit</span>
                                  </>
                                )}
                              </Button>

                              {/* Share Button with Dropdown */}
                              <div className="relative share-menu-container">
                                <Button
                                  variant="outline"
                                  size="sm"
                                  onClick={() => setShareMenuOpen(!shareMenuOpen)}
                                  className={`group border-2 transition-all duration-200 ${
                                    isHeaderScrolled
                                      ? 'px-2 py-1.5'
                                      : 'px-2 sm:px-2.5 lg:px-3 py-1.5 sm:py-2'
                                  } ${
                                    shareMenuOpen
                                      ? 'border-primary bg-primary/5 text-primary shadow-md'
                                      : 'border-border/60 hover:border-primary/40 hover:bg-primary/5 hover:text-primary'
                                  }`}
                                  title="Share this tool"
                                >
                                  <Share2 className={`transition-all duration-200 ${
                                    isHeaderScrolled ? 'h-3 w-3' : 'h-3 w-3 sm:h-4 sm:w-4'
                                  } ${
                                    shareMenuOpen ? 'rotate-12 scale-110' : 'group-hover:scale-110'
                                  }`} />
                                  {!isHeaderScrolled && (
                                    <span className="hidden lg:inline ml-1.5 font-medium text-xs sm:text-sm">Share</span>
                                  )}
                                </Button>

                                {/* Share Dropdown Menu */}
                                {shareMenuOpen && (
                                  <div className="absolute right-0 top-full mt-3 w-52 bg-background/95 backdrop-blur-sm border border-border/50 rounded-xl shadow-xl z-50 animate-in slide-in-from-top-2 duration-200">
                                    <div className="p-3 space-y-2">
                                      {/* Copy Success Message */}
                                      {copySuccess && (
                                        <div className="flex items-center gap-2 px-3 py-2.5 text-sm text-emerald-700 dark:text-emerald-300 bg-emerald-50 dark:bg-emerald-950/30 rounded-lg mb-3 border border-emerald-200 dark:border-emerald-800">
                                          <Check className="h-4 w-4" />
                                          <span className="font-medium">Link copied!</span>
                                        </div>
                                      )}

                                      <button
                                        onClick={async () => {
                                          try {
                                            await navigator.clipboard.writeText(window.location.href)
                                            setCopySuccess(true)
                                            setTimeout(() => setCopySuccess(false), 2000)
                                            setShareMenuOpen(false)
                                          } catch (error) {
                                            console.error('Failed to copy:', error)
                                          }
                                        }}
                                        className="w-full flex items-center gap-3 px-3 py-2.5 text-sm font-medium hover:bg-primary/10 hover:text-primary rounded-lg transition-all duration-200 group"
                                      >
                                        <div className="w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800/50 transition-colors">
                                          <Copy className="h-4 w-4 text-blue-600 dark:text-blue-400" />
                                        </div>
                                        <span>Copy Link</span>
                                      </button>

                                      <button
                                        onClick={() => {
                                          const url = `https://twitter.com/intent/tweet?text=Check out ${tool?.company_name}&url=${encodeURIComponent(window.location.href)}`
                                          window.open(url, '_blank', 'noopener,noreferrer')
                                          setShareMenuOpen(false)
                                        }}
                                        className="w-full flex items-center gap-3 px-3 py-2.5 text-sm font-medium hover:bg-primary/10 hover:text-primary rounded-lg transition-all duration-200 group"
                                      >
                                        <div className="w-8 h-8 rounded-lg bg-slate-100 dark:bg-slate-800/50 flex items-center justify-center group-hover:bg-slate-200 dark:group-hover:bg-slate-700/50 transition-colors">
                                          <svg className="h-4 w-4 text-slate-700 dark:text-slate-300" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M18.244 2.25h3.308l-7.227 8.26 8.502 11.24H16.17l-5.214-6.817L4.99 21.75H1.68l7.73-8.835L1.254 2.25H8.08l4.713 6.231zm-1.161 17.52h1.833L7.084 4.126H5.117z"/>
                                          </svg>
                                        </div>
                                        <span>Share on X</span>
                                      </button>

                                      <button
                                        onClick={() => {
                                          const url = `https://www.linkedin.com/sharing/share-offsite/?url=${encodeURIComponent(window.location.href)}`
                                          window.open(url, '_blank', 'noopener,noreferrer')
                                          setShareMenuOpen(false)
                                        }}
                                        className="w-full flex items-center gap-3 px-3 py-2.5 text-sm font-medium hover:bg-primary/10 hover:text-primary rounded-lg transition-all duration-200 group"
                                      >
                                        <div className="w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800/50 transition-colors">
                                          <svg className="h-4 w-4 text-blue-600 dark:text-blue-400" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M20.447 20.452h-3.554v-5.569c0-1.328-.027-3.037-1.852-3.037-1.853 0-2.136 1.445-2.136 2.939v5.667H9.351V9h3.414v1.561h.046c.477-.9 1.637-1.85 3.37-1.85 3.601 0 4.267 2.37 4.267 5.455v6.286zM5.337 7.433c-1.144 0-2.063-.926-2.063-2.065 0-1.138.92-2.063 2.063-2.063 1.14 0 2.064.925 2.064 2.063 0 1.139-.925 2.065-2.064 2.065zm1.782 13.019H3.555V9h3.564v11.452zM22.225 0H1.771C.792 0 0 .774 0 1.729v20.542C0 23.227.792 24 1.771 24h20.451C23.2 24 24 23.227 24 22.271V1.729C24 .774 23.2 0 22.222 0h.003z"/>
                                          </svg>
                                        </div>
                                        <span>Share on LinkedIn</span>
                                      </button>

                                      <button
                                        onClick={() => {
                                          const url = `https://www.facebook.com/sharer/sharer.php?u=${encodeURIComponent(window.location.href)}`
                                          window.open(url, '_blank', 'noopener,noreferrer')
                                          setShareMenuOpen(false)
                                        }}
                                        className="w-full flex items-center gap-3 px-3 py-2.5 text-sm font-medium hover:bg-primary/10 hover:text-primary rounded-lg transition-all duration-200 group"
                                      >
                                        <div className="w-8 h-8 rounded-lg bg-blue-100 dark:bg-blue-900/30 flex items-center justify-center group-hover:bg-blue-200 dark:group-hover:bg-blue-800/50 transition-colors">
                                          <svg className="h-4 w-4 text-blue-600 dark:text-blue-400" viewBox="0 0 24 24" fill="currentColor">
                                            <path d="M24 12.073c0-6.627-5.373-12-12-12s-12 5.373-12 12c0 5.99 4.388 10.954 10.125 11.854v-8.385H7.078v-3.47h3.047V9.43c0-3.007 1.792-4.669 4.533-4.669 1.312 0 2.686.235 2.686.235v2.953H15.83c-1.491 0-1.956.925-1.956 1.874v2.25h3.328l-.532 3.47h-2.796v8.385C19.612 23.027 24 18.062 24 12.073z"/>
                                          </svg>
                                        </div>
                                        <span>Share on Facebook</span>
                                      </button>
                                    </div>
                                  </div>
                                )}
                              </div>
                            </div>
                          </div>
                        </div>

                    {/* Navigation Tabs */}
                    <div className={`flex bg-muted/30 rounded-lg overflow-x-auto scrollbar-hide transition-all duration-300 ${
                      isHeaderScrolled
                        ? 'gap-0.5 p-0.5'
                        : 'gap-0.5 sm:gap-1 p-0.5 sm:p-1'
                    }`}>
                      {[
                        { id: 'overview', label: 'Overview' },
                        { id: 'features', label: 'Features' },
                        { id: 'pricing', label: 'Pricing' },
                        { id: 'alternatives', label: 'Alternatives' },
                        { id: 'reviews', label: 'Reviews' }
                      ].map((tab) => (
                        <button
                          key={tab.id}
                          className={`relative font-medium rounded-md transition-all duration-200 whitespace-nowrap flex-shrink-0 ${
                            isHeaderScrolled
                              ? 'px-1.5 sm:px-2 py-1.5 text-xs'
                              : 'px-2 sm:px-3 lg:px-4 py-2 sm:py-2.5 text-xs sm:text-sm'
                          } ${
                            activeTab === tab.id
                              ? 'bg-background text-primary shadow-sm border border-border/50'
                              : 'text-muted-foreground hover:text-foreground hover:bg-background/50'
                          }`}
                          onClick={() => scrollToSection(tab.id)}
                        >
                          {tab.label}
                          {tab.id === 'reviews' && reviews.length > 0 && (
                            <span className={`rounded-full transition-all duration-300 ${
                              isHeaderScrolled
                                ? 'ml-1 px-1 py-0.5 text-xs'
                                : 'ml-1 sm:ml-1.5 px-1 sm:px-1.5 py-0.5 text-xs'
                            } ${
                              activeTab === tab.id
                                ? 'bg-primary/10 text-primary'
                                : 'bg-muted text-muted-foreground'
                            }`}>
                              {reviews.length}
                            </span>
                          )}
                        </button>
                      ))}
                    </div>
                  </div>

                  {/* Overview Section */}
                  <div id="overview" className="space-y-4 sm:space-y-6 mb-6 sm:mb-8 lg:mb-10">
                    <div>
                      <div className="flex items-center gap-3 mb-3 sm:mb-4">
                        <div className="w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                          <Eye className="h-5 w-5 text-white" />
                        </div>
                        <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">{tool.company_name} Overview</h2>
                      </div>
                      <div className="prose prose-gray dark:prose-invert max-w-none">
                        <p className="text-readable leading-relaxed text-sm sm:text-base">
                          {tool.full_description || tool.short_description || `${tool.company_name} is an AI chatbot platform transforming customer engagement with automation, multilingual support, and seamless integrations—all in a no-code interface. Deploy personalized AI agents in minutes to handle 24/7 customer service, lead generation, and routine inquiries, cutting costs while boosting efficiency.`}
                        </p>
                      </div>
                    </div>

                    {/* Key Features Grid */}
                    {tool.applicable_tasks && tool.applicable_tasks.length > 0 && (
                      <div>
                        <div className="flex items-center gap-3 mb-3 sm:mb-4">
                          <div className="w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                            <Target className="h-4 w-4 text-white" />
                          </div>
                          <h3 className="text-lg sm:text-xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Key Capabilities</h3>
                        </div>
                        <div className="grid grid-cols-1 sm:grid-cols-2 gap-3 sm:gap-4">
                          {tool.applicable_tasks.map((task: any, i: number) => (
                            <div key={i} className="flex items-center gap-2 sm:gap-3 p-3 sm:p-4 bg-muted/50 rounded-lg">
                              <CheckCircle className="h-4 w-4 sm:h-5 sm:w-5 text-green-500 flex-shrink-0" />
                              <span className="font-medium text-sm sm:text-base">
                                {typeof task === 'string' ? task : (task?.name || task?.value || String(task))}
                              </span>
                            </div>
                          ))}
                        </div>
                      </div>
                    )}

                    {/* FAQ Section */}
                    {tool.faqs && Object.keys(tool.faqs).length > 0 && (
                      <div>
                        <div className="flex items-center gap-3 mb-6">
                          <div className="w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                            <HelpCircle className="h-5 w-5 text-white" />
                          </div>
                          <h3 className="text-xl font-semibold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Frequently Asked Questions</h3>
                        </div>
                        <div className="space-y-3">
                          {Array.isArray(tool.faqs) ?
                            tool.faqs.map((faq: any, i: number) => (
                              <Collapsible key={i}>
                                <Card className="border border-border/50 hover:border-primary/30 transition-all duration-200 overflow-hidden">
                                  <CollapsibleTrigger asChild>
                                    <button
                                      className="w-full p-4 sm:p-6 text-left hover:bg-muted/30 transition-colors duration-200 group"
                                      onClick={() => toggleFaqItem(i)}
                                    >
                                      <div className="flex items-center justify-between">
                                        <h4 className="font-semibold text-base sm:text-lg text-foreground group-hover:text-primary transition-colors duration-200 pr-4">
                                          {faq.question}
                                        </h4>
                                        <div className="flex-shrink-0">
                                          {openFaqItems.has(i) ? (
                                            <ChevronUp className="h-5 w-5 text-primary transition-transform duration-200" />
                                          ) : (
                                            <ChevronDown className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors duration-200" />
                                          )}
                                        </div>
                                      </div>
                                    </button>
                                  </CollapsibleTrigger>
                                  <CollapsibleContent className="data-[state=open]:animate-accordion-down data-[state=closed]:animate-accordion-up">
                                    <div className="px-4 sm:px-6 pb-4 sm:pb-6 pt-0">
                                      <div className="border-t border-border/30 pt-4">
                                        <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
                                          {faq.answer}
                                        </p>
                                      </div>
                                    </div>
                                  </CollapsibleContent>
                                </Card>
                              </Collapsible>
                            )) :
                            Object.entries(tool.faqs).map(([key, value]: [string, any], i: number) => (
                              <Collapsible key={i}>
                                <Card className="border border-border/50 hover:border-primary/30 transition-all duration-200 overflow-hidden">
                                  <CollapsibleTrigger asChild>
                                    <button
                                      className="w-full p-4 sm:p-6 text-left hover:bg-muted/30 transition-colors duration-200 group"
                                      onClick={() => toggleFaqItem(i)}
                                    >
                                      <div className="flex items-center justify-between">
                                        <h4 className="font-semibold text-base sm:text-lg text-foreground group-hover:text-primary transition-colors duration-200 pr-4">
                                          {key}
                                        </h4>
                                        <div className="flex-shrink-0">
                                          {openFaqItems.has(i) ? (
                                            <ChevronUp className="h-5 w-5 text-primary transition-transform duration-200" />
                                          ) : (
                                            <ChevronDown className="h-5 w-5 text-muted-foreground group-hover:text-primary transition-colors duration-200" />
                                          )}
                                        </div>
                                      </div>
                                    </button>
                                  </CollapsibleTrigger>
                                  <CollapsibleContent className="data-[state=open]:animate-accordion-down data-[state=closed]:animate-accordion-up">
                                    <div className="px-4 sm:px-6 pb-4 sm:pb-6 pt-0">
                                      <div className="border-t border-border/30 pt-4">
                                        <p className="text-muted-foreground leading-relaxed text-sm sm:text-base">
                                          {value}
                                        </p>
                                      </div>
                                    </div>
                                  </CollapsibleContent>
                                </Card>
                              </Collapsible>
                            ))
                          }
                        </div>
                      </div>
                    )}
                  </div>

                  {/* Features Section */}
                  <div id="features" className="space-y-4 sm:space-y-6 mb-6 sm:mb-8 lg:mb-10">
                    <div className="flex items-center gap-3 mb-3 sm:mb-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                        <Zap className="h-5 w-5 text-white" />
                      </div>
                      <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Features</h2>
                    </div>

                    {/* Pros and Cons */}
                    {((tool.pros && tool.pros.length > 0) || (tool.cons && tool.cons.length > 0)) && (
                      <div className="grid grid-cols-1 lg:grid-cols-2 gap-4 sm:gap-6 lg:gap-8">
                        {tool.pros && tool.pros.length > 0 && (
                          <Card className="border-green-200 dark:border-green-800">
                            <CardHeader className="pb-4">
                              <CardTitle className="text-xl flex items-center gap-3 text-green-700 dark:text-green-400">
                                <div className="w-8 h-8 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
                                  <ThumbsUp className="w-4 h-4" />
                                </div>
                                Pros
                              </CardTitle>
                            </CardHeader>
                            <CardContent>
                              <ul className="space-y-3">
                                {tool.pros.map((pro: any, i: number) => (
                                  <li key={i} className="flex items-start gap-3">
                                    <CheckCircle className="h-5 w-5 text-green-500 flex-shrink-0 mt-0.5" />
                                    <span className="leading-relaxed">
                                      {typeof pro === 'string' ? pro : (pro?.name || pro?.value || String(pro))}
                                    </span>
                                  </li>
                                ))}
                              </ul>
                            </CardContent>
                          </Card>
                        )}

                        {tool.cons && tool.cons.length > 0 && (
                          <Card className="border-red-200 dark:border-red-800">
                            <CardHeader className="pb-4">
                              <CardTitle className="text-xl flex items-center gap-3 text-red-700 dark:text-red-400">
                                <div className="w-8 h-8 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
                                  <ThumbsDown className="w-4 h-4" />
                                </div>
                                Cons
                              </CardTitle>
                            </CardHeader>
                            <CardContent>
                              <ul className="space-y-3">
                                {tool.cons.map((con: any, i: number) => (
                                  <li key={i} className="flex items-start gap-3">
                                    <AlertTriangle className="h-5 w-5 text-red-500 flex-shrink-0 mt-0.5" />
                                    <span className="leading-relaxed">
                                      {typeof con === 'string' ? con : (con?.name || con?.value || String(con))}
                                    </span>
                                  </li>
                                ))}
                              </ul>
                            </CardContent>
                          </Card>
                        )}
                      </div>
                    )}

                    {/* Technical Details */}
                    <div>
                      <div className="flex items-center gap-3 mb-6">
                        <div className="w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                          <Settings className="h-4 w-4 text-white" />
                        </div>
                        <h3 className="text-xl font-semibold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Technical Information</h3>
                      </div>
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {tool.primary_task && (
                          <Card>
                            <CardContent className="p-6">
                              <div className="flex items-center gap-4">
                                <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                                  <Tag className="h-5 w-5 text-primary" />
                                </div>
                                <div>
                                  <p className="font-semibold">Primary Category</p>
                                  <p className="text-muted-foreground">{tool.primary_task}</p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        )}

                        {tool.pricing && (
                          <Card>
                            <CardContent className="p-6">
                              <div className="flex items-center gap-4">
                                <div className="w-10 h-10 bg-green-100 dark:bg-green-900 rounded-lg flex items-center justify-center">
                                  <Tag className="h-5 w-5 text-green-600 dark:text-green-400" />
                                </div>
                                <div>
                                  <p className="font-semibold">Pricing Model</p>
                                  <p className="text-muted-foreground">{tool.pricing}</p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        )}

                        {tool.created_at && (
                          <Card>
                            <CardContent className="p-6">
                              <div className="flex items-center gap-4">
                                <div className="w-10 h-10 bg-blue-100 dark:bg-blue-900 rounded-lg flex items-center justify-center">
                                  <Calendar className="h-5 w-5 text-blue-600 dark:text-blue-400" />
                                </div>
                                <div>
                                  <p className="font-semibold">Date Added</p>
                                  <p className="text-muted-foreground">
                                    {new Date(tool.created_at).toLocaleDateString('en-US', {
                                      year: 'numeric',
                                      month: 'long',
                                      day: 'numeric'
                                    })}
                                  </p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        )}

                        {tool.updated_at && tool.updated_at !== tool.created_at && (
                          <Card>
                            <CardContent className="p-6">
                              <div className="flex items-center gap-4">
                                <div className="w-10 h-10 bg-orange-100 dark:bg-orange-900 rounded-lg flex items-center justify-center">
                                  <Clock className="h-5 w-5 text-orange-600 dark:text-orange-400" />
                                </div>
                                <div>
                                  <p className="font-semibold">Last Updated</p>
                                  <p className="text-muted-foreground">
                                    {new Date(tool.updated_at).toLocaleDateString('en-US', {
                                      year: 'numeric',
                                      month: 'long',
                                      day: 'numeric'
                                    })}
                                  </p>
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        )}
                      </div>
                    </div>
                  </div>

                  {/* Pricing Section */}
                  <div id="pricing" className="space-y-4 sm:space-y-6 mb-6 sm:mb-8 lg:mb-10">
                    <div className="flex items-center gap-3 mb-3 sm:mb-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                        <DollarSign className="h-5 w-5 text-white" />
                      </div>
                      <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Pricing</h2>
                    </div>
                    <div className="p-6 border rounded-lg bg-muted/30">
                      <div className="text-center">
                        <div className="text-2xl font-bold mb-2">{tool.pricing || 'Freemium'}</div>
                        <p className="text-muted-foreground">
                          {tool.pricing === 'Free' ? 'Completely free to use' :
                           tool.pricing === 'Freemium' ? 'Free tier available with premium features' :
                           tool.pricing === 'Paid' ? 'Paid subscription required' :
                           'Contact for pricing details'}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Alternatives Section */}
                  <div id="alternatives" className="space-y-4 sm:space-y-6 mb-6 sm:mb-8 lg:mb-10">
                    <div className="flex items-center gap-3 mb-3 sm:mb-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                        <Layers className="h-5 w-5 text-white" />
                      </div>
                      <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Alternative AI Tools</h2>
                    </div>

                    {loadingRelated ? (
                      <div className="text-center py-8">
                        <Loader2 className="h-6 w-6 animate-spin text-primary mx-auto mb-4" />
                        <p className="text-muted-foreground">Loading alternatives...</p>
                      </div>
                    ) : relatedTools.length > 0 || suggestedTools.length > 0 ? (
                      <div className="space-y-6">
                        {/* Related Tools (Same Category) */}
                        {relatedTools.length > 0 && (
                          <div>
                            <div className="flex items-center gap-3 mb-4">
                              <div className="w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                                <Target className="h-4 w-4 text-white" />
                              </div>
                              <h3 className="text-lg font-semibold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                                Similar {tool.primary_task} Tools
                              </h3>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {relatedTools.slice(0, 4).map((relatedTool) => (
                                <ToolCard key={relatedTool.id} tool={relatedTool} />
                              ))}
                            </div>
                          </div>
                        )}

                        {/* Suggested Tools (Popular) */}
                        {suggestedTools.length > 0 && (
                          <div>
                            <div className="flex items-center gap-3 mb-4">
                              <div className="w-8 h-8 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                                <TrendingUp className="h-4 w-4 text-white" />
                              </div>
                              <h3 className="text-lg font-semibold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
                                Popular AI Tools
                              </h3>
                            </div>
                            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                              {suggestedTools.slice(0, 4).map((suggestedTool) => (
                                <ToolCard key={suggestedTool.id} tool={suggestedTool} />
                              ))}
                            </div>
                          </div>
                        )}
                      </div>
                    ) : (
                      <Card className="border-dashed">
                        <CardContent className="text-center py-12">
                          <div className="w-16 h-16 bg-muted/50 rounded-full flex items-center justify-center mx-auto mb-4">
                            <Wrench className="h-8 w-8 text-muted-foreground" />
                          </div>
                          <h3 className="text-lg font-semibold mb-2">No alternatives found</h3>
                          <p className="text-muted-foreground mb-6 max-w-md mx-auto">
                            We couldn't find similar tools at the moment. Check back later for updates.
                          </p>
                          <Button variant="outline" onClick={() => router.push('/tools')}>
                            Browse All Tools
                          </Button>
                        </CardContent>
                      </Card>
                    )}
                  </div>

                  {/* Reviews Section */}
                  <div id="reviews" className="space-y-4 sm:space-y-6 mb-6 sm:mb-8 lg:mb-10">
                    <div className="flex items-center gap-3 mb-3 sm:mb-4">
                      <div className="w-10 h-10 bg-gradient-to-r from-primary to-accent rounded-full flex items-center justify-center">
                        <MessageSquare className="h-5 w-5 text-white" />
                      </div>
                      <h2 className="text-xl sm:text-2xl font-bold bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">Reviews</h2>
                    </div>
                    <ReviewsList />
                  </div>

                  {/* Related Tools Section for Mobile */}
                  <div className="block xl:hidden">
                    <RelatedToolsSection
                      currentToolId={tool.id}
                      currentCategory={tool.primary_task}
                    />
                  </div>
                </div>

                {/* Sidebar - Right Column */}
                <div className="xl:col-span-1">
                  <div className="sticky top-[72px] space-y-4 sm:space-y-6">
                    {/* Featured Tools */}
                    <FeaturedToolsSidebar currentToolId={tool.id} />

                    {/* Related Tools for Desktop */}
                    <div className="hidden xl:block">
                      <RelatedToolsSection
                        currentToolId={tool.id}
                        currentCategory={tool.primary_task}
                      />
                    </div>
                  </div>
                </div>
              </div>
                </>
              )}

              {/* Review Dialog */}
              <Dialog open={reviewDialogOpen} onOpenChange={setReviewDialogOpen}>
                <DialogContent className="sm:max-w-[500px]">
                  <DialogHeader>
                    <DialogTitle className="text-xl">
                      {editingReviewId ? 'Edit Your Review' : 'Write a Review'}
                    </DialogTitle>
                    <DialogDescription>
                      Share your experience with {tool?.company_name} to help others make informed decisions.
                    </DialogDescription>
                  </DialogHeader>
                  <div className="py-6 space-y-6">
                    <div className="space-y-3">
                      <Label htmlFor="rating" className="text-sm font-medium">Your Rating</Label>
                      <StarRating />
                    </div>
                    <div className="space-y-3">
                      <Label htmlFor="review" className="text-sm font-medium">Your Review (Optional)</Label>
                      <Textarea
                        id="review"
                        placeholder="What did you like or dislike about this tool? How did it help you?"
                        rows={4}
                        value={userReview}
                        onChange={(e) => setUserReview(e.target.value)}
                        className="resize-none"
                      />
                    </div>
                  </div>
                  <DialogFooter className="gap-3">
                    <Button
                      variant="outline"
                      onClick={() => {
                        setReviewDialogOpen(false)
                        setEditingReviewId(null)
                        setUserRating(5)
                        setUserReview('')
                      }}
                      disabled={submitting}
                    >
                      Cancel
                    </Button>
                    <Button
                      onClick={handleReviewSubmit}
                      disabled={submitting}
                      className="gap-2"
                    >
                      {submitting ? (
                        <>
                          <Loader2 className="h-4 w-4 animate-spin" />
                          {editingReviewId ? 'Updating...' : 'Submitting...'}
                        </>
                      ) : (
                        <>
                          <CheckCircle className="h-4 w-4" />
                          {editingReviewId ? 'Update Review' : 'Submit Review'}
                        </>
                      )}
                    </Button>
                  </DialogFooter>
                </DialogContent>
              </Dialog>
            </MotionWrapper>
          </div>
        </main>
      </div>
    </TooltipProvider>
  )
}
