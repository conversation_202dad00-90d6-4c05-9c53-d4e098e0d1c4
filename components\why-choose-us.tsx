import { CheckCircle, RefreshCw, Shield, ThumbsUp } from "lucide-react"

export default function WhyChooseUs() {
  const features = [
    {
      icon: <CheckCircle className="h-8 w-8 text-blue-600" />,
      title: "Curated Selection",
      description: "Every tool is carefully selected to ensure quality, usability, and value.",
    },
    {
      icon: <RefreshCw className="h-8 w-8 text-blue-600" />,
      title: "Updated Weekly",
      description: "We continuously add new tools and update existing ones to keep you informed.",
    },
    {
      icon: <ThumbsUp className="h-8 w-8 text-blue-600" />,
      title: "Verified Reviews",
      description: "Authentic reviews from real users help you make informed decisions.",
    },
    {
      icon: <Shield className="h-8 w-8 text-blue-600" />,
      title: "Privacy Focused",
      description: "We prioritize your privacy and security when recommending tools.",
    },
  ]

  return (
    <div className="text-center">
      <h2 className="text-3xl font-bold mb-4">Why Choose AI Any Tool</h2>
      <p className="text-slate-600 dark:text-slate-400 mb-12 max-w-2xl mx-auto">
        We help professionals and teams find the right AI solutions faster, with confidence
      </p>

      <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
        {features.map((feature, index) => (
          <div key={index} className="flex flex-col items-center">
            <div className="mb-4 p-3 bg-blue-100 dark:bg-blue-900/30 rounded-full">{feature.icon}</div>
            <h3 className="text-xl font-semibold mb-2">{feature.title}</h3>
            <p className="text-slate-600 dark:text-slate-400">{feature.description}</p>
          </div>
        ))}
      </div>
    </div>
  )
}
