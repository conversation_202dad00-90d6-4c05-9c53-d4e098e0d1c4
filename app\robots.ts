import { MetadataRoute } from 'next'

export default function robots(): MetadataRoute.Robots {
  return {
    rules: [
      {
        userAgent: '*',
        allow: '/',
        disallow: [
          '/api/',
          '/admin/',
          '/_next/',
          '/private/',
          '/cgi-bin/',
          '/cdn-cgi/',
          '/verify/',
          '/assets/cgi-bin/',
        ],
      },
    ],
    sitemap: 'https://aianytool.com/sitemap.xml',
    host: 'https://aianytool.com',
  }
}
