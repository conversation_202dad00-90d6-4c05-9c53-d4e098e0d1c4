/**
 * Query Timeout Configuration
 * Centralized configuration for managing database query timeouts
 */

// تحسين إعدادات timeout للاستجابة السريعة مع تجنب الأخطاء
export const QUERY_TIMEOUTS = {
  // Fast queries (simple selects, single table)
  FAST: 8000,         // 8 ثوان - زيادة لتجنب timeout errors

  // Medium queries (with filters, joins)
  MEDIUM: 12000,      // 12 ثانية - زيادة للاستقرار

  // Slow queries (complex searches, aggregations)
  SLOW: 15000,        // 15 ثانية - زيادة للاستعلامات المعقدة

  // Search queries - prioritize speed but avoid timeouts
  SEARCH: 15000,      // 15 ثانية - زيادة لتجنب timeout errors

  // Suggestions/autocomplete
  SUGGESTIONS: 8000,  // 8 ثوان - زيادة لتجنب timeout errors

  // Authentication queries
  AUTH: 8000,         // 8 ثوان - زيادة للأمان

  // Critical queries (should not timeout easily)
  CRITICAL: 15000,    // 15 ثانية - زيادة للاستعلامات الحرجة
} as const

// تحسين حدود الاستعلامات لتحسين الأداء
export const QUERY_LIMITS = {
  // Default limits for different query types - تقليل الأرقام لتحسين الأداء
  SEARCH_RESULTS: 16,    // تقليل أكثر للسرعة
  SUGGESTIONS: 6,        // تقليل أكثر للاستجابة الفورية
  TOOLS_GRID: 16,        // تقليل أكثر للأداء
  FEATURED_TOOLS: 8,     // تقليل أكثر للسرعة
  RECENT_TOOLS: 12,      // تقليل أكثر للأداء
  CATEGORIES: 20,        // تقليل أكثر للسرعة
  
  // Maximum limits (hard caps) - تقليل الحدود القصوى
  MAX_SEARCH: 50,        // تقليل من 200 إلى 50
  MAX_TOOLS: 100,        // تقليل من 500 إلى 100
  MAX_SUGGESTIONS: 12,   // تقليل من 50 إلى 12
} as const

// تحسين إعدادات إعادة المحاولة للاستقرار
export const RETRY_CONFIG = {
  MAX_RETRIES: 2,        // زيادة إلى 2 لتحسين الاستقرار
  RETRY_DELAY: 1000,     // زيادة إلى 1000ms للاستقرار
  BACKOFF_MULTIPLIER: 2, // إعادة إلى 2 للتدرج المناسب
} as const

/**
 * Creates a timeout promise that rejects after specified time
 */
export function createTimeoutPromise(timeoutMs: number, message?: string): Promise<never> {
  return new Promise((_, reject) => {
    setTimeout(() => {
      reject(new Error(message || `Query timeout after ${timeoutMs}ms`))
    }, timeoutMs)
  })
}

/**
 * Wraps a promise with timeout handling
 */
export function withQueryTimeout<T>(
  promise: Promise<T>, 
  timeoutMs: number = QUERY_TIMEOUTS.MEDIUM,
  timeoutMessage?: string
): Promise<T> {
  const timeoutPromise = createTimeoutPromise(timeoutMs, timeoutMessage)
  return Promise.race([promise, timeoutPromise])
}

/**
 * Wraps a Supabase query with timeout and retry logic
 */
export async function executeQueryWithTimeout<T>(
  queryFn: () => Promise<T>,
  options: {
    timeout?: number
    retries?: number
    retryDelay?: number
    fallbackData?: T
    onTimeout?: () => Promise<T>
  } = {}
): Promise<{ data: T | null; error: Error | null; fromFallback?: boolean }> {
  const {
    timeout = QUERY_TIMEOUTS.MEDIUM,
    retries = RETRY_CONFIG.MAX_RETRIES,
    retryDelay = RETRY_CONFIG.RETRY_DELAY,
    fallbackData = null,
    onTimeout
  } = options

  let lastError: Error | null = null
  
  for (let attempt = 0; attempt <= retries; attempt++) {
    try {
      const result = await withQueryTimeout(queryFn(), timeout)
      return { data: result, error: null }
    } catch (error: any) {
      lastError = error
      
      // If it's a timeout and we have a fallback handler
      if (error.message?.includes('timeout') && onTimeout) {
        try {
          const fallbackResult = await onTimeout()
          return { data: fallbackResult, error: null, fromFallback: true }
        } catch (fallbackError) {
          // Continue with retry logic
        }
      }
      
      // If it's the last attempt or not a retryable error, break
      if (attempt === retries || !isRetryableError(error)) {
        break
      }
      
      // Wait before retrying
      if (attempt < retries) {
        await new Promise(resolve => setTimeout(resolve, retryDelay * Math.pow(RETRY_CONFIG.BACKOFF_MULTIPLIER, attempt)))
      }
    }
  }
  
  // Return fallback data if available
  if (fallbackData !== null) {
    return { data: fallbackData, error: lastError, fromFallback: true }
  }
  
  return { data: null, error: lastError }
}

/**
 * Checks if an error is retryable
 */
function isRetryableError(error: any): boolean {
  const message = error.message?.toLowerCase() || ''
  
  // Retryable errors
  const retryableErrors = [
    'timeout',
    'network',
    'connection',
    'temporary',
    'rate limit',
    'too many requests'
  ]
  
  // Non-retryable errors
  const nonRetryableErrors = [
    'syntax error',
    'permission denied',
    'not found',
    'invalid',
    'unauthorized'
  ]
  
  // Check for non-retryable errors first
  if (nonRetryableErrors.some(err => message.includes(err))) {
    return false
  }
  
  // Check for retryable errors
  return retryableErrors.some(err => message.includes(err))
}

/**
 * Creates an optimized query builder with timeout handling
 */
export function createOptimizedQueryBuilder(supabase: any) {
  return {
    /**
     * Execute a simple select query with timeout
     */
    async selectWithTimeout<T>(
      table: string,
      columns: string,
      options: {
        timeout?: number
        limit?: number
        filters?: Record<string, any>
        orderBy?: { column: string; ascending?: boolean }[]
      } = {}
    ): Promise<{ data: T[] | null; error: Error | null }> {
      const {
        timeout = QUERY_TIMEOUTS.FAST,
        limit = QUERY_LIMITS.TOOLS_GRID,
        filters = {},
        orderBy = []
      } = options

      try {
        let query = supabase.from(table).select(columns)

        // Apply filters
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            query = query.eq(key, value)
          }
        })

        // Apply ordering
        orderBy.forEach(({ column, ascending = false }) => {
          query = query.order(column, { ascending })
        })

        // Apply limit
        query = query.limit(Math.min(limit, QUERY_LIMITS.MAX_TOOLS))

        const result = await withQueryTimeout(query, timeout)
        return { data: result.data, error: result.error }
      } catch (error: any) {
        return { data: null, error }
      }
    },

    /**
     * Execute a search query with timeout and fallback
     */
    async searchWithTimeout<T>(
      table: string,
      columns: string,
      searchTerm: string,
      searchColumns: string[],
      options: {
        timeout?: number
        limit?: number
        filters?: Record<string, any>
      } = {}
    ): Promise<{ data: T[] | null; error: Error | null; fromFallback?: boolean }> {
      const {
        timeout = QUERY_TIMEOUTS.SEARCH,
        limit = QUERY_LIMITS.SEARCH_RESULTS,
        filters = {}
      } = options

      const queryFn = async () => {
        let query = supabase.from(table).select(columns)

        // Apply filters first (more selective)
        Object.entries(filters).forEach(([key, value]) => {
          if (value !== undefined && value !== null) {
            query = query.eq(key, value)
          }
        })

        // Apply search if term is meaningful
        if (searchTerm.trim().length >= 2) {
          const searchConditions = searchColumns
            .map(col => `${col}.ilike.%${searchTerm}%`)
            .join(',')
          query = query.or(searchConditions)
        }

        query = query.limit(Math.min(limit, QUERY_LIMITS.MAX_SEARCH))

        return query
      }

      const fallbackFn = async () => {
        // Simple fallback query without search
        const fallbackQuery = supabase
          .from(table)
          .select(columns)
          .order('is_featured', { ascending: false })
          .limit(20)

        return fallbackQuery
      }

      return executeQueryWithTimeout(queryFn, {
        timeout,
        onTimeout: fallbackFn
      })
    }
  }
}

export default {
  QUERY_TIMEOUTS,
  QUERY_LIMITS,
  RETRY_CONFIG,
  withQueryTimeout,
  executeQueryWithTimeout,
  createOptimizedQueryBuilder
}
