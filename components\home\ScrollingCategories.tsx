"use client"

import { useRef, useEffect, useState } from "react"
import Link from "next/link"
import { motion } from "@/lib/motion-stub"
import { 
  Code, Image, Music, Video, Database, MessageSquare, 
  Lightbulb, PenTool, Briefcase, LineChart, Search, 
  Zap, Brain, Bot, Sparkles, Palette, FileText, 
  BarChart, Globe, Cpu, Layers
} from "lucide-react"

interface Category {
  id: string
  name: string
  count: number
  icon?: string
}

interface ScrollingCategoriesProps {
  categories: Category[]
  maxCategories?: number
}

// Map of category names to icons
const categoryIcons: Record<string, any> = {
  "Text Generation": FileText,
  "Image Generation": Image,
  "Audio Generation": Music,
  "Video Generation": Video,
  "Data Analysis": Database,
  "Chatbots": MessageSquare,
  "AI Assistants": Bot,
  "Content Creation": PenTool,
  "Business": Briefcase,
  "Marketing": LineChart,
  "Research": Search,
  "Productivity": Zap,
  "Machine Learning": Brain,
  "Creative": Palette,
  "Coding": Code,
  "Analytics": <PERSON><PERSON>hart,
  "Web Development": Globe,
  "API": C<PERSON>,
  "Innovation": Lightbulb,
  "Multimodal": Layers,
  // Default icon for any other category
  "default": Sparkles
}

export default function ScrollingCategories({ categories, maxCategories = 20 }: ScrollingCategoriesProps) {
  const containerRef = useRef<HTMLDivElement>(null)
  const [isHovered, setIsHovered] = useState(false)
  const [containerWidth, setContainerWidth] = useState(0)
  const [contentWidth, setContentWidth] = useState(0)
  const [shouldAnimate, setShouldAnimate] = useState(false)
  
  // Get icon component for a category
  const getIconForCategory = (categoryName: string) => {
    // Try to find an exact match
    const exactMatch = Object.keys(categoryIcons).find(
      key => categoryName.toLowerCase() === key.toLowerCase()
    )
    
    if (exactMatch) {
      return categoryIcons[exactMatch]
    }
    
    // Try to find a partial match
    const partialMatch = Object.keys(categoryIcons).find(
      key => categoryName.toLowerCase().includes(key.toLowerCase()) || 
             key.toLowerCase().includes(categoryName.toLowerCase())
    )
    
    if (partialMatch) {
      return categoryIcons[partialMatch]
    }
    
    // Return default icon if no match found
    return categoryIcons.default
  }

  // Limit to the specified number of categories
  const displayCategories = categories.slice(0, maxCategories)

  // Calculate container and content widths to determine if animation is needed
  useEffect(() => {
    if (containerRef.current) {
      const updateWidths = () => {
        if (containerRef.current) {
          const containerWidth = containerRef.current.offsetWidth
          const contentWidth = containerRef.current.scrollWidth
          
          setContainerWidth(containerWidth)
          setContentWidth(contentWidth)
          setShouldAnimate(contentWidth > containerWidth)
        }
      }
      
      updateWidths()
      
      // Update on window resize
      window.addEventListener('resize', updateWidths)
      
      return () => {
        window.removeEventListener('resize', updateWidths)
      }
    }
  }, [categories])

  return (
    <div 
      className="relative overflow-hidden py-2 px-1"
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
      ref={containerRef}
    >
      {/* Gradient fade on left side */}
      <div className="absolute left-0 top-0 bottom-0 w-8 bg-gradient-to-r from-background to-transparent z-10"></div>
      
      {/* Gradient fade on right side */}
      <div className="absolute right-0 top-0 bottom-0 w-8 bg-gradient-to-l from-background to-transparent z-10"></div>
      
      <motion.div
        className="flex gap-2 whitespace-nowrap"
        animate={shouldAnimate && !isHovered ? {
          x: [0, -contentWidth / 2],
        } : { x: 0 }}
        transition={shouldAnimate && !isHovered ? {
          x: {
            repeat: Infinity,
            repeatType: "loop",
            duration: 30,
            ease: "linear",
          }
        } : {}}
      >
        <span className="inline-flex items-center text-sm text-muted-foreground px-2">
          Popular:
        </span>
        
        {displayCategories.map((category) => {
          const IconComponent = getIconForCategory(category.name)
          
          return (
            <Link
              key={category.id}
              href={`/tools?category=${category.id}`}
              className="inline-flex items-center rounded-full bg-secondary/50 backdrop-blur-sm px-3 py-1.5 text-sm text-foreground/80 hover:bg-secondary/80 hover:text-foreground transition-colors hover:shadow-sm group"
            >
              <IconComponent className="w-3.5 h-3.5 mr-1.5 text-primary/70 group-hover:text-primary transition-colors" />
              <span className="relative whitespace-nowrap">
                {category.name} 
                <span className="text-xs text-muted-foreground ml-1">({category.count})</span>
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
              </span>
            </Link>
          )
        })}
        
        {/* Duplicate categories for seamless looping */}
        {shouldAnimate && displayCategories.map((category) => {
          const IconComponent = getIconForCategory(category.name)
          
          return (
            <Link
              key={`duplicate-${category.id}`}
              href={`/tools?category=${category.id}`}
              className="inline-flex items-center rounded-full bg-secondary/50 backdrop-blur-sm px-3 py-1.5 text-sm text-foreground/80 hover:bg-secondary/80 hover:text-foreground transition-colors hover:shadow-sm group"
            >
              <IconComponent className="w-3.5 h-3.5 mr-1.5 text-primary/70 group-hover:text-primary transition-colors" />
              <span className="relative whitespace-nowrap">
                {category.name} 
                <span className="text-xs text-muted-foreground ml-1">({category.count})</span>
                <span className="absolute bottom-0 left-0 w-0 h-0.5 bg-primary transition-all duration-300 group-hover:w-full"></span>
              </span>
            </Link>
          )
        })}
      </motion.div>
    </div>
  )
}
