"use client"

import dynamic from 'next/dynamic'

// Dynamically import AuthSection with no SSR to prevent hydration mismatch
const AuthSection = dynamic(() => import('./auth-section').then(mod => ({ default: mod.AuthSection })), {
  ssr: false,
  loading: () => (
    <div className="w-16 lg:w-20 h-9 bg-muted animate-pulse rounded-full"></div>
  )
})

const MobileAuthSection = dynamic(() => import('./auth-section').then(mod => ({ default: mod.MobileAuthSection })), {
  ssr: false,
  loading: () => (
    <div className="w-full h-12 bg-muted animate-pulse rounded-xl"></div>
  )
})

export function AuthSectionWrapper() {
  return <AuthSection />
}

export function MobileAuthSectionWrapper({ onMenuClose }: { onMenuClose: () => void }) {
  return <MobileAuthSection onMenuClose={onMenuClose} />
}
