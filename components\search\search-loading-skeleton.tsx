"use client"

import { Skeleton } from "@/components/ui/skeleton"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface SearchLoadingSkeletonProps {
  className?: string
  showHeader?: boolean
  itemCount?: number
  style?: React.CSSProperties
}

export default function SearchLoadingSkeleton({
  className,
  showHeader = true,
  itemCount = 3,
  style
}: SearchLoadingSkeletonProps) {
  return (
    <div
      className={cn("search-results-container", className)}
      style={{
        zIndex: 999999,
        position: 'absolute',
        top: '100%',
        left: 0,
        right: 0,
        ...(style || {})
      }}
    >
      <Card
        className="max-h-80 sm:max-h-96 overflow-hidden shadow-2xl border border-border/50 bg-background/98 backdrop-blur-xl rounded-xl"
        style={{
          zIndex: 999999,
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'
        }}
      >
        <CardContent className="p-0">
          {/* Header */}
          {showHeader && (
            <div className="px-4 py-2 border-b border-border/30">
              <Skeleton className="h-4 w-24" />
            </div>
          )}
          
          {/* Loading Items */}
          <div className="space-y-0">
            {Array.from({ length: itemCount }).map((_, index) => (
              <div key={index} className="flex items-center gap-3 px-4 py-3 border-b border-border/20 last:border-b-0">
                {/* Logo/Icon */}
                <div className="relative w-10 h-10 rounded-lg bg-muted/50 flex items-center justify-center flex-shrink-0">
                  <Skeleton className="w-8 h-8 rounded-md" />
                </div>
                
                {/* Content */}
                <div className="flex-1 min-w-0 space-y-2">
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-32" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                  <Skeleton className="h-3 w-full max-w-xs" />
                  <div className="flex items-center gap-2">
                    <Skeleton className="h-4 w-20" />
                    <Skeleton className="h-4 w-16" />
                  </div>
                </div>
                
                {/* Arrow */}
                <Skeleton className="h-4 w-4 rounded" />
              </div>
            ))}
          </div>
          
          {/* Footer */}
          <div className="p-3 border-t border-border/30 bg-muted/10">
            <Skeleton className="h-8 w-full rounded-md" />
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
