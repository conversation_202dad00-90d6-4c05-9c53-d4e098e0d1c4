import { Metadata } from 'next'
import { RequireAuth } from '@/components/auth/require-auth'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Plus, ArrowLeft } from 'lucide-react'
import Link from 'next/link'

export const metadata: Metadata = {
  title: 'Submit AI Tool | AiAnyTool.com',
  description: 'Submit your AI tool to our directory and reach thousands of users looking for AI solutions.',
  keywords: 'submit AI tool, add AI tool, AI tool directory, AI tool submission',
}

export default function SubmitToolPage() {
  return (
    <RequireAuth>
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 pt-20 pb-16">
        <div className="container max-w-4xl mx-auto px-4">
          {/* Header */}
          <div className="mb-8">
            <Button variant="ghost" asChild className="mb-4">
              <Link href="/dashboard">
                <ArrowLeft className="w-4 h-4 mr-2" />
                Back to Dashboard
              </Link>
            </Button>
            
            <div className="text-center">
              <h1 className="text-3xl lg:text-4xl font-bold text-gray-900 dark:text-white mb-4">
                Submit Your AI Tool
              </h1>
              <p className="text-lg text-gray-600 dark:text-gray-400 max-w-2xl mx-auto">
                Share your AI tool with our community and help others discover amazing AI solutions.
              </p>
            </div>
          </div>

          {/* Coming Soon Card */}
          <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700 shadow-lg">
            <CardHeader className="text-center pb-6">
              <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center mx-auto mb-4">
                <Plus className="w-8 h-8 text-white" />
              </div>
              <CardTitle className="text-2xl font-bold text-gray-900 dark:text-white">
                Tool Submission Coming Soon
              </CardTitle>
            </CardHeader>
            
            <CardContent className="text-center space-y-6">
              <p className="text-gray-600 dark:text-gray-400 text-lg">
                We're working hard to bring you an amazing tool submission experience. 
                Our submission form will be available very soon!
              </p>
              
              <div className="bg-blue-50 dark:bg-blue-900/20 p-6 rounded-lg">
                <h3 className="font-semibold text-blue-900 dark:text-blue-100 mb-2">
                  What to expect:
                </h3>
                <ul className="text-blue-700 dark:text-blue-300 space-y-2 text-left max-w-md mx-auto">
                  <li>• Easy-to-use submission form</li>
                  <li>• Quick review process</li>
                  <li>• Tool categorization and tagging</li>
                  <li>• Analytics and insights</li>
                  <li>• Community feedback</li>
                </ul>
              </div>
              
              <div className="flex flex-col sm:flex-row gap-4 justify-center">
                <Button asChild>
                  <Link href="/tools">
                    Explore Existing Tools
                  </Link>
                </Button>
                <Button variant="outline" asChild>
                  <Link href="/dashboard">
                    Back to Dashboard
                  </Link>
                </Button>
              </div>
              
              <p className="text-sm text-gray-500 dark:text-gray-400">
                Want to be notified when submissions open? 
                <Link href="/contact" className="text-blue-600 hover:text-blue-700 ml-1">
                  Contact us
                </Link>
              </p>
            </CardContent>
          </Card>
        </div>
      </div>
    </RequireAuth>
  )
}
