"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { ArrowR<PERSON>, Star, TrendingUp, Grid3X3 } from "lucide-react"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { motion } from "@/lib/motion-stub"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { toast } from "sonner"

interface Tool {
  id: string
  name: string
  slug: string
  description?: string
  logo_url?: string
  pricing?: string
  featured?: boolean
  verified?: boolean
}

interface Category {
  id: string
  name: string
  icon?: React.ElementType
  count: number
  color: string
  tools: Tool[]
}

// Define available category colors
const categoryColors = [
  "bg-blue-500",
  "bg-pink-500",
  "bg-purple-500",
  "bg-amber-500",
  "bg-emerald-500",
  "bg-violet-500",
  "bg-red-500",
  "bg-sky-500",
  "bg-teal-500",
  "bg-fuchsia-500",
  "bg-indigo-500",
  "bg-rose-500",
]

export default function TrendingToolsSection() {
  const [categories, setCategories] = useState<Category[]>([])
  const [displayCount, setDisplayCount] = useState(12) // Increased to 12 categories to show more content
  const [isLoading, setIsLoading] = useState(true)

  // Track if we're on the client side
  const [isClient, setIsClient] = useState(false)

  // First useEffect just to mark when we're client-side
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Fetch categories and their tools with caching
  useEffect(() => {
    // Skip if not on client side yet
    if (!isClient) return;

    // Check if we have cached data
    const getCachedData = () => {
      try {
        const cachedData = sessionStorage.getItem('trending-categories')
        if (cachedData) {
          return JSON.parse(cachedData)
        }
      } catch (e) {
        console.warn("Failed to read from cache:", e)
      }
      return null
    }

    const fetchCategoriesAndTools = async () => {
      try {
        // Check for cached data first
        const cachedCategories = getCachedData()
        if (cachedCategories) {
          setCategories(cachedCategories)
          setIsLoading(false)

          // Still fetch fresh data in the background
          setTimeout(() => fetchFreshData(false), 1000)
          return
        }

        await fetchFreshData(true)
      } catch (error) {
        console.error("Error in fetchCategoriesAndTools:", error)
        setIsLoading(false)
        toast.error("Failed to load categories")
      }
    }

    const fetchFreshData = async (showLoading = true) => {
      try {
        if (showLoading) {
          setIsLoading(true)
        }

        const supabase = createBrowserClient()

        // First get all distinct primary tasks (categories)
        const { data: taskCounts, error: distinctError } = await supabase
          .from('tools')
          .select('primary_task, id')
          .not('primary_task', 'is', null)

        if (distinctError) {
          console.error("Error fetching distinct tasks:", distinctError)
          throw distinctError
        }

        // Process the data to count occurrences of each primary task
        const taskCountMap: Record<string, number> = {}
        if (taskCounts && Array.isArray(taskCounts)) {
          taskCounts.forEach(item => {
            if (item.primary_task) {
              taskCountMap[item.primary_task] = (taskCountMap[item.primary_task] || 0) + 1
            }
          })
        }

        // Convert to the expected format
        const formattedTaskCounts = Object.entries(taskCountMap).map(([primary_task, count]) => ({
          primary_task,
          count
        })).sort((a, b) => b.count - a.count)

        // Handle special categories first
        const specialCategories = [
          {
            id: "top-trends",
            name: "Top 50 Trends [24H]",
            count: 0,
            tools: [] as Tool[],
            color: categoryColors[11],
          },
          {
            id: "latest",
            name: "Latest AI",
            count: 0,
            tools: [] as Tool[],
            color: categoryColors[10],
          }
        ]

        // Create a set to track unique category IDs
        const uniqueCategoryIds = new Set<string>()

        // Transform categories and count tools for each
        const regularCategories: Category[] = []

        // Process each distinct primary task
        if (formattedTaskCounts && Array.isArray(formattedTaskCounts)) {
          for (let i = 0; i < formattedTaskCounts.length; i++) {
            const task = formattedTaskCounts[i]
            const taskName = task.primary_task
            if (!taskName) continue

            const categoryId = taskName.toLowerCase().replace(/\s+/g, '-')

            // Check for duplicates
            if (uniqueCategoryIds.has(categoryId)) {
              continue
            }

            uniqueCategoryIds.add(categoryId)

            regularCategories.push({
              id: categoryId,
              name: taskName,
              count: typeof task.count === 'number' ? task.count : 0,
              tools: [] as Tool[],
              color: categoryColors[i % categoryColors.length],
            })
          }
        }

        // Merge special and regular categories - special categories will come first
        const allCategories = [...specialCategories, ...regularCategories]

        // Now fetch tools for each category
        const categoriesWithToolsPromises = allCategories.map(async (category) => {
          let query = supabase.from("tools").select("id, company_name, slug, logo_url, short_description, pricing, is_featured, is_verified").limit(12) // Increased to 12 tools per category

          if (category.id === "latest") {
            // For "Latest AI" category, get the most recently added tools
            query = query.order("created_at", { ascending: false })
          } else if (category.id === "top-trends") {
            // For "Top 50 Trends" category, get most viewed/rated tools
            query = query.order("click_count", { ascending: false })
          } else {
            // For regular categories, filter by primary_task
            query = query.eq("primary_task", category.name)
          }

          const { data: toolsData, error: toolsError } = await query

          if (toolsError) {
            console.error(`Error fetching tools for ${category.name}:`, toolsError)
            return { ...category, tools: [] as Tool[], count: 0 }
          }

          // Map the tools data to match our Tool interface
          const mappedTools: Tool[] = (toolsData || []).map(tool => ({
            id: String(tool.id),
            name: tool.company_name || "",
            slug: tool.slug || "",
            logo_url: tool.logo_url || "",
            description: tool.short_description || "",
            pricing: tool.pricing || "",
            featured: Boolean(tool.is_featured),
            verified: Boolean(tool.is_verified)
          }))

          return {
            ...category,
            tools: mappedTools,
            count: category.count || mappedTools.length
          }
        })

        // Wait for all tools to be fetched
        const categoriesWithTools = await Promise.all(categoriesWithToolsPromises)

        // Filter out categories with no tools
        const finalCategories = categoriesWithTools.filter(cat => cat.count > 0)

        // Cache the results
        try {
          sessionStorage.setItem('trending-categories', JSON.stringify(finalCategories))
        } catch (e) {
          console.warn("Failed to cache categories:", e)
        }

        setCategories(finalCategories)
      } catch (error) {
        console.error("Error fetching categories with tools:", error)
        if (showLoading) {
          toast.error("Failed to load categories")
        }
      } finally {
        setIsLoading(false)
      }
    }

    fetchCategoriesAndTools()
  }, [isClient])

  return (
    <section className="py-4 md:py-6 bg-secondary/30 dark:bg-secondary/10 theme-transition">
      <div className="container-wide">
        <motion.div
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-4">
            <div>
              <h2 className="text-2xl md:text-3xl font-bold">Browse Categories</h2>
              <p className="mt-1 text-sm text-muted-foreground">
                Explore AI tools by popular categories
              </p>
            </div>

            <Link
              href="/tools"
              className="mt-2 sm:mt-0 inline-flex items-center gap-1 text-primary text-sm font-medium hover:underline group theme-transition"
            >
              View all categories
              <ArrowRight size={16} className="ml-1 transition-transform group-hover:translate-x-1" />
            </Link>
          </div>
        </motion.div>

        {isLoading ? (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {Array.from({ length: 8 }).map((_, index) => (
              <Card key={index} className="h-full border border-slate-200 dark:border-slate-800 overflow-hidden animate-pulse">
                <CardContent className="p-4">
                  <div className="flex items-center justify-between mb-4">
                    <div className="h-5 w-24 bg-slate-200 dark:bg-slate-700 rounded"></div>
                    <div className="h-5 w-8 bg-slate-200 dark:bg-slate-700 rounded-full"></div>
                  </div>
                  <div className="space-y-2">
                    {Array.from({ length: 4 }).map((_, i) => (
                      <div key={i} className="flex items-center gap-2">
                        <div className="w-4 h-4 rounded-full bg-slate-200 dark:bg-slate-700"></div>
                        <div className="h-3 bg-slate-200 dark:bg-slate-700 rounded w-full"></div>
                      </div>
                    ))}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        ) : categories.length === 0 ? (
          <div className="p-8 text-center">
            <p className="text-muted-foreground">No categories found</p>
            <Button onClick={() => window.location.reload()} className="mt-4">
              Reload
            </Button>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
            {categories.slice(0, displayCount).map((category) => (
              <CategoryCard key={category.id} category={category} />
            ))}
          </div>
        )}

        <motion.div
          className="mt-8 text-center"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          transition={{ delay: 0.3 }}
        >
          <Button
            asChild
            variant="outline"
            size="lg"
            className="inline-flex items-center gap-2 px-8 py-3 text-base font-medium rounded-xl border-2 border-primary/20 bg-primary/5 hover:bg-primary/10 hover:border-primary/40 text-primary hover:text-primary transition-all duration-300 group shadow-sm hover:shadow-md"
          >
            <Link href="/tools" className="flex items-center gap-2">
              <Grid3X3 size={18} className="text-primary" />
              <span>Explore All Categories</span>
              <ArrowRight size={18} className="transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </motion.div>
      </div>
    </section>
  )
}

// Category card component
function CategoryCard({ category }: { category: Category }) {
  if (!category) {
    console.error("Received null or undefined category")
    return null
  }

  return (
    <Card className="h-full glass-dark hover:shadow-lg dark:hover:shadow-2xl dark:hover:shadow-primary/10 theme-transition overflow-hidden hover:scale-105">
      <CardContent className="p-4">
        {/* Header with category name, count, and view all button */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            {category.id === "latest" ? (
              <Star size={16} className="text-primary" />
            ) : category.id === "top-trends" ? (
              <TrendingUp size={16} className="text-primary" />
            ) : (
              <span className={`w-3 h-3 rounded-full ${category.color}`}></span>
            )}
            <h3 className="text-lg font-semibold truncate">{category.name}</h3>
          </div>
          <div className="flex items-center gap-2 flex-shrink-0">
            <Badge variant="outline" className="text-xs bg-secondary text-secondary-foreground border-border">
              {category.count}
            </Badge>
            <Link
              href={`/tools?category=${category.id}`}
              className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-primary hover:text-primary/80 bg-primary/10 hover:bg-primary/20 rounded-md transition-all duration-200 group/btn"
            >
              <span>View All</span>
              <ArrowRight size={12} className="transition-transform group-hover/btn:translate-x-0.5" />
            </Link>
          </div>
        </div>

        {/* Tools list with individual clickable items */}
        <div className="space-y-1">
          {(category.tools || []).slice(0, 8).map((tool) => (
            <div key={tool.id} className="relative group/tool">
              <Link
                href={`/Tool/${tool.slug || tool.name.toLowerCase().replace(/\s+/g, '-')}`}
                className="block"
              >
                <div
                  className="flex items-center justify-between gap-3 p-2 rounded-lg hover:bg-muted/50 transition-all duration-200 border border-transparent hover:border-border/30 hover:shadow-sm"
                  title={tool.description || `${tool.name} - AI productivity tool`}
                >
                  {/* Left side: Logo and tool name */}
                  <div className="flex items-center gap-3 min-w-0 flex-1">
                    {tool.logo_url ? (
                      <div className="relative w-6 h-6 rounded-lg overflow-hidden flex-shrink-0 shadow-sm border border-border/20 bg-white dark:bg-gray-800">
                        <Image
                          src={tool.logo_url}
                          alt={tool.name}
                          fill
                          className="object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.style.display = 'none'
                          }}
                        />
                      </div>
                    ) : (
                      <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-muted to-muted/70 flex-shrink-0 shadow-sm border border-border/20 flex items-center justify-center">
                        <div className="w-2.5 h-2.5 rounded bg-muted-foreground/20"></div>
                      </div>
                    )}
                    <div className="min-w-0 flex-1">
                      <div className="text-sm font-semibold text-foreground group-hover/tool:text-primary transition-colors truncate">
                        {tool.name.length > 20 ? `${tool.name.substring(0, 20)}...` : tool.name}
                      </div>
                    </div>
                  </div>

                  {/* Right side: Tool details and status */}
                  <div className="flex items-center gap-2 flex-shrink-0">
                    {tool.pricing && (
                      <span className={`text-xs px-2 py-1 rounded-lg font-medium shadow-sm border transition-all duration-200 hover:scale-105 ${
                        tool.pricing.toLowerCase().includes('free')
                          ? 'text-emerald-700 bg-emerald-50 border-emerald-200 dark:text-emerald-300 dark:bg-emerald-900/20 dark:border-emerald-800'
                          : 'text-amber-700 bg-amber-50 border-amber-200 dark:text-amber-300 dark:bg-amber-900/20 dark:border-amber-800'
                      }`}>
                        {tool.pricing.toLowerCase().includes('free') && (
                          <span className="inline-block w-1.5 h-1.5 bg-emerald-500 rounded-full animate-pulse mr-1"></span>
                        )}
                        {tool.pricing.toLowerCase().includes('free') ? 'Free' : 'Paid'}
                      </span>
                    )}

                    {/* Status indicators */}
                    <div className="flex items-center gap-1.5">
                      {tool.featured && (
                        <div className="relative">
                          <div className="w-2 h-2 bg-gradient-to-br from-amber-400 to-amber-500 rounded-full shadow-sm" title="Featured Tool"></div>
                          <div className="absolute inset-0 w-2 h-2 bg-amber-400 rounded-full animate-ping opacity-30"></div>
                        </div>
                      )}
                      {tool.verified && (
                        <div className="relative">
                          <div className="w-2 h-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full shadow-sm" title="Verified Tool"></div>
                          <div className="absolute inset-0 w-2 h-2 bg-blue-500 rounded-full animate-ping opacity-30"></div>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center justify-center w-6 h-6 rounded-lg bg-muted/30 group-hover/tool:bg-primary/10 transition-all duration-200 group-hover/tool:scale-110">
                      <ArrowRight size={12} className="text-muted-foreground group-hover/tool:text-primary transition-colors" />
                    </div>
                  </div>
                </div>
              </Link>

              {/* Tooltip for description */}
              {tool.description && (
                <div className="absolute left-0 top-full mt-2 w-64 p-3 bg-popover border border-border rounded-lg shadow-lg z-50 opacity-0 invisible group-hover/tool:opacity-100 group-hover/tool:visible transition-all duration-200 pointer-events-none">
                  <div className="text-xs text-popover-foreground leading-relaxed">
                    {tool.description}
                  </div>
                  <div className="absolute -top-1 left-4 w-2 h-2 bg-popover border-l border-t border-border rotate-45"></div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Show indicator if there are more tools than shown */}
        {(category.tools || []).length > 8 && (
          <div className="pt-3 text-center border-t border-border/50 mt-3">
            <span className="text-xs text-muted-foreground">
              +{category.tools.length - 8} more tools
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}
