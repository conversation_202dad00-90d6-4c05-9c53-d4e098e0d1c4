"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "@/lib/motion-stub"

interface PageLoadingWrapperProps {
  children: React.ReactNode
  isLoading: boolean
  loadingText?: string
  variant?: "pulse" | "dots" | "spinner"
  minLoadingTime?: number
}

export function PageLoadingWrapper({
  children,
  isLoading,
  loadingText = "Loading...",
  variant = "pulse",
  minLoadingTime = 1000,
}: PageLoadingWrapperProps) {
  const [showLoading, setShowLoading] = useState(isLoading)
  const [loadingStartTime, setLoadingStartTime] = useState<number | null>(null)

  useEffect(() => {
    if (isLoading && !loadingStartTime) {
      setLoadingStartTime(Date.now())
      setShowLoading(true)
    } else if (!isLoading && loadingStartTime) {
      const elapsedTime = Date.now() - loadingStartTime
      const remainingTime = Math.max(0, minLoadingTime - elapsedTime)

      if (remainingTime === 0) {
        setShowLoading(false)
        setLoadingStartTime(null)
      } else {
        const timer = setTimeout(() => {
          setShowLoading(false)
          setLoadingStartTime(null)
        }, remainingTime)

        return () => clearTimeout(timer)
      }
    }
  }, [isLoading, loadingStartTime, minLoadingTime])

  return (
    <AnimatePresence mode="wait">
      {showLoading ? (
        <motion.div
          key="loading"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
          className="flex flex-col items-center justify-center min-h-screen bg-background"
        >
          {variant === "pulse" && (
            <div className="flex items-center justify-center gap-2">
              <div className="w-3 h-3 rounded-full bg-primary animate-pulse" />
              <div className="w-3 h-3 rounded-full bg-primary animate-pulse" style={{ animationDelay: "0.2s" }} />
              <div className="w-3 h-3 rounded-full bg-primary animate-pulse" style={{ animationDelay: "0.4s" }} />
            </div>
          )}

          {variant === "dots" && (
            <div className="flex items-center justify-center">
              <div className="relative h-10 w-10">
                <div className="absolute top-0 left-0 h-full w-full flex items-center justify-center">
                  <div className="h-2 w-2 bg-primary rounded-full animate-ping" style={{ animationDuration: "1.5s" }} />
                </div>
                <div className="absolute top-0 left-0 h-full w-full flex items-center justify-center">
                  <div className="h-2 w-2 bg-primary rounded-full animate-ping" style={{ animationDuration: "1.5s", animationDelay: "0.2s" }} />
                </div>
                <div className="absolute top-0 left-0 h-full w-full flex items-center justify-center">
                  <div className="h-2 w-2 bg-primary rounded-full animate-ping" style={{ animationDuration: "1.5s", animationDelay: "0.4s" }} />
                </div>
              </div>
            </div>
          )}

          {variant === "spinner" && (
            <div className="h-8 w-8 border-2 border-primary border-t-transparent rounded-full animate-spin" />
          )}

          <p className="mt-4 text-sm text-muted-foreground">{loadingText}</p>
        </motion.div>
      ) : (
        <motion.div
          key="content"
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.3 }}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  )
}
