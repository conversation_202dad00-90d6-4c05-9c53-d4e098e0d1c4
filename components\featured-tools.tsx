"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { <PERSON>, CardContent, CardFooter } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Skeleton } from "@/components/ui/skeleton"
import { Star, CheckCircle, ArrowRight, ExternalLink } from "lucide-react"

interface Tool {
  id: number | string
  company_name?: string
  description?: string
  logo_url?: string
  slug?: string
  pricing?: string
  rating?: number
  categories?: string[]
  is_featured?: boolean
  is_verified?: boolean
  is_new?: boolean
  created_at?: string
}

interface FeaturedToolsProps {
  variant?: "primary" | "secondary"
  limit?: number
  sectionLimits?: {
    featured: number
  }
}

export default function FeaturedTools({ variant = "primary", limit = 6, sectionLimits = { featured: 6 } }: FeaturedToolsProps) {
  const [tools, setTools] = useState<Tool[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchFeaturedTools = async () => {
      try {
        setIsLoading(true)
        const supabase = createBrowserClient()
        
        const { data, error } = await supabase
          .from("tools")
          .select("*")
          .eq("is_featured", true)
          .order("rating", { ascending: false })
          .limit(limit || sectionLimits.featured)
        
        if (error) throw new Error(error.message)
        
        setTools(data || [])
      } catch (err: any) {
        console.error("Error fetching featured tools:", err.message)
        setError("Failed to load featured tools. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }
    
    fetchFeaturedTools()
  }, [limit, sectionLimits.featured])

  // Function to render star ratings
  const renderStars = (rating: number = 0) => {
    const fullStars = Math.floor(rating)
    return (
      <div className="flex items-center">
        {[...Array(5)].map((_, i) => (
          <Star 
            key={i} 
            className={`w-3 h-3 ${i < fullStars ? 'text-yellow-400 fill-yellow-400 dark:text-yellow-400 dark:fill-yellow-400' : 'text-slate-300 dark:text-slate-700'}`}
          />
        ))}
        <span className="ml-1 text-sm font-medium text-slate-700 dark:text-slate-300">{rating.toFixed(1)}</span>
      </div>
    )
  }

  // Function to render tool cards
  const renderToolCard = (tool: Tool) => {
    const companyName = tool.company_name || "Unnamed Tool"
    const firstChar = companyName.charAt(0)
    const toolUrl = tool.slug ? `/Tool/${tool.slug}` : `/Tool/${tool.id}`

    return (
      <Card key={tool.id} className="group overflow-hidden border border-slate-200/60 dark:border-slate-800/60 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm hover:shadow-md dark:shadow-slate-950/10 transition-all duration-200 hover:border-blue-200 dark:hover:border-blue-800/30 h-full flex flex-col">
        <CardContent className="p-5 flex flex-col flex-grow">
          {/* Logo and title section */}
          <div className="flex items-start gap-3 mb-3">
            {/* Tool logo/icon */}
            <div className="w-12 h-12 flex-shrink-0 rounded-lg overflow-hidden bg-gradient-to-br from-blue-100 to-blue-50 dark:from-blue-900/40 dark:to-blue-800/20 flex items-center justify-center border border-blue-200/70 dark:border-blue-800/30 group-hover:shadow-sm transition-all duration-200">
              {tool.logo_url ? (
                <Image 
                  src={tool.logo_url} 
                  alt={companyName} 
                  width={48} 
                  height={48} 
                  className="w-full h-full object-cover"
                  onError={(e) => {
                    const target = e.target as HTMLImageElement
                    target.src = "/placeholder-logo.svg"
                  }}
                />
              ) : (
                <span className="text-lg font-semibold text-blue-600 dark:text-blue-400">{firstChar}</span>
              )}
            </div>
            
            <div className="flex-1 min-w-0">
              <div className="flex items-center gap-1">
                <h3 className="text-base font-bold text-slate-900 dark:text-white truncate">
                  {companyName}
                </h3>
                {tool.is_verified && (
                  <CheckCircle className="h-4 w-4 text-blue-500 dark:text-blue-400" />
                )}
              </div>
              
              {/* Categories or tags */}
              {tool.categories && tool.categories.length > 0 && (
                <div className="flex flex-wrap gap-1 mt-1">
                  {tool.categories.slice(0, 2).map((category, i) => (
                    <Badge key={i} variant="secondary" className="text-xs px-1.5 py-0 h-5 rounded-sm font-normal bg-blue-100/50 dark:bg-blue-900/20 text-blue-700 dark:text-blue-300 border border-blue-200/50 dark:border-blue-800/30">
                      {category}
                    </Badge>
                  ))}
                </div>
              )}
            </div>
          </div>

          {/* Description */}
          <p className="text-sm text-slate-600 dark:text-slate-400 mb-4 line-clamp-2 flex-grow">
            {tool.description || "No description available."}
          </p>

          {/* Rating */}
          <div className="mt-auto">
            {renderStars(tool.rating || 0)}
          </div>
        </CardContent>

        <CardFooter className="px-5 py-4 border-t border-slate-200/60 dark:border-slate-800/60">
          <Button asChild variant="outline" className="w-full rounded-md hover:bg-blue-50 dark:hover:bg-blue-900/20 hover:text-blue-700 dark:hover:text-blue-300 hover:border-blue-200 dark:hover:border-blue-800/50 transition-all duration-200">
            <Link href={toolUrl}>
              View Details
            </Link>
          </Button>
        </CardFooter>
      </Card>
    )
  }

  // Render loading skeletons
  const renderSkeletons = () => {
    return Array(limit).fill(0).map((_, index) => (
      <Card key={`skeleton-${index}`} className="overflow-hidden border border-slate-200/60 dark:border-slate-800/60 bg-white/80 dark:bg-slate-900/80 backdrop-blur-sm h-full flex flex-col">
        <CardContent className="p-5 flex flex-col flex-grow">
          <div className="flex items-start gap-3 mb-3">
            <Skeleton className="w-12 h-12 rounded-lg" />
            <div className="flex-1">
              <Skeleton className="h-5 w-3/4 mb-2" />
              <div className="flex gap-1">
                <Skeleton className="h-4 w-16" />
                <Skeleton className="h-4 w-16" />
              </div>
            </div>
          </div>
          <Skeleton className="h-4 w-full mb-1" />
          <Skeleton className="h-4 w-5/6 mb-4" />
          <div className="mt-auto">
            <Skeleton className="h-4 w-24" />
          </div>
        </CardContent>
        <CardFooter className="px-5 py-4 border-t border-slate-200/60 dark:border-slate-800/60">
          <Skeleton className="h-9 w-full rounded-md" />
        </CardFooter>
      </Card>
    ))
  }

  return (
    <section className="pt-16 bg-background/80">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8">
        <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-6">
          <div>
            <h2 className="text-2xl md:text-3xl font-bold mb-2">Featured Tools</h2>
            <p className="text-muted-foreground">Discover our carefully curated selection of the best AI tools</p>
          </div>
          <Link 
            href="/tools?queryType=featured" 
            className="mt-2 sm:mt-0 inline-flex items-center text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 font-medium"
          >
            View all <ArrowRight className="ml-1 h-4 w-4" />
          </Link>
        </div>

        {error ? (
          <div className="text-center py-12">
            <p className="text-slate-600 dark:text-slate-400">{error}</p>
          </div>
        ) : (
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
            {isLoading ? renderSkeletons() : tools.map(renderToolCard)}
          </div>
        )}
      </div>
    </section>
  )
}
