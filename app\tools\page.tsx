import { Suspense } from "react"
import { fetchCategories, fetchPricingOptions } from "@/lib/supabase/server-utils"
import ToolsPageClientSimple from "@/components/tools/ToolsPageClientSimple"

export const dynamic = "force-dynamic"

export default async function ToolsPage({
  searchParams,
}: {
  searchParams: Promise<{
    category?: string
    pricing?: string
    sortBy?: string
    search?: string
    features?: string
  }>
}) {
  const resolvedSearchParams = await searchParams
  const params = {
    category: resolvedSearchParams?.category || "",
    pricing: resolvedSearchParams?.pricing || "",
    sortBy: resolvedSearchParams?.sortBy || "featured",
    search: resolvedSearchParams?.search || "",
    features: resolvedSearchParams?.features || "",
  }

  try {
    const [categories, pricingOptions] = await Promise.all([fetchCategories(50), fetchPricingOptions()])

    return (
      <div className="min-h-screen bg-background">
        <Suspense fallback={null}>
          <ToolsPageClientSimple searchParams={params} categories={categories} pricingOptions={pricingOptions} />
        </Suspense>
      </div>
    )
  } catch (error) {
    console.error("Error in ToolsPage:", error)
    return (
      <div className="min-h-screen bg-background pt-24 pb-16">
        <div className="container mx-auto px-4 text-center">
          <h1 className="text-2xl font-bold mb-4">Error Loading Page</h1>
          <p className="text-muted-foreground">An error occurred while loading the tools. Please try again.</p>
        </div>
      </div>
    )
  }
}
