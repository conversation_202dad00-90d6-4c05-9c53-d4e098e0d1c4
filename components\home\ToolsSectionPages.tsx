"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowRight } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import ToolGridPages from "@/components/tools/ToolGridPages"
import { LoadingGrid } from "@/components/ui/loading-grid"
import MotionWrapper from "@/components/ui/MotionWrapper"

interface ToolsSectionPagesProps {
  title: string
  description: string
  queryType: "featured" | "top-rated" | "recent" | "all"
  limit?: number
  variant?: "primary" | "secondary" | "accent" | "subtle" | "none"
}

export default function ToolsSectionPages({
  title,
  description,
  queryType,
  limit = 12,
  variant = "none"
}: ToolsSectionPagesProps) {
  const [deviceLimit, setDeviceLimit] = useState(limit)
  const [isMobile, setIsMobile] = useState(false)
  const [isLoading, setIsLoading] = useState(true)

  // Detect mobile view and adjust limits based on screen size
  useEffect(() => {
    const handleResize = () => {
      setIsMobile(window.innerWidth < 768)

      const width = window.innerWidth
      if (width >= 1536) { // 2xl breakpoint
        setDeviceLimit(20)
      } else if (width >= 1280) { // xl breakpoint
        setDeviceLimit(16)
      } else if (width >= 768) { // md breakpoint
        setDeviceLimit(12)
      } else {
        setDeviceLimit(9)
      }
    }

    handleResize() // Run on initial render
    window.addEventListener('resize', handleResize)
    setIsLoading(false)
    return () => window.removeEventListener('resize', handleResize)
  }, [])

  return (
    <section>
      <div className="container-wide">
        <MotionWrapper animation="fadeIn">
          <div className="flex items-center justify-between mb-6">
            <div>
              <h2 className="text-2xl md:text-3xl font-bold">{title}</h2>
              <p className="mt-1 text-sm text-muted-foreground">
                {description}
              </p>
            </div>
            <Link
              href="/tools"
              className="hidden sm:inline-flex items-center gap-1 rounded-lg border border-input bg-background/80 backdrop-blur-sm px-4 py-2 text-sm font-medium hover:bg-accent/20 hover:scale-105 transition-all group"
            >
              View all
              <ArrowRight size={16} className="ml-1 transition-transform group-hover:translate-x-1" />
            </Link>
          </div>
        </MotionWrapper>

        <MotionWrapper animation="fadeIn" delay="delay-200">
          {isLoading ? (
            <LoadingGrid count={deviceLimit} columns={isMobile ? 1 : 4} />
          ) : (
            <ToolGridPages
              queryType={queryType}
              limit={deviceLimit}
              columnsPerRow={isMobile ? 1 : 4}
            />
          )}
        </MotionWrapper>

        <MotionWrapper animation="fadeIn" delay="delay-300" className="mt-6 text-center sm:hidden">
          <Button asChild variant="outline" className="inline-flex items-center gap-2 group">
            <Link href="/tools">
              View all tools
              <ArrowRight size={16} className="ml-1 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </MotionWrapper>
      </div>
    </section>
  )
}
