import { Metadata } from 'next'

interface ToolsPageProps {
  searchParams: {
    category?: string
    pricing?: string
    sortBy?: string
    search?: string
    features?: string
  }
}

// Generate metadata for the page
export async function generateMetadata({ searchParams }: ToolsPageProps): Promise<Metadata> {
  // Convert searchParams to a regular object to avoid Next.js warnings (await in Next.js 15)
  const resolvedSearchParams = await searchParams;
  const params = {
    category: resolvedSearchParams?.category || '',
    search: resolvedSearchParams?.search || ''
  }

  // Use the params object
  const { category, search } = params

  let title = 'Browse AI Tools'
  let description = 'Explore our comprehensive collection of AI tools for various tasks and needs.'

  if (category) {
    const categoryName = category
      .split('-')
      .map(word => word.charAt(0).toUpperCase() + word.slice(1))
      .join(' ')

    title = `${categoryName} AI Tools`
    description = `Discover the best ${categoryName} AI tools in our curated collection.`
  } else if (search) {
    title = `Search Results for "${search}" - AI Tools`
    description = `Find AI tools matching "${search}" in our comprehensive directory.`
  }

  return {
    title,
    description,
    keywords: [
      'AI tools',
      'artificial intelligence',
      'AI directory',
      'AI software',
      'machine learning tools',
      'AI comparison',
      'best AI tools',
      ...(category ? [category, `${category} AI tools`] : []),
      ...(search ? [search, `${search} tools`] : []),
    ],
    openGraph: {
      title,
      description,
      url: 'https://aianytool.com/tools',
      type: 'website',
      locale: 'en_US',
      siteName: 'AiAnyTool.com',
      images: [
        {
          url: 'https://aianytool.com/og-tools.jpg',
          secureUrl: 'https://aianytool.com/og-tools.jpg',
          width: 1200,
          height: 630,
          alt: 'Browse AI Tools Directory - 1000+ Curated AI Tools | AiAnyTool.com',
          type: 'image/jpeg',
        }
      ],
      countryName: 'United States',
    },
    twitter: {
      card: 'summary_large_image',
      title: title.length > 70 ? title.substring(0, 67) + '...' : title,
      description: description.length > 200 ? description.substring(0, 197) + '...' : description,
      images: ['https://aianytool.com/og-tools.jpg'],
      creator: '@aianytool',
      site: '@aianytool',
    },
    alternates: {
      canonical: 'https://aianytool.com/tools',
    },
  }
}
