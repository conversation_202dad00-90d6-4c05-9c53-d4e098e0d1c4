"use client"

import { useState, useEffect, useCallback, useRef } from 'react'
import { withCache, withSessionCache, CACHE_DURATION, measurePerformance } from '@/lib/performance-utils'

interface UseCachedDataOptions<T> {
  // البيانات الأولية (اختياري)
  initialData?: T
  // مدة صلاحية التخزين المؤقت بالمللي ثانية
  cacheDuration?: number
  // ما إذا كان يجب إعادة التحقق من البيانات عند التركيز على النافذة
  revalidateOnFocus?: boolean
  // ما إذا كان يجب إعادة التحقق من البيانات عند إعادة الاتصال بالإنترنت
  revalidateOnReconnect?: boolean
  // ما إذا كان يجب استخدام التخزين المؤقت للجلسة بدلاً من التخزين المؤقت في الذاكرة
  useSessionCache?: boolean
  // دالة يتم استدعاؤها عند نجاح جلب البيانات
  onSuccess?: (data: T) => void
  // دالة يتم استدعاؤها عند فشل جلب البيانات
  onError?: (error: Error) => void
  // ما إذا كان يجب قياس أداء جلب البيانات
  measurePerformance?: boolean
  // اسم لاستخدامه في قياس الأداء
  performanceName?: string
}

interface CachedDataState<T> {
  // البيانات المخزنة مؤقتًا
  data: T | undefined
  // خطأ جلب البيانات (إن وجد)
  error: Error | null
  // ما إذا كانت البيانات قيد التحميل
  isLoading: boolean
  // ما إذا كانت البيانات قيد التحقق
  isValidating: boolean
}

/**
 * Hook مخصص لجلب البيانات مع التخزين المؤقت
 * @param key مفتاح التخزين المؤقت
 * @param fetcher دالة لجلب البيانات
 * @param options خيارات التكوين
 * @returns حالة البيانات ودالة للتحديث اليدوي
 */
export function useCachedData<T>(
  key: string | null,
  fetcher: () => Promise<T>,
  options: UseCachedDataOptions<T> = {}
) {
  const {
    initialData,
    cacheDuration = CACHE_DURATION.MEDIUM,
    revalidateOnFocus = true,
    revalidateOnReconnect = true,
    useSessionCache = false,
    onSuccess,
    onError,
    measurePerformance: shouldMeasurePerformance = false,
    performanceName = 'useCachedData',
  } = options

  // حالة البيانات
  const [state, setState] = useState<CachedDataState<T>>({
    data: initialData,
    error: null,
    isLoading: !initialData && !!key,
    isValidating: false,
  })

  // وقت آخر جلب للبيانات
  const lastFetchTimeRef = useRef<number>(0)
  // مرجع للمفتاح الحالي
  const keyRef = useRef<string | null>(key)
  // تحديث المرجع عند تغيير المفتاح
  useEffect(() => {
    keyRef.current = key
  }, [key])

  // دالة لجلب البيانات
  const fetchData = useCallback(async (shouldDedupe = true) => {
    const currentKey = keyRef.current
    if (!currentKey) return

    // التحقق من تكرار الطلبات
    const now = Date.now()
    if (shouldDedupe && now - lastFetchTimeRef.current < 500) {
      return
    }

    // تحديث حالة التحميل
    setState((prev) => ({
      ...prev,
      isValidating: true,
      ...(prev.data === undefined && { isLoading: true }),
    }))

    try {
      lastFetchTimeRef.current = now

      // استخدام التخزين المؤقت المناسب
      const fetchWithCache = useSessionCache
        ? (k: string, fn: () => Promise<T>) => withSessionCache<T>(k, fn, cacheDuration)
        : (k: string, fn: () => Promise<T>) => withCache<T>(k, fn, cacheDuration)

      // جلب البيانات مع قياس الأداء إذا تم تمكينه
      const fetchFunc = shouldMeasurePerformance
        ? () => measurePerformance(fetcher, `${performanceName}:${currentKey}`)
        : fetcher

      // جلب البيانات مع التخزين المؤقت
      const data = await fetchWithCache(currentKey, fetchFunc)

      // تحديث الحالة بالبيانات الجديدة
      setState({
        data,
        error: null,
        isLoading: false,
        isValidating: false,
      })

      // استدعاء دالة النجاح إذا تم توفيرها
      if (onSuccess) {
        onSuccess(data)
      }

      return data
    } catch (error) {
      // تحديث الحالة بالخطأ
      setState((prev) => ({
        ...prev,
        error: error as Error,
        isLoading: false,
        isValidating: false,
      }))

      // استدعاء دالة الخطأ إذا تم توفيرها
      if (onError) {
        onError(error as Error)
      }

      throw error
    }
  }, [
    fetcher,
    cacheDuration,
    useSessionCache,
    onSuccess,
    onError,
    shouldMeasurePerformance,
    performanceName,
  ])

  // جلب البيانات عند تغيير المفتاح
  useEffect(() => {
    if (key) {
      fetchData(false)
    }
  }, [key, fetchData])

  // إعادة التحقق من البيانات عند التركيز على النافذة
  useEffect(() => {
    if (!revalidateOnFocus) return

    const onFocus = () => {
      fetchData()
    }

    window.addEventListener('focus', onFocus)
    return () => {
      window.removeEventListener('focus', onFocus)
    }
  }, [revalidateOnFocus, fetchData])

  // إعادة التحقق من البيانات عند إعادة الاتصال بالإنترنت
  useEffect(() => {
    if (!revalidateOnReconnect) return

    const onOnline = () => {
      fetchData()
    }

    window.addEventListener('online', onOnline)
    return () => {
      window.removeEventListener('online', onOnline)
    }
  }, [revalidateOnReconnect, fetchData])

  // دالة للتحديث اليدوي
  const mutate = useCallback(async () => {
    return fetchData(false)
  }, [fetchData])

  return {
    ...state,
    mutate,
  }
}
