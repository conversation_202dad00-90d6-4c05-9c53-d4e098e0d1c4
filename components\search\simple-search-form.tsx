"use client"

import { useState } from "react"
import { useRouter } from "next/navigation"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Search, Filter, X } from "lucide-react"

interface SimpleSearchFormProps {
  initialQuery?: string
  initialCategory?: string
}

const categories = [
  "AI Writing",
  "Image Generation",
  "Video Creation",
  "Audio Processing",
  "Data Analysis",
  "Code Generation",
  "Personal Assistant",
  "Business Tools",
  "Education",
  "Healthcare"
]

const pricingOptions = [
  { value: "all", label: "All Pricing" },
  { value: "Free", label: "Free" },
  { value: "Freemium", label: "Freemium" },
  { value: "Paid", label: "Paid" }
]

export default function SimpleSearchForm({ initialQuery = "", initialCategory = "" }: SimpleSearchFormProps) {
  const [query, setQuery] = useState(initialQuery)
  const [selectedCategory, setSelectedCategory] = useState(initialCategory)
  const [selectedPricing, setSelectedPricing] = useState("all")
  const [verified, setVerified] = useState(false)
  const [featured, setFeatured] = useState(false)
  const [free, setFree] = useState(false)
  const router = useRouter()

  const handleSearch = () => {
    const params = new URLSearchParams()

    if (query.trim()) {
      params.set('q', query.trim())
    }

    if (selectedCategory) {
      params.set('categories', selectedCategory)
    }

    if (selectedPricing !== "all") {
      params.set('pricing', selectedPricing)
    }

    if (verified) {
      params.set('verified', 'true')
    }

    if (featured) {
      params.set('featured', 'true')
    }

    if (free) {
      params.set('free', 'true')
    }

    router.push(`/search?${params.toString()}`)
  }

  const handleKeyPress = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault()
      handleSearch()
    }
  }

  const clearFilters = () => {
    setSelectedCategory("")
    setSelectedPricing("all")
    setVerified(false)
    setFeatured(false)
    setFree(false)
  }

  const hasActiveFilters = selectedCategory || selectedPricing !== "all" || verified || featured || free

  return (
    <div className="space-y-6">
      {/* Main Search */}
      <div className="flex gap-3">
        <div className="flex-1 relative">
          <Search className="absolute left-3 top-1/2 transform -translate-y-1/2 h-5 w-5 text-muted-foreground" />
          <Input
            type="text"
            placeholder="Search AI tools..."
            value={query}
            onChange={(e) => setQuery(e.target.value)}
            onKeyPress={handleKeyPress}
            className="pl-10 h-12 text-lg border-2 border-blue-200 focus:border-blue-500 rounded-xl"
          />
        </div>
        <Button
          onClick={handleSearch}
          size="lg"
          className="h-12 px-8 bg-gradient-to-r from-blue-600 to-purple-600 hover:from-blue-700 hover:to-purple-700 rounded-xl"
        >
          <Search className="h-5 w-5 mr-2" />
          Search
        </Button>
      </div>

      {/* Filters */}
      <div className="space-y-4">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-muted-foreground" />
          <span className="text-sm font-medium text-muted-foreground">Filters:</span>
          {hasActiveFilters && (
            <Button
              variant="ghost"
              size="sm"
              onClick={clearFilters}
              className="text-xs text-red-600 hover:text-red-700 hover:bg-red-50"
            >
              <X className="h-3 w-3 mr-1" />
              Clear All
            </Button>
          )}
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-4">
          {/* Category Filter */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">Category</label>
            <Select value={selectedCategory || "all"} onValueChange={(value) => setSelectedCategory(value === "all" ? "" : value)}>
              <SelectTrigger className="h-10">
                <SelectValue placeholder="All Categories" />
              </SelectTrigger>
              <SelectContent>
                <SelectItem value="all">All Categories</SelectItem>
                {categories.map((category) => (
                  <SelectItem key={category} value={category}>
                    {category}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Pricing Filter */}
          <div>
            <label className="text-sm font-medium text-gray-700 mb-2 block">Pricing</label>
            <Select value={selectedPricing} onValueChange={setSelectedPricing}>
              <SelectTrigger className="h-10">
                <SelectValue />
              </SelectTrigger>
              <SelectContent>
                {pricingOptions.map((option) => (
                  <SelectItem key={option.value} value={option.value}>
                    {option.label}
                  </SelectItem>
                ))}
              </SelectContent>
            </Select>
          </div>

          {/* Quick Filters */}
          <div className="md:col-span-2 lg:col-span-2">
            <label className="text-sm font-medium text-gray-700 mb-2 block">Quick Filters</label>
            <div className="flex flex-wrap gap-2">
              <Badge
                variant={verified ? "default" : "outline"}
                className={`cursor-pointer transition-all duration-200 ${
                  verified
                    ? "bg-green-500 hover:bg-green-600 text-white"
                    : "hover:bg-green-50 hover:text-green-700 hover:border-green-300"
                }`}
                onClick={() => setVerified(!verified)}
              >
                ✓ Verified
              </Badge>

              <Badge
                variant={featured ? "default" : "outline"}
                className={`cursor-pointer transition-all duration-200 ${
                  featured
                    ? "bg-yellow-500 hover:bg-yellow-600 text-white"
                    : "hover:bg-yellow-50 hover:text-yellow-700 hover:border-yellow-300"
                }`}
                onClick={() => setFeatured(!featured)}
              >
                ⭐ Featured
              </Badge>

              <Badge
                variant={free ? "default" : "outline"}
                className={`cursor-pointer transition-all duration-200 ${
                  free
                    ? "bg-blue-500 hover:bg-blue-600 text-white"
                    : "hover:bg-blue-50 hover:text-blue-700 hover:border-blue-300"
                }`}
                onClick={() => setFree(!free)}
              >
                🆓 Free Tools
              </Badge>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
