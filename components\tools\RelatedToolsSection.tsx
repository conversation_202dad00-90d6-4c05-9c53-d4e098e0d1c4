'use client'

import { useState, useEffect } from 'react'
import { Card, CardContent } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { ExternalLink, ArrowRight } from 'lucide-react'
import { createClient } from '@supabase/supabase-js'
import { Skeleton } from '@/components/ui/skeleton'
import { useRouter } from 'next/navigation'

const supabase = createClient(
  process.env.NEXT_PUBLIC_SUPABASE_URL!,
  process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!
)

interface RelatedTool {
  id: number
  company_name: string
  short_description: string
  logo_url?: string
  slug?: string
  primary_task?: string
  pricing?: string
  visit_website_url?: string
}

interface RelatedToolsSectionProps {
  currentToolId: number
  currentCategory?: string
  className?: string
}

// Function to get related categories based on the current category
const getRelatedCategories = (category?: string): string[] => {
  const categoryMap: Record<string, string[]> = {
    'Text Generation': ['Writing Assistant', 'Content Creation', 'Copywriting', 'Translation'],
    'Image Generation': ['Art & Design', 'Photo Editing', 'Graphic Design', 'Visual Content'],
    'Code Assistant': ['Development Tools', 'Programming', 'Code Review', 'Documentation'],
    'Data Analysis': ['Analytics', 'Business Intelligence', 'Research', 'Statistics'],
    'Video Editing': ['Video Production', 'Animation', 'Media Processing', 'Content Creation'],
    'Audio Processing': ['Music Production', 'Voice Synthesis', 'Audio Editing', 'Podcasting'],
    'Writing Assistant': ['Text Generation', 'Content Creation', 'Editing', 'Grammar'],
    'Art & Design': ['Image Generation', 'Graphic Design', 'Creative Tools', 'Visual Content'],
    'Development Tools': ['Code Assistant', 'Programming', 'DevOps', 'Testing'],
    'Business Intelligence': ['Data Analysis', 'Analytics', 'Reporting', 'Insights'],
  }

  return categoryMap[category || ''] || ['Text Generation', 'Image Generation', 'Code Assistant', 'Data Analysis']
}

export function RelatedToolsSection({ currentToolId, currentCategory, className }: RelatedToolsSectionProps) {
  const [relatedTools, setRelatedTools] = useState<RelatedTool[]>([])
  const [loading, setLoading] = useState(true)
  const router = useRouter()

  useEffect(() => {
    const fetchRelatedTools = async () => {
      try {
        let relatedTools: RelatedTool[] = []

        // 1. First, get tools from the same category
        if (currentCategory) {
          const { data: sameCategoryData, error: sameCategoryError } = await supabase
            .from('tools')
            .select('id, company_name, short_description, logo_url, slug, primary_task, pricing, visit_website_url')
            .eq('primary_task', currentCategory)
            .neq('id', currentToolId)
            .limit(6)

          if (!sameCategoryError && sameCategoryData) {
            relatedTools = [...sameCategoryData]
          }
        }

        // 2. If we need more tools, get from related categories
        if (relatedTools.length < 6) {
          const relatedCategories = getRelatedCategories(currentCategory)
          
          for (const category of relatedCategories) {
            if (relatedTools.length >= 6) break
            
            const { data: categoryData } = await supabase
              .from('tools')
              .select('id, company_name, short_description, logo_url, slug, primary_task, pricing, visit_website_url')
              .eq('primary_task', category)
              .neq('id', currentToolId)
              .not('id', 'in', `(${relatedTools.map(t => t.id).join(',') || '0'})`)
              .limit(6 - relatedTools.length)

            if (categoryData) {
              relatedTools = [...relatedTools, ...categoryData]
            }
          }
        }

        // 3. If still need more, get popular tools
        if (relatedTools.length < 6) {
          const { data: popularData } = await supabase
            .from('tools')
            .select('id, company_name, short_description, logo_url, slug, primary_task, pricing, visit_website_url')
            .neq('id', currentToolId)
            .not('id', 'in', `(${relatedTools.map(t => t.id).join(',') || '0'})`)
            .order('click_count', { ascending: false, nullsFirst: false })
            .limit(6 - relatedTools.length)

          if (popularData) {
            relatedTools = [...relatedTools, ...popularData]
          }
        }

        setRelatedTools(relatedTools.slice(0, 6))
      } catch (error) {
        console.error('Error fetching related tools:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchRelatedTools()
  }, [currentToolId, currentCategory])

  const handleToolClick = (tool: RelatedTool) => {
    const slug = tool.slug || tool.id.toString()
    router.push(`/Tool/${slug}`)
  }

  const handleVisitWebsite = (e: React.MouseEvent, tool: RelatedTool) => {
    e.stopPropagation()
    if (tool.visit_website_url) {
      window.open(tool.visit_website_url, '_blank', 'noopener,noreferrer')
    }
  }

  if (loading) {
    return (
      <div className={className}>
        <div className="space-y-4">
          <h3 className="text-xl font-bold">Related Tools</h3>
          <div className="grid gap-4">
            {Array.from({ length: 4 }).map((_, i) => (
              <Card key={i} className="cursor-pointer hover:shadow-md transition-shadow">
                <CardContent className="p-4">
                  <div className="flex gap-3">
                    <Skeleton className="h-12 w-12 rounded-lg" />
                    <div className="flex-1 space-y-2">
                      <Skeleton className="h-4 w-3/4" />
                      <Skeleton className="h-3 w-full" />
                      <Skeleton className="h-3 w-1/2" />
                    </div>
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>
        </div>
      </div>
    )
  }

  if (relatedTools.length === 0) {
    return null
  }

  return (
    <div className={className}>
      <div className="space-y-4">
        <div className="flex items-center justify-between">
          <h3 className="text-xl font-bold">Related & Suggested Tools</h3>
          <Button
            variant="ghost"
            size="sm"
            className="gap-1"
            onClick={() => router.push('/tools')}
          >
            View All
            <ArrowRight className="h-3 w-3" />
          </Button>
        </div>
        
        <div className="grid gap-4">
          {relatedTools.map((tool, index) => {
            // Determine the category label
            let categoryLabel = ''
            if (index < 3 && tool.primary_task === currentCategory) {
              categoryLabel = 'Same Category'
            } else if (tool.primary_task && getRelatedCategories(currentCategory).includes(tool.primary_task)) {
              categoryLabel = 'Related Category'
            } else {
              categoryLabel = 'Popular Choice'
            }

            return (
              <Card
                key={tool.id}
                className="cursor-pointer hover:shadow-md transition-all duration-200 hover:border-primary/20"
                onClick={() => handleToolClick(tool)}
              >
                <CardContent className="p-4">
                  <div className="flex gap-3">
                    <div className="w-12 h-12 rounded-lg overflow-hidden bg-muted flex-shrink-0">
                      <img
                        src={tool.logo_url || "https://via.placeholder.com/48?text=AI"}
                        alt={`${tool.company_name} logo`}
                        className="w-full h-full object-cover"
                        onError={(e) => {
                          (e.target as HTMLImageElement).src = "https://via.placeholder.com/48?text=AI"
                        }}
                      />
                    </div>

                    <div className="flex-1 min-w-0">
                      <div className="flex items-start justify-between mb-1">
                        <div className="flex-1 min-w-0">
                          <div className="flex items-center gap-2 mb-1">
                            <h4 className="font-semibold text-sm truncate">{tool.company_name}</h4>
                            <Badge
                              variant={categoryLabel === 'Same Category' ? 'default' : categoryLabel === 'Related Category' ? 'secondary' : 'outline'}
                              className="text-xs py-0 px-1 flex-shrink-0"
                            >
                              {categoryLabel === 'Same Category' ? '🎯' : categoryLabel === 'Related Category' ? '🔗' : '⭐'} {categoryLabel}
                            </Badge>
                          </div>
                        </div>
                        <Button
                          variant="ghost"
                          size="icon"
                          className="h-6 w-6 opacity-60 hover:opacity-100 flex-shrink-0"
                          onClick={(e) => handleVisitWebsite(e, tool)}
                        >
                          <ExternalLink className="h-3 w-3" />
                        </Button>
                      </div>

                      <p className="text-xs text-readable mb-2 line-clamp-2">
                        {tool.short_description}
                      </p>

                      <div className="flex gap-1 flex-wrap">
                        {tool.primary_task && (
                          <Badge variant="secondary" className="text-xs py-0 px-1">
                            {tool.primary_task}
                          </Badge>
                        )}
                        {tool.pricing && (
                          <Badge variant="outline" className="text-xs py-0 px-1">
                            {tool.pricing}
                          </Badge>
                        )}
                      </div>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </div>
  )
}
