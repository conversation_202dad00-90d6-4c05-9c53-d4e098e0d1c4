"use client"

import { cn } from "@/lib/utils"
import MotionWrapper from "./MotionWrapper"
import { ReactNode } from "react"

interface GlassCardProps {
  children: React.ReactNode
  className?: string
  animation?:
    | "fadeIn"
    | "slideUp"
    | "slideDown"
    | "slideInRight"
    | "scaleIn"
    | "none"
  hoverEffect?: boolean
  glowEffect?: boolean
  glowColor?: string
  badge?: ReactNode
  onClick?: () => void
  pulseBorder?: boolean
  variant?: 'default' | 'featured' | 'elevated'
}

export default function GlassCard({
  children,
  className,
  animation = "fadeIn",
  hoverEffect = true,
  glowEffect = false,
  glowColor = "before:from-primary/20 before:to-primary/5", // Softer glow
  badge,
  onClick,
  pulseBorder = false,
  variant = 'default'
}: GlassCardProps) {
  return (
    <MotionWrapper animation={animation}>
      <div
        className={cn(
          "rounded-xl backdrop-blur-sm relative overflow-hidden theme-transition",
          variant === 'default' && "glass-dark",
          variant === 'featured' && "glass-dark border-primary/20 dark:border-primary/10",
          variant === 'elevated' && "glass-dark shadow-xl dark:shadow-2xl dark:shadow-primary/10",
          "shadow-[0_4px_20px_rgb(0,0,0,0.03)] dark:shadow-[0_4px_20px_rgb(0,0,0,0.3)]",
          hoverEffect && "hover:shadow-[0_8px_25px_rgb(0,0,0,0.08)] dark:hover:shadow-[0_8px_25px_rgb(0,0,0,0.5)] hover:-translate-y-1 hover:scale-[1.02]",
          glowEffect && "animate-glow",
          pulseBorder && "border-primary/20 animate-pulse-slow",
          onClick && "cursor-pointer hover:animate-glow",
          "p-6",
          className
        )}
        onClick={onClick}
      >
        {/* Enhanced glass highlight effects for dark mode */}
        <div className="absolute top-0 left-0 right-0 h-px bg-white/10 dark:bg-white/5" />
        <div className="absolute top-0 left-0 bottom-0 w-px bg-white/10 dark:bg-white/5 opacity-30 dark:opacity-20" />

        {/* Glow effect overlay */}
        {glowEffect && (
          <div className="absolute inset-0 opacity-0 hover:opacity-100 transition-opacity duration-500 pointer-events-none">
            <div className="absolute inset-0 bg-gradient-to-br from-primary/10 to-accent/10 dark:from-primary/20 dark:to-accent/20 rounded-xl blur-xl scale-110" />
          </div>
        )}

        {badge && (
          <div className="absolute -top-1 -right-1 z-10">
            {badge}
          </div>
        )}
        {children}
      </div>
    </MotionWrapper>
  )
}
