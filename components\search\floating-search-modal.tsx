"use client"

import React, { useEffect, useState, useRef } from "react"
import { createPortal } from "react-dom"
import { useRouter } from "next/navigation"
import { X, Search } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import UniversalSearch from "./universal-search"
import { motion, AnimatePresence } from "framer-motion"

interface FloatingSearchModalProps {
  isOpen: boolean
  onClose: () => void
  placeholder?: string
  className?: string
  triggerRef?: React.RefObject<HTMLElement>
}

export default function FloatingSearchModal({
  isOpen,
  onClose,
  placeholder = "Search AI tools...",
  className,
  triggerRef
}: FloatingSearchModalProps) {
  const [mounted, setMounted] = useState(false)
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 })
  const [searchQuery, setSearchQuery] = useState("")
  const modalRef = useRef<HTMLDivElement>(null)
  const router = useRouter()

  // Handle search execution
  const handleSearch = (query?: string) => {
    // Get current value from input if no query provided
    const searchInput = modalRef.current?.querySelector('input[type="search"]') as HTMLInputElement
    const searchTerm = query || searchQuery || searchInput?.value || ""

    if (searchTerm.trim()) {
      onClose()
      router.push(`/search?q=${encodeURIComponent(searchTerm.trim())}`)
    }
  }

  useEffect(() => {
    setMounted(true)
  }, [])

  // Calculate position based on trigger element
  useEffect(() => {
    if (triggerRef?.current && isOpen) {
      const rect = triggerRef.current.getBoundingClientRect()
      const viewportWidth = window.innerWidth

      let left = rect.left + window.scrollX
      let width = Math.min(600, viewportWidth - 32) // Max width with margins

      // Center the modal if trigger is too far right
      if (left + width > viewportWidth - 16) {
        left = viewportWidth - width - 16
      }

      // Ensure minimum left margin
      if (left < 16) {
        left = 16
      }

      setPosition({
        top: rect.bottom + window.scrollY + 8,
        left,
        width
      })
    }
  }, [triggerRef, isOpen])

  // Handle escape key and body scroll
  useEffect(() => {
    if (!isOpen) return

    const handleEscape = (e: KeyboardEvent) => {
      if (e.key === 'Escape') {
        onClose()
      }
    }

    // Prevent body scroll when modal is open
    document.body.style.overflow = 'hidden'
    document.addEventListener('keydown', handleEscape)

    return () => {
      document.body.style.overflow = 'unset'
      document.removeEventListener('keydown', handleEscape)
    }
  }, [isOpen, onClose])

  // Handle click outside to close
  useEffect(() => {
    if (!isOpen) return

    const handleClickOutside = (e: MouseEvent) => {
      if (modalRef.current && !modalRef.current.contains(e.target as Node)) {
        onClose()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [isOpen, onClose])

  if (!mounted) return null

  const modalContent = (
    <AnimatePresence>
      {isOpen && (
        <motion.div
          initial={{ opacity: 0 }}
          animate={{ opacity: 1 }}
          exit={{ opacity: 0 }}
          transition={{ duration: 0.15 }}
          className="fixed inset-0 z-[9999]"
          style={{ pointerEvents: 'none' }}
        >
          <motion.div
            ref={modalRef}
            initial={{ opacity: 0, scale: 0.95, y: -10 }}
            animate={{ opacity: 1, scale: 1, y: 0 }}
            exit={{ opacity: 0, scale: 0.95, y: -10 }}
            transition={{ duration: 0.15, ease: "easeOut" }}
            className={cn("absolute", className)}
            style={{
              top: position.top,
              left: position.left,
              width: position.width,
              zIndex: 999999,
              pointerEvents: 'auto'
            }}
          >
            <Card
              className="shadow-2xl border border-border/50 bg-background/98 backdrop-blur-xl rounded-xl overflow-hidden animate-in fade-in-0 zoom-in-95 duration-200"
              style={{
                boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)'
              }}
            >
              <CardContent className="p-0">
                {/* Compact Header */}
                <div className="flex items-center justify-between px-4 py-3 border-b border-border/30 bg-muted/20">
                  <div className="flex items-center gap-2">
                    <Search className="h-4 w-4 text-primary" />
                    <span className="text-sm font-medium text-foreground">Search AI Tools</span>
                  </div>
                  <Button
                    variant="ghost"
                    size="sm"
                    onClick={onClose}
                    className="h-7 w-7 p-0 rounded-full hover:bg-muted transition-colors"
                  >
                    <X className="h-3.5 w-3.5" />
                    <span className="sr-only">Close search</span>
                  </Button>
                </div>

                {/* Search Input with External Button */}
                <div className="p-4">
                  <div className="flex items-center gap-2">
                    <UniversalSearch
                      mode="hybrid"
                      context="general"
                      variant="hero"
                      size="lg"
                      placeholder={placeholder}
                      className="flex-1"
                      rounded="full"
                      glass={false}
                      showKeyboardShortcut={false}
                      fullWidth={true}
                      showInstantResults={true}
                      maxResults={8}
                      showSearchButton={false}
                      autoFocus={true}
                      onSearch={handleSearch}
                      onInstantFilter={setSearchQuery}
                    />
                    <Button
                      type="submit"
                      size="lg"
                      className="rounded-full h-12 w-12 p-0 bg-primary hover:bg-primary/90 text-primary-foreground shadow-sm transition-all duration-200 hover:scale-105 active:scale-95"
                      onClick={() => handleSearch()}
                    >
                      <Search className="h-5 w-5" />
                      <span className="sr-only">Search</span>
                    </Button>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        </motion.div>
      )}
    </AnimatePresence>
  )

  return createPortal(modalContent, document.body)
}
