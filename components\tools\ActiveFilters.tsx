"use client"

import { useRouter, useSearchParams } from 'next/navigation'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { X, Filter, RotateCcw } from 'lucide-react'

interface ActiveFiltersProps {
  searchQuery?: string
  category?: string
  pricing?: string
  features?: string[]
  sortBy?: string
}

export default function ActiveFilters({
  searchQuery,
  category,
  pricing,
  features = [],
  sortBy
}: ActiveFiltersProps) {
  const router = useRouter()
  const searchParams = useSearchParams()

  const removeFilter = (filterType: string, value?: string) => {
    const params = new URLSearchParams(searchParams.toString())
    
    switch (filterType) {
      case 'search':
        params.delete('search')
        break
      case 'category':
        params.delete('category')
        break
      case 'pricing':
        params.delete('pricing')
        break
      case 'sortBy':
        params.delete('sortBy')
        break
      case 'feature':
        if (value) {
          const currentFeatures = features.filter(f => f !== value)
          if (currentFeatures.length > 0) {
            params.set('features', currentFeatures.join(','))
          } else {
            params.delete('features')
          }
        }
        break
    }
    
    router.push(`/tools?${params.toString()}`)
  }

  const clearAllFilters = () => {
    router.push('/tools')
  }

  const hasActiveFilters = !!(searchQuery || category || pricing || features.length > 0 || (sortBy && sortBy !== 'featured'))

  if (!hasActiveFilters) {
    return null
  }

  return (
    <div className="bg-card rounded-xl border shadow-sm p-4 mb-6">
      <div className="flex items-center justify-between mb-3">
        <div className="flex items-center gap-2">
          <Filter className="h-4 w-4 text-primary" />
          <span className="font-medium text-sm">Active Filters</span>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={clearAllFilters}
          className="text-muted-foreground hover:text-foreground h-auto p-1"
        >
          <RotateCcw className="h-3 w-3 mr-1" />
          Clear All
        </Button>
      </div>

      <div className="flex flex-wrap gap-2">
        {searchQuery && (
          <Badge
            variant="secondary"
            className="bg-purple-50 text-purple-700 border-purple-200 hover:bg-purple-100 cursor-pointer"
            onClick={() => removeFilter('search')}
          >
            Search: "{searchQuery}"
            <X className="h-3 w-3 ml-1" />
          </Badge>
        )}

        {category && (
          <Badge
            variant="secondary"
            className="bg-blue-50 text-blue-700 border-blue-200 hover:bg-blue-100 cursor-pointer"
            onClick={() => removeFilter('category')}
          >
            Category: {category.replace(/-/g, ' ')}
            <X className="h-3 w-3 ml-1" />
          </Badge>
        )}

        {pricing && (
          <Badge
            variant="secondary"
            className="bg-green-50 text-green-700 border-green-200 hover:bg-green-100 cursor-pointer"
            onClick={() => removeFilter('pricing')}
          >
            Pricing: {pricing}
            <X className="h-3 w-3 ml-1" />
          </Badge>
        )}

        {sortBy && sortBy !== 'featured' && (
          <Badge
            variant="secondary"
            className="bg-orange-50 text-orange-700 border-orange-200 hover:bg-orange-100 cursor-pointer"
            onClick={() => removeFilter('sortBy')}
          >
            Sort: {sortBy.charAt(0).toUpperCase() + sortBy.slice(1)}
            <X className="h-3 w-3 ml-1" />
          </Badge>
        )}

        {features.map((feature) => (
          <Badge
            key={feature}
            variant="secondary"
            className="bg-indigo-50 text-indigo-700 border-indigo-200 hover:bg-indigo-100 cursor-pointer"
            onClick={() => removeFilter('feature', feature)}
          >
            {feature.split('-').map(word => word.charAt(0).toUpperCase() + word.slice(1)).join(' ')}
            <X className="h-3 w-3 ml-1" />
          </Badge>
        ))}
      </div>
    </div>
  )
}
