'use client'

import { useEffect } from 'react'
import { useGoogleAnalytics } from './GoogleAnalytics'
// Removed cookie consent dependency - using modern approach

/**
 * Component to initialize comprehensive tracking
 * This ensures all tracking is properly set up and working
 */
export default function TrackingInitializer() {
  const { trackCustomEvent } = useGoogleAnalytics()

  useEffect(() => {
    // Initialize tracking when component mounts
    const initializeTracking = () => {
      if (typeof window === 'undefined') return

      // Track session start - always enabled with modern approach
        trackCustomEvent('session_start', {
          page_location: window.location.href,
          page_title: document.title,
          user_agent: navigator.userAgent,
          screen_resolution: `${screen.width}x${screen.height}`,
          viewport_size: `${window.innerWidth}x${window.innerHeight}`,
          language: navigator.language,
          timestamp: new Date().toISOString()
        })

        // Track page engagement
        let engagementStartTime = Date.now()
        let isEngaged = false

        const trackEngagement = () => {
          if (!isEngaged) {
            isEngaged = true
            trackCustomEvent('user_engagement', {
              engagement_time_msec: Date.now() - engagementStartTime
            })
          }
        }

        // Track engagement on various interactions
        const engagementEvents = ['click', 'scroll', 'keydown', 'mousemove']
        engagementEvents.forEach(event => {
          document.addEventListener(event, trackEngagement, { once: true, passive: true })
        })

        // Track when user becomes active after being idle
        let idleTimer: NodeJS.Timeout
        const resetIdleTimer = () => {
          clearTimeout(idleTimer)
          idleTimer = setTimeout(() => {
            trackCustomEvent('user_idle', {
              idle_duration_msec: 30000 // 30 seconds
            })
          }, 30000)
        }

        document.addEventListener('mousemove', resetIdleTimer, { passive: true })
        document.addEventListener('keypress', resetIdleTimer, { passive: true })
        document.addEventListener('scroll', resetIdleTimer, { passive: true })
        document.addEventListener('click', resetIdleTimer, { passive: true })

        // Initial timer
        resetIdleTimer()

        console.log('📊 Tracking Initializer: Analytics tracking initialized (modern approach)')
    }

    // Initialize immediately
    initializeTracking()

    // Re-initialize when consent changes
    const handleConsentChange = () => {
      console.log('📊 Tracking Initializer: Consent changed, re-initializing')
      initializeTracking()
    }

    window.addEventListener('cookiePreferencesChanged', handleConsentChange)
    return () => {
      window.removeEventListener('cookiePreferencesChanged', handleConsentChange)
    }
  }, [trackCustomEvent])

  // Track page visibility changes
  useEffect(() => {
    if (typeof window === 'undefined') return

    const handleVisibilityChange = () => {
      trackCustomEvent('page_visibility_change', {
        visibility_state: document.visibilityState,
        hidden: document.hidden
      })
    }

    document.addEventListener('visibilitychange', handleVisibilityChange)
    return () => {
      document.removeEventListener('visibilitychange', handleVisibilityChange)
    }
  }, [trackCustomEvent])

  // Track performance metrics
  useEffect(() => {
    if (typeof window === 'undefined') return

    const trackPerformance = () => {
      if ('performance' in window) {
        // Track Core Web Vitals and performance metrics
        const navigation = performance.getEntriesByType('navigation')[0] as PerformanceNavigationTiming
        
        if (navigation) {
          trackCustomEvent('page_performance', {
            page_load_time: Math.round(navigation.loadEventEnd - navigation.fetchStart),
            dom_content_loaded: Math.round(navigation.domContentLoadedEventEnd - navigation.fetchStart),
            first_paint: Math.round(navigation.responseStart - navigation.fetchStart),
            dns_lookup_time: Math.round(navigation.domainLookupEnd - navigation.domainLookupStart),
            tcp_connect_time: Math.round(navigation.connectEnd - navigation.connectStart),
            server_response_time: Math.round(navigation.responseEnd - navigation.requestStart)
          })
        }
      }
    }

    // Track performance after page load
    if (document.readyState === 'complete') {
      setTimeout(trackPerformance, 1000)
    } else {
      window.addEventListener('load', () => {
        setTimeout(trackPerformance, 1000)
      })
    }
  }, [trackCustomEvent])

  return null // This component doesn't render anything
}
