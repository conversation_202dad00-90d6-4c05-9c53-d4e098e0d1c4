'use client'

import { useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { AlertTriangle, RefreshCw, Home } from 'lucide-react'
import Link from 'next/link'

export default function GlobalError({
  error,
  reset,
}: {
  error: Error & { digest?: string }
  reset: () => void
}) {
  useEffect(() => {
    // Log critical errors
    console.error('🚨 CRITICAL ERROR in tool page:', {
      message: error.message,
      digest: error.digest,
      stack: error.stack,
      timestamp: new Date().toISOString(),
      userAgent: typeof window !== 'undefined' ? navigator.userAgent : 'server',
      url: typeof window !== 'undefined' ? window.location.href : 'unknown'
    })

    // Send to error reporting service
    // Example: Sentry.captureException(error, { level: 'fatal' })
  }, [error])

  return (
    <html>
      <body>
        <div className="min-h-screen bg-background flex items-center justify-center p-4">
          <div className="w-full max-w-lg">
            <Card className="border-destructive/20 bg-destructive/5">
              <CardHeader className="text-center pb-4">
                <div className="mx-auto w-16 h-16 bg-destructive/10 rounded-full flex items-center justify-center mb-4">
                  <AlertTriangle className="w-8 h-8 text-destructive" />
                </div>
                <CardTitle className="text-2xl font-bold text-destructive">
                  Critical Error
                </CardTitle>
                <CardDescription className="text-lg">
                  A critical error occurred that prevented the page from loading.
                </CardDescription>
              </CardHeader>
              
              <CardContent className="space-y-6">
                <div className="text-center">
                  <p className="text-muted-foreground mb-6">
                    We apologize for the inconvenience. Our team has been notified and is working to fix this issue.
                  </p>
                </div>

                {/* Action Buttons */}
                <div className="flex flex-col gap-3 justify-center">
                  <Button 
                    onClick={reset}
                    className="flex items-center gap-2 justify-center"
                    size="lg"
                  >
                    <RefreshCw className="w-4 h-4" />
                    Try Again
                  </Button>
                  
                  <Button 
                    variant="outline" 
                    asChild
                    size="lg"
                  >
                    <Link href="/" className="flex items-center gap-2 justify-center">
                      <Home className="w-4 h-4" />
                      Go to Homepage
                    </Link>
                  </Button>
                </div>

                {/* Error ID */}
                <div className="text-center pt-4 border-t border-border">
                  <p className="text-xs text-muted-foreground">
                    Error ID: {error.digest || 'unknown'}
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </body>
    </html>
  )
}
