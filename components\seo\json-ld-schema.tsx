'use client'

import { useEffect } from 'react'

interface WebsiteSchema {
  '@context': string
  '@type': string
  name: string
  url: string
  description: string
  publisher: {
    '@type': string
    name: string
  }
  potentialAction: {
    '@type': string
    target: string
    'query-input': string
  }
}

interface ToolSchema {
  '@context': string
  '@type': string
  name: string
  description: string
  url: string
  image?: string
  applicationCategory: string
  operatingSystem: string
  offers?: {
    '@type': string
    price: string
    priceCurrency: string
  }
  aggregateRating?: {
    '@type': string
    ratingValue: number
    reviewCount: number
  }
  author: {
    '@type': string
    name: string
  }
}

interface BreadcrumbSchema {
  '@context': string
  '@type': string
  itemListElement: Array<{
    '@type': string
    position: number
    name: string
    item: string
  }>
}

interface JsonLdProps {
  type: 'website' | 'tool' | 'breadcrumb'
  data: any
}

export function JsonLdSchema({ type, data }: JsonLdProps) {
  useEffect(() => {
    let schema: any = {}

    switch (type) {
      case 'website':
        schema = generateWebsiteSchema(data)
        break
      case 'tool':
        schema = generateToolSchema(data)
        break
      case 'breadcrumb':
        schema = generateBreadcrumbSchema(data)
        break
      default:
        return
    }

    // إضافة Schema إلى head
    const script = document.createElement('script')
    script.type = 'application/ld+json'
    script.textContent = JSON.stringify(schema)
    script.id = `schema-${type}`

    // إزالة Schema السابق إذا وجد
    const existingScript = document.getElementById(`schema-${type}`)
    if (existingScript) {
      existingScript.remove()
    }

    document.head.appendChild(script)

    // تنظيف عند إلغاء التحميل
    return () => {
      const scriptToRemove = document.getElementById(`schema-${type}`)
      if (scriptToRemove) {
        scriptToRemove.remove()
      }
    }
  }, [type, data])

  return null
}

function generateWebsiteSchema(data: any): any {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: data.name || 'AiAnyTool.com - AI Tools Directory',
    url: data.url || 'https://aianytool.com',
    description: data.description || 'Discover, compare, and find the perfect AI tools for your business. Browse 1000+ AI tools across all categories with reviews, pricing, and detailed comparisons.',
    publisher: {
      '@type': 'Organization',
      name: 'AiAnyTool.com',
      url: 'https://aianytool.com',
      logo: {
        '@type': 'ImageObject',
        url: 'https://aianytool.com/logo.png',
        width: 512,
        height: 512
      },
      sameAs: [
        'https://twitter.com/aianytool',
        'https://linkedin.com/company/aianytool'
      ]
    },
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: 'https://aianytool.com/search?q={search_term_string}'
      },
      'query-input': 'required name=search_term_string'
    },
    mainEntity: {
      '@type': 'ItemList',
      name: 'AI Tools Directory',
      description: 'Comprehensive directory of AI tools and software',
      numberOfItems: '1000+'
    }
  }
}

function generateToolSchema(data: any): ToolSchema {
  const schema: ToolSchema = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: data.name || '',
    description: data.description || '',
    url: data.url || '',
    applicationCategory: data.category || 'AI Tool',
    operatingSystem: 'Web Browser',
    author: {
      '@type': 'Organization',
      name: 'AiAnyTool'
    }
  }

  // إضافة الصورة إذا توفرت
  if (data.image) {
    schema.image = data.image
  }

  // إضافة معلومات السعر
  if (data.pricing) {
    schema.offers = {
      '@type': 'Offer',
      price: data.pricing.toLowerCase().includes('free') ? '0' : 'varies',
      priceCurrency: 'USD'
    }
  }

  // إضافة التقييمات
  if (data.rating && data.reviewCount) {
    schema.aggregateRating = {
      '@type': 'AggregateRating',
      ratingValue: data.rating,
      reviewCount: data.reviewCount
    }
  }

  return schema
}

function generateBreadcrumbSchema(data: any): BreadcrumbSchema {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: data.items.map((item: any, index: number) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: item.name,
      item: item.url
    }))
  }
}

// مكونات مساعدة للاستخدام السهل
export function WebsiteJsonLd({ name, url, description }: { name?: string; url?: string; description?: string }) {
  return (
    <>
      <JsonLdSchema type="website" data={{ name, url, description }} />
      <OrganizationJsonLd />
    </>
  )
}

export function OrganizationJsonLd() {
  const organizationSchema = {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'AiAnyTool.com',
    alternateName: 'AI Any Tool',
    url: 'https://aianytool.com',
    logo: {
      '@type': 'ImageObject',
      url: 'https://aianytool.com/logo.png',
      width: 512,
      height: 512
    },
    description: 'Leading AI tools directory helping businesses discover and compare the best AI software solutions.',
    foundingDate: '2024',
    sameAs: [
      'https://twitter.com/aianytool',
      'https://linkedin.com/company/aianytool'
    ],
    contactPoint: {
      '@type': 'ContactPoint',
      contactType: 'customer service',
      email: '<EMAIL>',
      availableLanguage: 'English'
    },
    areaServed: 'Worldwide',
    knowsAbout: [
      'Artificial Intelligence',
      'AI Tools',
      'Machine Learning',
      'Software Directory',
      'Technology Reviews'
    ]
  }

  return (
    <script
      type="application/ld+json"
      dangerouslySetInnerHTML={{ __html: JSON.stringify(organizationSchema) }}
    />
  )
}

export function ToolJsonLd({ tool }: { tool: any }) {
  if (!tool) return null

  // Enhanced schema with multiple types for better SEO
  const schemas = [
    // Main SoftwareApplication schema
    {
      '@context': 'https://schema.org',
      '@type': 'SoftwareApplication',
      '@id': `https://aianytool.com/Tool/${tool.slug}`,
      name: tool.company_name,
      alternateName: tool.company_name ? [tool.company_name, `${tool.company_name} AI Tool`] : undefined,
      description: tool.full_description || tool.short_description || `${tool.company_name} is a powerful AI tool for ${tool.primary_task || 'various tasks'}.`,
      url: tool.visit_website_url || tool.website_url,
      sameAs: tool.visit_website_url ? [tool.visit_website_url] : undefined,
      image: [
        tool.logo_url,
        tool.featured_image_url
      ].filter(Boolean),
      screenshot: tool.featured_image_url,
      applicationCategory: [
        'AI Tool',
        tool.primary_task,
        'Software Application',
        'Business Software'
      ].filter(Boolean),
      applicationSubCategory: tool.primary_task || 'AI Tool',
      operatingSystem: ['Web Browser', 'Cross-platform', 'Online'],
      softwareVersion: 'Latest',
      dateCreated: tool.created_at,
      datePublished: tool.created_at,
      dateModified: tool.updated_at || tool.created_at,
      offers: tool.pricing_model ? {
        '@type': 'Offer',
        price: tool.pricing_model.toLowerCase().includes('free') ? '0' : undefined,
        priceCurrency: 'USD',
        availability: 'https://schema.org/InStock',
        description: tool.pricing_model,
        priceValidUntil: new Date(Date.now() + 365 * 24 * 60 * 60 * 1000).toISOString().split('T')[0] // 1 year from now
      } : undefined,
      aggregateRating: tool.rating ? {
        '@type': 'AggregateRating',
        ratingValue: tool.rating,
        ratingCount: tool.review_count || 1,
        bestRating: 5,
        worstRating: 1,
        reviewCount: tool.review_count || 1
      } : undefined,
      author: {
        '@type': 'Organization',
        name: 'AiAnyTool.com',
        url: 'https://aianytool.com',
        logo: {
          '@type': 'ImageObject',
          url: 'https://aianytool.com/logo.png'
        }
      },
      publisher: {
        '@type': 'Organization',
        name: 'AiAnyTool.com',
        url: 'https://aianytool.com',
        logo: {
          '@type': 'ImageObject',
          url: 'https://aianytool.com/logo.png',
          width: 200,
          height: 60
        }
      },
      keywords: [
        tool.company_name,
        `${tool.company_name} review`,
        tool.primary_task,
        tool.secondary_task,
        tool.tertiary_task,
        'AI tool',
        'artificial intelligence',
        'software',
        'review',
        'pricing'
      ].filter(Boolean).join(', '),
      featureList: tool.pros ? tool.pros.slice(0, 5) : undefined,
      applicationSuite: 'AI Tools Directory',
      downloadUrl: tool.visit_website_url || tool.website_url,
      installUrl: tool.visit_website_url || tool.website_url
    },

    // Article schema for the review
    {
      '@context': 'https://schema.org',
      '@type': 'Article',
      '@id': `https://aianytool.com/Tool/${tool.slug}#article`,
      headline: `${tool.company_name} Review 2024: Features, Pricing & Alternatives`,
      description: tool.short_description || `Comprehensive review of ${tool.company_name}, covering features, pricing, pros and cons, and alternatives.`,
      image: tool.featured_image_url || tool.logo_url,
      author: {
        '@type': 'Organization',
        name: 'AiAnyTool Team',
        url: 'https://aianytool.com'
      },
      publisher: {
        '@type': 'Organization',
        name: 'AiAnyTool.com',
        url: 'https://aianytool.com',
        logo: {
          '@type': 'ImageObject',
          url: 'https://aianytool.com/logo.png'
        }
      },
      datePublished: tool.created_at,
      dateModified: tool.updated_at || tool.created_at,
      mainEntityOfPage: `https://aianytool.com/Tool/${tool.slug}`,
      about: {
        '@type': 'SoftwareApplication',
        name: tool.company_name
      },
      articleSection: tool.primary_task || 'AI Tools',
      keywords: [tool.company_name, tool.primary_task, 'AI tool review', 'software review'].filter(Boolean).join(', ')
    }
  ]

  // FAQ schema is handled in the client component to avoid duplication

  return (
    <>
      {schemas.map((schema, index) => (
        <script
          key={index}
          type="application/ld+json"
          dangerouslySetInnerHTML={{ __html: JSON.stringify(schema) }}
        />
      ))}
    </>
  )
}

export function BreadcrumbJsonLd({ items }: { items: Array<{ name: string; url: string }> }) {
  return <JsonLdSchema type="breadcrumb" data={{ items }} />
}

// Hook للاستخدام في الصفحات
export function useJsonLdSchema(type: 'website' | 'tool' | 'breadcrumb', data: any) {
  useEffect(() => {
    const schema = type === 'website' 
      ? generateWebsiteSchema(data)
      : type === 'tool'
      ? generateToolSchema(data)
      : generateBreadcrumbSchema(data)

    const script = document.createElement('script')
    script.type = 'application/ld+json'
    script.textContent = JSON.stringify(schema)
    script.id = `schema-${type}-${Date.now()}`

    document.head.appendChild(script)

    return () => {
      const scriptToRemove = document.getElementById(script.id)
      if (scriptToRemove) {
        scriptToRemove.remove()
      }
    }
  }, [type, data])
}
