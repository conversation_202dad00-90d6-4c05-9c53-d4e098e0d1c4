"use client"

import { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { Search, SlidersHorizontal, X, Plus, Loader2, ArrowLeft } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { ToolCard } from "@/components/tool-card"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { useDebounce } from "@/hooks/use-debounce"
import { toast } from "sonner"
import { cn } from "@/lib/utils"
import { motion } from "framer-motion"
import { useRef, useState as useReactState } from "react"


import { useProgressiveEnhancement } from "@/hooks/use-progressive-enhancement"
import { HoverScale, StaggerContainer, StaggerItem } from "@/components/ui/micro-animations"
import UniversalSearch from "@/components/search/universal-search"
import SearchContainer from "@/components/search/search-container"
import SearchButton from "@/components/search/search-button"

interface Tool {
  id: string | number
  name?: string
  company_name?: string
  description?: string
  short_description?: string
  logo?: string
  logo_url?: string
  category?: string
  primary_task?: string
  rating?: number
  reviewCount?: number
  pricing?: string
  url?: string
  visit_website_url?: string
  detail_url?: string
  slug?: string
  isFeatured?: boolean
  isNew?: boolean
  isVerified?: boolean
  is_featured?: boolean
  is_verified?: boolean
  click_count?: number
  created_at?: string
}

interface Category {
  id: string
  name: string
  slug: string
  tool_count?: number
}

interface PricingOption {
  value: string
  label: string
  count?: number
}

interface ToolsPageClientProps {
  searchParams: {
    category?: string
    pricing?: string
    sortBy?: string
    search?: string
    features?: string
    view?: string
  }
  categories: Category[]
  pricingOptions: PricingOption[]
}

const ITEMS_PER_PAGE = 24

export default function ToolsPageClientSimple({
  searchParams,
  categories = []
}: ToolsPageClientProps) {
  const router = useRouter()

  // Pricing options - limited to 3 main categories using smart SQL pattern matching
  const standardPricingOptions = [
    { value: "free", label: "🆓 Free" },
    { value: "freemium", label: "⭐ Freemium" },
    { value: "paid", label: "💰 Paid" },
  ]

  // State management
  const [tools, setTools] = useState<Tool[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isLoadingMore, setIsLoadingMore] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [hasMore, setHasMore] = useState(true)

  // Filter states
  const [searchTerm, setSearchTerm] = useState(searchParams.search || "")
  const [selectedCategory, setSelectedCategory] = useState(searchParams.category || "all")
  const [selectedPricing, setSelectedPricing] = useState(searchParams.pricing || "all")
  const [sortBy, setSortBy] = useState(searchParams.sortBy || "featured")

  const [showFilters, setShowFilters] = useState(false)
  const [showSearchHint, setShowSearchHint] = useState(false)

  // Refs for keyboard shortcuts
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Progressive enhancement
  const { reducedMotion } = useProgressiveEnhancement()

  // Debounced search term
  const debouncedSearchTerm = useDebounce(searchTerm, 300)

  // Handle instant filtering for the universal search
  const handleInstantFilter = useCallback((query: string, filters?: any) => {
    setSearchTerm(query)
    // The existing useEffect will handle the actual filtering
  }, [])

  // Show search hint when user starts typing
  useEffect(() => {
    if (searchTerm.length > 0) {
      setShowSearchHint(true)
      // Hide hint after 5 seconds
      const timer = setTimeout(() => setShowSearchHint(false), 5000)
      return () => clearTimeout(timer)
    } else {
      setShowSearchHint(false)
    }
  }, [searchTerm])

  // Keyboard Shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only trigger shortcuts when not typing in an input
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return
      }

      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'k': // Ctrl+K for search
            e.preventDefault()
            searchInputRef.current?.focus()
            toast.success("Search focused! Start typing...")
            break
          case 'f': // Ctrl+F for filters
            e.preventDefault()
            setShowFilters(!showFilters)
            toast.success(showFilters ? "Filters hidden" : "Filters shown")
            break
          case 'r': // Ctrl+R to reset filters
            e.preventDefault()
            clearFilters()
            toast.success("Filters cleared!")
            break
        }
      }

      // Escape key to clear search
      if (e.key === 'Escape') {
        if (searchTerm) {
          setSearchTerm('')
          toast.success("Search cleared!")
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [showFilters, searchTerm])

  // Fetch tools from Supabase
  const fetchTools = useCallback(
    async (page = 1, append = false) => {
      try {
        if (page === 1 && !append) {
          setIsLoading(true)
        } else {
          setIsLoadingMore(true)
        }
        setError(null)

        // Debug logging (only in development)
        if (process.env.NODE_ENV === 'development') {
          console.log("Fetching tools with filters:", {
            selectedCategory,
            selectedPricing: `${selectedPricing} (using smart SQL pattern matching)`,
            searchTerm: debouncedSearchTerm,
            sortBy,
            page
          })
        }

        const supabase = createBrowserClient()
        if (!supabase) {
          throw new Error("Failed to initialize Supabase client")
        }

        // Build query
        let query = supabase.from("tools").select(
          `
          id,
          company_name,
          short_description,
          logo_url,
          primary_task,
          pricing,
          visit_website_url,
          detail_url,
          slug,
          is_featured,
          is_verified,
          click_count,
          created_at
        `,
          { count: "exact" },
        )

        // Apply filters
        if (selectedCategory && selectedCategory !== "all") {
          // Try both slug and name matching for better compatibility
          query = query.or(`primary_task.eq.${selectedCategory},primary_task.ilike.%${selectedCategory}%`)
        }

        // Apply smart pricing filter - handle complex pricing patterns
        if (selectedPricing && selectedPricing !== "all") {
          if (selectedPricing === "free") {
            // Match ONLY completely free tools (no paid options at all)
            // Examples: "Free", "free", "$0"
            query = query.or(
              "pricing.eq.Free,pricing.eq.free,pricing.eq.FREE,pricing.ilike.$0%,pricing.ilike.%$0%,pricing.ilike.%no cost%,pricing.ilike.%gratis%"
            )
            // Exclude anything that has paid components
            query = query
              .not("pricing", "ilike", "%+%")      // Exclude "Free + $19/mo"
              .not("pricing", "ilike", "%from%")   // Exclude "from $X"
              .not("pricing", "ilike", "%trial%")  // Exclude "Free trial"
              .not("pricing", "ilike", "%plan%")   // Exclude "Free plan"
          } else if (selectedPricing === "freemium") {
            // Match freemium patterns (free + paid options)
            // Examples: "Free + from $19/mo", "Free trial + $9/mo"
            query = query.or(
              "pricing.ilike.%free + %,pricing.ilike.%free+%,pricing.ilike.%freemium%,pricing.ilike.%free trial%,pricing.ilike.%free plan%"
            )
          } else if (selectedPricing === "paid") {
            // Match ONLY paid (no free options)
            // Examples: "from $4.99/mo", "$19/month", "Contact for pricing"
            query = query
              .or("pricing.ilike.%$%,pricing.ilike.%from %,pricing.ilike.%contact%,pricing.ilike.%subscription%,pricing.ilike.%premium%")
              .not("pricing", "ilike", "%free%")  // Exclude anything with "free"
              .not("pricing", "ilike", "%$0%")    // Exclude $0
          }
        }

        // Apply search
        if (debouncedSearchTerm && debouncedSearchTerm.trim().length >= 2) {
          query = query.or(
            `company_name.ilike.%${debouncedSearchTerm}%,short_description.ilike.%${debouncedSearchTerm}%`,
          )
        }

        // Apply sorting
        switch (sortBy) {
          case "featured":
            query = query
              .order("is_featured", { ascending: false })
              .order("is_verified", { ascending: false })
              .order("click_count", { ascending: false })
            break
          case "newest":
            query = query.order("created_at", { ascending: false })
            break
          case "popular":
            query = query.order("click_count", { ascending: false })
            break
          case "alphabetical":
            query = query.order("company_name", { ascending: true })
            break
          default:
            query = query
              .order("is_featured", { ascending: false })
              .order("is_verified", { ascending: false })
              .order("click_count", { ascending: false })
        }

        // Apply pagination
        const from = (page - 1) * ITEMS_PER_PAGE
        const to = from + ITEMS_PER_PAGE - 1
        query = query.range(from, to)

        const { data, error: queryError, count } = await query

        if (queryError) {
          console.error("Supabase query error:", queryError)
          throw queryError
        }

        if (process.env.NODE_ENV === 'development') {
          console.log("Query results:", {
            data: data?.length,
            count,
            filters: { selectedCategory, selectedPricing, searchTerm: debouncedSearchTerm },
            samplePricingValues: data?.slice(0, 5).map(tool => ({ id: tool.id, pricing: tool.pricing }))
          })
        }

        // Transform data
        const transformedTools = (data || []).map((tool) => ({
          id: tool.id,
          name: tool.company_name || "",
          company_name: tool.company_name || "",
          description: tool.short_description || "",
          short_description: tool.short_description || "",
          logo: tool.logo_url || "",
          logo_url: tool.logo_url || "",
          category: tool.primary_task || "",
          primary_task: tool.primary_task || "",
          rating: 4.5,
          reviewCount: 0,
          pricing: tool.pricing || "",
          url: tool.visit_website_url || tool.detail_url || "#",
          visit_website_url: tool.visit_website_url || "",
          detail_url: tool.detail_url || "",
          slug: tool.slug || tool.id.toString(),
          isFeatured: Boolean(tool.is_featured),
          isVerified: Boolean(tool.is_verified),
          is_featured: tool.is_featured,
          is_verified: tool.is_verified,
          isNew: new Date(tool.created_at || "").getTime() > Date.now() - 7 * 24 * 60 * 60 * 1000,
          click_count: tool.click_count || 0,
          created_at: tool.created_at,
        }))

        if (append) {
          setTools((prev) => [...prev, ...transformedTools])
        } else {
          setTools(transformedTools)
        }

        setTotalCount(count || 0)
        setHasMore(transformedTools.length === ITEMS_PER_PAGE && (count || 0) > page * ITEMS_PER_PAGE)

        if (transformedTools.length === 0 && page === 1) {
          setError("No tools found matching your criteria.")
        }
      } catch (err) {
        console.error("Error fetching tools:", err)
        setError("Failed to load tools. Please try again.")
      } finally {
        setIsLoading(false)
        setIsLoadingMore(false)
      }
    },
    [selectedCategory, selectedPricing, debouncedSearchTerm, sortBy],
  )

  // Load more tools
  const loadMore = useCallback(() => {
    if (!isLoadingMore && hasMore) {
      const nextPage = currentPage + 1
      setCurrentPage(nextPage)
      fetchTools(nextPage, true)
    }
  }, [currentPage, isLoadingMore, hasMore, fetchTools])

  // Effects
  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log("Filters changed, resetting page and fetching tools")
    }
    setCurrentPage(1)
    setTools([])
    setHasMore(true)
    setError(null)
  }, [selectedCategory, selectedPricing, debouncedSearchTerm, sortBy])

  useEffect(() => {
    if (process.env.NODE_ENV === 'development') {
      console.log("fetchTools dependency changed, calling fetchTools")
    }
    // Don't show loading state here since Suspense already handles initial loading
    fetchTools(1, false)
  }, [fetchTools])

  // Clear filters
  const clearFilters = () => {
    setSearchTerm("")
    setSelectedCategory("all")
    setSelectedPricing("all")
    setSortBy("featured")
    setError(null)
  }

  // Active filters count
  const activeFiltersCount = [
    selectedCategory !== "all" ? 1 : 0,
    selectedPricing !== "all" ? 1 : 0,
    searchTerm.trim() ? 1 : 0
  ].reduce((a, b) => a + b, 0)





  // Enhanced Grid classes with better responsive design
  const getGridClasses = () => {
    return "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 2xl:grid-cols-5 gap-6"
  }

  // Container classes for better spacing
  const getContainerClasses = () => {
    return "container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl"
  }

  return (
    <>
      <style jsx>{`
        @keyframes fadeIn {
          from {
            opacity: 0;
            transform: translateY(10px);
          }
          to {
            opacity: 1;
            transform: translateY(0);
          }
        }

        .animate-fadeIn {
          animation: fadeIn 0.5s ease-out forwards;
        }

        .theme-transition {
          transition: all 0.3s cubic-bezier(0.4, 0, 0.2, 1);
        }
      `}</style>

      <div className="min-h-screen bg-background">
        {/* Hero Section - Minimal */}
      <section className="relative pt-16 pb-4">
        <div className="container mx-auto px-4">
          <div className="text-center mb-3">
            <motion.h1
              className="text-2xl md:text-3xl font-bold mb-2 text-gradient"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              Discover AI Tools
            </motion.h1>
            <motion.p
              className="text-sm md:text-base text-muted-foreground max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              {selectedCategory !== "all" ? `Explore ${selectedCategory} AI tools with ${totalCount}+ options available` : `Find the perfect AI tools for your needs from ${totalCount}+ available options`}
            </motion.p>
          </div>

          {/* Back to Categories Link */}
          {selectedCategory !== "all" && (
            <div className="mb-4">
              <Link
                href="/categories"
                className="inline-flex items-center gap-2 text-sm text-primary hover:text-primary/80 transition-colors duration-200"
              >
                <ArrowLeft className="h-4 w-4" />
                <span>Back to All Categories</span>
              </Link>
            </div>
          )}

          {/* Search Bar - Universal Search Component */}
          <motion.div
            className="max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <SearchContainer maxWidth="2xl">
              <div className="flex items-center p-3">
                <div className="flex-1">
                  <UniversalSearch
                    mode="instant-filter"
                    context="tools"
                    variant="hero"
                    size="lg"
                    placeholder="Search AI tools..."
                    className="w-full"
                    rounded="full"
                    glass={false}
                    showKeyboardShortcut={false}
                    fullWidth={true}
                    maxResults={8}
                    autoFocus={false}
                    showSearchButton={false}
                    initialValue={searchTerm}
                    onInstantFilter={handleInstantFilter}
                    onSearch={(query) => {
                      if (query.trim()) {
                        router.push(`/search?q=${encodeURIComponent(query.trim())}`)
                      }
                    }}
                  />
                </div>

                {/* Search Button */}
                <div className="flex-shrink-0 ml-3">
                  <SearchButton
                    onClick={() => {
                      if (searchTerm?.trim()) {
                        router.push(`/search?q=${encodeURIComponent(searchTerm.trim())}`)
                      }
                    }}
                    size="md"
                    showText={true}
                    text="Search"
                  />
                </div>
              </div>
            </SearchContainer>

            {/* Search Hint - Mobile Friendly */}
            <motion.div
              className="mt-3 text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.4 }}
            >
              <div className="inline-flex items-center gap-2 px-3 py-2 bg-blue-50/80 dark:bg-blue-950/30 text-blue-700 dark:text-blue-300 rounded-full text-xs sm:text-sm border border-blue-200/50 dark:border-blue-800/50 backdrop-blur-sm">
                <div className="flex items-center gap-1">
                  <Search className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="font-medium">Tip:</span>
                </div>
                <span className="hidden sm:inline">Type to filter tools instantly or press Enter for full search</span>
                <span className="sm:hidden">Type to filter or press Enter</span>
                <div className="hidden sm:flex items-center gap-1 ml-1">
                  <span className="text-blue-600 dark:text-blue-400">•</span>
                  <span>Results update as you type</span>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Compact Filters and Controls */}
      <div className="sticky top-0 z-40 bg-white/95 dark:bg-slate-900/95 backdrop-blur-xl border-b border-border/50 shadow-lg">
        <div className="container mx-auto px-4 py-3 sm:py-4">
          <div className="flex flex-col xl:flex-row gap-3 sm:gap-4 items-start xl:items-center justify-between">
            {/* Enhanced Filters Section - Mobile First */}
            <div className="flex flex-col sm:flex-row flex-wrap items-start sm:items-center gap-3 sm:gap-4 w-full xl:w-auto">
              {/* Filter Label - Hidden on mobile */}
              <div className="hidden sm:flex items-center gap-2 text-sm font-semibold text-foreground">
                <SlidersHorizontal className="h-4 w-4 text-primary" />
                <span>Filters:</span>
              </div>

              {/* Filter Controls - Responsive */}
              <div className="flex flex-col sm:flex-row flex-wrap items-stretch sm:items-center gap-2 sm:gap-3 w-full sm:w-auto">
                <Select value={selectedCategory} onValueChange={setSelectedCategory}>
                  <SelectTrigger className={`w-full sm:w-48 md:w-52 h-10 sm:h-11 rounded-lg sm:rounded-xl border-2 transition-all duration-200 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-sm hover:shadow-md ${
                    selectedCategory !== "all"
                      ? "border-primary/70 ring-2 ring-primary/20 bg-primary/5"
                      : "border-border/50 hover:border-primary/50"
                  }`}>
                    <SelectValue placeholder="All Categories" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg sm:rounded-xl border-border/50 shadow-xl">
                    <SelectItem value="all" className="rounded-lg">All Categories</SelectItem>
                    {categories.map((category) => (
                      <SelectItem key={category.id} value={category.name || category.slug} className="rounded-lg">
                        {category.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={selectedPricing} onValueChange={setSelectedPricing}>
                  <SelectTrigger className={`w-full sm:w-40 md:w-44 h-10 sm:h-11 rounded-lg sm:rounded-xl border-2 transition-all duration-200 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-sm hover:shadow-md ${
                    selectedPricing !== "all"
                      ? "border-primary/70 ring-2 ring-primary/20 bg-primary/5"
                      : "border-border/50 hover:border-primary/50"
                  }`}>
                    <SelectValue placeholder="All Pricing" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg sm:rounded-xl border-border/50 shadow-xl">
                    <SelectItem value="all" className="rounded-lg">All Pricing</SelectItem>
                    {standardPricingOptions.map((option) => (
                      <SelectItem key={option.value} value={option.value} className="rounded-lg">
                        {option.label}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>

                <Select value={sortBy} onValueChange={setSortBy}>
                  <SelectTrigger className={`w-full sm:w-40 md:w-44 h-10 sm:h-11 rounded-lg sm:rounded-xl border-2 transition-all duration-200 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-sm hover:shadow-md ${
                    sortBy !== "featured"
                      ? "border-primary/70 ring-2 ring-primary/20 bg-primary/5"
                      : "border-border/50 hover:border-primary/50"
                  }`}>
                    <SelectValue placeholder="Sort by" />
                  </SelectTrigger>
                  <SelectContent className="rounded-lg sm:rounded-xl border-border/50 shadow-xl">
                    <SelectItem value="featured" className="rounded-lg">⭐ Featured</SelectItem>
                    <SelectItem value="newest" className="rounded-lg">🆕 Newest</SelectItem>
                    <SelectItem value="popular" className="rounded-lg">🔥 Popular</SelectItem>
                    <SelectItem value="alphabetical" className="rounded-lg">🔤 A-Z</SelectItem>
                  </SelectContent>
                </Select>

                {activeFiltersCount > 0 && (
                  <div className="animate-fadeIn">
                    <Button
                      variant="outline"
                      size="sm"
                      onClick={clearFilters}
                      className="w-full sm:w-auto h-10 sm:h-11 px-4 rounded-lg sm:rounded-xl border-2 border-destructive/30 hover:border-destructive/50 hover:bg-destructive/10 hover:text-destructive transition-all duration-200 bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm shadow-sm hover:shadow-md hover:scale-105"
                    >
                      <X className="h-4 w-4 mr-2 transition-transform hover:rotate-90" />
                      Clear Filters ({activeFiltersCount})
                    </Button>
                  </div>
                )}
              </div>
            </div>

            {/* Enhanced View Controls - Mobile Responsive */}
            <div className="flex flex-col sm:flex-row items-stretch sm:items-center gap-3 sm:gap-4 w-full xl:w-auto">
              {/* Results Counter - Responsive */}
              <div className="flex items-center justify-center sm:justify-start gap-2 px-3 sm:px-4 py-2 rounded-lg sm:rounded-xl bg-white/80 dark:bg-slate-800/80 backdrop-blur-sm border border-border/50 shadow-sm">
                <span className="text-xs sm:text-sm text-muted-foreground font-medium">
                  {isLoading ? (
                    <div className="flex items-center gap-2">
                      <Loader2 className="h-3 sm:h-4 w-3 sm:w-4 animate-spin text-primary" />
                      <span>
                        {activeFiltersCount > 0 ? "Filtering..." : "Loading..."}
                      </span>
                    </div>
                  ) : (
                    <span className="text-foreground">
                      <span className="font-semibold text-primary">{tools.length}</span> of{" "}
                      <span className="font-semibold text-primary">{totalCount}</span> tools
                      {activeFiltersCount > 0 && (
                        <span className="ml-2 text-xs text-primary">
                          ({activeFiltersCount} filter{activeFiltersCount > 1 ? 's' : ''} active)
                        </span>
                      )}
                    </span>
                  )}
                </span>
              </div>


            </div>
          </div>
        </div>
      </div>

      {/* Dynamic Search Hint - Shows when user is searching */}
      {showSearchHint && searchTerm.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="fixed top-16 sm:top-20 left-0 right-0 z-40 bg-gradient-to-r from-blue-500/95 to-purple-500/95 backdrop-blur-xl text-white py-3 px-4 shadow-xl border-b border-white/20"
        >
          <div className="container mx-auto px-4 text-center">
            <div className="flex items-center justify-center gap-2 text-sm sm:text-base">
              <Search className="h-4 w-4 flex-shrink-0" />
              <span className="font-medium truncate">
                Searching for "{searchTerm}" - Check results below
              </span>
              <button
                onClick={() => setShowSearchHint(false)}
                className="ml-2 hover:bg-white/20 rounded-full p-1 transition-colors flex-shrink-0"
                aria-label="Close hint"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Compact Main Content */}
      <div className={getContainerClasses()}>
        <div className="py-4 sm:py-6 lg:py-8">
          {error && (
            <div className="text-center py-8 sm:py-12">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-red-100 to-red-50 dark:from-red-900/20 dark:to-red-800/20 rounded-full flex items-center justify-center">
                  <X className="h-8 w-8 text-red-500" />
                </div>
                <h3 className="text-lg font-semibold mb-2 text-foreground">Something went wrong</h3>
                <p className="text-red-500 mb-4 text-sm">{error}</p>
                <Button
                  onClick={() => fetchTools(1, false)}
                  variant="outline"
                  size="sm"
                  className="gap-2 hover:scale-105 transition-transform"
                >
                  <Plus className="h-4 w-4" />
                  Try Again
                </Button>
              </div>
            </div>
          )}

          {isLoading && tools.length === 0 && (
            <div className={getGridClasses()}>
              {Array.from({ length: 20 }).map((_, i) => (
                <div
                  key={i}
                  className={cn(
                    "group relative rounded-xl shadow-sm bg-card border overflow-hidden flex flex-col p-5 min-h-[240px] border-border opacity-60"
                  )}
                >
                  {/* Main Content */}
                  <div className="flex gap-3 items-start">
                    {/* Logo */}
                    <div className="relative flex-shrink-0 overflow-hidden rounded-xl bg-gradient-to-br from-gray-100 to-gray-200 dark:from-gray-800 dark:to-gray-700 shadow-md border border-gray-200/50 dark:border-gray-700/50 h-11 w-11">
                      <div className="h-full w-full bg-muted/60 animate-pulse rounded-xl"></div>
                    </div>

                    <div className="flex-1 min-w-0 relative">
                      {/* Status Icons */}
                      <div className="absolute -top-1 right-0 flex gap-1 z-10">
                        <div className="w-7 h-7 bg-gradient-to-br from-amber-400 via-yellow-500 to-orange-500 rounded-full animate-pulse shadow-lg ring-2 ring-white/80 dark:ring-gray-900/80"></div>
                      </div>

                      {/* Title */}
                      <div className="h-4 bg-muted/60 rounded animate-pulse mb-1 w-3/4 pr-8"></div>

                      {/* Category and Pricing */}
                      <div className="flex flex-col gap-1.5 mt-1">
                        <div className="h-5 bg-gradient-to-r from-gray-100 to-gray-50 dark:from-gray-800 dark:to-gray-750 rounded-lg w-20 animate-pulse border border-gray-200/50 dark:border-gray-700/50"></div>
                        <div className="h-5 bg-gradient-to-r from-emerald-50 to-green-50 dark:from-emerald-900/20 dark:to-green-900/20 rounded-lg w-16 animate-pulse border border-emerald-200 dark:border-emerald-700/50"></div>
                      </div>
                    </div>
                  </div>

                  {/* Description */}
                  <div className="mt-2.5 space-y-2">
                    <div className="h-3.5 bg-muted/40 rounded animate-pulse w-full"></div>
                    <div className="h-3.5 bg-muted/40 rounded animate-pulse w-4/5"></div>
                  </div>

                  {/* Rating Section */}
                  <div className="flex items-center gap-2 mt-2.5">
                    <div className="flex items-center gap-0.5">
                      {[...Array(5)].map((_, starIndex) => (
                        <div key={starIndex} className="h-3.5 w-3.5 bg-yellow-200 dark:bg-yellow-800/30 rounded animate-pulse"></div>
                      ))}
                    </div>
                    <div className="h-3 bg-muted/40 rounded w-16 animate-pulse"></div>
                  </div>

                  {/* Action Buttons */}
                  <div className="mt-auto pt-4 flex gap-2 items-center">
                    <div className="flex-1 h-10 bg-muted hover:bg-muted/80 rounded-lg animate-pulse border border-border"></div>
                    <div className="h-10 w-10 bg-background border-border rounded-lg animate-pulse border"></div>
                    <div className="flex-1 h-10 bg-gradient-to-r from-primary to-primary/90 rounded-lg animate-pulse border border-primary/20"></div>
                  </div>
                </div>
              ))}
            </div>
          )}

          {isLoading && tools.length > 0 && (
            <div className="fixed top-4 right-4 z-50 bg-primary text-primary-foreground px-4 py-2 rounded-lg shadow-lg flex items-center gap-2">
              <Loader2 className="h-4 w-4 animate-spin" />
              <span className="text-sm">Updating...</span>
            </div>
          )}

        {!isLoading && !error && tools.length === 0 && (
          <div className="text-center py-12 sm:py-16">
            <div className="max-w-md mx-auto">
              <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-primary/10 via-primary/5 to-transparent rounded-full flex items-center justify-center relative">
                <div className="absolute inset-0 bg-gradient-to-br from-primary/20 to-primary/5 rounded-full animate-pulse"></div>
                <Search className="h-10 w-10 text-primary/60 relative z-10" />
              </div>
              <h3 className="text-xl font-bold mb-3 text-foreground">No tools found</h3>
              <p className="text-muted-foreground mb-6 text-sm leading-relaxed">
                {searchTerm ? (
                  <>
                    No results for "<span className="font-semibold text-primary">{searchTerm}</span>".{" "}
                    Try different keywords or clear filters.
                  </>
                ) : (
                  "Try adjusting your search criteria or filters."
                )}
              </p>
              <div className="flex flex-col sm:flex-row gap-2 justify-center">
                <Button
                  onClick={clearFilters}
                  variant="outline"
                  size="sm"
                  className="gap-2 rounded-lg hover:scale-105 transition-transform"
                >
                  <X className="h-4 w-4" />
                  Clear filters
                </Button>
                {searchTerm && (
                  <Button
                    onClick={() => setSearchTerm("")}
                    variant="ghost"
                    size="sm"
                    className="gap-2 rounded-lg hover:scale-105 transition-transform"
                  >
                    <Search className="h-4 w-4" />
                    Clear search
                  </Button>
                )}
              </div>
            </div>
          </div>
        )}

        {!isLoading && !error && tools.length > 0 && (
          <>
            {/* Enhanced Tools Grid with Stagger Animation */}
            <StaggerContainer className={getGridClasses()} disabled={reducedMotion}>
              {tools.map((tool) => (
                <StaggerItem key={tool.id} disabled={reducedMotion}>
                  <HoverScale disabled={reducedMotion} scale={1.02}>
                    <ToolCard
                      tool={tool}
                      className={cn(
                        "transition-all duration-300 ease-out",
                        !reducedMotion && "hover:shadow-xl hover:-translate-y-2"
                      )}
                    />
                  </HoverScale>
                </StaggerItem>
              ))}
            </StaggerContainer>

            {/* Enhanced Load More Button */}
            {hasMore && (
              <div className="flex justify-center mt-8 sm:mt-12">
                <div className="relative group">
                  {/* Glow effect */}
                  <div className="absolute -inset-0.5 bg-gradient-to-r from-primary to-primary/80 rounded-xl blur opacity-30 group-hover:opacity-50 transition duration-300"></div>

                  <Button
                    onClick={loadMore}
                    disabled={isLoadingMore}
                    className="relative gap-2 px-6 py-2.5 text-sm font-semibold rounded-xl shadow-lg hover:shadow-xl transition-all duration-300 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary border border-primary/20 hover:scale-105 disabled:hover:scale-100"
                  >
                    {isLoadingMore ? (
                      <>
                        <Loader2 className="h-4 w-4 animate-spin" />
                        <span>Loading...</span>
                      </>
                    ) : (
                      <>
                        <Plus className="h-4 w-4 transition-transform group-hover:rotate-90" />
                        <span>Load More ({totalCount - tools.length})</span>
                      </>
                    )}
                  </Button>
                </div>
              </div>
            )}

            {/* Compact End Message */}
            {!hasMore && tools.length > 0 && (
              <div className="text-center mt-12 py-8 border-t border-border/30 bg-gradient-to-r from-transparent via-primary/5 to-transparent rounded-t-2xl">
                <div className="max-w-md mx-auto">
                  <div className="w-16 h-16 mx-auto mb-4 bg-gradient-to-br from-green-100 to-emerald-50 dark:from-green-900/20 dark:to-emerald-900/20 rounded-full flex items-center justify-center">
                    <div className="text-2xl animate-bounce">🎉</div>
                  </div>
                  <h3 className="text-lg font-bold mb-2 text-foreground">All tools explored!</h3>
                  <p className="text-muted-foreground mb-6 text-sm leading-relaxed">
                    You've seen all{" "}
                    <span className="font-bold text-primary bg-primary/10 px-1.5 py-0.5 rounded">{totalCount}</span>{" "}
                    tools matching your criteria.
                  </p>
                  <div className="flex flex-col sm:flex-row gap-2 justify-center">
                    <Button
                      onClick={clearFilters}
                      variant="outline"
                      size="sm"
                      className="gap-2 rounded-lg hover:scale-105 transition-transform"
                    >
                      <SlidersHorizontal className="h-4 w-4" />
                      Explore more
                    </Button>
                    <Button
                      onClick={() => setSearchTerm("")}
                      variant="ghost"
                      size="sm"
                      className="gap-2 rounded-lg hover:scale-105 transition-transform"
                    >
                      <Search className="h-4 w-4" />
                      New search
                    </Button>
                  </div>
                </div>
              </div>
            )}
          </>
        )}
        </div>
      </div>
      </div>
    </>
  )
}
