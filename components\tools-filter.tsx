"use client"

import { useState, useEffect } from "react"
import { useRouter, usePathname } from "next/navigation"
import { Input } from "./ui/input"
import { Button } from "./ui/button"
import { Checkbox } from "./ui/checkbox"
import { RadioGroup, RadioGroupItem } from "./ui/radio-group"
import { Label } from "./ui/label"
import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "./ui/accordion"
import { Search, SlidersHorizontal, X } from "lucide-react"
import { createClient } from "@supabase/supabase-js"

interface ToolsFilterProps {
  initialQuery: string
  initialCategories: string[]
  initialPricing: string[]
  initialFeatures: string[]
  initialSort: string
}

export default function ToolsFilter({
  initialQuery,
  initialCategories,
  initialPricing,
  initialFeatures,
  initialSort,
}: ToolsFilterProps) {
  const [query, setQuery] = useState(initialQuery)
  const [categories, setCategories] = useState<string[]>(initialCategories)
  const [pricing, setPricing] = useState<string[]>(initialPricing)
  const [features, setFeatures] = useState<string[]>(initialFeatures)
  const [sort, setSort] = useState(initialSort)
  const [availableCategories, setAvailableCategories] = useState<string[]>([])
  const [availableFeatures, setAvailableFeatures] = useState<string[]>([])
  const [isFilterOpen, setIsFilterOpen] = useState(false)

  const router = useRouter()
  const pathname = usePathname()
  const supabase = createClient(process.env.NEXT_PUBLIC_SUPABASE_URL!, process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY!)

  // Fetch available categories and features
  useEffect(() => {
    const fetchFilterOptions = async () => {
      // Fetch categories
      const { data: categoryData } = await supabase.from("tools").select("primary_task").not("primary_task", "is", null)

      if (categoryData) {
        const uniqueCategories = Array.from(new Set(categoryData.map((item) => item.primary_task as string))).sort()
        setAvailableCategories(uniqueCategories)
      }

      // Fetch features (assuming there's a common set of features across tools)
      // In a real app, you might have a separate table for features
      const commonFeatures = [
        "API Access",
        "Offline Mode",
        "Mobile App",
        "Chrome Extension",
        "Team Collaboration",
        "Custom Templates",
        "Export Options",
        "Free Trial",
        "24/7 Support",
      ]
      setAvailableFeatures(commonFeatures)
    }

    fetchFilterOptions()
  }, [supabase])

  const applyFilters = () => {
    const params = new URLSearchParams()

    if (query) params.set("q", query)
    if (categories.length) params.set("categories", categories.join(","))
    if (pricing.length) params.set("pricing", pricing.join(","))
    if (features.length) params.set("features", features.join(","))
    if (sort !== "featured") params.set("sort", sort)

    router.push(`${pathname}?${params.toString()}`)
    setIsFilterOpen(false)
  }

  const resetFilters = () => {
    setQuery("")
    setCategories([])
    setPricing([])
    setFeatures([])
    setSort("featured")
    router.push(pathname)
    setIsFilterOpen(false)
  }

  const toggleCategory = (category: string) => {
    setCategories((prev) => (prev.includes(category) ? prev.filter((c) => c !== category) : [...prev, category]))
  }

  const togglePricing = (price: string) => {
    setPricing((prev) => (prev.includes(price) ? prev.filter((p) => p !== price) : [...prev, price]))
  }

  const toggleFeature = (feature: string) => {
    setFeatures((prev) => (prev.includes(feature) ? prev.filter((f) => f !== feature) : [...prev, feature]))
  }

  // Count active filters
  const activeFilterCount = categories.length + pricing.length + features.length + (sort !== "featured" ? 1 : 0)

  return (
    <>
      {/* Mobile Filter Button */}
      <div className="md:hidden mb-4">
        <Button
          variant="outline"
          className="w-full flex justify-between items-center"
          onClick={() => setIsFilterOpen(true)}
        >
          <div className="flex items-center">
            <SlidersHorizontal className="mr-2 h-4 w-4" />
            Filters
          </div>
          {activeFilterCount > 0 && (
            <span className="bg-blue-600 text-white text-xs rounded-full h-5 w-5 flex items-center justify-center">
              {activeFilterCount}
            </span>
          )}
        </Button>
      </div>

      {/* Mobile Filter Drawer */}
      <div
        className={`
        fixed inset-0 bg-black/50 z-50 transition-opacity md:hidden
        ${isFilterOpen ? "opacity-100" : "opacity-0 pointer-events-none"}
      `}
      >
        <div
          className={`
          fixed inset-y-0 left-0 w-full max-w-xs bg-white dark:bg-slate-900 shadow-xl 
          transition-transform p-4 overflow-y-auto
          ${isFilterOpen ? "translate-x-0" : "-translate-x-full"}
        `}
        >
          <div className="flex justify-between items-center mb-4">
            <h2 className="text-xl font-bold">Filters</h2>
            <Button variant="ghost" size="icon" onClick={() => setIsFilterOpen(false)}>
              <X className="h-5 w-5" />
            </Button>
          </div>

          {/* Filter Content - Same as desktop but in the mobile drawer */}
          <div className="space-y-6">
            {/* Search */}
            <div>
              <Label htmlFor="mobile-search">Search</Label>
              <div className="relative mt-1">
                <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
                <Input
                  id="mobile-search"
                  type="search"
                  placeholder="Search tools..."
                  className="pl-8"
                  value={query}
                  onChange={(e) => setQuery(e.target.value)}
                />
              </div>
            </div>

            {/* Sort */}
            <div>
              <h3 className="font-medium mb-2">Sort By</h3>
              <RadioGroup value={sort} onValueChange={setSort}>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="featured" id="mobile-sort-featured" />
                  <Label htmlFor="mobile-sort-featured">Featured</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="newest" id="mobile-sort-newest" />
                  <Label htmlFor="mobile-sort-newest">Newest</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="popular" id="mobile-sort-popular" />
                  <Label htmlFor="mobile-sort-popular">Popular</Label>
                </div>
                <div className="flex items-center space-x-2">
                  <RadioGroupItem value="top-rated" id="mobile-sort-top-rated" />
                  <Label htmlFor="mobile-sort-top-rated">Top Rated</Label>
                </div>
              </RadioGroup>
            </div>

            {/* Categories */}
            <div>
              <h3 className="font-medium mb-2">Categories</h3>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {availableCategories.map((category) => (
                  <div key={category} className="flex items-center space-x-2">
                    <Checkbox
                      id={`mobile-category-${category}`}
                      checked={categories.includes(category)}
                      onCheckedChange={() => toggleCategory(category)}
                    />
                    <Label htmlFor={`mobile-category-${category}`}>{category}</Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Pricing */}
            <div>
              <h3 className="font-medium mb-2">Pricing</h3>
              <div className="space-y-2">
                {["Free", "Freemium", "Paid", "Enterprise"].map((price) => (
                  <div key={price} className="flex items-center space-x-2">
                    <Checkbox
                      id={`mobile-price-${price}`}
                      checked={pricing.includes(price)}
                      onCheckedChange={() => togglePricing(price)}
                    />
                    <Label htmlFor={`mobile-price-${price}`}>{price}</Label>
                  </div>
                ))}
              </div>
            </div>

            {/* Features */}
            <div>
              <h3 className="font-medium mb-2">Features</h3>
              <div className="space-y-2 max-h-48 overflow-y-auto">
                {availableFeatures.map((feature) => (
                  <div key={feature} className="flex items-center space-x-2">
                    <Checkbox
                      id={`mobile-feature-${feature}`}
                      checked={features.includes(feature)}
                      onCheckedChange={() => toggleFeature(feature)}
                    />
                    <Label htmlFor={`mobile-feature-${feature}`}>{feature}</Label>
                  </div>
                ))}
              </div>
            </div>

            <div className="flex gap-2 pt-4">
              <Button onClick={applyFilters} className="flex-1">
                Apply Filters
              </Button>
              <Button variant="outline" onClick={resetFilters}>
                Reset
              </Button>
            </div>
          </div>
        </div>
      </div>

      {/* Desktop Filters */}
      <div className="hidden md:block space-y-6 sticky top-20">
        <div className="bg-white dark:bg-slate-800 rounded-lg shadow-sm p-4">
          {/* Search */}
          <div className="mb-4">
            <Label htmlFor="search">Search</Label>
            <div className="relative mt-1">
              <Search className="absolute left-2 top-1/2 transform -translate-y-1/2 h-4 w-4 text-slate-400" />
              <Input
                id="search"
                type="search"
                placeholder="Search tools..."
                className="pl-8"
                value={query}
                onChange={(e) => setQuery(e.target.value)}
              />
            </div>
          </div>

          {/* Sort */}
          <div className="mb-4">
            <h3 className="font-medium mb-2">Sort By</h3>
            <RadioGroup value={sort} onValueChange={setSort}>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="featured" id="sort-featured" />
                <Label htmlFor="sort-featured">Featured</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="newest" id="sort-newest" />
                <Label htmlFor="sort-newest">Newest</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="popular" id="sort-popular" />
                <Label htmlFor="sort-popular">Popular</Label>
              </div>
              <div className="flex items-center space-x-2">
                <RadioGroupItem value="top-rated" id="sort-top-rated" />
                <Label htmlFor="sort-top-rated">Top Rated</Label>
              </div>
            </RadioGroup>
          </div>

          <Accordion type="multiple" defaultValue={["categories", "pricing", "features"]} className="space-y-2">
            {/* Categories */}
            <AccordionItem value="categories" className="border-b-0">
              <AccordionTrigger className="py-2">Categories</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-2 max-h-48 overflow-y-auto pr-2">
                  {availableCategories.map((category) => (
                    <div key={category} className="flex items-center space-x-2">
                      <Checkbox
                        id={`category-${category}`}
                        checked={categories.includes(category)}
                        onCheckedChange={() => toggleCategory(category)}
                      />
                      <Label htmlFor={`category-${category}`} className="text-sm">
                        {category}
                      </Label>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Pricing */}
            <AccordionItem value="pricing" className="border-b-0">
              <AccordionTrigger className="py-2">Pricing</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-2">
                  {["Free", "Freemium", "Paid", "Enterprise"].map((price) => (
                    <div key={price} className="flex items-center space-x-2">
                      <Checkbox
                        id={`price-${price}`}
                        checked={pricing.includes(price)}
                        onCheckedChange={() => togglePricing(price)}
                      />
                      <Label htmlFor={`price-${price}`} className="text-sm">
                        {price}
                      </Label>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>

            {/* Features */}
            <AccordionItem value="features" className="border-b-0">
              <AccordionTrigger className="py-2">Features</AccordionTrigger>
              <AccordionContent>
                <div className="space-y-2 max-h-48 overflow-y-auto pr-2">
                  {availableFeatures.map((feature) => (
                    <div key={feature} className="flex items-center space-x-2">
                      <Checkbox
                        id={`feature-${feature}`}
                        checked={features.includes(feature)}
                        onCheckedChange={() => toggleFeature(feature)}
                      />
                      <Label htmlFor={`feature-${feature}`} className="text-sm">
                        {feature}
                      </Label>
                    </div>
                  ))}
                </div>
              </AccordionContent>
            </AccordionItem>
          </Accordion>

          <div className="flex gap-2 pt-4">
            <Button onClick={applyFilters} className="flex-1">
              Apply Filters
            </Button>
            <Button variant="outline" onClick={resetFilters}>
              Reset
            </Button>
          </div>
        </div>
      </div>
    </>
  )
}
