"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { Card, CardContent, CardDescription, Card<PERSON>ooter, CardHeader, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import LoadingSpinner from "@/components/loading-spinner"
import { TrendingU<PERSON>, <PERSON><PERSON><PERSON>, ArrowRight } from "lucide-react"

// Define Category interface with all required properties
interface Category {
  name: string;
  count: number;
  slug: string;
  isHighlighted?: boolean;
  icon?: React.ReactNode;
}

export default function CategoryGrid() {
  const [categories, setCategories] = useState<Category[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Predefined default categories including the required ones
  const fallbackCategories = [
    { name: "Top 50 Trends [24H]", count: 50, slug: "top-50-trends", isHighlighted: true, icon: <TrendingUp className="w-4 h-4 mr-1.5" /> },
    { name: "Latest AI", count: 32, slug: "latest-ai", isHighlighted: true, icon: <Sparkles className="w-4 h-4 mr-1.5" /> },
    { name: "Design", count: 24, slug: "design" },
    { name: "Writing", count: 18, slug: "writing" },
    { name: "Productivity", count: 32, slug: "productivity" },
    { name: "Development", count: 16, slug: "development" },
    { name: "Marketing", count: 12, slug: "marketing" },
    { name: "Research", count: 8, slug: "research" },
    { name: "Business", count: 15, slug: "business" },
    { name: "Education", count: 11, slug: "education" }
  ]

  useEffect(() => {
    const fetchCategories = async () => {
      setLoading(true)
      setError(null)
      
      try {
        const supabase = createBrowserClient()
        // Fetch unique primary_task values and count of tools per task
        const { data, error } = await supabase
          .from("tools")
          .select("primary_task, id")
          .not("primary_task", "is", null)
          
        if (error) {
          console.error("Error fetching categories:", error)
          setError("Failed to load categories. Please try again later.")
          // Use fallback categories instead of empty array
          setCategories(ensureHighlightedCategories([...fallbackCategories]))
          return
        }

        // Process the data to get counts by category
        const categoryMap: Record<string, number> = {}
        data.forEach(tool => {
          if (tool.primary_task) {
            categoryMap[tool.primary_task] = (categoryMap[tool.primary_task] || 0) + 1
          }
        })
        
        // Convert to array of objects with name, count, and slug
        const categoryArray = Object.entries(categoryMap)
          .filter(([name]) => Boolean(name)) // Remove any null/undefined/empty values
          .map(([name, count]): Category => {
            // Add default icon for regular categories
            const defaultIcon = name.toLowerCase().includes('design') ? 
              <TrendingUp className="w-4 h-4 mr-1.5" /> : 
              null;
            
            return {
              name,
              count,
              slug: name.toLowerCase().replace(/\s+/g, '-'),
              isHighlighted: false,
              icon: defaultIcon
            };
          })
          // Special sorting to ensure "Top 50 Trends" and "Latest AI" are at the top
          // Other categories sorted by count
        
        // Apply our special sorting logic to ensure highlighted categories are at the top
        const sortedCategories = ensureHighlightedCategories(categoryArray);
        setCategories(sortedCategories)
      } catch (err) {
        console.error("Error in category fetch:", err)
        setError("An unexpected error occurred. Please try again.")
        // Use fallback categories on error
        setCategories(ensureHighlightedCategories([...fallbackCategories]))
      } finally {
        setLoading(false)
      }
    }
    
    fetchCategories()
  }, [])

  // Modern gradient colors for category cards
  const gradientClasses = [
    "from-purple-500 to-pink-600", // Primary gradient for highlighted categories
    "from-blue-500 to-indigo-600",
    "from-emerald-500 to-teal-600",
    "from-amber-500 to-orange-600",
    "from-rose-500 to-red-600",
    "from-cyan-500 to-blue-600",
    "from-fuchsia-500 to-purple-600",
    "from-lime-500 to-green-600"
  ]
  
  // Add custom highlighted categories that should appear at the top
  const ensureHighlightedCategories = (categories: Category[]): Category[] => {
    // Check if Top 50 Trends exists, if not add it
    if (!categories.some((c: Category) => c.name === "Top 50 Trends [24H]")) {
      categories.unshift({
        name: "Top 50 Trends [24H]",
        count: 50,
        slug: "top-50-trends",
        isHighlighted: true,
        icon: <TrendingUp className="w-4 h-4 mr-1.5" />
      });
    }
    
    // Check if Latest AI exists, if not add it
    if (!categories.some((c: Category) => c.name === "Latest AI")) {
      categories.splice(1, 0, {
        name: "Latest AI",
        count: 32,
        slug: "latest-ai",
        isHighlighted: true,
        icon: <Sparkles className="w-4 h-4 mr-1.5" />
      });
    }
    
    // Make sure the specified categories are at the top in the correct order
    return categories.sort((a: Category, b: Category) => {
      if (a.name === "Top 50 Trends [24H]") return -1;
      if (b.name === "Top 50 Trends [24H]") return 1;
      if (a.name === "Latest AI") return -1;
      if (b.name === "Latest AI") return 1;
      return b.count - a.count; // Sort remaining by count
    });
  }

  if (loading) {
    return <div className="flex justify-center py-12"><LoadingSpinner /></div>
  }

  if (error && categories.length === 0) {
    return (
      <div className="text-center py-12">
        <p>{error}</p>
        <button 
          onClick={() => window.location.reload()}
          className="mt-4 px-4 py-2 bg-blue-600 text-white rounded-md hover:bg-blue-700 transition-colors"
        >
          Retry
        </button>
      </div>
    )
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-xl font-bold text-slate-900 dark:text-white">Browse Categories</h2>
          <p className="text-sm text-slate-600 dark:text-slate-400">Explore the full range of AI writing tools by category</p>
        </div>
        
        <Link href="/categories" className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 flex items-center">
          See all <ArrowRight className="ml-1 h-3.5 w-3.5" />
        </Link>
      </div>
      
      {/* Multi-column category lists as shown in the image */}
      <div className="grid grid-cols-2 sm:grid-cols-3 md:grid-cols-4 lg:grid-cols-5 gap-x-6 gap-y-1">
        {/* Group categories in columns rather than cards */}
        {Array.from({ length: 5 }).map((_, colIndex) => (
          <div key={colIndex} className="flex flex-col space-y-2">
            {/* Column header */}
            {colIndex === 0 && (
              <h3 className="font-medium text-sm text-slate-900 dark:text-white mb-1 flex items-center">
                <TrendingUp className="w-3.5 h-3.5 mr-1 text-blue-500" /> Top 50 Trends [24H]
              </h3>
            )}
            {colIndex === 1 && (
              <h3 className="font-medium text-sm text-slate-900 dark:text-white mb-1 flex items-center">
                <Sparkles className="w-3.5 h-3.5 mr-1 text-blue-500" /> Latest AI
              </h3>
            )}
            {colIndex === 2 && (
              <h3 className="font-medium text-sm text-slate-900 dark:text-white mb-1">
                Writing
              </h3>
            )}
            {colIndex === 3 && (
              <h3 className="font-medium text-sm text-slate-900 dark:text-white mb-1">
                Productivity
              </h3>
            )}
            {colIndex === 4 && (
              <h3 className="font-medium text-sm text-slate-900 dark:text-white mb-1">
                Research
              </h3>
            )}
            
            {/* Category items */}
            {categories
              .filter((_, i) => i % 5 === colIndex) // Distribute categories across columns
              .map((category) => (
                <Link 
                  href={`/category/${category.slug}`} 
                  key={category.slug}
                  className="text-xs text-slate-600 dark:text-slate-400 hover:text-blue-600 dark:hover:text-blue-400 hover:underline transition-colors"
                >
                  {category.name} ({category.count})
                </Link>
              ))}
          </div>
        ))}
      </div>
    </div>
  )
}
