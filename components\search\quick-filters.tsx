"use client"

import { useState } from "react"
import { Filter, CheckCircle, Sparkles, Target, Zap, X } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Badge } from "@/components/ui/badge"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue
} from "@/components/ui/select"

interface QuickFiltersProps {
  onFiltersChange: (filters: {
    pricing?: string
    sortBy?: string
    features?: string[]
  }) => void
  className?: string
  compact?: boolean
}

export default function QuickFilters({
  onFiltersChange,
  className = "",
  compact = false
}: QuickFiltersProps) {
  const [selectedPricing, setSelectedPricing] = useState("all")
  const [selectedSort, setSelectedSort] = useState("relevance")
  const [selectedFeatures, setSelectedFeatures] = useState<string[]>([])

  const handlePricingChange = (value: string) => {
    setSelectedPricing(value)
    onFiltersChange({
      pricing: value === "all" ? undefined : value,
      sortBy: selectedSort === "relevance" ? undefined : selectedSort,
      features: selectedFeatures
    })
  }

  const handleSortChange = (value: string) => {
    setSelectedSort(value)
    onFiltersChange({
      pricing: selectedPricing === "all" ? undefined : selectedPricing,
      sortBy: value === "relevance" ? undefined : value,
      features: selectedFeatures
    })
  }

  const handleFeatureToggle = (feature: string) => {
    const newFeatures = selectedFeatures.includes(feature)
      ? selectedFeatures.filter(f => f !== feature)
      : [...selectedFeatures, feature]

    setSelectedFeatures(newFeatures)
    onFiltersChange({
      pricing: selectedPricing === "all" ? undefined : selectedPricing,
      sortBy: selectedSort === "relevance" ? undefined : selectedSort,
      features: newFeatures
    })
  }

  const clearAllFilters = () => {
    setSelectedPricing("all")
    setSelectedSort("relevance")
    setSelectedFeatures([])
    onFiltersChange({})
  }

  const hasActiveFilters = selectedPricing !== "all" || selectedSort !== "relevance" || selectedFeatures.length > 0

  const quickFeatures = [
    { value: "verified", label: "Verified", icon: CheckCircle },
    { value: "featured", label: "Featured", icon: Sparkles },
    { value: "free", label: "Free", icon: Target },
  ]

  if (compact) {
    return (
      <div className={`flex flex-wrap gap-2 items-center ${className}`}>
        {/* Quick Feature Buttons */}
        {quickFeatures.map((feature) => (
          <Button
            key={feature.value}
            variant={selectedFeatures.includes(feature.value) ? "default" : "outline"}
            size="sm"
            onClick={() => handleFeatureToggle(feature.value)}
            className="h-8 px-3 rounded-full text-xs"
          >
            <feature.icon size={12} className="mr-1" />
            {feature.label}
          </Button>
        ))}

        {/* Clear Button */}
        {hasActiveFilters && (
          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="h-8 w-8 p-0 rounded-full text-slate-500 hover:text-red-500"
          >
            <X size={14} />
          </Button>
        )}
      </div>
    )
  }

  return (
    <div className={`space-y-4 ${className}`}>
      {/* Active Filters Indicator */}
      {hasActiveFilters && (
        <div className="flex flex-wrap gap-2 items-center bg-primary/5 dark:bg-primary/10 rounded-lg px-3 py-2 border border-primary/20">
          <Filter size={14} className="text-primary" />
          <span className="text-sm font-medium text-primary">Active:</span>

          {selectedPricing !== "all" && (
            <Badge variant="secondary" className="text-xs">
              {selectedPricing}
            </Badge>
          )}

          {selectedSort !== "relevance" && (
            <Badge variant="secondary" className="text-xs">
              {selectedSort}
            </Badge>
          )}

          {selectedFeatures.map(feature => (
            <Badge key={feature} variant="secondary" className="text-xs">
              {quickFeatures.find(f => f.value === feature)?.label || feature}
            </Badge>
          ))}

          <Button
            variant="ghost"
            size="sm"
            onClick={clearAllFilters}
            className="h-6 w-6 p-0 text-slate-500 hover:text-red-500"
          >
            <X size={12} />
          </Button>
        </div>
      )}

      {/* Filter Controls */}
      <div className="flex flex-wrap gap-3">
        {/* Pricing Filter */}
        <div className="flex-1 min-w-[150px]">
          <Select value={selectedPricing} onValueChange={handlePricingChange}>
            <SelectTrigger className="h-9 rounded-lg">
              <SelectValue placeholder="Pricing" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="all">Any pricing</SelectItem>
              <SelectItem value="Free">Free</SelectItem>
              <SelectItem value="Freemium">Freemium</SelectItem>
              <SelectItem value="Paid">Paid</SelectItem>
              <SelectItem value="Subscription">Subscription</SelectItem>
            </SelectContent>
          </Select>
        </div>

        {/* Sort Filter */}
        <div className="flex-1 min-w-[150px]">
          <Select value={selectedSort} onValueChange={handleSortChange}>
            <SelectTrigger className="h-9 rounded-lg">
              <SelectValue placeholder="Sort by" />
            </SelectTrigger>
            <SelectContent>
              <SelectItem value="relevance">Best match</SelectItem>
              <SelectItem value="featured">Featured</SelectItem>
              <SelectItem value="newest">Newest</SelectItem>
              <SelectItem value="popular">Popular</SelectItem>
            </SelectContent>
          </Select>
        </div>
      </div>

      {/* Quick Feature Filters */}
      <div className="flex flex-wrap gap-2">
        {quickFeatures.map((feature) => (
          <Button
            key={feature.value}
            variant={selectedFeatures.includes(feature.value) ? "default" : "outline"}
            size="sm"
            onClick={() => handleFeatureToggle(feature.value)}
            className="h-8 px-3 rounded-full text-xs transition-all duration-200"
          >
            <feature.icon size={12} className="mr-1" />
            {feature.label}
          </Button>
        ))}
      </div>
    </div>
  )
}
