import { Accordion, AccordionContent, AccordionItem, AccordionTrigger } from "@/components/ui/accordion"

interface ToolFAQsProps {
  faqs: Record<string, string> | null
}

export default function ToolFAQs({ faqs }: ToolFAQsProps) {
  if (!faqs || Object.keys(faqs).length === 0) {
    return (
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
        <p className="text-slate-600 dark:text-slate-400 text-center py-8">No FAQs available for this tool.</p>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold mb-6">Frequently Asked Questions</h2>

      <Accordion type="single" collapsible className="w-full">
        {Object.entries(faqs).map(([question, answer], index) => (
          <AccordionItem key={index} value={`item-${index}`}>
            <AccordionTrigger className="text-left">{question}</AccordionTrigger>
            <AccordionContent>
              <div dangerouslySetInnerHTML={{ __html: answer as string }} />
            </AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  )
}
