'use client'

import Script from 'next/script'
import { useEffect, useState } from 'react'
// Removed cookie consent dependency - using modern approach

interface GoogleAnalyticsProps {
  GA_MEASUREMENT_ID: string
}

export default function GoogleAnalytics({ GA_MEASUREMENT_ID }: GoogleAnalyticsProps) {
  const [isClient, setIsClient] = useState(false)

  useEffect(() => {
    setIsClient(true)
    console.log('✅ GA: Initializing Google Analytics with modern approach (no consent popups)')
  }, [])

  // Always load GA scripts on client side - modern approach
  if (!isClient) {
    return null
  }

  console.log('✅ GA: Loading Google Analytics with ID:', GA_MEASUREMENT_ID)

  return (
    <>
      <Script
        strategy="afterInteractive"
        src={`https://www.googletagmanager.com/gtag/js?id=${GA_MEASUREMENT_ID}`}
      />
      <Script
        id="google-analytics"
        strategy="afterInteractive"
        dangerouslySetInnerHTML={{
          __html: `
            window.dataLayer = window.dataLayer || [];
            function gtag(){dataLayer.push(arguments);}

            // Set default consent state - ALL tracking enabled by default
            gtag('consent', 'default', {
              analytics_storage: 'granted',
              ad_storage: 'granted',
              ad_user_data: 'granted',
              ad_personalization: 'granted',
              wait_for_update: 500
            });

            gtag('js', new Date());
            gtag('config', '${GA_MEASUREMENT_ID}', {
              page_title: document.title,
              page_location: window.location.href,
              // Enhanced tracking
              send_page_view: true,
              // Track outbound links
              link_attribution: true,
              // Track file downloads
              enhanced_link_attribution: true,
              // Track scroll depth and engagement
              custom_map: {
                'custom_parameter_1': 'scroll_depth'
              },
              // Privacy settings
              anonymize_ip: true,
              cookie_flags: 'SameSite=Lax;Secure',
              // Additional tracking features - ALL enabled by default
              allow_google_signals: true,
              allow_ad_personalization_signals: true
            });

            // Track initial page view
            gtag('event', 'page_view', {
              page_title: document.title,
              page_location: window.location.href,
              page_path: window.location.pathname
            });

            console.log('📊 GA: Initialized with modern approach - no consent popups needed');
          `,
        }}
      />
    </>
  )
}

// Hook for comprehensive tracking - Modern approach without consent checks
export function useGoogleAnalytics() {
  const trackEvent = (action: string, category: string, label?: string, value?: number) => {
    // Always track - modern approach without consent popups
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', action, {
        event_category: category,
        event_label: label,
        value: value,
        // Add timestamp for better tracking
        custom_parameter_timestamp: Date.now()
      })
      console.log('📊 GA Event:', { action, category, label, value })
    }
  }

  const trackPageView = (url: string, title?: string) => {
    // Always track - modern approach without consent popups
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('config', process.env.NEXT_PUBLIC_GA_MEASUREMENT_ID, {
        page_title: title || document.title,
        page_location: url,
        page_path: new URL(url).pathname,
        // Enhanced page tracking
        send_page_view: true
      })
      console.log('📊 GA Page View:', { url, title })
    }
  }

  // Track tool clicks
  const trackToolClick = (toolName: string, toolSlug: string, position?: number) => {
    trackEvent('tool_click', 'engagement', `${toolName} (${toolSlug})`, position)
  }

  // Track search queries
  const trackSearch = (searchTerm: string, resultsCount?: number) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'search', {
        search_term: searchTerm,
        results_count: resultsCount,
        engagement_time_msec: 100
      })
      console.log('📊 GA Search:', { searchTerm, resultsCount })
    }
  }

  // Track category navigation
  const trackCategoryView = (categoryName: string) => {
    trackEvent('category_view', 'navigation', categoryName)
  }

  // Track outbound links
  const trackOutboundLink = (url: string, linkText?: string) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', 'click', {
        event_category: 'outbound_link',
        event_label: url,
        link_text: linkText,
        transport_type: 'beacon'
      })
      console.log('📊 GA Outbound Link:', { url, linkText })
    }
  }

  // Track form submissions
  const trackFormSubmission = (formName: string, success: boolean = true) => {
    trackEvent(success ? 'form_submit_success' : 'form_submit_error', 'engagement', formName)
  }

  // Track scroll depth
  const trackScrollDepth = (percentage: number) => {
    trackEvent('scroll', 'engagement', `${percentage}%`, percentage)
  }

  // Track time on page
  const trackTimeOnPage = (seconds: number, pagePath: string) => {
    trackEvent('time_on_page', 'engagement', pagePath, seconds)
  }

  // Track user interactions
  const trackUserInteraction = (interactionType: string, element: string, details?: string) => {
    trackEvent(interactionType, 'user_interaction', `${element}${details ? ` - ${details}` : ''}`)
  }

  // Track custom events for enhanced analytics
  const trackCustomEvent = (eventName: string, parameters: Record<string, any> = {}) => {
    if (typeof window !== 'undefined' && window.gtag) {
      window.gtag('event', eventName, {
        ...parameters,
        timestamp: Date.now()
      })
      console.log('📊 GA Custom Event:', { eventName, parameters })
    }
  }

  return {
    trackEvent,
    trackPageView,
    trackToolClick,
    trackSearch,
    trackCategoryView,
    trackOutboundLink,
    trackFormSubmission,
    trackScrollDepth,
    trackTimeOnPage,
    trackUserInteraction,
    trackCustomEvent
  }
}

// Extend Window interface for TypeScript
declare global {
  interface Window {
    gtag: (...args: any[]) => void
  }
}
