"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import ToolCard from "@/components/tool-card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import MotionWrapper from "@/components/ui/MotionWrapper"

interface ToolGridClientProps {
  queryType?: "featured" | "top-rated" | "recent" | "all"
  limit?: number
  columnsPerRow?: number
}

export default function ToolGridClient({
  queryType = "all",
  limit = 12,
  columnsPerRow = 4,
}: ToolGridClientProps) {
  const [tools, setTools] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchTools = async () => {
      try {
        setIsLoading(true)
        const supabase = createBrowserClient()

        // Start building the query
        let query = supabase.from("tools").select("*")

        // Apply query type filter
        if (queryType === "featured") {
          query = query.eq("is_featured", true)
        } else if (queryType === "top-rated") {
          // Assuming top-rated is based on rating field
          query = query.order("rating", { ascending: false })
        } else if (queryType === "recent") {
          query = query.order("created_at", { ascending: false })
        }

        // Apply default sorting based on query type
        if (queryType === "featured") {
          query = query.order("is_featured", { ascending: false }).order("click_count", { ascending: false })
        } else if (queryType === "top-rated") {
          query = query.order("rating", { ascending: false })
        } else if (queryType === "recent") {
          query = query.order("created_at", { ascending: false })
        } else {
          // Default sorting for "all"
          query = query.order("is_featured", { ascending: false }).order("click_count", { ascending: false })
        }

        // Apply limit
        query = query.limit(limit)

        // Execute the query
        const { data, error: supabaseError } = await query

        if (supabaseError) throw supabaseError

        setTools(data || [])
      } catch (err) {
        console.error("Error fetching tools:", err)
        setError(err instanceof Error ? err : new Error("Failed to fetch tools"))
      } finally {
        setIsLoading(false)
      }
    }

    fetchTools()
  }, [queryType, limit])

  // Determine grid columns class based on columnsPerRow
  const gridColsClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 sm:grid-cols-2",
    3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
  }[columnsPerRow] || "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"

  if (isLoading) {
    return (
      <div className="flex justify-center items-center py-8">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
      </div>
    )
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load tools. Please try again later.
        </AlertDescription>
      </Alert>
    )
  }

  // If no tools found
  if (!tools || tools.length === 0) {
    return (
      <Alert variant="default" className="bg-muted">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>No tools found</AlertTitle>
        <AlertDescription>
          Try adjusting your search or filter criteria to find more tools.
        </AlertDescription>
      </Alert>
    )
  }

  // Render the grid
  return (
    <div className={`grid ${gridColsClasses} gap-4`}>
      {tools.map((tool, index) => (
        <MotionWrapper
          key={tool.id}
          animation="fadeIn"
          delay={index < 5 ? (index === 0 ? "none" : index === 1 ? "delay-100" : index === 2 ? "delay-200" : index === 3 ? "delay-300" : "delay-400") : "none"}
        >
          <ToolCard tool={tool} />
        </MotionWrapper>
      ))}
    </div>
  )
}
