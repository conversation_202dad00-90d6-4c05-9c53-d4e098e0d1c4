"use client"

import { useState, useEffect, memo } from "react"
import { motion, useInView } from "@/lib/motion-stub"
import { useRef } from "react"
import { TrendingUp, Users, Star, Zap } from "lucide-react"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { cn } from "@/lib/utils"

interface StatItem {
  id: string
  label: string
  value: number
  icon: React.ReactNode
  suffix?: string
  prefix?: string
  color?: string
  description?: string
}

interface StatsDisplayProps {
  className?: string
  variant?: "default" | "compact" | "detailed"
  animated?: boolean
  refreshInterval?: number
}

const StatsDisplay = memo(function StatsDisplay({
  className,
  variant = "default",
  animated = true,
  refreshInterval = 60000 // 1 minute
}: StatsDisplayProps) {
  const [stats, setStats] = useState<StatItem[]>([
    {
      id: "tools",
      label: "AI Tools",
      value: 0,
      icon: <Zap className="h-5 w-5" />,
      suffix: "+",
      color: "text-blue-600",
      description: "Curated AI tools in our directory"
    },
    {
      id: "categories", 
      label: "Categories",
      value: 0,
      icon: <TrendingUp className="h-5 w-5" />,
      color: "text-green-600",
      description: "Different tool categories available"
    },
    {
      id: "users",
      label: "Users",
      value: 0,
      icon: <Users className="h-5 w-5" />,
      suffix: "+",
      color: "text-purple-600",
      description: "Active users in our community"
    },
    {
      id: "reviews",
      label: "Reviews",
      value: 0,
      icon: <Star className="h-5 w-5" />,
      suffix: "+",
      color: "text-yellow-600",
      description: "User reviews and ratings"
    }
  ])
  
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)
  const ref = useRef<HTMLDivElement>(null)
  const isInView = useInView(ref, { once: true, margin: "-100px" })

  const supabase = createBrowserClient()

  // Fetch statistics from database
  const fetchStats = async () => {
    try {
      setIsLoading(true)
      setError(null)

      // Fetch all stats in parallel - removed profiles to avoid 500 error
      const [toolsResult, categoriesResult, reviewsResult] = await Promise.allSettled([
        supabase.from('tools').select('id', { count: 'exact', head: true }),
        supabase.from('tools').select('primary_task').not('primary_task', 'is', null),
        supabase.from('reviews').select('id', { count: 'exact', head: true })
      ])

      // Process results
      const toolsCount = toolsResult.status === 'fulfilled' ? toolsResult.value.count || 0 : 0
      
      let categoriesCount = 0
      if (categoriesResult.status === 'fulfilled' && categoriesResult.value.data) {
        const uniqueCategories = new Set(categoriesResult.value.data.map(item => item.primary_task))
        categoriesCount = uniqueCategories.size
      }
      
      const usersCount = usersResult.status === 'fulfilled' ? usersResult.value.count || 0 : 0
      const reviewsCount = reviewsResult.status === 'fulfilled' ? reviewsResult.value.count || 0 : 0

      // Update stats with real data
      setStats(prev => prev.map(stat => {
        switch (stat.id) {
          case 'tools':
            return { ...stat, value: toolsCount }
          case 'categories':
            return { ...stat, value: categoriesCount }
          case 'users':
            return { ...stat, value: usersCount }
          case 'reviews':
            return { ...stat, value: reviewsCount }
          default:
            return stat
        }
      }))

    } catch (err) {
      console.error('Failed to fetch stats:', err)
      setError('Failed to load statistics')
      
      // Fallback to default values
      setStats(prev => prev.map(stat => ({
        ...stat,
        value: stat.id === 'tools' ? 300 :
               stat.id === 'categories' ? 18 :
               stat.id === 'users' ? 1200 :
               stat.id === 'reviews' ? 850 : 0
      })))
    } finally {
      setIsLoading(false)
    }
  }

  // Initial fetch and periodic refresh
  useEffect(() => {
    fetchStats()
    
    if (refreshInterval > 0) {
      const interval = setInterval(fetchStats, refreshInterval)
      return () => clearInterval(interval)
    }
  }, [refreshInterval])

  // Animated counter component
  const AnimatedCounter = memo(function AnimatedCounter({ 
    value, 
    duration = 2000 
  }: { 
    value: number
    duration?: number 
  }) {
    const [count, setCount] = useState(0)
    
    useEffect(() => {
      if (!animated || !isInView) {
        setCount(value)
        return
      }

      let startTime: number
      let animationFrame: number

      const animate = (timestamp: number) => {
        if (!startTime) startTime = timestamp
        const progress = Math.min((timestamp - startTime) / duration, 1)
        
        // Easing function for smooth animation
        const easeOutQuart = 1 - Math.pow(1 - progress, 4)
        setCount(Math.floor(value * easeOutQuart))
        
        if (progress < 1) {
          animationFrame = requestAnimationFrame(animate)
        }
      }
      
      animationFrame = requestAnimationFrame(animate)
      return () => cancelAnimationFrame(animationFrame)
    }, [value, duration, animated, isInView])
    
    return <span>{count.toLocaleString()}</span>
  })

  const containerVariants = {
    hidden: { opacity: 0 },
    visible: {
      opacity: 1,
      transition: {
        staggerChildren: 0.1
      }
    }
  }

  const itemVariants = {
    hidden: { opacity: 0, y: 20 },
    visible: {
      opacity: 1,
      y: 0,
      transition: {
        duration: 0.5,
        ease: "easeOut"
      }
    }
  }

  if (error && variant !== "compact") {
    return (
      <div className="text-center py-4">
        <p className="text-sm text-muted-foreground">{error}</p>
      </div>
    )
  }

  return (
    <div ref={ref} className={cn("w-full", className)}>
      <motion.div
        variants={animated ? containerVariants : undefined}
        initial={animated ? "hidden" : undefined}
        animate={animated && isInView ? "visible" : undefined}
        className={cn(
          "grid gap-4",
          variant === "compact" ? "grid-cols-2 sm:grid-cols-4" :
          variant === "detailed" ? "grid-cols-1 sm:grid-cols-2 lg:grid-cols-4" :
          "grid-cols-2 sm:grid-cols-4"
        )}
      >
        {stats.map((stat) => (
          <motion.div
            key={stat.id}
            variants={animated ? itemVariants : undefined}
            className={cn(
              "text-center p-4 rounded-lg",
              variant === "detailed" && "bg-card border shadow-sm hover:shadow-md transition-shadow",
              variant === "compact" && "p-2"
            )}
          >
            <div className={cn(
              "flex items-center justify-center mb-2",
              variant === "compact" && "mb-1"
            )}>
              <div className={cn(
                "p-2 rounded-full bg-background border",
                stat.color,
                variant === "compact" && "p-1"
              )}>
                {stat.icon}
              </div>
            </div>
            
            <div className={cn(
              "text-2xl font-bold",
              stat.color,
              variant === "compact" && "text-lg"
            )}>
              {stat.prefix}
              <AnimatedCounter value={stat.value} />
              {stat.suffix}
            </div>
            
            <div className={cn(
              "text-sm font-medium text-foreground",
              variant === "compact" && "text-xs"
            )}>
              {stat.label}
            </div>
            
            {variant === "detailed" && stat.description && (
              <p className="text-xs text-muted-foreground mt-1">
                {stat.description}
              </p>
            )}
          </motion.div>
        ))}
      </motion.div>
    </div>
  )
})

export default StatsDisplay
