"use client"

import { useState, useEffect, Suspense, lazy, ComponentType, LazyExoticComponent } from 'react'
import { useInView } from 'react-intersection-observer'

interface DeferredComponentProps<P = any> {
  // Component to be loaded lazily
  component: LazyExoticComponent<ComponentType<P>>
  // Props to be passed to the component
  props?: P
  // Fallback component to display during loading
  fallback?: React.ReactNode
  // Visibility threshold (0-1)
  threshold?: number
  // Root margin for the observer
  rootMargin?: string
  // Whether the observer should trigger only once
  triggerOnce?: boolean
  // Whether the component has priority (will be loaded immediately)
  priority?: boolean
  // Height of the fallback component
  height?: string | number
  // Width of the fallback component
  width?: string | number
  // CSS class for the container
  className?: string
}

/**
 * Component for loading components lazily when they appear in view
 * Helps improve performance by deferring component loading until visible
 */
export function DeferredComponent<P = any>({
  component: Component,
  props,
  fallback = <div className="h-32 w-full animate-pulse bg-muted/30 rounded-md"></div>,
  threshold = 0.1,
  rootMargin = '300px 0px',
  triggerOnce = true,
  priority = false,
  height,
  width,
  className = '',
}: DeferredComponentProps<P>) {
  const [isClient, setIsClient] = useState(false)
  const [shouldRender, setShouldRender] = useState(false)
  
  // Use IntersectionObserver for lazy loading
  const { ref, inView } = useInView({
    threshold,
    rootMargin,
    triggerOnce,
    skip: priority, // Skip observation if component has priority
  })

  // Avoid server/client rendering mismatch
  useEffect(() => {
    setIsClient(true)
    
    // If component has priority, load it immediately
    if (priority) {
      setShouldRender(true)
    }
  }, [priority])
  
  // When component appears in view, load it
  useEffect(() => {
    if (inView) {
      setShouldRender(true)
    }
  }, [inView])

  // If we're not on the client, don't render anything to avoid rendering errors
  if (!isClient) {
    return null
  }
  
  return (
    <div 
      ref={!priority ? ref : undefined} 
      className={`w-full ${className}`}
      style={{
        height: !shouldRender && height ? height : undefined,
        width: width,
        minHeight: !shouldRender && height ? height : undefined,
      }}
    >
      {shouldRender ? (
        <Suspense fallback={fallback}>
          <Component {...(props as P)} />
        </Suspense>
      ) : (
        fallback
      )}
    </div>
  )
}

/**
 * Helper to create a lazy-loaded component
 * @param importFunc Function to import the component
 * @returns Lazy-loaded component
 */
export function createDeferredComponent<P = any>(
  importFunc: () => Promise<{ default: ComponentType<P> }>
): LazyExoticComponent<ComponentType<P>> {
  return lazy(importFunc)
}

/**
 * Usage example:
 * 
 * // Create a lazy-loaded component
 * const HeavyComponent = createDeferredComponent(() => import('@/components/heavy-component'))
 * 
 * // Use the component in the application
 * <DeferredComponent 
 *   component={HeavyComponent} 
 *   props={{ someData: data }} 
 *   height={300}
 * />
 */
