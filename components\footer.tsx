import Link from "next/link"
import { Gith<PERSON>, Twitter, Mail } from "lucide-react"
import { CookieSettingsLink } from "@/components/cookie-consent"
import { features } from "@/lib/config"

export default function Footer() {
  const currentYear = new Date().getFullYear()

  return (
    <footer className="bg-background border-t border-border theme-transition">
      <div className="container mx-auto px-4 sm:px-6 lg:px-8 max-w-7xl py-12">
        <div className="grid grid-cols-1 md:grid-cols-3 gap-8 lg:gap-12">
          {/* Logo and Description */}
          <div className="md:col-span-1">
            <Link href="/" className="flex items-center group mb-4">
              <div className="flex items-center">
                <div className="relative bg-gradient-to-br from-purple-500 to-pink-600 text-white font-bold rounded-lg w-9 h-9 flex items-center justify-center text-base shadow-sm group-hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                  <span className="relative z-10">AI</span>
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg opacity-0 group-hover:opacity-20 transition-opacity duration-300"></div>
                </div>
                <span className="text-xl font-bold ml-3 text-foreground group-hover:text-primary transition-colors duration-300">
                  AI<span className="text-primary">Any</span>Tool
                </span>
              </div>
            </Link>
            <p className="text-muted-foreground text-sm leading-relaxed max-w-sm">
              Discover and explore the best AI tools to enhance your productivity and workflow. Your ultimate directory for AI-powered solutions.
            </p>
          </div>

          {/* Quick Links */}
          <div>
            <h3 className="text-sm font-semibold text-foreground mb-4 uppercase tracking-wider">Quick Links</h3>
            <ul className="space-y-3">
              <li>
                <Link href="/tools" className="text-muted-foreground hover:text-primary transition-colors duration-200 text-sm">
                  Browse Tools
                </Link>
              </li>
              <li>
                <Link href="/categories" className="text-muted-foreground hover:text-primary transition-colors duration-200 text-sm">
                  Categories
                </Link>
              </li>
              <li>
                <Link href="/submit" className="text-muted-foreground hover:text-primary transition-colors duration-200 text-sm">
                  Submit Tool
                </Link>
              </li>
              <li>
                <Link href="/about" className="text-muted-foreground hover:text-primary transition-colors duration-200 text-sm">
                  About Us
                </Link>
              </li>
            </ul>
          </div>

          {/* Legal & Contact */}
          <div>
            <h3 className="text-sm font-semibold text-foreground mb-4 uppercase tracking-wider">Legal</h3>
            <ul className="space-y-3 mb-6">
              <li>
                <Link href="/privacy" className="text-muted-foreground hover:text-primary transition-colors duration-200 text-sm">
                  Privacy Policy
                </Link>
              </li>
              <li>
                <Link href="/terms" className="text-muted-foreground hover:text-primary transition-colors duration-200 text-sm">
                  Terms of Service
                </Link>
              </li>
              <li>
                <Link href="/contact" className="text-muted-foreground hover:text-primary transition-colors duration-200 text-sm">
                  Contact Us
                </Link>
              </li>
              {features.enableCookieConsent && (
                <li>
                  <CookieSettingsLink
                    variant="link"
                    size="sm"
                    className="text-muted-foreground hover:text-primary transition-colors duration-200 text-sm p-0 h-auto"
                  >
                    Cookie Settings
                  </CookieSettingsLink>
                </li>
              )}
            </ul>

            {/* Social Links */}
            <div className="flex space-x-4">
              <a
                href="https://twitter.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary hover:scale-110 transition-all duration-200 p-2 rounded-lg hover:bg-muted/50"
                aria-label="Twitter"
              >
                <Twitter size={18} />
              </a>
              <a
                href="https://github.com"
                target="_blank"
                rel="noopener noreferrer"
                className="text-muted-foreground hover:text-primary hover:scale-110 transition-all duration-200 p-2 rounded-lg hover:bg-muted/50"
                aria-label="GitHub"
              >
                <Github size={18} />
              </a>
              <a
                href="mailto:<EMAIL>"
                className="text-muted-foreground hover:text-primary hover:scale-110 transition-all duration-200 p-2 rounded-lg hover:bg-muted/50"
                aria-label="Email"
              >
                <Mail size={18} />
              </a>
            </div>
          </div>
        </div>

        {/* Bottom Bar */}
        <div className="mt-12 pt-8 border-t border-border">
          <div className="flex flex-col md:flex-row justify-between items-center gap-4">
            <div className="flex flex-col md:flex-row items-center gap-4">
              <p className="text-muted-foreground text-sm">
                &copy; {currentYear} AI Any Tool. All rights reserved.
              </p>
              {/* Modern Cookie Notice */}
              <p className="text-muted-foreground text-xs">
                🍪 We use cookies to enhance your experience.{' '}
                <Link href="/privacy" className="text-primary hover:underline font-medium">
                  Learn more
                </Link>
              </p>
            </div>
            <p className="text-muted-foreground text-xs">
              Made with ❤️ for the AI community
            </p>
          </div>
        </div>
      </div>
    </footer>
  )
}
