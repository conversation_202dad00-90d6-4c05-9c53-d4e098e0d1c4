import { <PERSON><PERSON><PERSON> } from 'next'
import { notFound } from 'next/navigation'
import { Suspense } from 'react'
import { createServerClient } from '@/lib/supabase/server'
import { Button } from '@/components/ui/button'
import { Separator } from '@/components/ui/separator'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, TabsTrigger } from '@/components/ui/tabs'
import { ChevronLeft, ExternalLink, Star, User, Calendar, Tag, Clock } from 'lucide-react'
import Link from 'next/link'
import Image from 'next/image'
import MotionWrapper from '@/components/ui/MotionWrapper'

interface Tool {
  id: number
  company_name: string
  short_description?: string
  full_description?: string
  logo_url?: string
  featured_image_url?: string
  primary_task?: string
  pricing?: string
  slug?: string
  visit_website_url?: string
  detail_url?: string
  pros?: string[]
  cons?: string[]
  faqs?: Record<string, string>
  applicable_tasks?: string[]
  created_at?: string
  updated_at?: string
}

interface Review {
  id: string
  rating: number
  comment: string | null
  created_at: string
  user_id: string
  user_email?: string
}

async function getTool(slug: string): Promise<Tool | null> {
  const supabase = createServerClient()

  try {
    console.log('🔍 Searching for tool with slug:', slug)

    // Try different search strategies
    let tool = null

    // 1. Try exact slug match
    const { data: slugData, error: slugError } = await supabase
      .from('tools')
      .select('*')
      .eq('slug', slug)
      .maybeSingle()

    if (slugError) {
      console.error('Error fetching by slug:', slugError)
    } else if (slugData) {
      tool = slugData
      console.log('✅ Found tool by slug:', tool.company_name)
    }

    // 2. If no result, try to extract ID from slug and search by ID
    if (!tool) {
      const idMatch = slug.match(/^(\d+)/)
      if (idMatch) {
        const toolId = parseInt(idMatch[1])
        console.log('🏢 Trying to search by ID:', toolId)

        const { data: idData, error: idError } = await supabase
          .from('tools')
          .select('*')
          .eq('id', toolId)
          .maybeSingle()

        if (idError) {
          console.error('Error fetching by ID:', idError)
        } else if (idData) {
          tool = idData
          console.log('✅ Found tool by ID:', tool.company_name)
        }
      }
    }

    // 3. If still no result, try searching by company name (case insensitive)
    if (!tool) {
      const searchTerm = slug.replace(/-/g, ' ')
      console.log('🏢 Trying to search by company name:', searchTerm)

      const { data: nameData, error: nameError } = await supabase
        .from('tools')
        .select('*')
        .ilike('company_name', `%${searchTerm}%`)
        .limit(1)
        .maybeSingle()

      if (nameError) {
        console.error('Error searching by name:', nameError)
      } else if (nameData) {
        tool = nameData
        console.log('✅ Found tool by company name:', tool.company_name)
      }
    }

    if (!tool) {
      console.error('❌ Tool not found with slug:', slug)
      return null
    }

    console.log('✅ Tool found:', tool.company_name, '(search took', Date.now(), 'ms)')

    // Track view
    try {
      await supabase.rpc('increment_tool_view_count', { tool_id: tool.id })
      console.log('📊 Analytics: View tracked for', tool.company_name, {
        tool_id: tool.id,
        tool_name: tool.company_name,
        page_type: 'tool_detail',
        timestamp: new Date().toISOString(),
        user_agent: 'server',
        referrer: null
      })
    } catch (error) {
      console.error('Error tracking view:', error)
    }

    return tool
  } catch (error) {
    console.error('Error in getTool:', error)
    return null
  }
}

async function getReviews(toolId: number): Promise<Review[]> {
  const supabase = createServerClient()

  try {
    const { data: reviewsData, error: reviewsError } = await supabase
      .from('reviews')
      .select(`
        id,
        rating,
        comment,
        created_at,
        user_id
      `)
      .eq('tool_id', toolId)
      .order('created_at', { ascending: false })

    if (reviewsError) throw reviewsError

    const formattedReviews = reviewsData.map(review => ({
      id: review.id,
      rating: review.rating,
      comment: review.comment,
      created_at: new Date(review.created_at).toLocaleDateString(),
      user_id: review.user_id,
      user_email: `User-${review.user_id.substring(0, 8)}`
    }))

    return formattedReviews
  } catch (error) {
    console.error('Error fetching reviews:', error)
    return []
  }
}

export async function generateMetadata({ params }: { params: { slug: string } }): Promise<Metadata> {
  const tool = await getTool(params.slug)

  if (!tool) {
    return {
      title: 'Tool Not Found',
      description: 'The requested tool could not be found.',
    }
  }

  return {
    title: `${tool.company_name} - AI Tool Details`,
    description: tool.short_description || `Learn about ${tool.company_name}, its features, pricing, and user reviews.`,
    keywords: `${tool.company_name}, AI tool, ${tool.primary_task || ''}, ${tool.pricing || ''}, software`,
    openGraph: {
      title: `${tool.company_name} - AI Tool Details`,
      description: tool.short_description || `Learn about ${tool.company_name}`,
      images: tool.featured_image_url || tool.logo_url ? [tool.featured_image_url || tool.logo_url] : [],
      type: 'website',
    },
    twitter: {
      card: 'summary_large_image',
      title: `${tool.company_name} - AI Tool Details`,
      description: tool.short_description || `Learn about ${tool.company_name}`,
      images: tool.featured_image_url || tool.logo_url ? [tool.featured_image_url || tool.logo_url] : [],
    },
  }
}

export default async function ToolPage({ params }: { params: { slug: string } }) {
  const { slug } = params
  const tool = await getTool(slug)

  if (!tool) {
    notFound()
  }

  const reviews = await getReviews(tool.id)
  const averageRating = reviews.length > 0
    ? Number((reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length).toFixed(1))
    : null

  const RatingStars = ({ rating }: { rating: number }) => {
    return (
      <div className="flex">
        {[1, 2, 3, 4, 5].map((star) => (
          <Star
            key={star}
            size={16}
            className={`${
              star <= rating
                ? "fill-yellow-400 text-yellow-400"
                : "text-muted-foreground/30"
            }`}
          />
        ))}
      </div>
    )
  }

  const handleVisitWebsite = () => {
    const url = tool.visit_website_url || tool.detail_url || '#'
    window.open(url, '_blank', 'noopener,noreferrer')
  }

  return (
    <div className="flex min-h-screen flex-col">
      <main className="flex-1 py-24">
        <div className="container-wide">
          <MotionWrapper animation="fadeIn">
            <Button
              variant="ghost"
              className="mb-6"
              asChild
            >
              <Link href="/tools">
                <ChevronLeft className="mr-2 h-4 w-4" />
                Back to Tools
              </Link>
            </Button>

            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2 space-y-8">
                {/* Tool Header */}
                <div className="flex items-center gap-4">
                  <div className="h-16 w-16 overflow-hidden rounded-lg border bg-background flex-shrink-0">
                    <img
                      src={tool.logo_url || "https://via.placeholder.com/120?text=AI+Tool"}
                      alt={`${tool.company_name} logo`}
                      className="h-full w-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).src = "https://via.placeholder.com/120?text=AI+Tool"
                      }}
                    />
                  </div>

                  <div>
                    <h1 className="text-2xl md:text-3xl font-bold">{tool.company_name}</h1>
                    <p className="text-muted-foreground">{tool.short_description}</p>
                  </div>
                </div>

                {/* Featured Image */}
                {tool.featured_image_url && (
                  <div className="rounded-xl overflow-hidden aspect-video bg-secondary/30">
                    <img
                      src={tool.featured_image_url}
                      alt={`${tool.company_name} featured image`}
                      className="w-full h-full object-cover"
                      onError={(e) => {
                        (e.target as HTMLImageElement).style.display = 'none'
                      }}
                    />
                  </div>
                )}

                {/* Tabs */}
                <Tabs defaultValue="overview" className="w-full">
                  <TabsList className="grid w-full grid-cols-3">
                    <TabsTrigger value="overview">Overview</TabsTrigger>
                    <TabsTrigger value="details">Details</TabsTrigger>
                    <TabsTrigger value="reviews">Reviews</TabsTrigger>
                  </TabsList>

                  <TabsContent value="overview" className="mt-6">
                    <div className="space-y-6">
                      <div>
                        <h2 className="text-xl font-semibold mb-3">About</h2>
                        <p className="text-muted-foreground whitespace-pre-line">
                          {tool.full_description || tool.short_description || "No description available."}
                        </p>
                      </div>

                      {/* Pros & Cons */}
                      {(tool.pros && tool.pros.length > 0) || (tool.cons && tool.cons.length > 0) ? (
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                          {tool.pros && tool.pros.length > 0 && (
                            <div className="space-y-2">
                              <h3 className="font-medium">Pros</h3>
                              <ul className="space-y-1">
                                {tool.pros.map((pro: any, i: number) => (
                                  <li key={i} className="flex items-start">
                                    <span className="text-green-500 mr-2">✓</span>
                                    <span>{typeof pro === 'string' ? pro : (pro?.name || pro?.value || String(pro))}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}

                          {tool.cons && tool.cons.length > 0 && (
                            <div className="space-y-2">
                              <h3 className="font-medium">Cons</h3>
                              <ul className="space-y-1">
                                {tool.cons.map((con: any, i: number) => (
                                  <li key={i} className="flex items-start">
                                    <span className="text-red-500 mr-2">✗</span>
                                    <span>{typeof con === 'string' ? con : (con?.name || con?.value || String(con))}</span>
                                  </li>
                                ))}
                              </ul>
                            </div>
                          )}
                        </div>
                      ) : null}

                      {/* FAQs */}
                      {tool.faqs && Object.keys(tool.faqs).length > 0 && (
                        <div>
                          <h2 className="text-xl font-semibold mb-3">Frequently Asked Questions</h2>
                          <div className="space-y-4">
                            {Array.isArray(tool.faqs) ?
                              tool.faqs.map((faq: any, i: number) => (
                                <div key={i} className="border rounded-lg p-4">
                                  <h3 className="font-medium mb-2">{faq.question}</h3>
                                  <p className="text-muted-foreground">{faq.answer}</p>
                                </div>
                              )) :
                              Object.entries(tool.faqs).map(([key, value]: [string, any], i: number) => (
                                <div key={i} className="border rounded-lg p-4">
                                  <h3 className="font-medium mb-2">{key}</h3>
                                  <p className="text-muted-foreground">{value}</p>
                                </div>
                              ))
                            }
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="details" className="mt-6">
                    <div className="space-y-6">
                      <div>
                        <h2 className="text-xl font-semibold mb-3">Technical Details</h2>
                        <div className="grid grid-cols-1 md:grid-cols-2 gap-x-8 gap-y-4">
                          {tool.primary_task && (
                            <div className="flex items-center gap-2">
                              <Tag className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="text-sm font-medium">Primary Task</p>
                                <p className="text-sm text-muted-foreground">{tool.primary_task}</p>
                              </div>
                            </div>
                          )}

                          {tool.pricing && (
                            <div className="flex items-center gap-2">
                              <Tag className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="text-sm font-medium">Pricing Model</p>
                                <p className="text-sm text-muted-foreground">{tool.pricing}</p>
                              </div>
                            </div>
                          )}

                          {tool.created_at && (
                            <div className="flex items-center gap-2">
                              <Calendar className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="text-sm font-medium">Date Added</p>
                                <p className="text-sm text-muted-foreground">
                                  {new Date(tool.created_at).toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                          )}

                          {tool.updated_at && tool.updated_at !== tool.created_at && (
                            <div className="flex items-center gap-2">
                              <Clock className="h-4 w-4 text-muted-foreground" />
                              <div>
                                <p className="text-sm font-medium">Last Updated</p>
                                <p className="text-sm text-muted-foreground">
                                  {new Date(tool.updated_at).toLocaleDateString()}
                                </p>
                              </div>
                            </div>
                          )}
                        </div>
                      </div>

                      {tool.applicable_tasks && tool.applicable_tasks.length > 0 && (
                        <div>
                          <h3 className="font-medium mb-2">Applicable Tasks</h3>
                          <div className="flex flex-wrap gap-2">
                            {tool.applicable_tasks.map((task: string, i: number) => (
                              <span
                                key={i}
                                className="bg-secondary text-foreground text-sm rounded-full px-3 py-1"
                              >
                                {task}
                              </span>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>

                  <TabsContent value="reviews" className="mt-6">
                    <div className="space-y-6">
                      {reviews.length === 0 ? (
                        <div className="text-center py-8">
                          <User className="h-12 w-12 mx-auto mb-4 text-muted-foreground opacity-50" />
                          <h3 className="text-lg font-medium mb-2">No reviews yet</h3>
                          <p className="text-muted-foreground mb-6">
                            Be the first to review this tool and help others make better decisions.
                          </p>
                        </div>
                      ) : (
                        <div className="space-y-6">
                          <div className="flex justify-between items-center">
                            <h3 className="text-lg font-medium">
                              {reviews.length} {reviews.length === 1 ? 'Review' : 'Reviews'}
                            </h3>
                          </div>

                          <div className="divide-y">
                            {reviews.map(review => (
                              <div key={review.id} className="py-4">
                                <div className="flex justify-between">
                                  <div className="flex items-center gap-2">
                                    <RatingStars rating={review.rating} />
                                    <span className="text-sm font-medium">{review.user_email}</span>
                                    <span className="text-xs text-muted-foreground">
                                      {review.created_at}
                                    </span>
                                  </div>
                                </div>

                                {review.comment && (
                                  <p className="mt-2 text-muted-foreground">
                                    {review.comment}
                                  </p>
                                )}
                              </div>
                            ))}
                          </div>
                        </div>
                      )}
                    </div>
                  </TabsContent>
                </Tabs>
              </div>

              {/* Sidebar */}
              <div className="space-y-6">
                <div className="border rounded-xl p-6 sticky top-24">
                  <div className="flex items-center gap-2 mb-4">
                    {averageRating ? (
                      <>
                        <div className="flex">
                          <RatingStars rating={Math.round(averageRating)} />
                        </div>
                        <span className="font-medium">{averageRating}</span>
                        <span className="text-muted-foreground">
                          ({reviews.length} {reviews.length === 1 ? 'review' : 'reviews'})
                        </span>
                      </>
                    ) : (
                      <>
                        <div className="flex">
                          {[...Array(5)].map((_, i) => (
                            <Star
                              key={i}
                              size={18}
                              className="text-muted-foreground/30"
                            />
                          ))}
                        </div>
                        <span className="text-muted-foreground">No reviews yet</span>
                      </>
                    )}
                  </div>

                  <div className="space-y-4">
                    <Button
                      className="w-full"
                      size="lg"
                      onClick={handleVisitWebsite}
                    >
                      Visit Website
                      <ExternalLink className="ml-2 h-4 w-4" />
                    </Button>

                    <Separator />

                    <div>
                      <h3 className="font-medium mb-2">Quick Info</h3>
                      <ul className="space-y-2 text-sm">
                        {tool.primary_task && (
                          <li className="flex justify-between">
                            <span className="text-muted-foreground">Category</span>
                            <span>{tool.primary_task}</span>
                          </li>
                        )}

                        {tool.pricing && (
                          <li className="flex justify-between">
                            <span className="text-muted-foreground">Pricing</span>
                            <span>{tool.pricing}</span>
                          </li>
                        )}

                        <li className="flex justify-between">
                          <span className="text-muted-foreground">Reviews</span>
                          <span>{reviews.length}</span>
                        </li>
                      </ul>
                    </div>
                  </div>
                </div>
              </div>
            </div>
          </MotionWrapper>
        </div>
      </main>
    </div>
  )
}
