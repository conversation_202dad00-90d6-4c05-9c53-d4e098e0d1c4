'use client'

import { useState, useEffect } from 'react'
import {
  Dialog,
  DialogContent,
  DialogDescription,
  DialogHeader,
  DialogTitle,
} from '@/components/ui/dialog'
import { Button } from '@/components/ui/button'
import { Switch } from '@/components/ui/switch'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Separator } from '@/components/ui/separator'
import { ScrollArea } from '@/components/ui/scroll-area'
import { 
  Shield, 
  BarChart3, 
  Target, 
  Settings, 
  Cookie,
  Info,
  CheckCircle,
  AlertCircle
} from 'lucide-react'
import {
  COOKIE_CATEGORIES,
  CookiePreferences,
  CookieCategory,
  saveCookiePreferences,
  getCookiePreferences,
  resetCookiePreferences,
  forceEnableAnalytics
} from '@/lib/cookie-consent'
import { useCookieConsent } from '@/hooks/use-cookie-consent'

interface CookieSettingsModalProps {
  open: boolean
  onOpenChange: (open: boolean) => void
  onClose?: () => void
}

const categoryIcons = {
  essential: Shield,
  analytics: BarChart3,
  marketing: Target,
  preferences: Settings
} as const

const categoryColors = {
  essential: 'text-green-600 bg-green-100',
  analytics: 'text-blue-600 bg-blue-100',
  marketing: 'text-purple-600 bg-purple-100',
  preferences: 'text-orange-600 bg-orange-100'
} as const

export function CookieSettingsModal({ 
  open, 
  onOpenChange, 
  onClose 
}: CookieSettingsModalProps) {
  const [preferences, setPreferences] = useState<CookiePreferences>({
    essential: true,
    analytics: true, // All cookies enabled by default
    marketing: true, // Marketing enabled by default
    preferences: true, // Preferences enabled by default
    timestamp: Date.now(),
    version: '1.0'
  })
  const [isLoading, setIsLoading] = useState(false)
  const [hasChanges, setHasChanges] = useState(false)

  useEffect(() => {
    if (open) {
      // Force enable analytics for existing users first
      forceEnableAnalytics()

      // Load current preferences when modal opens
      const currentPreferences = getCookiePreferences()
      if (currentPreferences) {
        setPreferences(currentPreferences)
      } else {
        // If no preferences saved, use defaults with ALL cookies enabled
        setPreferences({
          essential: true,
          analytics: true, // All cookies enabled by default
          marketing: true, // Marketing enabled by default
          preferences: true, // Preferences enabled by default
          timestamp: Date.now(),
          version: '1.0'
        })
      }
      setHasChanges(false)
    }
  }, [open])

  const handlePreferenceChange = (category: CookieCategory, enabled: boolean) => {
    if (category === 'essential') return // Essential cookies cannot be disabled

    setPreferences(prev => ({
      ...prev,
      [category]: enabled
    }))
    setHasChanges(true)
  }

  const handleSavePreferences = async () => {
    setIsLoading(true)
    try {
      saveCookiePreferences(preferences)
      setHasChanges(false)
      onOpenChange(false)
      onClose?.()
    } catch (error) {
      console.error('Error saving cookie preferences:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAcceptAll = async () => {
    setIsLoading(true)
    try {
      const allAccepted: CookiePreferences = {
        essential: true,
        analytics: true,
        marketing: true,
        preferences: true,
        timestamp: Date.now(),
        version: '1.0'
      }
      saveCookiePreferences(allAccepted)
      setPreferences(allAccepted)
      setHasChanges(false)
      onOpenChange(false)
      onClose?.()
    } catch (error) {
      console.error('Error accepting all cookies:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleRejectAll = async () => {
    setIsLoading(true)
    try {
      const essentialOnly: CookiePreferences = {
        essential: true,
        analytics: false,
        marketing: false,
        preferences: false,
        timestamp: Date.now(),
        version: '1.0'
      }
      saveCookiePreferences(essentialOnly)
      setPreferences(essentialOnly)
      setHasChanges(false)
      onOpenChange(false)
      onClose?.()
    } catch (error) {
      console.error('Error rejecting cookies:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleResetAll = () => {
    resetCookiePreferences()
    setPreferences({
      essential: true,
      analytics: false,
      marketing: false,
      preferences: false,
      timestamp: Date.now(),
      version: '1.0'
    })
    setHasChanges(true)
  }

  return (
    <Dialog open={open} onOpenChange={onOpenChange}>
      <DialogContent className="max-w-lg max-h-[85vh] p-0">
        <DialogHeader className="p-4 pb-0">
          <div className="flex items-center gap-2">
            <div className="p-1.5 bg-primary/10 rounded-md">
              <Cookie className="h-5 w-5 text-primary" />
            </div>
            <div>
              <DialogTitle className="text-lg">Cookie Settings</DialogTitle>
              <DialogDescription className="text-sm">
                Manage your privacy preferences
              </DialogDescription>
            </div>
          </div>
        </DialogHeader>

        <ScrollArea className="max-h-[55vh] px-4">
          <div className="space-y-4 py-3">
            {/* Overview */}
            <div className="space-y-2">
              <div className="flex items-center gap-2">
                <Info className="h-3.5 w-3.5 text-muted-foreground" />
                <p className="text-sm text-muted-foreground">
                  Customize your cookie preferences below.
                </p>
              </div>

              <div className="flex items-center gap-3 text-xs text-muted-foreground">
                <div className="flex items-center gap-1">
                  <CheckCircle className="h-3 w-3 text-green-600" />
                  <span>Required</span>
                </div>
                <div className="flex items-center gap-1">
                  <AlertCircle className="h-3 w-3 text-blue-600" />
                  <span>Optional</span>
                </div>
              </div>
            </div>

            <Separator />

            {/* Cookie Categories */}
            <div className="space-y-3">
              {Object.entries(COOKIE_CATEGORIES).map(([key, category]) => {
                const categoryKey = key as CookieCategory
                const Icon = categoryIcons[categoryKey]
                const isEnabled = preferences[categoryKey]
                const isRequired = category.required

                return (
                  <Card key={categoryKey} className="relative">
                    <CardHeader className="pb-2 pt-3">
                      <div className="flex items-center justify-between">
                        <div className="flex items-center gap-2">
                          <div className={`p-1.5 rounded-md ${categoryColors[categoryKey]}`}>
                            <Icon className="h-4 w-4" />
                          </div>
                          <div>
                            <CardTitle className="text-sm flex items-center gap-2">
                              {category.name}
                              {isRequired && (
                                <Badge variant="outline" className="text-xs">
                                  Required
                                </Badge>
                              )}
                            </CardTitle>
                            <CardDescription className="text-xs mt-0.5">
                              {category.description}
                            </CardDescription>
                          </div>
                        </div>
                        <Switch
                          checked={isEnabled}
                          onCheckedChange={(checked) => handlePreferenceChange(categoryKey, checked)}
                          disabled={isRequired || isLoading}
                        />
                      </div>
                    </CardHeader>
                    <CardContent className="pt-0 pb-3">
                      <div className="space-y-1">
                        <p className="text-xs font-medium text-muted-foreground">Examples:</p>
                        <div className="flex flex-wrap gap-1">
                          {category.examples.slice(0, 3).map((example, index) => (
                            <Badge key={index} variant="secondary" className="text-xs">
                              {example}
                            </Badge>
                          ))}
                          {category.examples.length > 3 && (
                            <Badge variant="secondary" className="text-xs">
                              +{category.examples.length - 3} more
                            </Badge>
                          )}
                        </div>
                      </div>
                    </CardContent>
                  </Card>
                )
              })}
            </div>

            <Separator />

            {/* Additional Information */}
            <div className="space-y-2">
              <h4 className="font-medium text-sm">Additional Information</h4>
              <div className="text-xs text-muted-foreground space-y-1">
                <p>
                  • Change settings anytime via footer link or{' '}
                  <a href="/privacy" className="text-primary hover:underline">
                    Privacy Policy
                  </a>
                </p>
                <p>
                  • Disabling cookies may affect website functionality
                </p>
              </div>
            </div>
          </div>
        </ScrollArea>

        {/* Footer Actions */}
        <div className="p-4 pt-0 space-y-2">
          <div className="flex flex-col sm:flex-row gap-2">
            <Button
              onClick={handleAcceptAll}
              disabled={isLoading}
              className="flex-1 h-9"
            >
              Accept All
            </Button>
            <Button
              variant="outline"
              onClick={handleRejectAll}
              disabled={isLoading}
              className="flex-1 h-9"
            >
              Essential Only
            </Button>
            <Button
              variant="outline"
              onClick={handleSavePreferences}
              disabled={isLoading || !hasChanges}
              className="flex-1 h-9"
            >
              Save Changes
            </Button>
          </div>

          <div className="flex justify-center">
            <Button
              variant="ghost"
              size="sm"
              onClick={handleResetAll}
              disabled={isLoading}
              className="text-xs text-muted-foreground h-8"
            >
              Reset to Defaults
            </Button>
          </div>
        </div>
      </DialogContent>
    </Dialog>
  )
}

export default CookieSettingsModal
