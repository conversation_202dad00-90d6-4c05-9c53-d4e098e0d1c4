"use client"

import { motion, AnimatePresence, Variants } from 'framer-motion'
import { ReactNode } from 'react'
import { cn } from '@/lib/utils'
import { useReducedMotion } from '@/hooks/use-progressive-enhancement'

interface AnimationProps {
  children: ReactNode
  className?: string
  delay?: number
  duration?: number
  disabled?: boolean
}

// Animation variants
const fadeInVariants: Variants = {
  hidden: { opacity: 0 },
  visible: { opacity: 1 }
}

const slideUpVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
}

const slideInFromLeftVariants: Variants = {
  hidden: { opacity: 0, x: -20 },
  visible: { opacity: 1, x: 0 }
}

const slideInFromRightVariants: Variants = {
  hidden: { opacity: 0, x: 20 },
  visible: { opacity: 1, x: 0 }
}

const scaleInVariants: Variants = {
  hidden: { opacity: 0, scale: 0.8 },
  visible: { opacity: 1, scale: 1 }
}

const bounceInVariants: Variants = {
  hidden: { opacity: 0, scale: 0.3 },
  visible: { 
    opacity: 1, 
    scale: 1,
    transition: {
      type: "spring",
      damping: 15,
      stiffness: 300
    }
  }
}

const rotateInVariants: Variants = {
  hidden: { opacity: 0, rotate: -10 },
  visible: { opacity: 1, rotate: 0 }
}

const staggerContainerVariants: Variants = {
  hidden: { opacity: 0 },
  visible: {
    opacity: 1,
    transition: {
      staggerChildren: 0.1
    }
  }
}

const staggerItemVariants: Variants = {
  hidden: { opacity: 0, y: 20 },
  visible: { opacity: 1, y: 0 }
}

// Base animation component
const BaseAnimation = ({ 
  children, 
  variants, 
  className, 
  delay = 0, 
  duration = 0.5,
  disabled = false,
  ...props 
}: AnimationProps & { variants: Variants } & any) => {
  const reducedMotion = useReducedMotion()
  
  if (disabled || reducedMotion) {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div
      variants={variants}
      initial="hidden"
      animate="visible"
      transition={{ duration, delay, ease: "easeOut" }}
      className={className}
      {...props}
    >
      {children}
    </motion.div>
  )
}

// Specific animation components
export const FadeIn = (props: AnimationProps) => (
  <BaseAnimation variants={fadeInVariants} {...props} />
)

export const SlideUp = (props: AnimationProps) => (
  <BaseAnimation variants={slideUpVariants} {...props} />
)

export const SlideInFromLeft = (props: AnimationProps) => (
  <BaseAnimation variants={slideInFromLeftVariants} {...props} />
)

export const SlideInFromRight = (props: AnimationProps) => (
  <BaseAnimation variants={slideInFromRightVariants} {...props} />
)

export const ScaleIn = (props: AnimationProps) => (
  <BaseAnimation variants={scaleInVariants} {...props} />
)

export const BounceIn = (props: AnimationProps) => (
  <BaseAnimation variants={bounceInVariants} {...props} />
)

export const RotateIn = (props: AnimationProps) => (
  <BaseAnimation variants={rotateInVariants} {...props} />
)

// Stagger animation for lists
export const StaggerContainer = ({ children, className, delay = 0, disabled = false }: AnimationProps) => {
  const reducedMotion = useReducedMotion()
  
  if (disabled || reducedMotion) {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div
      variants={staggerContainerVariants}
      initial="hidden"
      animate="visible"
      transition={{ delay }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

export const StaggerItem = ({ children, className, disabled = false }: AnimationProps) => {
  const reducedMotion = useReducedMotion()
  
  if (disabled || reducedMotion) {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div
      variants={staggerItemVariants}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// Hover animations
export const HoverScale = ({ 
  children, 
  className, 
  scale = 1.05, 
  disabled = false 
}: AnimationProps & { scale?: number }) => {
  const reducedMotion = useReducedMotion()
  
  if (disabled || reducedMotion) {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div
      whileHover={{ scale }}
      whileTap={{ scale: scale * 0.95 }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

export const HoverLift = ({ 
  children, 
  className, 
  y = -4, 
  disabled = false 
}: AnimationProps & { y?: number }) => {
  const reducedMotion = useReducedMotion()
  
  if (disabled || reducedMotion) {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div
      whileHover={{ y }}
      transition={{ type: "spring", stiffness: 300, damping: 20 }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

export const HoverGlow = ({ 
  children, 
  className, 
  disabled = false 
}: AnimationProps) => {
  const reducedMotion = useReducedMotion()
  
  if (disabled || reducedMotion) {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div
      whileHover={{ 
        boxShadow: "0 0 20px rgba(var(--primary-rgb), 0.3)" 
      }}
      transition={{ duration: 0.2 }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// Loading animations
export const Pulse = ({ 
  children, 
  className, 
  disabled = false 
}: AnimationProps) => {
  const reducedMotion = useReducedMotion()
  
  if (disabled || reducedMotion) {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div
      animate={{ opacity: [1, 0.5, 1] }}
      transition={{ duration: 2, repeat: Infinity, ease: "easeInOut" }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

export const Spin = ({ 
  children, 
  className, 
  disabled = false 
}: AnimationProps) => {
  const reducedMotion = useReducedMotion()
  
  if (disabled || reducedMotion) {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div
      animate={{ rotate: 360 }}
      transition={{ duration: 1, repeat: Infinity, ease: "linear" }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// Page transition animations
export const PageTransition = ({ 
  children, 
  className, 
  disabled = false 
}: AnimationProps) => {
  const reducedMotion = useReducedMotion()
  
  if (disabled || reducedMotion) {
    return <div className={className}>{children}</div>
  }

  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      exit={{ opacity: 0, y: -20 }}
      transition={{ duration: 0.3, ease: "easeInOut" }}
      className={className}
    >
      {children}
    </motion.div>
  )
}

// Conditional animation wrapper
export const ConditionalAnimation = ({ 
  children, 
  condition, 
  animation = "fadeIn",
  className,
  disabled = false 
}: AnimationProps & { 
  condition: boolean
  animation?: "fadeIn" | "slideUp" | "scaleIn" | "bounceIn"
}) => {
  const reducedMotion = useReducedMotion()
  
  if (disabled || reducedMotion) {
    return <div className={className}>{children}</div>
  }

  const variants = {
    fadeIn: fadeInVariants,
    slideUp: slideUpVariants,
    scaleIn: scaleInVariants,
    bounceIn: bounceInVariants
  }

  return (
    <AnimatePresence>
      {condition && (
        <motion.div
          variants={variants[animation]}
          initial="hidden"
          animate="visible"
          exit="hidden"
          transition={{ duration: 0.3, ease: "easeOut" }}
          className={className}
        >
          {children}
        </motion.div>
      )}
    </AnimatePresence>
  )
}

export default {
  FadeIn,
  SlideUp,
  SlideInFromLeft,
  SlideInFromRight,
  ScaleIn,
  BounceIn,
  RotateIn,
  StaggerContainer,
  StaggerItem,
  HoverScale,
  HoverLift,
  HoverGlow,
  Pulse,
  Spin,
  PageTransition,
  ConditionalAnimation
}
