"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { User, LogOut, Settings } from "lucide-react"

// Simple auth state without complex hooks
interface AuthState {
  isLoading: boolean
  isAuthenticated: boolean
  user: any | null
}

export function SimpleAuthSection() {
  const [authState, setAuthState] = useState<AuthState>({
    isLoading: true,
    isAuthenticated: false,
    user: null
  })

  useEffect(() => {
    // Simulate auth check - replace with actual Supabase check later
    const checkAuth = async () => {
      try {
        // For now, just set to not authenticated to prevent flickering
        // This will be replaced with actual Supabase auth check
        setTimeout(() => {
          setAuthState({
            isLoading: false,
            isAuthenticated: false,
            user: null
          })
        }, 100) // Small delay to prevent flash
      } catch (error) {
        setAuthState({
          isLoading: false,
          isAuthenticated: false,
          user: null
        })
      }
    }

    checkAuth()
  }, [])

  // Always show consistent loading state first
  if (authState.isLoading) {
    return (
      <div className="w-20 h-9 bg-muted/50 animate-pulse rounded-full"></div>
    )
  }

  // If authenticated (when we implement it)
  if (authState.isAuthenticated && authState.user) {
    return (
      <DropdownMenu>
        <DropdownMenuTrigger asChild>
          <Button 
            variant="ghost" 
            size="sm" 
            className="rounded-full h-9 w-9 p-0 glass-dark theme-transition min-h-[44px] min-w-[44px] touch-manipulation"
          >
            <User className="h-4 w-4" />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align="end" className="w-56">
          <DropdownMenuItem asChild>
            <Link href="/dashboard" className="flex items-center">
              <Settings className="mr-2 h-4 w-4" />
              Dashboard
            </Link>
          </DropdownMenuItem>
          <DropdownMenuSeparator />
          <DropdownMenuItem className="text-red-600">
            <LogOut className="mr-2 h-4 w-4" />
            Sign Out
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    )
  }

  // Not authenticated - show sign in button
  return (
    <Button 
      asChild 
      size="sm" 
      className="rounded-full bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-1.5 h-9 shadow-sm hover:shadow-lg theme-transition hover-button text-sm min-h-[44px] touch-manipulation"
    >
      <Link href="/auth">Sign In</Link>
    </Button>
  )
}

export function SimpleMobileAuthSection({ onMenuClose }: { onMenuClose: () => void }) {
  const [authState, setAuthState] = useState<AuthState>({
    isLoading: true,
    isAuthenticated: false,
    user: null
  })

  useEffect(() => {
    const checkAuth = async () => {
      try {
        setTimeout(() => {
          setAuthState({
            isLoading: false,
            isAuthenticated: false,
            user: null
          })
        }, 100)
      } catch (error) {
        setAuthState({
          isLoading: false,
          isAuthenticated: false,
          user: null
        })
      }
    }

    checkAuth()
  }, [])

  if (authState.isLoading) {
    return (
      <div className="w-full h-12 bg-muted/50 animate-pulse rounded-xl"></div>
    )
  }

  if (authState.isAuthenticated && authState.user) {
    return (
      <div className="space-y-2">
        <Link
          href="/dashboard"
          className="flex items-center px-4 py-4 rounded-xl text-base font-medium text-foreground hover:text-primary hover:bg-muted/70 transition-all duration-200 min-h-[48px] touch-manipulation border border-transparent hover:border-primary/20"
          onClick={onMenuClose}
        >
          <Settings className="w-5 h-5 mr-3" />
          Dashboard
        </Link>
        <button
          onClick={onMenuClose}
          className="flex items-center w-full px-4 py-4 rounded-xl text-base font-medium text-red-600 hover:bg-red-50 dark:hover:bg-red-900/30 transition-all duration-200 min-h-[48px] touch-manipulation border border-transparent hover:border-red-200 dark:hover:border-red-800"
        >
          <LogOut className="w-5 h-5 mr-3" />
          Sign Out
        </button>
      </div>
    )
  }

  return (
    <div className="space-y-2">
      <Button 
        asChild 
        className="w-full rounded-xl bg-primary hover:bg-primary/90 shadow-md hover:shadow-lg py-4 h-12 text-base text-primary-foreground font-medium touch-manipulation border border-primary/20"
      >
        <Link href="/auth" onClick={onMenuClose}>
          Sign In
        </Link>
      </Button>
    </div>
  )
}
