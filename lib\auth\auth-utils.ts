'use client'

/**
 * Authentication utility functions
 * Helper functions for better authentication experience
 */

/**
 * Validate email format
 */
export function isValidEmail(email: string): boolean {
  const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
  return emailRegex.test(email)
}

/**
 * Validate password strength
 */
export function validatePassword(password: string): { isValid: boolean; message?: string } {
  if (password.length < 6) {
    return { isValid: false, message: 'Password must be at least 6 characters long' }
  }
  
  if (password.length > 128) {
    return { isValid: false, message: 'Password must be less than 128 characters' }
  }
  
  // Check for at least one letter and one number (optional but recommended)
  const hasLetter = /[a-zA-Z]/.test(password)
  const hasNumber = /\d/.test(password)
  
  if (!hasLetter || !hasNumber) {
    return { 
      isValid: true, // Still valid but with warning
      message: 'For better security, use a mix of letters and numbers' 
    }
  }
  
  return { isValid: true }
}

/**
 * Normalize error messages for better user experience
 */
export function normalizeAuthError(error: string): string {
  const errorLower = error.toLowerCase()
  
  // Handle duplicate user errors
  if (errorLower.includes('user already registered') || 
      errorLower.includes('already been registered') ||
      errorLower.includes('email address is already registered') ||
      errorLower.includes('email already exists')) {
    return 'This email is already registered. Please sign in instead or use "Forgot Password" if you forgot your password.'
  }
  
  // Handle email confirmation errors
  if (errorLower.includes('email not confirmed')) {
    return 'Please confirm your email address first. Check your inbox for the confirmation link.'
  }
  
  // Handle invalid credentials
  if (errorLower.includes('invalid login credentials')) {
    return 'Invalid email or password. Please check your credentials and try again.'
  }
  
  // Handle password errors
  if (errorLower.includes('password should be at least')) {
    return 'Password must be at least 6 characters long.'
  }
  
  // Handle invalid email
  if (errorLower.includes('invalid email')) {
    return 'Please enter a valid email address.'
  }
  
  // Handle signup disabled
  if (errorLower.includes('signup is disabled')) {
    return 'New user registration is currently disabled. Please contact support.'
  }
  
  // Handle rate limiting
  if (errorLower.includes('too many requests')) {
    return 'Too many attempts. Please wait a moment before trying again.'
  }
  
  // Return original error if no specific handling
  return error
}

/**
 * Check if error indicates user already exists
 */
export function isUserExistsError(error: string): boolean {
  const errorLower = error.toLowerCase()
  return errorLower.includes('already registered') || 
         errorLower.includes('already been registered') ||
         errorLower.includes('email address is already registered') ||
         errorLower.includes('email already exists')
}

/**
 * Generate helpful suggestions based on error type
 */
export function getErrorSuggestion(error: string): string | null {
  if (isUserExistsError(error)) {
    return 'Try signing in instead, or use "Forgot Password" if you need to reset your password.'
  }
  
  if (error.toLowerCase().includes('email not confirmed')) {
    return 'Check your email inbox and spam folder for the confirmation link.'
  }
  
  if (error.toLowerCase().includes('invalid login credentials')) {
    return 'Double-check your email and password, or try "Forgot Password" if needed.'
  }
  
  return null
}
