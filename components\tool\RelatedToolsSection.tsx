"use client"

import Link from "next/link"
import Image from "next/image"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, CardTitle } from "@/components/ui/card"
import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { <PERSON>R<PERSON>, Star, CheckCircle } from "lucide-react"
import MotionWrapper from "@/components/ui/MotionWrapper"

interface RelatedTool {
  id: number
  company_name: string
  short_description?: string
  logo_url?: string
  primary_task?: string
  pricing?: string
  slug?: string
  is_featured?: boolean
  is_verified?: boolean
}

interface RelatedToolsSectionProps {
  tools: RelatedTool[]
  category?: string
}

export default function RelatedToolsSection({ tools, category }: RelatedToolsSectionProps) {
  if (!tools || tools.length === 0) {
    return (
      <section className="py-16 bg-gradient-to-b from-background via-secondary/10 to-background">
        <div className="container-tight">
          <div className="text-center py-16 px-8 bg-gradient-to-br from-gray-50/50 via-slate-50/30 to-gray-50/50 dark:from-gray-950/20 dark:via-slate-950/10 dark:to-gray-950/20 rounded-3xl border border-border/30 backdrop-blur-sm">
            <div className="w-20 h-20 mx-auto mb-6 bg-gradient-to-br from-primary/10 to-accent/10 rounded-full flex items-center justify-center shadow-lg">
              <span className="text-3xl">🔍</span>
            </div>
            <h3 className="text-xl font-bold text-foreground mb-3">Similar Tools Coming Soon!</h3>
            <p className="text-muted-foreground mb-4 max-w-md mx-auto">
              We're analyzing our database to find the best alternatives and similar tools for you.
            </p>
            <div className="inline-flex items-center gap-2 px-4 py-2 bg-primary/10 text-primary rounded-full text-sm font-medium">
              <span className="w-2 h-2 bg-primary rounded-full animate-pulse"></span>
              Analysis in progress
            </div>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-16 bg-gradient-to-b from-background via-secondary/10 to-background">
      <div className="container-tight">
        <MotionWrapper animation="fadeIn">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-4 bg-gradient-to-r from-primary to-accent bg-clip-text text-transparent">
              🔗 Similar Tools You Might Like
            </h2>
            <p className="text-muted-foreground max-w-2xl mx-auto">
              {category ? `Explore other AI tools in the ${category} space` : "Discover similar AI tools that might interest you"}
            </p>
          </div>
        </MotionWrapper>

        <MotionWrapper animation="fadeIn" delay="delay-200">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6 mb-8">
            {tools.slice(0, 8).map((tool, index) => (
              <MotionWrapper
                key={tool.id}
                animation="fadeIn"
                delay={`delay-${(index + 1) * 100}`}
              >
                <RelatedToolCard tool={tool} />
              </MotionWrapper>
            ))}
          </div>
        </MotionWrapper>

        {tools.length > 8 && category && (
          <MotionWrapper animation="fadeIn" delay="delay-400" className="text-center">
            <Button asChild variant="outline" size="lg" className="group">
              <Link href={`/tools?category=${encodeURIComponent(category)}`}>
                <span>View All {category} Tools</span>
                <ArrowRight className="ml-2 h-4 w-4 group-hover:translate-x-1 transition-transform" />
              </Link>
            </Button>
          </MotionWrapper>
        )}
      </div>
    </section>
  )
}

function RelatedToolCard({ tool }: { tool: RelatedTool }) {
  return (
    <Card className="group h-full bg-background/95 dark:bg-card/95 backdrop-blur-xl rounded-2xl border border-border/60 shadow-xl shadow-primary/5 dark:shadow-primary/10 overflow-hidden hover:shadow-2xl hover:-translate-y-2 transition-all duration-300">
      <CardContent className="p-6 space-y-4">
        <div className="flex items-center gap-3">
          {/* Enhanced Logo */}
          <div className="h-12 w-12 rounded-xl border border-border/40 overflow-hidden flex-shrink-0 bg-background group-hover:border-primary/30 transition-all duration-300">
            {tool.logo_url ? (
              <Image
                src={tool.logo_url}
                alt={tool.company_name}
                width={48}
                height={48}
                className="h-full w-full object-cover group-hover:scale-110 transition-transform duration-300"
              />
            ) : (
              <div className="h-full w-full flex items-center justify-center text-muted-foreground bg-gradient-to-br from-primary/10 to-accent/10 font-semibold text-lg">
                {tool.company_name.charAt(0)}
              </div>
            )}
          </div>

          {/* Title and Badges */}
          <div className="flex-1 min-w-0">
            <div className="flex items-start justify-between gap-2">
              <h3 className="font-semibold text-foreground group-hover:text-primary transition-colors duration-200 truncate">
                {tool.company_name}
              </h3>

              <div className="flex gap-1 flex-shrink-0">
                {tool.is_verified && (
                  <CheckCircle className="h-4 w-4 text-blue-500" />
                )}
                {tool.is_featured && (
                  <Star className="h-4 w-4 text-amber-500 fill-current" />
                )}
              </div>
            </div>

            {tool.primary_task && (
              <p className="text-xs text-muted-foreground truncate mt-0.5">
                {tool.primary_task}
              </p>
            )}
          </div>
        </div>

        {/* Description */}
        {tool.short_description && (
          <p className="text-sm text-muted-foreground line-clamp-2 leading-relaxed">
            {tool.short_description}
          </p>
        )}

        {/* Enhanced Action Section */}
        <div className="flex items-center justify-between pt-2">
          {tool.pricing && (
            <Badge variant="outline" className="text-xs text-green-600 dark:text-green-400">
              {tool.pricing}
            </Badge>
          )}
          <Link
            href={`/Tool/${tool.slug || tool.id}`}
            className="inline-flex items-center gap-1 px-3 py-1.5 bg-primary/10 hover:bg-primary/20 text-primary rounded-lg text-sm font-medium transition-colors duration-200 group-hover:bg-primary group-hover:text-primary-foreground"
          >
            View Tool
            <ArrowRight className="h-3 w-3" />
          </Link>
        </div>
      </CardContent>
    </Card>
  )
}
