"use client"

import { useState } from "react"
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>Trigger } from "@/components/ui/tabs"
import ToolOverview from "@/components/tool-overview"
import ToolReviews from "@/components/tool-reviews"
import ToolFAQs from "@/components/tool-faqs"
import SimilarTools from "@/components/similar-tools"

interface ToolTabsProps {
  tool: any
}

export default function ToolTabs({ tool }: ToolTabsProps) {
  const [activeTab, setActiveTab] = useState("overview")

  return (
    <Tabs defaultValue="overview" value={activeTab} onValueChange={setActiveTab} className="w-full">
      <TabsList className="w-full grid grid-cols-4 mb-6">
        <TabsTrigger value="overview">Overview</TabsTrigger>
        <TabsTrigger value="reviews">Reviews</TabsTrigger>
        <TabsTrigger value="faqs">FAQs</TabsTrigger>
        <TabsTrigger value="similar">Similar Tools</TabsTrigger>
      </TabsList>

      <TabsContent value="overview" className="mt-0">
        <ToolOverview tool={tool} />
      </TabsContent>

      <TabsContent value="reviews" className="mt-0">
        <ToolReviews toolId={tool.id} />
      </TabsContent>

      <TabsContent value="faqs" className="mt-0">
        <ToolFAQs faqs={tool.faqs} />
      </TabsContent>

      <TabsContent value="similar" className="mt-0">
        <SimilarTools currentToolId={tool.id} category={tool.primary_task} />
      </TabsContent>
    </Tabs>
  )
}
