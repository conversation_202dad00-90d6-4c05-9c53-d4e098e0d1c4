"use client"

import { ReactNode, useEffect, useState } from 'react'

/**
 * A wrapper component specifically for safely rendering server components
 * that use next/headers or other App Router-specific features.
 * 
 * This component will:
 * 1. In App Router: Import and render the server component directly
 * 2. In Pages Router: Import and render the client-side alternative
 * 
 * Usage:
 * ```tsx
 * // In a Pages Router page
 * import SafeServerComponent from '@/components/routing/SafeServerComponent'
 * 
 * export default function MyPage() {
 *   return (
 *     <SafeServerComponent
 *       fallback={<div>Loading...</div>}
 *       serverComponent="@/components/MyServerComponent" // Server component path
 *       clientAlternative="@/components/MyClientComponent" // Client alternative path
 *       componentProps={{ myProp: "value" }}
 *     />
 *   )
 * }
 * ```
 */
interface SafeServerComponentProps {
  serverComponent: string
  clientAlternative: string
  componentProps?: Record<string, any>
  fallback: ReactNode
}

export default function SafeServerComponent({
  serverComponent,
  clientAlternative,
  componentProps = {},
  fallback
}: SafeServerComponentProps) {
  const [Component, setComponent] = useState<React.ComponentType<any> | null>(null)
  const [isAppRouter, setIsAppRouter] = useState<boolean | null>(null)

  useEffect(() => {
    // Detect if we're in App Router
    try {
      // If this import succeeds, we're in App Router
      require('next/navigation')
      setIsAppRouter(true)
    } catch (error) {
      // If it fails, we're in Pages Router
      setIsAppRouter(false)
    }
  }, [])

  useEffect(() => {
    if (isAppRouter === null) return

    let isMounted = true
    
    const loadComponent = async () => {
      try {
        // Import the appropriate component based on the router
        const modulePath = isAppRouter ? serverComponent : clientAlternative
        
        // Dynamic import to load the component
        const module = await import(/* @vite-ignore */ modulePath)
        
        if (isMounted) {
          setComponent(() => module.default)
        }
      } catch (error) {
        console.error('Error loading component:', error)
      }
    }
    
    loadComponent()
    
    return () => {
      isMounted = false
    }
  }, [isAppRouter, serverComponent, clientAlternative])

  if (!Component) {
    return <>{fallback}</>
  }

  return <Component {...componentProps} />
}
