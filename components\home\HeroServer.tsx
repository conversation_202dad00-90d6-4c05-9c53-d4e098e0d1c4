import { Suspense } from "react"
import { <PERSON><PERSON><PERSON> } from "lucide-react"
import Link from "next/link"
import { unstable_cache } from "next/cache"
import { createServerClient } from "@/lib/supabase/server"
import { fetchCategories, fetchPricingOptions, fetchReviewCount, fetchToolCount } from "@/lib/supabase/server-utils"
import UniversalSearch from "@/components/search/universal-search"
import SearchContainer from "@/components/search/search-container"
import SearchButton from "@/components/search/search-button"
import ScrollingCategories from "./ScrollingCategories"
import TextCyclerClient from "@/components/ui/TextCyclerClient"
import RealTimeStats from "@/components/ui/real-time-stats"
import GradientBackground from "@/components/ui/GradientBackground"
import MotionWrapper from "@/components/ui/MotionWrapper"
import { Skeleton } from "@/components/ui/SkeletonLoader"
import DynamicToolsBadge from "@/components/ui/DynamicToolsBadge"

interface Category {
  id: string
  name: string
  count: number
}



async function HeroCategories() {
  try {
    // Execute all queries in parallel for better performance using optimized functions
    const [categories, pricingOptions] = await Promise.all([
      fetchCategories(12, 'hero-categories-list'), // Reduced to 12 for better performance
      fetchPricingOptions('hero-pricing-options')
    ])

    // Get the top 10 categories for the scrolling component (reduced for performance)
    const popularCategories = categories.slice(0, 10)

    return (
      <>
        <MotionWrapper animation="fadeIn" delay="delay-200" className="mt-6">
          <SearchContainer maxWidth="3xl">
            <div className="flex items-center p-3">
              <div className="flex-1">
                <UniversalSearch
                  mode="navigation"
                  context="general"
                  variant="hero"
                  size="lg"
                  placeholder="Search for AI tools, categories, features, or companies..."
                  className="w-full"
                  rounded="full"
                  glass={false}
                  showKeyboardShortcut={false}
                  fullWidth={true}
                  maxResults={8}
                  autoFocus={false}
                  showSearchButton={false}
                />
              </div>

              {/* Search Button */}
              <div className="flex-shrink-0 ml-3">
                <SearchButton
                  size="md"
                  showText={true}
                  text="Search"
                />
              </div>
            </div>
          </SearchContainer>
        </MotionWrapper>

        <MotionWrapper animation="fadeIn" delay="delay-300" className="mt-6">
          <div className="relative">
            <ScrollingCategories categories={popularCategories} maxCategories={10} />
          </div>
        </MotionWrapper>
      </>
    )
  } catch (error) {
    console.error('Error in HeroCategories:', error)
    // Fallback UI
    return (
      <>
        <MotionWrapper animation="fadeIn" delay="delay-200" className="mt-6">
          <SearchContainer maxWidth="3xl">
            <div className="flex items-center p-3">
              <div className="flex-1">
                <UniversalSearch
                  mode="navigation"
                  context="general"
                  variant="hero"
                  size="lg"
                  placeholder="Search for AI tools, categories, features, or companies..."
                  className="w-full"
                  rounded="full"
                  glass={false}
                  showKeyboardShortcut={false}
                  fullWidth={true}
                  maxResults={8}
                  autoFocus={false}
                  showSearchButton={false}
                />
              </div>

              {/* Search Button */}
              <div className="flex-shrink-0 ml-3">
                <SearchButton
                  size="md"
                  showText={true}
                  text="Search"
                />
              </div>
            </div>
          </SearchContainer>
        </MotionWrapper>
        <MotionWrapper animation="fadeIn" delay="delay-300" className="mt-6">
          <div className="relative">
            <ScrollingCategories categories={[]} maxCategories={10} />
          </div>
        </MotionWrapper>
      </>
    )
  }
}

// Project types for text cycling
const projectTypes = [
  "business",
  "content",
  "marketing",
  "design",
  "coding",
  "writing"
]

export default function HeroServer() {
  return (
    <GradientBackground variant="primary" className="pt-4 pb-4 md:pt-6 md:pb-6">
      <div className="container-tight relative z-10">
        <MotionWrapper animation="fadeIn" className="text-center">
          <DynamicToolsBadge />

          <h1 className="mt-3 text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-bold tracking-tight">
            <span className="text-gradient">AI tools</span> for{" "}
            <TextCyclerClient
              texts={projectTypes}
              interval={2500}
              className="inline-block"
            />
            {" "}projects
          </h1>

          <p className="mt-3 text-base md:text-lg text-muted-foreground max-w-2xl mx-auto">
            Discover, compare, and choose the best AI-powered tools for your specific needs
          </p>
        </MotionWrapper>

        <Suspense fallback={
          <div className="mt-6 animate-pulse">
            <div className="h-12 bg-muted/60 rounded-full w-full max-w-xl mx-auto mb-3"></div>
            <div className="flex gap-2 overflow-x-auto py-2 max-w-4xl mx-auto">
              {Array.from({ length: 6 }).map((_, i) => (
                <div key={i} className="h-8 w-20 bg-muted/60 rounded-full flex-shrink-0"></div>
              ))}
            </div>
          </div>
        }>
          <HeroCategories />
        </Suspense>

        <RealTimeStats />
      </div>
    </GradientBackground>
  )
}
