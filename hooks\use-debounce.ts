import { useState, useEffect, useRef, useCallback } from 'react';

/**
 * Hook محسن للـ debounce مع تحسينات الأداء
 * @param value القيمة المراد تأخيرها
 * @param delay التأخير بالمللي ثانية
 * @param options خيارات إضافية
 * @returns القيمة المؤخرة
 */
export function useDebounce<T>(
  value: T, 
  delay: number,
  options: {
    leading?: boolean; // تنفيذ فوري في البداية
    trailing?: boolean; // تنفيذ في النهاية (افتراضي)
    maxWait?: number; // أقصى وقت انتظار
  } = {}
): T {
  const { leading = false, trailing = true, maxWait } = options;
  const [debouncedValue, setDebouncedValue] = useState<T>(value);
  const timeoutRef = useRef<NodeJS.Timeout>();
  const maxTimeoutRef = useRef<NodeJS.Timeout>();
  const lastCallTimeRef = useRef<number>();
  const lastInvokeTimeRef = useRef<number>(0);

  const updateDebouncedValue = useCallback((newValue: T) => {
    setDebouncedValue(newValue);
    lastInvokeTimeRef.current = Date.now();
  }, []);

  useEffect(() => {
    const currentTime = Date.now();
    const timeSinceLastCall = currentTime - (lastCallTimeRef.current || 0);
    const timeSinceLastInvoke = currentTime - lastInvokeTimeRef.current;

    lastCallTimeRef.current = currentTime;

    // تنفيذ فوري في البداية إذا كان مطلوباً
    if (leading && timeSinceLastInvoke >= delay) {
      updateDebouncedValue(value);
      return;
    }

    // مسح المؤقتات السابقة
    if (timeoutRef.current) {
      clearTimeout(timeoutRef.current);
    }
    if (maxTimeoutRef.current) {
      clearTimeout(maxTimeoutRef.current);
    }

    // إعداد مؤقت التأخير العادي
    if (trailing) {
      timeoutRef.current = setTimeout(() => {
        updateDebouncedValue(value);
      }, delay);
    }

    // إعداد مؤقت الحد الأقصى للانتظار
    if (maxWait && timeSinceLastInvoke < maxWait) {
      const remainingWait = maxWait - timeSinceLastInvoke;
      maxTimeoutRef.current = setTimeout(() => {
        updateDebouncedValue(value);
      }, remainingWait);
    }

    // تنظيف المؤقتات عند إلغاء التحميل
    return () => {
      if (timeoutRef.current) {
        clearTimeout(timeoutRef.current);
      }
      if (maxTimeoutRef.current) {
        clearTimeout(maxTimeoutRef.current);
      }
    };
  }, [value, delay, leading, trailing, maxWait, updateDebouncedValue]);

  return debouncedValue;
}

/**
 * Hook مبسط للـ debounce السريع (للبحث)
 */
export function useFastDebounce<T>(value: T, delay: number = 300): T {
  return useDebounce(value, delay, { trailing: true, maxWait: delay * 2 });
}

/**
 * Hook للـ debounce مع تنفيذ فوري (للأزرار)
 */
export function useImmediateDebounce<T>(value: T, delay: number = 500): T {
  return useDebounce(value, delay, { leading: true, trailing: false });
} 