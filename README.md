# AI Tools Directory

A comprehensive directory of AI tools built with Next.js, TypeScript, and Supabase with complete authentication and admin system.

## 🔧 Environment Variables
Make sure to set the following environment variables in your deployment platform:
- `NEXT_PUBLIC_SUPABASE_URL`
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`
- `SUPABASE_SERVICE_ROLE_KEY`

## 🚀 Deployment
This project is configured for automatic deployment on Vercel with GitHub integration.

## 🚀 Features

### 🔐 Authentication System
- **User Registration & Login** with email/password
- **Google OAuth** integration
- **Role-based Access Control** (User)
- **Protected Routes** with middleware
- **Session Management** with Supabase Auth

### 👥 User Dashboard
- **Profile Management** - View and update user information
- **Saved Tools** - Save and manage favorite AI tools
- **Reviews Management** - Write, edit, and delete tool reviews

### 🔍 Tool Features
- **Advanced Search** and filtering
- **Categories** and tags
- **Ratings & Reviews** system
- **Tool Submissions** by users
- **Detailed Tool Pages** with comprehensive information

### 🎨 Modern UI/UX
- **Responsive Design** for all devices
- **Dark/Light Mode** support
- **Modern UI** with Tailwind CSS and shadcn/ui
- **Smooth Animations** and transitions
- **Loading States** and error handling

## System Requirements

- Node.js 18.x or higher
- Supabase database

## Project Setup

1. Clone the project:
```bash
git clone <project_url>
cd ai-tools-directory
```

2. Install dependencies:
```bash
npm install
# or
pnpm install
```

3. Create a `.env.local` file and add environment variables:
```
NEXT_PUBLIC_SUPABASE_URL=<your_supabase_url>
NEXT_PUBLIC_SUPABASE_ANON_KEY=<your_supabase_anon_key>
```

4. Run the development server:
```bash
npm run dev
# or
pnpm dev
```

5. Open [http://localhost:3000](http://localhost:3000) in your browser

## Project Structure

- `/app` - Next.js App Router files
- `/components` - Reusable UI components
- `/lib` - Helper functions and TypeScript types
- `/pages` - Traditional route pages (legacy)
- `/styles` - Global CSS styles and base styles
- `/public` - Static files like images and icons

## Database Setup

The project requires a Supabase database setup with the following tables:
- `tools` - Stores tool information
- `reviews` - Stores user reviews
- `favorites` - Stores user favorite tools
- `profiles` - Stores user information
- `tool_submissions` - Stores submitted tools

You can find the database schema in the `lib/database.types.ts` file.

## Production Build

To create a production version of the application:

```bash
npm run build
# or
pnpm build
```

Then to run the production version:

```bash
npm start
# or
pnpm start
```

## Deployment on Vercel

This project is configured for seamless deployment on Vercel.

### Prerequisites

1. A Vercel account
2. A GitHub repository with your project
3. Supabase project with necessary tables

### Environment Variables

Make sure to add the following environment variables in your Vercel project settings:

- `NEXT_PUBLIC_SUPABASE_URL`: Your Supabase project URL
- `NEXT_PUBLIC_SUPABASE_ANON_KEY`: Your Supabase anonymous key

### Deployment Steps

1. Push your code to GitHub
2. Import your repository in Vercel
3. Configure the environment variables
4. Deploy the project

## Contributing

Contributions from the community are welcome! If you want to contribute, please follow these steps:

1. Fork the project
2. Create your feature branch (`git checkout -b feature/amazing-feature`)
3. Commit your changes (`git commit -m 'Add some amazing feature'`)
4. Push to the branch (`git push origin feature/amazing-feature`)
5. Open a Pull Request