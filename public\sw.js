const CACHE_NAME = 'aianytool-v1'
const STATIC_CACHE_NAME = 'aianytool-static-v1'
const DYNAMIC_CACHE_NAME = 'aianytool-dynamic-v1'

// الملفات الأساسية للتخزين المؤقت
const STATIC_ASSETS = [
  '/',
  '/tools',
  '/categories',
  '/about',
  '/featured',
  '/manifest.json',
  '/favicon.svg'
]

// تثبيت Service Worker
self.addEventListener('install', (event) => {
  console.log('Service Worker installing...')
  event.waitUntil(
    caches.open(STATIC_CACHE_NAME)
      .then((cache) => {
        console.log('Caching static assets')
        return cache.addAll(STATIC_ASSETS)
      })
      .catch((error) => {
        console.error('Failed to cache static assets:', error)
      })
  )
  self.skipWaiting()
})

// تفعيل Service Worker
self.addEventListener('activate', (event) => {
  console.log('Service Worker activating...')
  event.waitUntil(
    caches.keys().then((cacheNames) => {
      return Promise.all(
        cacheNames.map((cacheName) => {
          if (cacheName !== STATIC_CACHE_NAME && cacheName !== DYNAMIC_CACHE_NAME) {
            console.log('Deleting old cache:', cacheName)
            return caches.delete(cacheName)
          }
        })
      )
    })
  )
  self.clients.claim()
})

// اعتراض الطلبات
self.addEventListener('fetch', (event) => {
  const { request } = event
  const url = new URL(request.url)

  // تجاهل الطلبات غير HTTP/HTTPS
  if (!request.url.startsWith('http')) {
    return
  }

  // تجاهل طلبات API
  if (url.pathname.startsWith('/api/')) {
    return
  }

  // استراتيجية Cache First للملفات الثابتة
  if (STATIC_ASSETS.includes(url.pathname)) {
    event.respondWith(
      caches.match(request)
        .then((response) => {
          return response || fetch(request)
        })
    )
    return
  }

  // استراتيجية Network First للصفحات الديناميكية
  event.respondWith(
    fetch(request)
      .then((response) => {
        // تخزين الاستجابة الناجحة في التخزين المؤقت الديناميكي
        if (response.status === 200 && request.method === 'GET') {
          const responseClone = response.clone()
          caches.open(DYNAMIC_CACHE_NAME)
            .then((cache) => {
              cache.put(request, responseClone)
            })
            .catch((error) => {
              console.log('Cache put failed:', error)
            })
        }
        return response
      })
      .catch(() => {
        // في حالة فشل الشبكة، جرب التخزين المؤقت
        return caches.match(request)
          .then((response) => {
            if (response) {
              return response
            }
            // إذا لم توجد في التخزين المؤقت، أرجع صفحة offline
            if (request.destination === 'document') {
              return caches.match('/offline.html')
            }
          })
      })
  )
})

// معالجة رسائل من العميل
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'SKIP_WAITING') {
    self.skipWaiting()
  }
})

// تنظيف التخزين المؤقت الديناميكي عند امتلائه
self.addEventListener('message', (event) => {
  if (event.data && event.data.type === 'CLEAN_CACHE') {
    caches.open(DYNAMIC_CACHE_NAME)
      .then((cache) => {
        cache.keys().then((requests) => {
          if (requests.length > 50) { // حد أقصى 50 عنصر
            // احذف أقدم 10 عناصر
            const oldRequests = requests.slice(0, 10)
            oldRequests.forEach((request) => {
              cache.delete(request)
            })
          }
        })
      })
  }
})
