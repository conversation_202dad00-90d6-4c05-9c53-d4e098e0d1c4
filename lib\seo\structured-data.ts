/**
 * Professional JSON-LD Structured Data Configuration
 * Optimized for search engines and rich snippets
 */

export interface StructuredDataConfig {
  '@context': string
  '@type': string
  [key: string]: any
}

/**
 * Generate Organization structured data for the website
 */
export function generateOrganizationData(): StructuredDataConfig {
  return {
    '@context': 'https://schema.org',
    '@type': 'Organization',
    name: 'AiAnyTool.com',
    alternateName: 'AI Any Tool',
    url: 'https://aianytool.com',
    logo: 'https://aianytool.com/logo.png',
    description: 'Your ultimate directory for discovering the best AI tools and software solutions. Browse 1000+ curated AI tools across all categories.',
    foundingDate: '2024',
    contactPoint: {
      '@type': 'ContactPoint',
      telephone: '',
      contactType: 'Customer Service',
      email: '<EMAIL>',
      availableLanguage: 'English'
    },
    sameAs: [
      'https://twitter.com/aianytool',
      'https://facebook.com/aianytool',
      'https://linkedin.com/company/aianytool'
    ],
    address: {
      '@type': 'PostalAddress',
      addressCountry: 'US'
    }
  }
}

/**
 * Generate WebSite structured data for search functionality
 */
export function generateWebSiteData(): StructuredDataConfig {
  return {
    '@context': 'https://schema.org',
    '@type': 'WebSite',
    name: 'AiAnyTool.com',
    alternateName: 'AI Any Tool Directory',
    url: 'https://aianytool.com',
    description: 'Discover, compare, and find the perfect AI tools for your business. Browse 1000+ AI tools across all categories with reviews, pricing, and detailed comparisons.',
    inLanguage: 'en-US',
    potentialAction: {
      '@type': 'SearchAction',
      target: {
        '@type': 'EntryPoint',
        urlTemplate: 'https://aianytool.com/tools?search={search_term_string}'
      },
      'query-input': 'required name=search_term_string'
    },
    publisher: {
      '@type': 'Organization',
      name: 'AiAnyTool.com',
      logo: {
        '@type': 'ImageObject',
        url: 'https://aianytool.com/logo.png'
      }
    }
  }
}

/**
 * Generate SoftwareApplication structured data for individual tools
 */
export function generateToolStructuredData(tool: any): StructuredDataConfig {
  const baseData: StructuredDataConfig = {
    '@context': 'https://schema.org',
    '@type': 'SoftwareApplication',
    name: tool.company_name,
    description: tool.short_description || `${tool.company_name} is a powerful AI tool for ${tool.primary_task?.toLowerCase() || 'various tasks'}.`,
    url: tool.visit_website_url,
    applicationCategory: tool.primary_task || 'AI Tool',
    operatingSystem: 'Web Browser',
    datePublished: tool.created_at,
    dateModified: tool.updated_at,
    inLanguage: 'en-US',
    author: {
      '@type': 'Organization',
      name: 'AiAnyTool.com',
      url: 'https://aianytool.com'
    },
    publisher: {
      '@type': 'Organization',
      name: 'AiAnyTool.com',
      url: 'https://aianytool.com',
      logo: {
        '@type': 'ImageObject',
        url: 'https://aianytool.com/logo.png'
      }
    }
  }

  // Add images if available
  if (tool.featured_image_url || tool.logo_url) {
    const images = []
    if (tool.featured_image_url) {
      images.push({
        '@type': 'ImageObject',
        url: tool.featured_image_url.startsWith('http') ? tool.featured_image_url : `https://aianytool.com${tool.featured_image_url}`,
        caption: `${tool.company_name} Screenshot`
      })
    }
    if (tool.logo_url) {
      images.push({
        '@type': 'ImageObject',
        url: tool.logo_url.startsWith('http') ? tool.logo_url : `https://aianytool.com${tool.logo_url}`,
        caption: `${tool.company_name} Logo`
      })
    }
    baseData.image = images.length === 1 ? images[0] : images
  }

  // Add pricing information if available
  if (tool.pricing) {
    const pricingLower = tool.pricing.toLowerCase()
    if (pricingLower.includes('free')) {
      baseData.offers = {
        '@type': 'Offer',
        price: '0',
        priceCurrency: 'USD',
        availability: 'https://schema.org/InStock'
      }
    } else if (pricingLower.includes('$') || pricingLower.includes('paid')) {
      baseData.offers = {
        '@type': 'Offer',
        priceRange: tool.pricing,
        priceCurrency: 'USD',
        availability: 'https://schema.org/InStock'
      }
    }
  }

  // Add features as keywords
  if (tool.features && Array.isArray(tool.features)) {
    baseData.keywords = tool.features.map((feature: any) => 
      typeof feature === 'string' ? feature : feature.name || feature.value || String(feature)
    ).join(', ')
  }

  return baseData
}

/**
 * Generate CollectionPage structured data for category pages
 */
export function generateCategoryStructuredData(category: string, toolsCount: number, tools: any[]): StructuredDataConfig {
  return {
    '@context': 'https://schema.org',
    '@type': 'CollectionPage',
    name: `Best ${category} AI Tools 2024`,
    description: `Discover the best ${category} AI tools. Compare ${toolsCount}+ ${category} tools with reviews, pricing, and features.`,
    url: `https://aianytool.com/category/${category.toLowerCase().replace(/\s+/g, '-')}`,
    inLanguage: 'en-US',
    about: {
      '@type': 'Thing',
      name: `${category} AI Tools`,
      description: `Collection of AI tools for ${category.toLowerCase()}`
    },
    mainEntity: {
      '@type': 'ItemList',
      numberOfItems: toolsCount,
      itemListElement: tools.slice(0, 10).map((tool, index) => ({
        '@type': 'ListItem',
        position: index + 1,
        item: {
          '@type': 'SoftwareApplication',
          name: tool.company_name,
          description: tool.short_description,
          url: tool.visit_website_url,
          applicationCategory: tool.primary_task
        }
      }))
    },
    publisher: {
      '@type': 'Organization',
      name: 'AiAnyTool.com',
      url: 'https://aianytool.com'
    }
  }
}

/**
 * Generate ItemList structured data for tools listing page
 */
export function generateToolsListStructuredData(tools: any[], totalCount: number): StructuredDataConfig {
  return {
    '@context': 'https://schema.org',
    '@type': 'ItemList',
    name: 'AI Tools Directory',
    description: 'Complete directory of AI tools and software applications',
    url: 'https://aianytool.com/tools',
    numberOfItems: totalCount,
    itemListElement: tools.slice(0, 20).map((tool, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      item: {
        '@type': 'SoftwareApplication',
        name: tool.company_name,
        description: tool.short_description,
        url: tool.visit_website_url,
        applicationCategory: tool.primary_task,
        ...(tool.pricing && {
          offers: {
            '@type': 'Offer',
            price: tool.pricing.toLowerCase().includes('free') ? '0' : 'varies',
            priceCurrency: 'USD'
          }
        })
      }
    }))
  }
}

/**
 * Generate BreadcrumbList structured data
 */
export function generateBreadcrumbData(breadcrumbs: Array<{ name: string; url: string }>): StructuredDataConfig {
  return {
    '@context': 'https://schema.org',
    '@type': 'BreadcrumbList',
    itemListElement: breadcrumbs.map((crumb, index) => ({
      '@type': 'ListItem',
      position: index + 1,
      name: crumb.name,
      item: crumb.url
    }))
  }
}

/**
 * Generate FAQ structured data
 */
export function generateFAQData(faqs: Array<{ question: string; answer: string }>): StructuredDataConfig {
  return {
    '@context': 'https://schema.org',
    '@type': 'FAQPage',
    mainEntity: faqs.map(faq => ({
      '@type': 'Question',
      name: faq.question,
      acceptedAnswer: {
        '@type': 'Answer',
        text: faq.answer
      }
    }))
  }
}

/**
 * Generate Review structured data for tools
 */
export function generateReviewData(tool: any, rating?: number): StructuredDataConfig | null {
  if (!rating) return null

  return {
    '@context': 'https://schema.org',
    '@type': 'Review',
    itemReviewed: {
      '@type': 'SoftwareApplication',
      name: tool.company_name,
      description: tool.short_description,
      applicationCategory: tool.primary_task
    },
    reviewRating: {
      '@type': 'Rating',
      ratingValue: rating,
      bestRating: 5,
      worstRating: 1
    },
    author: {
      '@type': 'Organization',
      name: 'AiAnyTool.com'
    },
    datePublished: tool.created_at,
    reviewBody: `Professional review of ${tool.company_name}, an AI tool for ${tool.primary_task?.toLowerCase() || 'various tasks'}.`
  }
}

/**
 * Generate Article structured data for blog posts or detailed pages
 */
export function generateArticleData(params: {
  title: string
  description: string
  url: string
  datePublished: string
  dateModified?: string
  author?: string
  image?: string
}): StructuredDataConfig {
  return {
    '@context': 'https://schema.org',
    '@type': 'Article',
    headline: params.title,
    description: params.description,
    url: params.url,
    datePublished: params.datePublished,
    dateModified: params.dateModified || params.datePublished,
    author: {
      '@type': 'Organization',
      name: params.author || 'AiAnyTool.com',
      url: 'https://aianytool.com'
    },
    publisher: {
      '@type': 'Organization',
      name: 'AiAnyTool.com',
      url: 'https://aianytool.com',
      logo: {
        '@type': 'ImageObject',
        url: 'https://aianytool.com/logo.png'
      }
    },
    mainEntityOfPage: {
      '@type': 'WebPage',
      '@id': params.url
    },
    ...(params.image && {
      image: {
        '@type': 'ImageObject',
        url: params.image
      }
    })
  }
}

/**
 * Combine multiple structured data objects
 */
export function combineStructuredData(dataObjects: StructuredDataConfig[]): string {
  if (dataObjects.length === 1) {
    return JSON.stringify(dataObjects[0])
  }

  return JSON.stringify({
    '@context': 'https://schema.org',
    '@graph': dataObjects
  })
}

/**
 * Validate structured data format
 */
export function validateStructuredData(data: StructuredDataConfig): boolean {
  try {
    // Basic validation
    if (!data['@context'] || !data['@type']) {
      console.warn('Structured data missing required @context or @type')
      return false
    }

    // Validate JSON format
    JSON.stringify(data)
    return true
  } catch (error) {
    console.error('Invalid structured data format:', error)
    return false
  }
}
