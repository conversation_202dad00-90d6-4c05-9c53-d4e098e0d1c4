'use client'

import { useState, useEffect, useRef, useMemo } from "react"
import { useRouter } from "next/navigation"
import Link from "next/link"
import { Search, ArrowLeft, Grid, List, SlidersHorizontal, Star, Share2, Bookmark, Tag, DollarSign, Filter, TrendingUp, Users } from "lucide-react"
import { motion, AnimatePresence } from "@/lib/motion-stub"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import ToolCard from "@/components/tool-card"
import HeroSearch from "@/components/home/<USER>"
import { cn } from "@/lib/utils"

interface Tool {
  id: number
  company_name: string
  description?: string
  logo_url?: string
  primary_task?: string
  pricing?: string
  rating?: number
  slug?: string
  url?: string
  featured?: boolean
  verified?: boolean
  new?: boolean
  review_count?: number
  tags?: string[]
}

interface CategoryClientProps {
  category: string
  tools: Tool[]
}

type SortOption = 'featured' | 'rating' | 'alphabetical' | 'newest'
type ViewMode = 'grid' | 'list'

export default function CategoryClient({ category, tools }: CategoryClientProps) {
  const router = useRouter()
  const [searchQuery, setSearchQuery] = useState("")
  const [sortBy, setSortBy] = useState<SortOption>('featured')
  const [viewMode, setViewMode] = useState<ViewMode>('grid')
  const [showFilters, setShowFilters] = useState(false)
  const [activeLetter, setActiveLetter] = useState<string>("")  

  // Refs for scrolling
  const letterRefs = useRef<{[key: string]: HTMLDivElement | null}>({})

  // Log for debugging
  useEffect(() => {
    console.log(`CategoryClient: Rendering category "${category}" with ${tools?.length || 0} tools`);
  }, [category, tools]);

  // Alphabet for quick navigation
  const alphabet = "ABCDEFGHIJKLMNOPQRSTUVWXYZ".split("")

  // Filter and sort tools
  const filteredAndSortedTools = useMemo(() => {
    let filtered = tools || []

    // Apply search filter
    if (searchQuery.trim()) {
      const query = searchQuery.toLowerCase()
      filtered = filtered.filter(tool =>
        (tool.company_name && tool.company_name.toLowerCase().includes(query)) ||
        (tool.description && tool.description.toLowerCase().includes(query)) ||
        (tool.tags && tool.tags.some(tag => tag.toLowerCase().includes(query)))
      )
    }

    // Apply sorting
    const sorted = [...filtered].sort((a, b) => {
      switch (sortBy) {
        case 'featured':
          // Featured first, then verified, then by rating
          if (a.featured && !b.featured) return -1
          if (!a.featured && b.featured) return 1
          if (a.verified && !b.verified) return -1
          if (!a.verified && b.verified) return 1
          return (b.rating || 0) - (a.rating || 0)
        case 'rating':
          return (b.rating || 0) - (a.rating || 0)
        case 'alphabetical':
          return (a.company_name || '').localeCompare(b.company_name || '')
        case 'newest':
          if (a.new && !b.new) return -1
          if (!a.new && b.new) return 1
          return (b.rating || 0) - (a.rating || 0)
        default:
          return 0
      }
    })

    return sorted
  }, [tools, searchQuery, sortBy])

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query)
  }

  // Group tools by first letter for alphabetical display
  const toolsByLetter = filteredAndSortedTools.reduce((acc, tool) => {
    if (!tool.company_name) return acc
    const firstLetter = tool.company_name.charAt(0).toUpperCase()
    if (!acc[firstLetter]) {
      acc[firstLetter] = []
    }
    acc[firstLetter].push(tool)
    return acc
  }, {} as {[key: string]: Tool[]})

  // Sort the keys alphabetically
  const sortedLetters = Object.keys(toolsByLetter).sort()

  // Scroll to letter section when clicking on alphabet navigation
  const scrollToLetter = (letter: string) => {
    setActiveLetter(letter)
    const element = letterRefs.current[letter]
    if (element) {
      element.scrollIntoView({ behavior: 'smooth', block: 'start' })
    }
  }

  // Get category stats
  const totalTools = tools?.length || 0
  const featuredCount = tools?.filter(tool => tool.featured)?.length || 0
  const verifiedCount = tools?.filter(tool => tool.verified)?.length || 0
  const averageRating = totalTools > 0 
    ? (tools?.reduce((sum, tool) => sum + (tool.rating || 0), 0) || 0) / totalTools 
    : 0

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative pt-24 pb-16 bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4">
          {/* Back Button */}
          <motion.div 
            className="mb-6"
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ duration: 0.4 }}
          >
            <Button 
              variant="ghost" 
              onClick={() => router.back()}
              className="gap-2 hover:bg-muted/50"
            >
              <ArrowLeft className="h-4 w-4" />
              Back
            </Button>
          </motion.div>

          <div className="text-center mb-12">
            <motion.h1 
              className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              {category} AI Tools
            </motion.h1>
            <motion.p 
              className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto mb-8"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              Discover {totalTools} AI tools in the {category} category. Find the perfect solution for your needs.
            </motion.p>
          </div>

          {/* Search Bar */}
          <motion.div 
            className="max-w-2xl mx-auto mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <HeroSearch 
              onSearch={handleSearch}
              initialValue={searchQuery}
              placeholder={`Search ${category} tools...`}
            />
          </motion.div>

          {/* Stats Cards */}
          <motion.div 
            className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="text-center border-border/50 bg-card/50 backdrop-blur-sm">
              <CardContent className="pt-4 pb-4">
                <div className="text-2xl font-bold">{totalTools}</div>
                <div className="text-xs text-muted-foreground">Total Tools</div>
              </CardContent>
            </Card>
            <Card className="text-center border-border/50 bg-card/50 backdrop-blur-sm">
              <CardContent className="pt-4 pb-4">
                <div className="text-2xl font-bold">{featuredCount}</div>
                <div className="text-xs text-muted-foreground">Featured</div>
              </CardContent>
            </Card>
            <Card className="text-center border-border/50 bg-card/50 backdrop-blur-sm">
              <CardContent className="pt-4 pb-4">
                <div className="text-2xl font-bold">{verifiedCount}</div>
                <div className="text-xs text-muted-foreground">Verified</div>
              </CardContent>
            </Card>
            <Card className="text-center border-border/50 bg-card/50 backdrop-blur-sm">
              <CardContent className="pt-4 pb-4">
                <div className="text-2xl font-bold">{averageRating.toFixed(1)}</div>
                <div className="text-xs text-muted-foreground">Avg Rating</div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Filters and Controls */}
      <section className="py-8 border-b border-border/50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            {/* Results Count */}
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">
                Showing {filteredAndSortedTools.length} of {totalTools} tools
              </span>
              {searchQuery && (
                <Badge variant="secondary" className="text-xs">
                  Search: {searchQuery}
                </Badge>
              )}
            </div>

            {/* Controls */}
            <div className="flex items-center gap-4">
              {/* Sort Options */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Sort by:</span>
                <select 
                  value={sortBy} 
                  onChange={(e) => setSortBy(e.target.value as SortOption)}
                  className="text-sm border border-border rounded-md px-3 py-1 bg-background"
                >
                  <option value="featured">Featured</option>
                  <option value="rating">Rating</option>
                  <option value="alphabetical">A-Z</option>
                  <option value="newest">Newest</option>
                </select>
              </div>

              {/* View Mode Toggle */}
              <div className="flex items-center border border-border rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-8 w-8 p-0"
                >
                  <Grid className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-8 w-8 p-0"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Tools Grid/List */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <AnimatePresence mode="wait">
            {filteredAndSortedTools.length > 0 ? (
              <motion.div 
                key={`${viewMode}-${sortBy}`}
                className={cn(
                  "gap-6",
                  viewMode === 'grid' 
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" 
                    : "flex flex-col space-y-4"
                )}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4 }}
              >
                {filteredAndSortedTools.map((tool, index) => (
                  <motion.div
                    key={tool.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.05 }}
                  >
                    <ToolCard
                      tool={{
                        id: tool.id?.toString() || '',
                        slug: tool.slug || '',
                        company_name: tool.company_name || 'Unknown Tool',
                        short_description: tool.description || 'No description available',
                        visit_website_url: tool.url || '#',
                        logo_url: tool.logo_url || '',
                        primary_task: tool.primary_task || category,
                        pricing: tool.pricing || 'Unknown',
                        rating: tool.rating || 0,
                        review_count: tool.review_count || 0,
                        is_verified: tool.verified || false,
                        is_featured: tool.featured || false,
                        click_count: 0,
                        isNew: tool.new || false
                      }}
                      className={viewMode === 'list' ? 'flex-row' : ''}
                    />
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              <motion.div 
                className="text-center py-16"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.4 }}
              >
                <Search className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No tools found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery 
                    ? `No tools match "${searchQuery}" in the ${category} category. Try a different search term.`
                    : `No tools available in the ${category} category at the moment.`
                  }
                </p>
                {searchQuery && (
                  <Button 
                    variant="outline" 
                    onClick={() => handleSearch('')}
                  >
                    Clear Search
                  </Button>
                )}
              </motion.div>
            )}
          </AnimatePresence>
        </div>
      </section>

      {/* Alphabet Navigation - Only show in grid view and when not searching */}
      {viewMode === 'grid' && !searchQuery && sortBy === 'alphabetical' && filteredAndSortedTools.length > 10 && (
        <section className="py-8 border-t border-border/50">
          <div className="container mx-auto px-4">
            <div className="flex flex-wrap justify-center gap-2">
              {alphabet.map(letter => {
                const hasTools = toolsByLetter[letter]?.length > 0
                return (
                  <Button
                    key={letter}
                    variant={activeLetter === letter ? 'default' : 'ghost'}
                    size="sm"
                    onClick={() => hasTools && scrollToLetter(letter)}
                    disabled={!hasTools}
                    className={cn(
                      "h-8 w-8 p-0 text-xs",
                      hasTools ? "hover:bg-primary/10" : "opacity-30 cursor-not-allowed"
                    )}
                  >
                    {letter}
                  </Button>
                )
              })}
            </div>
          </div>
        </section>
      )}
    </div>
  )
}
