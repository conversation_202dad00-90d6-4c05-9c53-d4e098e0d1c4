'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { Card, CardContent, CardHeader, CardTitle, CardFooter } from '@/components/ui/card'
import { <PERSON><PERSON>, <PERSON><PERSON><PERSON>ontent, <PERSON><PERSON>List, TabsTrigger } from '@/components/ui/tabs'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Input } from '@/components/ui/input'
import { Label } from '@/components/ui/label'
import { Textarea } from '@/components/ui/textarea'
import { Switch } from '@/components/ui/switch'
import { LoadingSpinner } from '@/components/loading-spinner'
import { MotionWrapper } from '@/components/ui/MotionWrapper'
import {
  Heart,
  Star,
  User,
  Calendar,
  Plus,
  Search,
  Activity,
  Zap,
  ArrowRight,
  Eye,
  ThumbsUp,
  ExternalLink,
  Clock,
  TrendingUp,
  Edit,
  Save,
  Mail,
  Phone,
  MapPin,
  Globe,
  Shield,
  Bell,
  Trash2,
  LogOut,
  Download,
  Upload,
  Settings
} from 'lucide-react'
import Link from 'next/link'
import { create<PERSON><PERSON>erClient } from '@/lib/supabase/client-utils'
import { toast } from 'sonner'
import { useRouter } from 'next/navigation'
import { SavedToolCard } from '@/components/dashboard/SavedToolCard'

interface UserStats {
  favoritesCount: number
  reviewsCount: number
  submissionsCount: number
  joinDate: string
}

interface Tool {
  id: number
  company_name: string
  short_description: string
  logo_url: string
  primary_task: string
  pricing: string
  is_verified: boolean
  slug: string
}

interface Review {
  id: string
  rating: number
  comment: string
  created_at: string
  tools: {
    company_name: string
    logo_url: string
    slug: string
  }
}

export function UserDashboard() {
  const { user, isLoading: authLoading, signOut } = useAuth()
  const router = useRouter()
  const [stats, setStats] = useState<UserStats>({
    favoritesCount: 0,
    reviewsCount: 0,
    submissionsCount: 0,
    joinDate: ''
  })
  const [favorites, setFavorites] = useState<Tool[]>([])
  const [recentReviews, setRecentReviews] = useState<Review[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [isEditing, setIsEditing] = useState(false)
  const [isSaving, setIsSaving] = useState(false)

  // Profile form state
  const [profileData, setProfileData] = useState({
    full_name: '',
    bio: '',
    avatar_url: '',
    email: '',
    notifications_enabled: true,
    email_updates: true,
    marketing_emails: false
  })

  const userName = user?.name || user?.email?.split('@')[0] || 'User'

  // Convert favorites data to SavedToolCard format
  const convertToSavedTool = (tool: Tool, favoriteId: string, createdAt: string) => ({
    id: favoriteId,
    tool_id: tool.id,
    created_at: createdAt,
    tool: {
      id: tool.id,
      name: tool.company_name || 'Unknown Tool',
      description: tool.short_description || 'No description available',
      logo_url: tool.logo_url || '',
      website_url: tool.visit_website_url || '',
      category: tool.primary_task || 'General',
      pricing_type: tool.pricing || 'Unknown',
      rating: 4.5, // Default rating since we don't have it in current data
      slug: tool.slug || ''
    }
  })

  // Handle removing favorite
  const handleRemoveFavorite = async (toolId: number) => {
    if (!user?.id) return

    try {
      const supabase = createBrowserClient()
      if (!supabase) return

      const { error } = await supabase
        .from('favorites')
        .delete()
        .eq('user_id', user.id)
        .eq('tool_id', toolId)

      if (error) throw error

      // Update local state
      setFavorites(prev => prev.filter(tool => tool.id !== toolId))
      setStats(prev => ({ ...prev, favoritesCount: prev.favoritesCount - 1 }))

      toast.success('Removed from favorites')
    } catch (error: any) {
      console.error('Error removing favorite:', error)
      toast.error('Failed to remove from favorites')
    }
  }

  const formatDate = (dateString: string) => {
    try {
      const date = new Date(dateString)
      return date.toLocaleDateString('en-US', {
        year: 'numeric',
        month: 'short',
        day: 'numeric'
      })
    } catch {
      return 'Recently'
    }
  }

  // Update profile data
  const updateProfile = async () => {
    if (!user?.id) return

    try {
      setIsSaving(true)
      const supabase = createBrowserClient()
      if (!supabase) return

      // Update user metadata instead of profiles table
      const { error } = await supabase.auth.updateUser({
        data: {
          full_name: profileData.full_name,
          bio: profileData.bio,
          avatar_url: profileData.avatar_url,
        }
      })

      if (error) throw error

      toast.success('Profile updated successfully!')
      setIsEditing(false)
    } catch (error: any) {
      console.error('Error updating profile:', error)
      toast.error('Failed to update profile')
    } finally {
      setIsSaving(false)
    }
  }

  // Sign out
  const handleSignOut = async () => {
    try {
      await signOut()
      toast.success('Signed out successfully')
      router.push('/')
    } catch (error: any) {
      console.error('Error signing out:', error)
      toast.error('Failed to sign out')
    }
  }

  // Delete account
  const deleteAccount = async () => {
    if (!user?.id) return

    if (!confirm('Are you sure you want to delete your account? This action cannot be undone.')) {
      return
    }

    try {
      const supabase = createBrowserClient()
      if (!supabase) return

      // Delete user favorites and reviews first
      await Promise.allSettled([
        supabase.from('favorites').delete().eq('user_id', user.id),
        supabase.from('reviews').delete().eq('user_id', user.id)
      ])

      // Sign out user
      await signOut()

      toast.success('Account data deleted successfully')
      router.push('/')
    } catch (error: any) {
      console.error('Error deleting account:', error)
      toast.error('Failed to delete account')
    }
  }

  const fetchUserData = async () => {
    if (!user?.id) return

    try {
      setIsLoading(true)
      const supabase = createBrowserClient()
      if (!supabase) return

      const [
        favoritesResult,
        reviewsResult,
        favoritesDataResult,
        recentReviewsResult
      ] = await Promise.allSettled([
        supabase.from('favorites').select('*', { count: 'exact', head: true }).eq('user_id', user.id),
        supabase.from('reviews').select('*', { count: 'exact', head: true }).eq('user_id', user.id),
        
        supabase
          .from('favorites')
          .select(`
            id,
            tool_id,
            created_at,
            tools:tool_id (
              id,
              company_name,
              short_description,
              logo_url,
              primary_task,
              pricing,
              is_verified,
              slug,
              visit_website_url
            )
          `)
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(6),
          
        supabase
          .from('reviews')
          .select(`
            id,
            rating,
            comment,
            created_at,
            tools:tool_id (
              company_name,
              logo_url,
              slug
            )
          `)
          .eq('user_id', user.id)
          .order('created_at', { ascending: false })
          .limit(5)
      ])

      const favoritesCount = favoritesResult.status === 'fulfilled' ? favoritesResult.value.count || 0 : 0
      const reviewsCount = reviewsResult.status === 'fulfilled' ? reviewsResult.value.count || 0 : 0
      
      const favoritesData = favoritesDataResult.status === 'fulfilled'
        ? favoritesDataResult.value.data?.filter(fav => fav.tools) || []
        : []
        
      const reviewsData = recentReviewsResult.status === 'fulfilled'
        ? recentReviewsResult.value.data || []
        : []

      setStats({
        favoritesCount,
        reviewsCount,
        submissionsCount: 0, // Static value to avoid 500 errors
        joinDate: user.created_at || new Date().toISOString()
      })

      setFavorites(favoritesData)
      setRecentReviews(reviewsData)

      // Set profile data from user object
      setProfileData({
        full_name: user.user_metadata?.full_name || user.name || '',
        bio: user.user_metadata?.bio || '',
        avatar_url: user.user_metadata?.avatar_url || '',
        email: user.email || '',
        notifications_enabled: true,
        email_updates: true,
        marketing_emails: false
      })

    } catch (error: any) {
      console.error('Error fetching user data:', error)
      toast.error('Failed to load dashboard data')
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    if (user?.id) {
      fetchUserData()
    }
  }, [user?.id])

  if (authLoading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 pt-20 pb-16">
        <div className="container max-w-7xl mx-auto px-4">
          <div className="flex items-center justify-center py-20">
            <LoadingSpinner size={40} />
          </div>
        </div>
      </div>
    )
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-blue-50 via-white to-purple-50 dark:from-gray-900 dark:via-gray-800 dark:to-gray-900 pt-20 pb-16">
      <div className="container max-w-7xl mx-auto px-4">
        <MotionWrapper>
          {/* Modern Header */}
          <div className="mb-8">
            <div className="bg-white dark:bg-gray-800 rounded-2xl p-6 shadow-sm border border-gray-200 dark:border-gray-700">
              <div className="flex flex-col lg:flex-row lg:items-center lg:justify-between gap-6">
                <div className="flex items-center gap-4">
                  <div className="w-16 h-16 bg-gradient-to-br from-blue-500 to-purple-600 rounded-2xl flex items-center justify-center">
                    <User className="w-8 h-8 text-white" />
                  </div>
                  <div>
                    <h1 className="text-2xl lg:text-3xl font-bold text-gray-900 dark:text-white mb-1">
                      Welcome back, {userName}!
                    </h1>
                    <p className="text-gray-600 dark:text-gray-400 flex items-center gap-2">
                      <Calendar className="w-4 h-4" />
                      Member since {formatDate(stats.joinDate || user?.created_at || new Date().toISOString())}
                    </p>
                  </div>
                </div>
                <div className="flex items-center gap-3">
                  <Button variant="outline" size="sm" asChild className="bg-white dark:bg-gray-700">
                    <Link href="/tools">
                      <Search className="w-4 h-4 mr-2" />
                      Discover Tools
                    </Link>
                  </Button>
                  <Button size="sm" asChild className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700">
                    <Link href="/tools/submit">
                      <Plus className="w-4 h-4 mr-2" />
                      Submit Tool
                    </Link>
                  </Button>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={handleSignOut}
                    className="text-red-600 border-red-200 hover:bg-red-50 dark:text-red-400 dark:border-red-800 dark:hover:bg-red-900/20"
                  >
                    <LogOut className="w-4 h-4 mr-2" />
                    Sign Out
                  </Button>
                </div>
              </div>
            </div>
          </div>

          {/* Enhanced Stats Grid */}
          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-6 mb-8">
            <Card className="bg-gradient-to-br from-blue-500 to-blue-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-blue-100 text-sm font-medium mb-1">Favorite Tools</p>
                    <p className="text-3xl font-bold">{isLoading ? '...' : stats.favoritesCount}</p>
                    <p className="text-blue-100 text-xs mt-1">Tools you love</p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-xl">
                    <Heart className="h-6 w-6" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-green-500 to-green-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-green-100 text-sm font-medium mb-1">Reviews Written</p>
                    <p className="text-3xl font-bold">{isLoading ? '...' : stats.reviewsCount}</p>
                    <p className="text-green-100 text-xs mt-1">Your feedback</p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-xl">
                    <Star className="h-6 w-6" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-purple-500 to-purple-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-purple-100 text-sm font-medium mb-1">Tool Submissions</p>
                    <p className="text-3xl font-bold">{isLoading ? '...' : stats.submissionsCount}</p>
                    <p className="text-purple-100 text-xs mt-1">Tools shared</p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-xl">
                    <Zap className="h-6 w-6" />
                  </div>
                </div>
              </CardContent>
            </Card>

            <Card className="bg-gradient-to-br from-orange-500 to-orange-600 text-white border-0 shadow-lg hover:shadow-xl transition-all duration-300">
              <CardContent className="p-6">
                <div className="flex items-center justify-between">
                  <div>
                    <p className="text-orange-100 text-sm font-medium mb-1">Activity Score</p>
                    <p className="text-3xl font-bold">{isLoading ? '...' : Math.min(100, (stats.favoritesCount * 10) + (stats.reviewsCount * 15) + (stats.submissionsCount * 25))}</p>
                    <p className="text-orange-100 text-xs mt-1">Engagement level</p>
                  </div>
                  <div className="bg-white/20 p-3 rounded-xl">
                    <Activity className="h-6 w-6" />
                  </div>
                </div>
              </CardContent>
            </Card>
          </div>

          {/* Main Content Tabs */}
          <Tabs defaultValue="overview" className="space-y-6">
            <TabsList className="grid w-full grid-cols-5 lg:w-auto lg:grid-cols-5 bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
              <TabsTrigger value="overview" className="flex items-center gap-2">
                <TrendingUp className="w-4 h-4" />
                Overview
              </TabsTrigger>
              <TabsTrigger value="favorites" className="flex items-center gap-2">
                <Heart className="w-4 h-4" />
                Favorites ({stats.favoritesCount})
              </TabsTrigger>
              <TabsTrigger value="reviews" className="flex items-center gap-2">
                <Star className="w-4 h-4" />
                Reviews ({stats.reviewsCount})
              </TabsTrigger>
              <TabsTrigger value="account" className="flex items-center gap-2">
                <User className="w-4 h-4" />
                Account
              </TabsTrigger>
              <TabsTrigger value="settings" className="flex items-center gap-2">
                <Settings className="w-4 h-4" />
                Settings
              </TabsTrigger>
            </TabsList>

            {/* Overview Tab */}
            <TabsContent value="overview" className="space-y-6">
              <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
                {/* Recent Favorites */}
                <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-semibold flex items-center gap-2">
                        <Heart className="w-5 h-5 text-red-500" />
                        Recent Favorites
                      </CardTitle>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href="/dashboard?tab=favorites" className="text-blue-600 hover:text-blue-700">
                          View All <ArrowRight className="w-4 h-4 ml-1" />
                        </Link>
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {isLoading ? (
                      <div className="space-y-3">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="flex items-center gap-3">
                            <div className="w-12 h-12 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />
                            <div className="flex-1 space-y-2">
                              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 animate-pulse" />
                            </div>
                          </div>
                        ))}
                      </div>
                    ) : favorites.length > 0 ? (
                      <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
                        {favorites.slice(0, 4).map((favorite) => (
                          <SavedToolCard
                            key={favorite.id}
                            savedTool={convertToSavedTool(favorite.tools, favorite.id, favorite.created_at)}
                            onRemove={() => handleRemoveFavorite(favorite.tools.id)}
                          />
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <Heart className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                        <p className="text-gray-600 dark:text-gray-400 mb-4">No favorite tools yet</p>
                        <Button asChild size="sm">
                          <Link href="/tools">
                            <Search className="w-4 h-4 mr-2" />
                            Discover Tools
                          </Link>
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>

                {/* Recent Reviews */}
                <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                  <CardHeader className="pb-4">
                    <div className="flex items-center justify-between">
                      <CardTitle className="text-lg font-semibold flex items-center gap-2">
                        <Star className="w-5 h-5 text-yellow-500" />
                        Recent Reviews
                      </CardTitle>
                      <Button variant="ghost" size="sm" asChild>
                        <Link href="/dashboard?tab=reviews" className="text-blue-600 hover:text-blue-700">
                          View All <ArrowRight className="w-4 h-4 ml-1" />
                        </Link>
                      </Button>
                    </div>
                  </CardHeader>
                  <CardContent>
                    {isLoading ? (
                      <div className="space-y-4">
                        {[...Array(3)].map((_, i) => (
                          <div key={i} className="space-y-2">
                            <div className="flex items-center gap-2">
                              <div className="w-8 h-8 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded flex-1 animate-pulse" />
                            </div>
                            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                            <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-3/4 animate-pulse" />
                          </div>
                        ))}
                      </div>
                    ) : recentReviews.length > 0 ? (
                      <div className="space-y-4">
                        {recentReviews.slice(0, 3).map((review) => (
                          <Card key={review.id} className="hover:shadow-sm transition-shadow">
                            <CardContent className="p-4">
                              <div className="flex items-start gap-3">
                                <div className="w-10 h-10 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
                                  {review.tools.logo_url ? (
                                    <img src={review.tools.logo_url} alt={review.tools.company_name} className="w-8 h-8 object-contain" />
                                  ) : (
                                    <Star className="w-5 h-5 text-gray-400" />
                                  )}
                                </div>
                                <div className="flex-1 min-w-0">
                                  <div className="flex items-center justify-between mb-2">
                                    <h5 className="font-semibold text-sm text-gray-900 dark:text-white truncate">
                                      {review.tools.company_name}
                                    </h5>
                                    <span className="text-xs text-gray-500 flex items-center gap-1">
                                      <Clock className="w-3 h-3" />
                                      {formatDate(review.created_at)}
                                    </span>
                                  </div>

                                  <div className="flex items-center gap-1 mb-2">
                                    {[...Array(5)].map((_, i) => (
                                      <Star
                                        key={i}
                                        className={`w-4 h-4 ${
                                          i < review.rating
                                            ? 'text-yellow-400 fill-current'
                                            : 'text-gray-300 dark:text-gray-600'
                                        }`}
                                      />
                                    ))}
                                    <span className="text-sm font-medium text-gray-700 dark:text-gray-300 ml-1">
                                      {review.rating}/5
                                    </span>
                                  </div>

                                  {review.comment && (
                                    <p className="text-sm text-gray-600 dark:text-gray-400 line-clamp-2 bg-gray-50 dark:bg-gray-800 p-2 rounded">
                                      "{review.comment}"
                                    </p>
                                  )}
                                </div>
                              </div>
                            </CardContent>
                          </Card>
                        ))}
                      </div>
                    ) : (
                      <div className="text-center py-8">
                        <Star className="w-12 h-12 text-gray-300 dark:text-gray-600 mx-auto mb-3" />
                        <p className="text-gray-600 dark:text-gray-400 mb-4">No reviews written yet</p>
                        <Button asChild size="sm">
                          <Link href="/tools">
                            <Star className="w-4 h-4 mr-2" />
                            Review Tools
                          </Link>
                        </Button>
                      </div>
                    )}
                  </CardContent>
                </Card>
              </div>

              {/* Quick Actions */}
              <Card className="bg-gradient-to-r from-blue-500 to-purple-600 text-white border-0">
                <CardContent className="p-6">
                  <div className="flex flex-col md:flex-row items-center justify-between gap-4">
                    <div>
                      <h3 className="text-xl font-bold mb-2">Ready to explore more AI tools?</h3>
                      <p className="text-blue-100">Discover cutting-edge AI tools to boost your productivity</p>
                    </div>
                    <div className="flex items-center gap-3">
                      <Button variant="secondary" asChild>
                        <Link href="/tools">
                          <Search className="w-4 h-4 mr-2" />
                          Browse Tools
                        </Link>
                      </Button>
                      <Button variant="outline" asChild className="bg-white/10 border-white/20 text-white hover:bg-white/20">
                        <Link href="/tools/submit">
                          <Plus className="w-4 h-4 mr-2" />
                          Submit Tool
                        </Link>
                      </Button>
                    </div>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>

            {/* Favorites Tab */}
            <TabsContent value="favorites" className="space-y-6">
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-semibold flex items-center gap-2">
                      <Heart className="w-5 h-5 text-red-500" />
                      Your Favorite Tools ({stats.favoritesCount})
                    </CardTitle>
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/tools">
                        <Plus className="w-4 h-4 mr-2" />
                        Discover More
                      </Link>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {[...Array(6)].map((_, i) => (
                        <div key={i} className="space-y-3">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />
                            <div className="flex-1 space-y-2">
                              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-2/3 animate-pulse" />
                            </div>
                          </div>
                        </div>
                      ))}
                    </div>
                  ) : favorites.length > 0 ? (
                    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-4">
                      {favorites.map((favorite) => (
                        <SavedToolCard
                          key={favorite.id}
                          savedTool={convertToSavedTool(favorite.tools, favorite.id, favorite.created_at)}
                          onRemove={() => handleRemoveFavorite(favorite.tools.id)}
                        />
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Heart className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No favorite tools yet</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-6">Start exploring and save tools you love!</p>
                      <Button asChild>
                        <Link href="/tools">
                          <Search className="w-4 h-4 mr-2" />
                          Discover AI Tools
                        </Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Reviews Tab */}
            <TabsContent value="reviews" className="space-y-6">
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-semibold flex items-center gap-2">
                      <Star className="w-5 h-5 text-yellow-500" />
                      Your Reviews ({stats.reviewsCount})
                    </CardTitle>
                    <Button variant="outline" size="sm" asChild>
                      <Link href="/tools">
                        <Plus className="w-4 h-4 mr-2" />
                        Review More Tools
                      </Link>
                    </Button>
                  </div>
                </CardHeader>
                <CardContent>
                  {isLoading ? (
                    <div className="space-y-4">
                      {[...Array(5)].map((_, i) => (
                        <div key={i} className="space-y-3 p-4 border border-gray-200 dark:border-gray-700 rounded-lg">
                          <div className="flex items-center gap-3">
                            <div className="w-10 h-10 bg-gray-200 dark:bg-gray-700 rounded-lg animate-pulse" />
                            <div className="flex-1 space-y-2">
                              <div className="h-4 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                              <div className="h-3 bg-gray-200 dark:bg-gray-700 rounded w-1/2 animate-pulse" />
                            </div>
                          </div>
                          <div className="h-16 bg-gray-200 dark:bg-gray-700 rounded animate-pulse" />
                        </div>
                      ))}
                    </div>
                  ) : recentReviews.length > 0 ? (
                    <div className="space-y-4">
                      {recentReviews.map((review) => (
                        <Card key={review.id} className="hover:shadow-sm transition-shadow">
                          <CardContent className="p-4">
                            <div className="flex items-start gap-3">
                              <div className="w-12 h-12 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center overflow-hidden flex-shrink-0">
                                {review.tools.logo_url ? (
                                  <img src={review.tools.logo_url} alt={review.tools.company_name} className="w-10 h-10 object-contain" />
                                ) : (
                                  <Star className="w-6 h-6 text-gray-400" />
                                )}
                              </div>
                              <div className="flex-1 min-w-0">
                                <div className="flex items-center justify-between mb-2">
                                  <h5 className="font-semibold text-gray-900 dark:text-white truncate">
                                    {review.tools.company_name}
                                  </h5>
                                  <span className="text-xs text-gray-500 flex items-center gap-1">
                                    <Clock className="w-3 h-3" />
                                    {formatDate(review.created_at)}
                                  </span>
                                </div>

                                <div className="flex items-center gap-1 mb-3">
                                  {[...Array(5)].map((_, i) => (
                                    <Star
                                      key={i}
                                      className={`w-4 h-4 ${
                                        i < review.rating
                                          ? 'text-yellow-400 fill-current'
                                          : 'text-gray-300 dark:text-gray-600'
                                      }`}
                                    />
                                  ))}
                                  <span className="text-sm font-medium text-gray-700 dark:text-gray-300 ml-2">
                                    {review.rating}/5
                                  </span>
                                </div>

                                {review.comment && (
                                  <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
                                    <p className="text-sm text-gray-600 dark:text-gray-400">
                                      "{review.comment}"
                                    </p>
                                  </div>
                                )}

                                <div className="flex items-center gap-2 mt-3">
                                  <Button variant="outline" size="sm" asChild>
                                    <Link href={`/Tool/${review.tools.slug}`}>
                                      <Eye className="w-3 h-3 mr-1" />
                                      View Tool
                                    </Link>
                                  </Button>
                                </div>
                              </div>
                            </div>
                          </CardContent>
                        </Card>
                      ))}
                    </div>
                  ) : (
                    <div className="text-center py-12">
                      <Star className="w-16 h-16 text-gray-300 dark:text-gray-600 mx-auto mb-4" />
                      <h3 className="text-lg font-semibold text-gray-900 dark:text-white mb-2">No reviews yet</h3>
                      <p className="text-gray-600 dark:text-gray-400 mb-6">Share your experience with AI tools!</p>
                      <Button asChild>
                        <Link href="/tools">
                          <Star className="w-4 h-4 mr-2" />
                          Start Reviewing
                        </Link>
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Account Tab */}
            <TabsContent value="account" className="space-y-6">
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <CardHeader className="pb-4">
                  <div className="flex items-center justify-between">
                    <CardTitle className="text-xl font-semibold flex items-center gap-2">
                      <User className="w-5 h-5 text-blue-500" />
                      Account Information
                    </CardTitle>
                    <Button
                      variant={isEditing ? "default" : "outline"}
                      size="sm"
                      onClick={() => {
                        if (isEditing) {
                          updateProfile()
                        } else {
                          setIsEditing(true)
                        }
                      }}
                      disabled={isSaving}
                    >
                      {isSaving ? (
                        <LoadingSpinner size={16} />
                      ) : isEditing ? (
                        <>
                          <Save className="w-4 h-4 mr-2" />
                          Save Changes
                        </>
                      ) : (
                        <>
                          <Edit className="w-4 h-4 mr-2" />
                          Edit Profile
                        </>
                      )}
                    </Button>
                  </div>
                </CardHeader>
                <CardContent className="space-y-6">
                  <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                    {/* Profile Picture */}
                    <div className="space-y-4">
                      <Label className="text-sm font-medium">Profile Picture</Label>
                      <div className="flex items-center gap-4">
                        <div className="w-20 h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-full flex items-center justify-center overflow-hidden">
                          {profileData.avatar_url ? (
                            <img src={profileData.avatar_url} alt="Profile" className="w-full h-full object-cover" />
                          ) : (
                            <User className="w-10 h-10 text-white" />
                          )}
                        </div>
                        {isEditing && (
                          <div className="flex-1">
                            <Input
                              placeholder="Avatar URL"
                              value={profileData.avatar_url}
                              onChange={(e) => setProfileData(prev => ({ ...prev, avatar_url: e.target.value }))}
                            />
                          </div>
                        )}
                      </div>
                    </div>

                    {/* Basic Info */}
                    <div className="space-y-4">
                      <div>
                        <Label className="text-sm font-medium">Full Name</Label>
                        {isEditing ? (
                          <Input
                            value={profileData.full_name}
                            onChange={(e) => setProfileData(prev => ({ ...prev, full_name: e.target.value }))}
                            placeholder="Enter your full name"
                          />
                        ) : (
                          <p className="mt-1 text-gray-900 dark:text-white">{profileData.full_name || 'Not set'}</p>
                        )}
                      </div>

                      <div>
                        <Label className="text-sm font-medium">Email</Label>
                        <p className="mt-1 text-gray-600 dark:text-gray-400 flex items-center gap-2">
                          <Mail className="w-4 h-4" />
                          {profileData.email}
                        </p>
                      </div>
                    </div>
                  </div>

                  {/* Bio */}
                  <div>
                    <Label className="text-sm font-medium">Bio</Label>
                    {isEditing ? (
                      <Textarea
                        value={profileData.bio}
                        onChange={(e) => setProfileData(prev => ({ ...prev, bio: e.target.value }))}
                        placeholder="Tell us about yourself..."
                        rows={3}
                      />
                    ) : (
                      <p className="mt-1 text-gray-900 dark:text-white">{profileData.bio || 'No bio added yet'}</p>
                    )}
                  </div>

                  {isEditing && (
                    <div className="flex items-center gap-3 pt-4 border-t border-gray-200 dark:border-gray-700">
                      <Button
                        variant="outline"
                        onClick={() => {
                          setIsEditing(false)
                          // Reset form data
                        }}
                      >
                        Cancel
                      </Button>
                      <Button
                        onClick={updateProfile}
                        disabled={isSaving}
                        className="bg-gradient-to-r from-blue-500 to-purple-600 hover:from-blue-600 hover:to-purple-700"
                      >
                        {isSaving ? (
                          <>
                            <LoadingSpinner size={16} />
                            Saving...
                          </>
                        ) : (
                          <>
                            <Save className="w-4 h-4 mr-2" />
                            Save Changes
                          </>
                        )}
                      </Button>
                    </div>
                  )}
                </CardContent>
              </Card>
            </TabsContent>

            {/* Settings Tab */}
            <TabsContent value="settings" className="space-y-6">
              {/* Notification Settings */}
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold flex items-center gap-2">
                    <Bell className="w-5 h-5 text-green-500" />
                    Notification Preferences
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Push Notifications</Label>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Receive notifications about new tools and updates</p>
                    </div>
                    <Switch
                      checked={profileData.notifications_enabled}
                      onCheckedChange={(checked) => setProfileData(prev => ({ ...prev, notifications_enabled: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Email Updates</Label>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Get weekly updates about new AI tools</p>
                    </div>
                    <Switch
                      checked={profileData.email_updates}
                      onCheckedChange={(checked) => setProfileData(prev => ({ ...prev, email_updates: checked }))}
                    />
                  </div>

                  <div className="flex items-center justify-between">
                    <div>
                      <Label className="text-sm font-medium">Marketing Emails</Label>
                      <p className="text-xs text-gray-600 dark:text-gray-400">Receive promotional content and special offers</p>
                    </div>
                    <Switch
                      checked={profileData.marketing_emails}
                      onCheckedChange={(checked) => setProfileData(prev => ({ ...prev, marketing_emails: checked }))}
                    />
                  </div>
                </CardContent>
              </Card>

              {/* Data Management */}
              <Card className="bg-white dark:bg-gray-800 border border-gray-200 dark:border-gray-700">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold flex items-center gap-2">
                    <Download className="w-5 h-5 text-blue-500" />
                    Data Management
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-blue-50 dark:bg-blue-900/20 rounded-lg">
                    <div>
                      <h4 className="font-medium text-blue-900 dark:text-blue-100">Export Your Data</h4>
                      <p className="text-sm text-blue-700 dark:text-blue-300">Download all your favorites, reviews, and account data</p>
                    </div>
                    <Button variant="outline" size="sm">
                      <Download className="w-4 h-4 mr-2" />
                      Export
                    </Button>
                  </div>
                </CardContent>
              </Card>

              {/* Danger Zone */}
              <Card className="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800">
                <CardHeader>
                  <CardTitle className="text-lg font-semibold flex items-center gap-2 text-red-700 dark:text-red-300">
                    <Shield className="w-5 h-5" />
                    Danger Zone
                  </CardTitle>
                </CardHeader>
                <CardContent className="space-y-4">
                  <div className="flex items-center justify-between p-4 bg-red-100 dark:bg-red-900/30 rounded-lg">
                    <div>
                      <h4 className="font-medium text-red-900 dark:text-red-100">Delete Account</h4>
                      <p className="text-sm text-red-700 dark:text-red-300">Permanently delete your account and all associated data</p>
                    </div>
                    <Button
                      variant="destructive"
                      size="sm"
                      onClick={deleteAccount}
                    >
                      <Trash2 className="w-4 h-4 mr-2" />
                      Delete Account
                    </Button>
                  </div>
                </CardContent>
              </Card>
            </TabsContent>
          </Tabs>
        </MotionWrapper>
      </div>
    </div>
  )
}
