'use client'

import { useEffect } from 'react'
import { usePathname } from 'next/navigation'
import { useGoogleAnalytics } from './GoogleAnalytics'

export default function AutoTracker() {
  const pathname = usePathname()
  const { trackPageView, trackScrollDepth, trackTimeOnPage, trackUserInteraction, trackOutboundLink } = useGoogleAnalytics()

  useEffect(() => {
    // Track page view on route change
    trackPageView(window.location.href, document.title)
  }, [pathname, trackPageView])

  useEffect(() => {
    let startTime = Date.now()
    let scrollDepthTracked = new Set<number>()

    // Track scroll depth
    const handleScroll = () => {
      const scrollPercent = Math.round(
        (window.scrollY / (document.documentElement.scrollHeight - window.innerHeight)) * 100
      )

      // Track at 25%, 50%, 75%, 90% intervals
      const milestones = [25, 50, 75, 90]
      milestones.forEach(milestone => {
        if (scrollPercent >= milestone && !scrollDepthTracked.has(milestone)) {
          trackScrollDepth(milestone)
          scrollDepthTracked.add(milestone)
        }
      })
    }

    // Track clicks on important elements
    const handleClick = (event: MouseEvent) => {
      const target = event.target as HTMLElement
      
      // Track tool card clicks
      if (target.closest('[data-tool-card]')) {
        const toolCard = target.closest('[data-tool-card]')
        const toolName = toolCard?.getAttribute('data-tool-name') || 'Unknown'
        const toolSlug = toolCard?.getAttribute('data-tool-slug') || 'unknown'
        trackUserInteraction('tool_card_click', 'tool_card', `${toolName} (${toolSlug})`)
      }

      // Track category clicks
      if (target.closest('[data-category]')) {
        const category = target.closest('[data-category]')?.getAttribute('data-category')
        trackUserInteraction('category_click', 'category', category || 'unknown')
      }

      // Track search interactions
      if (target.closest('[data-search]')) {
        trackUserInteraction('search_interaction', 'search', 'search_click')
      }

      // Track navigation clicks
      if (target.closest('nav') || target.closest('[data-nav]')) {
        const linkText = target.textContent?.trim() || 'nav_link'
        trackUserInteraction('navigation_click', 'navigation', linkText)
      }

      // Track button clicks
      if (target.tagName === 'BUTTON' || target.closest('button')) {
        const button = target.tagName === 'BUTTON' ? target : target.closest('button')
        const buttonText = button?.textContent?.trim() || 'button'
        trackUserInteraction('button_click', 'button', buttonText)
      }

      // Track outbound links
      if (target.tagName === 'A' || target.closest('a')) {
        const link = target.tagName === 'A' ? target as HTMLAnchorElement : target.closest('a')
        if (link && link.href && !link.href.includes(window.location.hostname)) {
          trackOutboundLink(link.href, link.textContent?.trim())
        }
      }
    }

    // Track time on page when user leaves
    const handleBeforeUnload = () => {
      const timeSpent = Math.round((Date.now() - startTime) / 1000)
      if (timeSpent > 5) { // Only track if user spent more than 5 seconds
        trackTimeOnPage(timeSpent, pathname)
      }
    }

    // Track form submissions
    const handleFormSubmit = (event: Event) => {
      const form = event.target as HTMLFormElement
      const formName = form.getAttribute('data-form-name') || form.id || 'unknown_form'
      trackUserInteraction('form_submit', 'form', formName)
    }

    // Add event listeners
    window.addEventListener('scroll', handleScroll, { passive: true })
    document.addEventListener('click', handleClick)
    window.addEventListener('beforeunload', handleBeforeUnload)
    document.addEventListener('submit', handleFormSubmit)

    // Cleanup
    return () => {
      window.removeEventListener('scroll', handleScroll)
      document.removeEventListener('click', handleClick)
      window.removeEventListener('beforeunload', handleBeforeUnload)
      document.removeEventListener('submit', handleFormSubmit)
    }
  }, [pathname, trackScrollDepth, trackTimeOnPage, trackUserInteraction, trackOutboundLink])

  return null // This component doesn't render anything
}
