'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { createBrowserClient } from '@/lib/supabase/client-utils'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { LoadingSpinner } from '@/components/loading-spinner'
import { ReviewItem } from './ReviewItem'
import { MessageSquare, Search, Star } from 'lucide-react'
import { toast } from 'sonner'

interface UserReview {
  id: string
  tool_id: number
  rating: number
  comment: string
  created_at: string
  updated_at: string
  tool: {
    id: number
    name: string
    logo_url: string
    slug: string
  }
}

export function UserReviewsTab() {
  const [user, setUser] = useState<any>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [reviews, setReviews] = useState<UserReview[]>([])
  const [filteredReviews, setFilteredReviews] = useState<UserReview[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedRating, setSelectedRating] = useState('all')

  const supabase = createBrowserClient()

  useEffect(() => {
    // Get real user data
    const fetchUser = async () => {
      if (!supabase) return

      try {
        const { data: { user: authUser }, error } = await supabase.auth.getUser()

        if (error || !authUser) {
          // For development, use mock user
          const mockUser = {
            id: 'dev-user-123',
            email: '<EMAIL>',
            name: 'Development User'
          }
          setUser(mockUser)
          setIsAuthenticated(true)
        } else {
          setUser(authUser)
          setIsAuthenticated(true)
        }
      } catch (error) {
        console.error('Error fetching user:', error)
        // Use mock user for development
        const mockUser = {
          id: 'dev-user-123',
          email: '<EMAIL>',
          name: 'Development User'
        }
        setUser(mockUser)
        setIsAuthenticated(true)
      }
    }

    fetchUser()
  }, [supabase])

  useEffect(() => {
    if (user && isAuthenticated) {
      fetchUserReviews()
    }
  }, [user, isAuthenticated])

  useEffect(() => {
    filterReviews()
  }, [reviews, searchQuery, selectedRating])

  const fetchUserReviews = async () => {
    if (!user || !supabase) return

    try {
      setIsLoading(true)

      const { data, error } = await supabase
        .from('reviews')
        .select(`
          id,
          tool_id,
          rating,
          comment,
          created_at,
          updated_at,
          tools (
            id,
            company_name,
            logo_url,
            slug
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.warn('Error fetching user reviews:', error.message)
        setReviews([])
        return
      }

      // Process the data to match expected format
      const processedData = data?.map(item => ({
        id: item.id,
        tool_id: item.tool_id,
        rating: item.rating,
        comment: item.comment,
        created_at: item.created_at,
        updated_at: item.updated_at,
        tool: {
          id: item.tools?.id || 0,
          name: item.tools?.company_name || 'Unknown Tool',
          logo_url: item.tools?.logo_url || '',
          slug: item.tools?.slug || ''
        }
      })).filter(item => item.tool.id) || []

      setReviews(processedData)
    } catch (error: any) {
      console.warn('Error fetching user reviews:', error.message)
      setReviews([])
      // Don't show error toast for missing data, just log it
    } finally {
      setIsLoading(false)
    }
  }

  const filterReviews = () => {
    let filtered = reviews

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(review =>
        review.tool.name.toLowerCase().includes(searchQuery.toLowerCase()) ||
        review.comment.toLowerCase().includes(searchQuery.toLowerCase())
      )
    }

    // Filter by rating
    if (selectedRating !== 'all') {
      filtered = filtered.filter(review =>
        review.rating === parseInt(selectedRating)
      )
    }

    setFilteredReviews(filtered)
  }

  const handleDeleteReview = async (reviewId: string) => {
    if (!supabase) return

    try {
      const { error } = await supabase
        .from('reviews')
        .delete()
        .eq('id', reviewId)

      if (error) {
        console.warn('Error deleting review:', error.message)
        toast.error('Failed to delete review')
        return
      }

      setReviews(prev => prev.filter(review => review.id !== reviewId))
      toast.success('Review deleted successfully')
    } catch (error: any) {
      console.warn('Error deleting review:', error.message)
      toast.error('Failed to delete review')
    }
  }

  const handleUpdateReview = async (reviewId: string, rating: number, comment: string) => {
    if (!supabase) return

    try {
      const { error } = await supabase
        .from('reviews')
        .update({
          rating,
          comment,
          updated_at: new Date().toISOString()
        })
        .eq('id', reviewId)

      if (error) {
        console.warn('Error updating review:', error.message)
        toast.error('Failed to update review')
        return
      }

      setReviews(prev => prev.map(review =>
        review.id === reviewId
          ? { ...review, rating, comment, updated_at: new Date().toISOString() }
          : review
      ))
      toast.success('Review updated successfully')
    } catch (error: any) {
      console.warn('Error updating review:', error.message)
      toast.error('Failed to update review')
    }
  }

  const averageRating = reviews.length > 0
    ? reviews.reduce((sum, review) => sum + review.rating, 0) / reviews.length
    : 0

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner size={32} />
        <span className="ml-2 text-muted-foreground">Loading reviews...</span>
      </div>
    )
  }

  return (
    <div className="space-y-6">
      <Card>
        <CardHeader>
          <CardTitle className="flex items-center gap-2">
            <MessageSquare className="h-5 w-5" />
            My Reviews ({reviews.length})
          </CardTitle>
          <CardDescription>
            Reviews you've written for AI tools
          </CardDescription>
          {reviews.length > 0 && (
            <div className="flex items-center gap-2 text-sm text-muted-foreground">
              <Star className="h-4 w-4 fill-yellow-400 text-yellow-400" />
              <span>Average rating: {averageRating.toFixed(1)}</span>
            </div>
          )}
        </CardHeader>
        <CardContent>
          {reviews.length > 0 && (
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search reviews..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="relative">
                <Star className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <select
                  value={selectedRating}
                  onChange={(e) => setSelectedRating(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  <option value="all">All Ratings</option>
                  <option value="5">5 Stars</option>
                  <option value="4">4 Stars</option>
                  <option value="3">3 Stars</option>
                  <option value="2">2 Stars</option>
                  <option value="1">1 Star</option>
                </select>
              </div>
            </div>
          )}

          {reviews.length === 0 ? (
            <div className="text-center py-8">
              <MessageSquare className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No reviews yet</h3>
              <p className="text-muted-foreground mb-4">
                Start exploring tools and share your experience with others
              </p>
              <Button asChild>
                <a href="/tools">Browse Tools</a>
              </Button>
            </div>
          ) : filteredReviews.length === 0 ? (
            <div className="text-center py-8">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No reviews found</h3>
              <p className="text-muted-foreground">
                Try adjusting your search or filter criteria
              </p>
            </div>
          ) : (
            <div className="space-y-4">
              {filteredReviews.map((review) => (
                <ReviewItem
                  key={review.id}
                  review={review}
                  onDelete={() => handleDeleteReview(review.id)}
                  onUpdate={(rating, comment) => handleUpdateReview(review.id, rating, comment)}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
    </div>
  )
}
