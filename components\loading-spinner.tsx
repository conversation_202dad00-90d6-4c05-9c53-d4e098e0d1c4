import { cn } from '@/lib/utils'

interface LoadingSpinnerProps {
  size?: number
  className?: string
}

export function LoadingSpinner({ size = 32, className }: LoadingSpinnerProps) {
  return (
    <div
      className={cn("animate-spin rounded-full border-b-2 border-primary", className)}
      style={{ width: size, height: size }}
    />
  )
}

export default function LoadingSpinnerDefault() {
  return (
    <div className="flex justify-center py-12">
      <LoadingSpinner />
    </div>
  )
}
