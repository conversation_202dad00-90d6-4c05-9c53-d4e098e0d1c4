"use client"

import { useState, useEffect } from "react"
import ToolCard from "@/components/tool-card"
import { createBrowserClient } from "@/lib/supabase/client-utils"

interface SearchResultsClientProps {
  query: string
  category: string
}

export default function SearchResultsClient({ query, category }: SearchResultsClientProps) {
  const [tools, setTools] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchSearchResults = async () => {
      setIsLoading(true)
      const supabase = createBrowserClient()

      try {
        let supabaseQuery = supabase.from("tools").select("*")

        if (query) {
          supabaseQuery = supabaseQuery.or(
            `company_name.ilike.%${query}%,short_description.ilike.%${query}%,full_description.ilike.%${query}%`,
          )
        }

        if (category && category !== "all") {
          supabaseQuery = supabaseQuery.eq("primary_task", category)
        }

        const { data, error: supabaseError } = await supabaseQuery
          .order("is_featured", { ascending: false })
          .order("click_count", { ascending: false })

        if (supabaseError) throw supabaseError

        setTools(data || [])
      } catch (err: any) {
        console.error("Error searching tools:", err.message)
        setError("Error searching tools. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchSearchResults()
  }, [query, category])

  if (isLoading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12 bg-white dark:bg-slate-800 rounded-lg shadow-sm">
        <p className="text-slate-600 dark:text-slate-400">{error}</p>
      </div>
    )
  }

  if (!tools || tools.length === 0) {
    return (
      <div className="text-center py-12 bg-white dark:bg-slate-800 rounded-lg shadow-sm">
        <h2 className="text-2xl font-bold mb-2">No results found</h2>
        <p className="text-slate-600 dark:text-slate-400">
          Try adjusting your search or filter to find what you're looking for.
        </p>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-2xl font-bold">
          {tools.length} {tools.length === 1 ? "result" : "results"} found
        </h2>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {tools.map((tool) => (
          <ToolCard key={tool.id} tool={tool} />
        ))}
      </div>
    </div>
  )
}
