# See https://help.github.com/articles/ignoring-files/ for more about ignoring files.

# dependencies
/node_modules
/.pnp
.pnp.js

# testing
/coverage

# next.js
/.next/
/out/

# production
/build

# misc
.DS_Store
*.pem

# debug
npm-debug.log*
yarn-debug.log*
yarn-error.log*
.pnpm-debug.log*

# local env files
.env*.local
.env

# vercel
.vercel

# typescript
*.tsbuildinfo
next-env.d.ts

# IDE
.vscode/
.idea/
*.swp
*.swo

# OS
Thumbs.db

# Temporary files
*.tmp
*.temp
*.log

# Backup files
*_backup/
*-backup/
*.bak
*.backup

# Documentation temp files
*_TEMP.md
*_OLD.md
TEMP_*.md

# Certificates
certificates/
*.pem
*.key
*.crt

# Scripts
*.bat
*.sh

# CSV data files
*.csv