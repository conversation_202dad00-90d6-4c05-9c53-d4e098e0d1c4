"use client"

import type React from "react"

import { useState, useEffect, useRef } from "react"
import { useRouter } from "next/navigation"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Search, ArrowRight, Sparkles, Filter, ChevronDown, ChevronUp, X, Check, Star, Calendar, Sparkle, Clock } from "lucide-react"
import { motion, AnimatePresence } from "@/lib/motion-stub"
import { 
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import Link from "next/link"
import DynamicStats from "@/components/DynamicStats"

// Define a specific type for suggestions
interface Suggestion {
  id: number
  company_name: string
  detail_url: string | null
  slug: string | null
  is_fallback: boolean
}

export default function Hero() {
  const [searchQuery, setSearchQuery] = useState("")
  const [showSuggestions, setShowSuggestions] = useState(false)
  const [suggestions, setSuggestions] = useState<Suggestion[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const router = useRouter()
  const [showAdvancedFilters, setShowAdvancedFilters] = useState(false)
  const searchInputRef = useRef<HTMLInputElement>(null)
  const [dbCategories, setDbCategories] = useState<string[]>([])
  const [loadingCategories, setLoadingCategories] = useState(true)
  
  // Advanced filter states
  const [category, setCategory] = useState("all")
  const [pricing, setPricing] = useState("all")
  const [rating, setRating] = useState([0])
  
  // Animation states
  const [currentPhrase, setCurrentPhrase] = useState(0)
  const [isTyping, setIsTyping] = useState(true)
  const phrases = ["content creation", "copywriting", "article research", "editing assistance", "SEO optimization", "productivity"]
  
  // Fallback categories if database fetch fails - focused on writing tools
  const fallbackCategories = ["Popular", "Writing", "Content", "Copywriting", "SEO", "Grammar", "Research", "Editing", "Productivity"]
  
  // Modern gradient color classes (complete classes)
  const categoryStyles = [
    "hover:bg-gradient-to-r hover:from-blue-500 hover:to-indigo-500",
    "hover:bg-gradient-to-r hover:from-purple-500 hover:to-pink-500", 
    "hover:bg-gradient-to-r hover:from-emerald-500 hover:to-teal-500",
    "hover:bg-gradient-to-r hover:from-amber-500 hover:to-orange-500",
    "hover:bg-gradient-to-r hover:from-rose-500 hover:to-red-500",
    "hover:bg-gradient-to-r hover:from-cyan-500 hover:to-blue-500",
    "hover:bg-gradient-to-r hover:from-fuchsia-500 hover:to-purple-500",
    "hover:bg-gradient-to-r hover:from-lime-500 hover:to-green-500",
    "hover:bg-gradient-to-r hover:from-pink-500 hover:to-rose-500",
    "hover:bg-gradient-to-r hover:from-violet-500 hover:to-indigo-500",
    "hover:bg-gradient-to-r hover:from-yellow-500 hover:to-amber-500",
    "hover:bg-gradient-to-r hover:from-teal-500 hover:to-cyan-500"
  ];

  // Add category icons mapping with proper type - focused on writing categories
  const categoryIcons: Record<string, React.ReactNode> = {
    "popular": <Star className="w-3 h-3 mr-1" />,
    "writing": <Search className="w-3 h-3 mr-1" />,
    "content": <Sparkle className="w-3 h-3 mr-1" />,
    "productivity": <Clock className="w-3 h-3 mr-1" />,
    "copywriting": <ArrowRight className="w-3 h-3 mr-1" />,
    "seo": <Filter className="w-3 h-3 mr-1" />,
    "grammar": <Check className="w-3 h-3 mr-1" />,
    "research": <Calendar className="w-3 h-3 mr-1" />,
    "editing": <Sparkles className="w-3 h-3 mr-1" />
  };

  // Default icon for categories without a specific icon
  const defaultIcon = <Sparkle className="w-3 h-3 mr-1" />;

  // Fetch categories from database - directly from primary_task field in tools table
  useEffect(() => {
    const fetchCategories = async () => {
      setLoadingCategories(true);
      try {
        const supabase = createBrowserClient();
        
        // Query primary_task values directly from tools table
        const { data, error } = await supabase
          .from("tools")
          .select("primary_task")
          .not("primary_task", "is", null)
          .order("primary_task");
        
        if (error) {
          console.error("Error fetching primary_task values:", error);
          // Use fallback categories
          setDbCategories(shuffleArray([...fallbackCategories]));
          return;
        } 
        
        if (data && data.length > 0) {
          // Extract unique primary_task values
          const uniqueTasks = [...new Set(data.map(item => item.primary_task))];
          console.log("Fetched primary_task values:", uniqueTasks);
          setDbCategories(shuffleArray(uniqueTasks));
        } else {
          // Use fallback if no data
          setDbCategories(shuffleArray([...fallbackCategories]));
        }
      } catch (err) {
        console.error("Error in categories fetch:", err);
        setDbCategories(shuffleArray([...fallbackCategories]));
      } finally {
        setLoadingCategories(false);
      }
    };
    
    // Function to shuffle array elements
    const shuffleArray = (array: string[]) => {
      const newArray = [...array];
      for (let i = newArray.length - 1; i > 0; i--) {
        const j = Math.floor(Math.random() * (i + 1));
        [newArray[i], newArray[j]] = [newArray[j], newArray[i]];
      }
      return newArray;
    };
    
    fetchCategories();
  }, []);

  // Control the typing effect
  useEffect(() => {
    const typingTimer = setTimeout(() => {
      setIsTyping(false)
    }, 2000)
    
    const changeTimer = setTimeout(() => {
      setIsTyping(true)
      setCurrentPhrase((prev) => (prev + 1) % phrases.length)
    }, 3000)
    
    return () => {
      clearTimeout(typingTimer)
      clearTimeout(changeTimer)
    }
  }, [currentPhrase, phrases.length])

  // Fallback suggestions if database query fails
  const fallbackSuggestions = [
    "ChatGPT", "Midjourney", "DALL-E", "Stable Diffusion", "Claude",
    "Anthropic", "Jasper", "Synthesia", "RunwayML", "Hugging Face",
    "GPT-4", "Copy.ai", "Replika", "Krisp", "Notion AI",
    "Perplexity", "Bard", "Gemini", "GitHub Copilot", "Canva AI"
  ];

  // Function to fetch suggestions from database
  const fetchSuggestionsFromDB = async (query: string): Promise<Suggestion[]> => {
    if (!query || query.length < 1) return [];
    
    setIsLoading(true);
    try {
      const supabase = createBrowserClient();
      
      // Query the database for tools with more data
      const { data, error } = await supabase
        .from("tools")
        .select("id, company_name, detail_url, slug")
        .ilike("company_name", `%${query}%`)
        .limit(5);
      
      if (error) {
        console.error("Error fetching suggestions:", error);
        // Return filtered fallback suggestions if there's an error
        return fallbackSuggestions.filter(suggestion => 
          suggestion.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5).map(name => ({ 
          id: 0, 
          company_name: name, 
          detail_url: null,
          slug: null,
          is_fallback: true
        }));
      }
      
      if (data && data.length > 0) {
        // Add a flag to indicate these are from database
        return data.map(item => ({
          ...item,
          is_fallback: false
        }));
      } else {
        // If no results, use fallback with disclaimer
        return fallbackSuggestions.filter(suggestion => 
          suggestion.toLowerCase().includes(query.toLowerCase())
        ).slice(0, 5).map(name => ({ 
          id: 0, 
          company_name: name, 
          detail_url: null,
          slug: null,
          is_fallback: true
        }));
      }
    } catch (err) {
      console.error("Error in database connection:", err);
      // Return filtered fallback suggestions
      return fallbackSuggestions.filter(suggestion => 
        suggestion.toLowerCase().includes(query.toLowerCase())
      ).slice(0, 5).map(name => ({ 
        id: 0, 
        company_name: name, 
        detail_url: null,
        slug: null,
        is_fallback: true
      }));
    } finally {
      setIsLoading(false);
    }
  };

  // Debounce function to prevent excessive database calls
  const useDebounce = (value: string, delay: number) => {
    const [debouncedValue, setDebouncedValue] = useState(value);
    
    useEffect(() => {
      const timer = setTimeout(() => {
        setDebouncedValue(value);
      }, delay);
      
      return () => {
        clearTimeout(timer);
      };
    }, [value, delay]);
    
    return debouncedValue;
  };

  // Use debounce to limit API calls
  const debouncedQuery = useDebounce(searchQuery, 150);

  // Fetch suggestions when debounced query changes
  useEffect(() => {
    const getSuggestions = async () => {
      if (!debouncedQuery) {
        setSuggestions([]);
        return;
      }
      
      const results = await fetchSuggestionsFromDB(debouncedQuery);
      setSuggestions(results);
    };
    
    getSuggestions();
  }, [debouncedQuery]);

  // Handle search input change
  const handleSearchInputChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const query = e.target.value;
    setSearchQuery(query);
    setShowSuggestions(true);
  };

  // Handle click outside to close suggestions
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (
        searchInputRef.current &&
        !searchInputRef.current.contains(event.target as Node)
      ) {
        setShowSuggestions(false);
      }
    };

    document.addEventListener("mousedown", handleClickOutside);
    return () => {
      document.removeEventListener("mousedown", handleClickOutside);
    };
  }, []);

  const handleSearch = (e: React.FormEvent) => {
    e.preventDefault()
    // Include advanced filters in the search query if open
    if (searchQuery.trim()) {
      let searchParams = `q=${encodeURIComponent(searchQuery.trim())}`
      if (showAdvancedFilters) {
        if (category !== "all") searchParams += `&category=${encodeURIComponent(category)}`
        if (pricing !== "all") searchParams += `&pricing=${encodeURIComponent(pricing)}`
        if (rating[0] > 0) searchParams += `&rating=${rating[0]}`
      }
      router.push(`/search?${searchParams}`)
    }
  }

  // Helper function to create and validate URL
  const createToolUrl = (suggestion: any, index: number) => {
    let path = '';
    
    // Create URL based on available data
    if (!suggestion.is_fallback) {
      if (suggestion.slug) {
        path = `/Tool/${suggestion.slug}`;
        console.log(`Using slug URL for suggestion ${index}:`, path);
      } else if (suggestion.id) {
        // For items from database but no slug
        if (suggestion.company_name) {
          // Create clean slug from company name
          const cleanSlug = suggestion.company_name
            .toLowerCase()
            .replace(/[^a-z0-9]+/g, '-')
            .replace(/-+/g, '-')
            .replace(/^-|-$/g, '');
          
          path = `/Tool/${cleanSlug}-${suggestion.id}`;
          console.log(`Created slug URL from name+id for suggestion ${index}:`, path);
        } else {
          path = `/Tool/${suggestion.id}`;
          console.log(`Using ID URL for suggestion ${index}:`, path);
        }
      } else {
        path = `/search?q=${encodeURIComponent(suggestion.company_name.trim())}`;
        console.log(`Using search URL for suggestion ${index}:`, path);
      }
    } else {
      path = `/search?q=${encodeURIComponent(suggestion.company_name.trim())}`;
      console.log(`Using fallback search URL for suggestion ${index}:`, path);
    }
    
    console.log(`Final URL path: ${path}`);
    return path;
  };

  return (
    <div className="relative overflow-hidden">
      {/* Background with gradient */}
      <div className="absolute inset-0 bg-gradient-to-br from-blue-50 via-indigo-50 to-violet-50 dark:from-slate-950 dark:via-indigo-950/20 dark:to-slate-900 -z-10"></div>

      {/* Decorative elements */}
      <div className="absolute -top-24 -left-24 w-72 md:w-96 h-72 md:h-96 bg-blue-200 dark:bg-blue-900/20 rounded-full filter blur-3xl opacity-30 animate-pulse"></div>
      <div className="absolute -bottom-24 -right-24 w-72 md:w-96 h-72 md:h-96 bg-indigo-200 dark:bg-indigo-900/20 rounded-full filter blur-3xl opacity-30 animate-pulse" style={{ animationDelay: '2s' }}></div>

      {/* Hero Content */}
      <div className="container mx-auto px-3 md:px-4 py-8 md:py-12">
        <motion.div 
          className="max-w-4xl mx-auto text-center"
          initial={{ opacity: 0, y: 20 }}
          animate={{ opacity: 1, y: 0 }}
          transition={{ duration: 0.5 }}
        >
          <div className="inline-block mb-2 px-3 py-1 rounded-full bg-blue-100 dark:bg-blue-900/30 text-blue-600 dark:text-blue-400 text-xs sm:text-sm font-medium">
            <Sparkles className="inline h-3 w-3 sm:h-4 sm:w-4 mr-1" /> Discover AI-Powered Solutions
          </div>
          
          <h1 className="text-3xl sm:text-4xl md:text-5xl lg:text-6xl font-extrabold mb-6 tracking-tight text-center">
            <div className="inline-flex flex-wrap justify-center items-baseline gap-x-2">
              <span>AI tools for</span>
              <div className="relative inline-block w-auto">
                <AnimatePresence mode="wait">
                  <motion.span
                    key={currentPhrase}
                    initial={{ opacity: 0, y: 10 }}
                    animate={{ opacity: 1, y: 0, transition: { duration: 0.3 } }}
                    exit={{ opacity: 0, y: -10, transition: { duration: 0.3 } }}
                    className="bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 dark:from-blue-400 dark:via-indigo-400 dark:to-purple-400"
                  >
                    {phrases[currentPhrase]}
                  </motion.span>
                </AnimatePresence>
              </div>
            </div>
          </h1>
          
          <p className="text-sm sm:text-base md:text-lg text-slate-700 dark:text-slate-300 mb-3 max-w-xl mx-auto">
            AI Any Tool helps you discover, compare, and choose the best AI-powered tools for your specific needs.
          </p>

          <div className="max-w-3xl mx-auto space-y-2">
            <motion.form 
              onSubmit={handleSearch} 
              className="relative group"
              initial={{ scale: 0.95 }}
              animate={{ scale: 1 }}
              transition={{ delay: 0.15 }}
            >
              <div className="absolute inset-y-0 left-0 flex items-center pl-3 sm:pl-4 pointer-events-none">
                <Search className="h-4 w-4 sm:h-5 sm:w-5 text-blue-500 group-hover:text-blue-600 transition-colors duration-200" />
              </div>
              <Input
                ref={searchInputRef}
                type="search"
                placeholder="Search for AI tools..."
                className="pl-9 sm:pl-12 h-11 sm:h-12 rounded-xl sm:rounded-2xl pr-24 sm:pr-32 text-sm sm:text-base shadow-lg border-0 ring-1 ring-blue-200 dark:ring-blue-900/30 focus-visible:ring-2 focus-visible:ring-blue-500 dark:focus-visible:ring-blue-400 transition-all duration-200 bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm group-hover:shadow-blue-500/10 dark:group-hover:shadow-blue-400/10"
                value={searchQuery}
                onChange={handleSearchInputChange}
                onFocus={() => {
                  if (searchQuery) {
                    setShowSuggestions(true);
                  }
                }}
              />
              {showSuggestions && (isLoading || suggestions.length > 0) && (
                <div className="absolute z-10 mt-1 w-full bg-white dark:bg-slate-800 rounded-xl shadow-xl overflow-hidden border border-blue-100 dark:border-blue-900/30">
                  {isLoading ? (
                    <div className="px-3 py-2 text-slate-500 dark:text-slate-400 text-center text-sm">
                      <div className="flex items-center justify-center">
                        <div className="animate-spin h-3 w-3 border-2 border-primary border-t-transparent rounded-full mr-2"></div>
                        <span>Loading suggestions...</span>
                      </div>
                    </div>
                  ) : (
                    <ul className="max-h-48 sm:max-h-52 overflow-auto py-1">
                      {suggestions.map((suggestion, index) => {
                        // Use the existing createToolUrl function for navigation
                        const path = createToolUrl(suggestion, index);
                        
                        return (
                          <li key={index} className="transition-colors">
                            <div 
                              onClick={() => {
                                setSearchQuery(suggestion.company_name);
                                setShowSuggestions(false);
                                router.push(path);
                              }}
                              className="block px-3 py-1.5 hover:bg-blue-50 dark:hover:bg-blue-900/20 cursor-pointer text-slate-700 dark:text-slate-200 text-sm"
                            >
                              <div className="flex items-center">
                                <Search className="h-3 w-3 mr-2 text-slate-400" />
                                <span>{suggestion.company_name}</span>
                                {suggestion.is_fallback && (
                                  <span className="ml-auto text-xs text-slate-400">(search)</span>
                                )}
                              </div>
                            </div>
                          </li>
                        );
                      })}
                    </ul>
                  )}
                </div>
              )}
              <motion.div
                className="absolute right-1.5 sm:right-2 top-1.5 sm:top-2 bottom-1.5 sm:bottom-2 rounded-lg sm:rounded-xl bg-gradient-to-r from-blue-500 to-indigo-500 dark:from-blue-600 dark:to-indigo-600 overflow-hidden"
                whileHover={{ 
                  scale: 1.03,
                  boxShadow: "0 10px 15px -3px rgba(59, 130, 246, 0.3)",
                  transition: { duration: 0.2 } 
                }}
                whileTap={{ scale: 0.97 }}
              >
              <Button 
                type="submit" 
                  className="relative h-full px-3 sm:px-5 text-xs sm:text-sm font-medium border-0 text-white shadow-none bg-transparent z-10 flex items-center overflow-hidden group"
                >
                  <span className="relative z-10 flex items-center">
                    Search 
                    <motion.span
                      initial={{ x: 0 }}
                      animate={{ x: [0, 5, 0] }}
                      transition={{ 
                        duration: 1.5, 
                        repeat: Infinity, 
                        ease: "easeInOut",
                        repeatType: "loop" 
                      }}
                    >
                      <ArrowRight className="ml-1 sm:ml-2 h-3 w-3 sm:h-4 sm:w-4" />
                    </motion.span>
                  </span>
                  <motion.div 
                    className="absolute inset-0 bg-gradient-to-r from-blue-600 to-indigo-600 dark:from-blue-500 dark:to-indigo-500"
                    initial={{ x: "-100%" }}
                    whileHover={{ x: "0%" }}
                    transition={{ duration: 0.4 }}
                  />
              </Button>
              </motion.div>
            </motion.form>

            <motion.div 
              className="flex flex-wrap justify-center gap-1 sm:gap-1.5 overflow-hidden relative"
              initial={{ opacity: 1 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.2 }}
            >
              <div className="relative overflow-hidden flex-1 h-7 sm:h-8">
                <motion.div 
                  className="flex gap-1 sm:gap-1.5"
                  initial={{ x: "0%" }}
                  animate={{ x: "-100%" }}
                  transition={{ 
                    repeat: Infinity, 
                    duration: 60,
                    ease: "linear",
                    repeatType: "loop"
                  }}
                >
                  {loadingCategories ? (
                    // Show skeleton loaders while loading
                    Array(6).fill(0).map((_, i) => (
                      <motion.div 
                        key={`skeleton-${i}`}
                        initial={{ opacity: 1, scale: 1 }}
                        animate={{ opacity: 1, scale: 1 }}
                        className="h-6 sm:h-8 w-16 sm:w-20 rounded-full bg-slate-200 dark:bg-slate-700 animate-pulse"
                      />
                    ))
                  ) : (
                    // Duplicate the array to create a seamless loop
                    [...dbCategories, ...dbCategories].map((cat, index) => {
                      // Calculate a color index that wraps around
                      const styleIndex = index % categoryStyles.length;
                      const catLower = cat.toLowerCase();
                      const icon = categoryIcons[catLower] || defaultIcon;
                      
                      return (
                        <motion.div
                          key={`${cat}-${index}`}
                          whileHover={{ 
                            scale: 1.05, 
                            transition: { duration: 0.2 } 
                          }}
                          whileTap={{ scale: 0.95 }}
                          initial={{ opacity: 1, y: 0 }}
                          animate={{ opacity: 1, y: 0 }}
                          transition={{ 
                            type: "spring",
                            stiffness: 260,
                            damping: 20 
                          }}
            >
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => {
                  setCategory(catLower);
                  // Navigation with proper encoding and preserving original category name
                  const categoryPath = `/category/${encodeURIComponent(catLower)}`;
                  router.push(categoryPath);
                }}
                            className={`h-6 sm:h-8 text-xs px-2 sm:px-2.5 py-0 rounded-full bg-white/80 backdrop-blur-sm dark:bg-slate-800/80 hover:text-white hover:border-transparent dark:hover:text-white hover:shadow-lg border-slate-200 dark:border-slate-700 shadow-sm transition-all duration-300 ${categoryStyles[styleIndex]}`}
              >
                <span className="flex items-center">
                              <span className="hidden sm:inline">{icon}</span>
                  {cat}
                </span>
              </Button>
                        </motion.div>
                      );
                    })
                  )}
                </motion.div>
              </div>
              
              {/* All Categories Button - Styled link to categories page */}
              <div className="flex items-center gap-1 sm:gap-1.5 ml-1">
                <Link href="/categories" className="self-center">
                <Button 
                  variant="outline" 
                  size="sm" 
                    className="h-6 sm:h-8 text-xs px-2 sm:px-2.5 py-0 rounded-full bg-gradient-to-r from-purple-500/20 to-blue-500/20 hover:from-purple-500 hover:to-blue-500 hover:text-white border-purple-300 dark:border-purple-800 shadow-md backdrop-blur-sm transition-all duration-300 flex items-center gap-1"
                >
                    <Sparkles className="w-3 h-3 sm:w-3.5 sm:h-3.5" />
                    <span className="font-medium">All</span>
                    <ArrowRight className="w-3 h-3 sm:w-3.5 sm:h-3.5 hidden sm:inline" />
                </Button>
              </Link>
              
              <Button 
                variant="outline" 
                size="sm" 
                onClick={() => setShowAdvancedFilters(!showAdvancedFilters)}
                  className={`h-6 sm:h-8 text-xs px-2 sm:px-2.5 py-0 rounded-full border ${showAdvancedFilters ? 'bg-blue-100 dark:bg-blue-900/40 border-blue-300 dark:border-blue-800' : 'border-blue-200 dark:border-blue-900 bg-blue-50/50 dark:bg-blue-900/20 hover:bg-blue-100/50 dark:hover:bg-blue-900/30'} backdrop-blur-sm shadow-sm transform transition-all hover:-translate-y-0.5 hover:shadow-md`}
              >
                <span className="text-blue-600 dark:text-blue-400 font-medium">
                  <span className="flex items-center">
                      <Filter className="w-3 h-3 sm:w-3.5 sm:h-3.5 mr-1" />
                      <span>Filters</span>
                    {showAdvancedFilters ? (
                        <ChevronUp className="ml-1 w-3 h-3 sm:w-3.5 sm:h-3.5" />
                    ) : (
                        <ChevronDown className="ml-1 w-3 h-3 sm:w-3.5 sm:h-3.5" />
                    )}
                  </span>
                </span>
              </Button>
              </div>
            </motion.div>
            
            {/* Advanced Filters Section */}
            <AnimatePresence>
              {showAdvancedFilters && (
                <motion.div
                  initial={{ opacity: 0, height: 0, y: -10 }}
                  animate={{ opacity: 1, height: 'auto', y: 0 }}
                  exit={{ opacity: 0, height: 0, y: -10 }}
                  transition={{ duration: 0.3 }}
                  className="bg-white/90 dark:bg-slate-800/90 backdrop-blur-sm rounded-xl p-2.5 shadow-lg border border-slate-200/50 dark:border-slate-700/50 text-xs"
                >
                  <div className="grid sm:grid-cols-2 md:grid-cols-3 gap-2">
                    <div className="space-y-1">
                      <label className="font-medium text-slate-700 dark:text-slate-300 flex items-center">
                        <Sparkle className="w-3 h-3 sm:w-3.5 sm:h-3.5 mr-1 text-blue-500" />
                        Category
                      </label>
                      <Select value={category} onValueChange={setCategory}>
                        <SelectTrigger className="w-full h-7 sm:h-8 rounded-lg bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
                          <SelectValue placeholder="Select category" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Categories</SelectItem>
                          <SelectItem value="writing">Writing</SelectItem>
                          <SelectItem value="productivity">Productivity</SelectItem>
                          <SelectItem value="copywriting">Copywriting</SelectItem>
                          <SelectItem value="seo">SEO</SelectItem>
                          <SelectItem value="editing">Editing</SelectItem>
                        </SelectContent>
                      </Select>
                    </div>
                    
                    <div className="space-y-1">
                      <label className="font-medium text-slate-700 dark:text-slate-300 flex items-center">
                        <Calendar className="w-3 h-3 sm:w-3.5 sm:h-3.5 mr-1 text-blue-500" />
                        Pricing
                      </label>
                      <Select value={pricing} onValueChange={setPricing}>
                        <SelectTrigger className="w-full h-7 sm:h-8 rounded-lg bg-white dark:bg-slate-800 border-slate-200 dark:border-slate-700">
                          <SelectValue placeholder="Select pricing" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="all">All Pricing Options</SelectItem>
                          <SelectItem value="free">Free</SelectItem>
                          <SelectItem value="freemium">Freemium</SelectItem>
                          <SelectItem value="paid">Paid</SelectItem>
                          <SelectItem value="subscription">Subscription</SelectItem>
                        </SelectContent>
                      </Select>
            </div>
                    
                    <div className="space-y-1 sm:col-span-2 md:col-span-1">
                      <label className="font-medium text-slate-700 dark:text-slate-300 flex items-center">
                        <Star className="w-3 h-3 sm:w-3.5 sm:h-3.5 mr-1 text-blue-500" />
                        Minimum Rating
                      </label>
                      <div className="pt-1 px-2 flex items-center gap-2">
                        <Slider 
                          defaultValue={[0]} 
                          max={5} 
                          step={1}
                          value={rating}
                          onValueChange={setRating}
                          className="flex-1"
                        />
                        <span className="font-medium w-6 sm:w-7 text-center text-blue-600 dark:text-blue-400">
                          {rating[0]}★
                        </span>
          </div>
        </div>
                  </div>
                  
                  <div className="flex items-center justify-between mt-2 pt-1.5 border-t border-slate-200 dark:border-slate-700">
                    <Button 
                      variant="ghost"
                      size="sm"
                      className="h-6 sm:h-7 text-xs text-slate-600 dark:text-slate-400 hover:text-slate-900 dark:hover:text-white px-2"
                      onClick={() => {
                        setCategory("all");
                        setPricing("all");
                        setRating([0]);
                      }}
                    >
                      <X className="w-3 h-3 mr-1" />
                      Reset
                    </Button>
                    
                    <Button 
                      size="sm"
                      className="h-6 sm:h-7 text-xs rounded-lg bg-blue-500 text-white hover:bg-blue-600 dark:bg-blue-600 dark:hover:bg-blue-500 px-2"
                      onClick={handleSearch}
                    >
                      <Check className="w-3 h-3 mr-1" />
                      Apply
                    </Button>
                  </div>
                </motion.div>
              )}
            </AnimatePresence>
          </div>
        </motion.div>
      </div>

      {/* Stats Section */}
      <div className="container mx-auto px-3 md:px-4 -mt-3 sm:-mt-4 md:-mt-6">
        <DynamicStats />
      </div>
    </div>
  )
}
