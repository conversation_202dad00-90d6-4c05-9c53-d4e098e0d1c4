import {
  MessageSquare,
  ImageIcon,
  FileAudio,
  FileVideo,
  Database,
  Bot,
  Code,
  LineChart,
  Search,
  Sparkles,
} from "lucide-react"

export default function CategoryHeader({ category }: { category: string }) {
  const categoryInfo = getCategoryInfo(category)

  return (
    <div className="mb-12 text-center">
      <div className="inline-flex items-center justify-center p-4 mb-4 rounded-full bg-slate-100 dark:bg-slate-800">
        {categoryInfo.icon}
      </div>
      <h1 className="text-3xl md:text-4xl font-bold mb-4">{categoryInfo.title}</h1>
      <p className="text-lg text-slate-600 dark:text-slate-400 max-w-3xl mx-auto">{categoryInfo.description}</p>
    </div>
  )
}

function getCategoryInfo(category: string) {
  const normalizedCategory = category.toLowerCase()

  const categories: Record<string, { title: string; description: string; icon: JSX.Element }> = {
    "text generation": {
      title: "Text Generation AI Tools",
      description: "Discover AI tools that can generate human-like text for content creation, copywriting, and more.",
      icon: <MessageSquare className="h-8 w-8 text-blue-600 dark:text-blue-400" />,
    },
    "image generation": {
      title: "Image Generation AI Tools",
      description: "Explore AI tools that can create stunning images, artwork, and designs from text descriptions.",
      icon: <ImageIcon className="h-8 w-8 text-purple-600 dark:text-purple-400" />,
    },
    "audio generation": {
      title: "Audio Generation AI Tools",
      description: "Find AI tools that can generate music, sound effects, and voice content for your projects.",
      icon: <FileAudio className="h-8 w-8 text-green-600 dark:text-green-400" />,
    },
    "video generation": {
      title: "Video Generation AI Tools",
      description: "Discover AI tools that can create and edit videos, animations, and visual content.",
      icon: <FileVideo className="h-8 w-8 text-red-600 dark:text-red-400" />,
    },
    "data analysis": {
      title: "Data Analysis AI Tools",
      description: "Explore AI tools that can analyze and extract insights from your data.",
      icon: <Database className="h-8 w-8 text-amber-600 dark:text-amber-400" />,
    },
    chatbots: {
      title: "AI Chatbot Tools",
      description: "Find AI-powered chatbots and conversational agents for customer service and engagement.",
      icon: <Bot className="h-8 w-8 text-cyan-600 dark:text-cyan-400" />,
    },
    "code generation": {
      title: "Code Generation AI Tools",
      description: "Discover AI tools that can help you write, debug, and optimize code.",
      icon: <Code className="h-8 w-8 text-indigo-600 dark:text-indigo-400" />,
    },
    analytics: {
      title: "AI Analytics Tools",
      description: "Explore AI tools for advanced analytics, business intelligence, and data visualization.",
      icon: <LineChart className="h-8 w-8 text-emerald-600 dark:text-emerald-400" />,
    },
    research: {
      title: "AI Research Tools",
      description: "Find AI tools that can help with academic and scientific research, literature reviews, and more.",
      icon: <Search className="h-8 w-8 text-rose-600 dark:text-rose-400" />,
    },
  }

  return (
    categories[normalizedCategory] || {
      title: `${category.charAt(0).toUpperCase() + category.slice(1)} AI Tools`,
      description: `Discover the best AI tools for ${category.toLowerCase()} to enhance your productivity and workflow.`,
      icon: <Sparkles className="h-8 w-8 text-blue-600 dark:text-blue-400" />,
    }
  )
}
