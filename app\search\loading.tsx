import { Skeleton } from "@/components/ui/skeleton"
import { LoadingGrid } from "@/components/ui/loading-grid"

export default function SearchLoading() {
  return (
    <div className="min-h-screen bg-background pt-24 pb-16">
      <div className="container mx-auto px-4">
        <div className="max-w-4xl mx-auto mb-8">
          <Skeleton className="h-10 w-64 mb-6" />
          
          <div className="flex flex-col md:flex-row gap-4 mb-8">
            <Skeleton className="h-10 flex-grow" />
            <Skeleton className="h-10 w-64" />
            <Skeleton className="h-10 w-24" />
          </div>
          
          <div className="mt-8">
            <LoadingGrid count={6} columns={3} />
          </div>
        </div>
      </div>
    </div>
  )
}
