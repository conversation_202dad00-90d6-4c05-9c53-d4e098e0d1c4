"use client"

import { useState, useEffect } from "react"
import { motion, AnimatePresence } from "@/lib/motion-stub"

interface TextCyclerProps {
  texts: string[]
  interval?: number
  className?: string
}

export default function TextCycler({ texts, interval = 2500, className = "" }: TextCyclerProps) {
  const [currentIndex, setCurrentIndex] = useState(0)
  const [isTyping, setIsTyping] = useState(true)

  useEffect(() => {
    const typingTimer = setTimeout(() => {
      setIsTyping(false)
    }, interval * 0.6)
    
    const changeTimer = setTimeout(() => {
      setIsTyping(true)
      setCurrentIndex((prev) => (prev + 1) % texts.length)
    }, interval)
    
    return () => {
      clearTimeout(typingTimer)
      clearTimeout(changeTimer)
    }
  }, [currentIndex, interval, texts.length])

  return (
    <span className={`relative inline-block ${className}`}>
      <AnimatePresence mode="wait">
        <motion.span
          key={currentIndex}
          initial={{ opacity: 0, y: 10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          transition={{ duration: 0.3 }}
          className="bg-clip-text text-transparent bg-gradient-to-r from-blue-500 via-indigo-500 to-purple-500 dark:from-blue-400 dark:via-indigo-400 dark:to-purple-400"
        >
          {texts[currentIndex]}
        </motion.span>
      </AnimatePresence>
    </span>
  )
}
