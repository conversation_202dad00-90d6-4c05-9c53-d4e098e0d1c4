'use client'

import { usePathname } from 'next/navigation'

interface ConditionalLayoutProps {
  children: React.ReactNode
  header: React.ReactNode
  footer: React.ReactNode
}

export function ConditionalLayout({ children, header, footer }: ConditionalLayoutProps) {
  const pathname = usePathname()

  // For all routes, render with header/footer
  return (
    <>
      {header}
      <main className="flex-grow">{children}</main>
      {footer}
    </>
  )
}
