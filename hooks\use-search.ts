"use client"

import { useState, useEffect, useCallback, useMemo } from 'react'
import { useDebounce } from './use-debounce'
import { createBrowserClient } from '@/lib/supabase/client-utils'
import { toast } from 'sonner'

interface SearchFilters {
  query?: string
  category?: string
  pricing?: string[]
  features?: string[]
  sortBy?: 'featured' | 'rating' | 'recent' | 'popular' | 'name'
  sortOrder?: 'asc' | 'desc'
}

interface SearchResult {
  id: string | number
  company_name: string
  short_description?: string
  logo_url?: string
  slug?: string
  pricing?: string
  rating?: number
  is_featured?: boolean
  is_verified?: boolean
  is_new?: boolean
  primary_task?: string
  click_count?: number
  created_at?: string
  categories?: string[]
}

interface UseSearchReturn {
  results: SearchResult[]
  isLoading: boolean
  error: string | null
  hasMore: boolean
  totalCount: number
  search: (filters: SearchFilters) => Promise<void>
  loadMore: () => Promise<void>
  clearResults: () => void
  clearError: () => void
}

interface UseSearchOptions {
  initialFilters?: SearchFilters
  pageSize?: number
  debounceMs?: number
  enableCache?: boolean
  autoSearch?: boolean
}

export function useSearch(options: UseSearchOptions = {}): UseSearchReturn {
  const {
    initialFilters = {},
    pageSize = 12,
    debounceMs = 300,
    enableCache = true,
    autoSearch = true
  } = options

  const [results, setResults] = useState<SearchResult[]>([])
  const [isLoading, setIsLoading] = useState(false)
  const [error, setError] = useState<string | null>(null)
  const [hasMore, setHasMore] = useState(true)
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(0)
  const [currentFilters, setCurrentFilters] = useState<SearchFilters>(initialFilters)

  const debouncedQuery = useDebounce(currentFilters.query || '', debounceMs)

  // Cache for search results
  const cache = useMemo(() => new Map<string, {
    results: SearchResult[]
    totalCount: number
    timestamp: number
  }>(), [])

  const supabase = createBrowserClient()

  // Generate cache key from filters
  const getCacheKey = useCallback((filters: SearchFilters, page: number) => {
    return JSON.stringify({ ...filters, page })
  }, [])

  // Clear cache entries older than 5 minutes
  const cleanCache = useCallback(() => {
    const now = Date.now()
    const maxAge = 5 * 60 * 1000 // 5 minutes
    
    for (const [key, value] of cache.entries()) {
      if (now - value.timestamp > maxAge) {
        cache.delete(key)
      }
    }
  }, [cache])

  // Perform search with Supabase
  const performSearch = useCallback(async (
    filters: SearchFilters, 
    page: number = 0,
    append: boolean = false
  ): Promise<void> => {
    try {
      setIsLoading(true)
      setError(null)

      // Check cache first
      const cacheKey = getCacheKey(filters, page)
      if (enableCache && cache.has(cacheKey)) {
        const cached = cache.get(cacheKey)!
        if (append) {
          setResults(prev => [...prev, ...cached.results])
        } else {
          setResults(cached.results)
        }
        setTotalCount(cached.totalCount)
        setHasMore(cached.results.length === pageSize)
        setIsLoading(false)
        return
      }

      // Build query
      let query = supabase
        .from('tools')
        .select(`
          id,
          company_name,
          short_description,
          logo_url,
          slug,
          pricing,
          rating,
          is_featured,
          is_verified,
          is_new,
          primary_task,
          click_count,
          created_at
        `, { count: 'exact' })

      // Apply search filter
      if (filters.query && filters.query.trim()) {
        const searchTerm = filters.query.trim()
        query = query.or(
          `company_name.ilike.%${searchTerm}%,short_description.ilike.%${searchTerm}%,primary_task.ilike.%${searchTerm}%`
        )
      }

      // Apply category filter
      if (filters.category && filters.category !== 'all') {
        query = query.eq('primary_task', filters.category)
      }

      // Apply pricing filter
      if (filters.pricing && filters.pricing.length > 0) {
        query = query.in('pricing', filters.pricing)
      }

      // Apply sorting
      switch (filters.sortBy) {
        case 'rating':
          query = query.order('rating', { ascending: filters.sortOrder === 'asc' })
          break
        case 'recent':
          query = query.order('created_at', { ascending: false })
          break
        case 'popular':
          query = query.order('click_count', { ascending: false })
          break
        case 'name':
          query = query.order('company_name', { ascending: filters.sortOrder === 'asc' })
          break
        default:
          query = query.order('is_featured', { ascending: false })
                      .order('rating', { ascending: false })
      }

      // Apply pagination
      const from = page * pageSize
      const to = from + pageSize - 1
      query = query.range(from, to)

      const { data, error: searchError, count } = await query

      if (searchError) {
        throw new Error(searchError.message)
      }

      const searchResults = data || []
      const total = count || 0

      // Cache results
      if (enableCache) {
        cleanCache()
        cache.set(cacheKey, {
          results: searchResults,
          totalCount: total,
          timestamp: Date.now()
        })
      }

      // Update state
      if (append) {
        setResults(prev => [...prev, ...searchResults])
      } else {
        setResults(searchResults)
      }
      
      setTotalCount(total)
      setHasMore(searchResults.length === pageSize && (from + searchResults.length) < total)
      setCurrentPage(page)

    } catch (err) {
      const errorMessage = err instanceof Error ? err.message : 'Search failed'
      setError(errorMessage)
      toast.error('Search failed', {
        description: errorMessage
      })
    } finally {
      setIsLoading(false)
    }
  }, [supabase, pageSize, enableCache, cache, getCacheKey, cleanCache])

  // Main search function
  const search = useCallback(async (filters: SearchFilters) => {
    setCurrentFilters(filters)
    setCurrentPage(0)
    await performSearch(filters, 0, false)
  }, [performSearch])

  // Load more results
  const loadMore = useCallback(async () => {
    if (!hasMore || isLoading) return
    const nextPage = currentPage + 1
    await performSearch(currentFilters, nextPage, true)
  }, [hasMore, isLoading, currentPage, currentFilters, performSearch])

  // Clear results
  const clearResults = useCallback(() => {
    setResults([])
    setTotalCount(0)
    setHasMore(true)
    setCurrentPage(0)
    setError(null)
  }, [])

  // Clear error
  const clearError = useCallback(() => {
    setError(null)
  }, [])

  // Auto search when debounced query changes
  useEffect(() => {
    if (autoSearch && debouncedQuery !== currentFilters.query) {
      search({ ...currentFilters, query: debouncedQuery })
    }
  }, [debouncedQuery, autoSearch, search, currentFilters])

  return {
    results,
    isLoading,
    error,
    hasMore,
    totalCount,
    search,
    loadMore,
    clearResults,
    clearError
  }
}

// Hook for search suggestions
export function useSearchSuggestions(query: string, limit: number = 5) {
  const [suggestions, setSuggestions] = useState<string[]>([])
  const [isLoading, setIsLoading] = useState(false)
  
  const debouncedQuery = useDebounce(query, 200)
  const supabase = createBrowserClient()

  useEffect(() => {
    const fetchSuggestions = async () => {
      if (!debouncedQuery || debouncedQuery.length < 2) {
        setSuggestions([])
        return
      }

      try {
        setIsLoading(true)
        
        // Add timeout handling for suggestions
        const timeoutPromise = new Promise((_, reject) => {
          setTimeout(() => reject(new Error('Suggestions timeout')), 4000) // 4 second timeout
        })

        const queryPromise = supabase
          .from('tools')
          .select('company_name, primary_task')
          .or(`company_name.ilike.%${debouncedQuery}%,primary_task.ilike.%${debouncedQuery}%`)
          .limit(Math.min(limit, 30)) // Cap suggestions at 30

        const result = await Promise.race([queryPromise, timeoutPromise])
        const { data, error } = result as any

        if (error) throw error

        const uniqueSuggestions = Array.from(new Set([
          ...data.map(item => item.company_name),
          ...data.map(item => item.primary_task).filter(Boolean)
        ])).slice(0, limit)

        setSuggestions(uniqueSuggestions)
      } catch (err) {
        console.error('Failed to fetch suggestions:', err)
        setSuggestions([])
      } finally {
        setIsLoading(false)
      }
    }

    fetchSuggestions()
  }, [debouncedQuery, limit, supabase])

  return { suggestions, isLoading }
}
