'use client'

import { useEffect } from 'react'
import { useRouter } from 'next/navigation'
import { createBrowserClient } from '@/lib/supabase/client-utils'
import { Loader2 } from 'lucide-react'
import { toast } from 'sonner'

export default function AuthCallbackPage() {
  const router = useRouter()

  useEffect(() => {
    const handleAuthCallback = async () => {
      try {
        const supabase = createBrowserClient()
        
        if (!supabase) {
          throw new Error('Failed to initialize Supabase client')
        }

        // Handle the OAuth callback
        const { data, error } = await supabase.auth.getSession()

        if (error) {
          console.error('Auth callback error:', error)
          toast.error('Sign in error occurred')
          router.push('/auth?error=callback_error')
          return
        }

        if (data.session) {
          toast.success('Successfully signed in!')
          // Redirect to the intended page or home
          const urlParams = new URLSearchParams(window.location.search)
          const redirectTo = urlParams.get('redirect_to') || '/'
          router.push(redirectTo)
        } else {
          // No session found, redirect to auth page
          router.push('/auth')
        }
      } catch (error) {
        console.error('Unexpected error in auth callback:', error)
        toast.error('An unexpected error occurred')
        router.push('/auth?error=unexpected_error')
      }
    }

    handleAuthCallback()
  }, [router])

  return (
    <div className="min-h-screen flex items-center justify-center">
      <div className="text-center">
        <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
        <h2 className="text-lg font-semibold mb-2">Signing you in...</h2>
        <p className="text-muted-foreground">
          Please wait while we verify your identity
        </p>
      </div>
    </div>
  )
}
