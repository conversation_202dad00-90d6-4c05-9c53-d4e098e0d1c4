"use client"

import { useState, useCallback, memo } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>ge } from "@/components/ui/badge"
import { Input } from "@/components/ui/input"
import { Label } from "@/components/ui/label"
import { Checkbox } from "@/components/ui/checkbox"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Slider } from "@/components/ui/slider"
import { Sheet, Sheet<PERSON>ontent, SheetHeader, Sheet<PERSON>itle, SheetTrigger } from "@/components/ui/sheet"
import { Collapsible, CollapsibleContent, CollapsibleTrigger } from "@/components/ui/collapsible"
import { 
  Filter, 
  X, 
  ChevronDown, 
  ChevronUp, 
  Star, 
  DollarSign, 
  Calendar,
  Tag,
  Sparkles,
  SlidersHorizontal
} from "lucide-react"
import { cn } from "@/lib/utils"

export interface FilterOptions {
  categories: string[]
  pricing: string[]
  features: string[]
  rating: [number, number]
  sortBy: string
  sortOrder: 'asc' | 'desc'
  dateRange: string
  verified: boolean
  featured: boolean
  isNew: boolean
}

interface AdvancedFiltersProps {
  filters: FilterOptions
  onFiltersChange: (filters: FilterOptions) => void
  availableCategories?: string[]
  availableFeatures?: string[]
  className?: string
  variant?: 'sidebar' | 'sheet' | 'inline'
}

const defaultFilters: FilterOptions = {
  categories: [],
  pricing: [],
  features: [],
  rating: [0, 5],
  sortBy: 'featured',
  sortOrder: 'desc',
  dateRange: 'all',
  verified: false,
  featured: false,
  isNew: false
}

const pricingOptions = [
  { value: 'free', label: 'Free', icon: '🆓' },
  { value: 'freemium', label: 'Freemium', icon: '⭐' },
  { value: 'paid', label: 'Paid', icon: '💰' },
  { value: 'subscription', label: 'Subscription', icon: '🔄' }
]

const sortOptions = [
  { value: 'featured', label: 'Featured First' },
  { value: 'rating', label: 'Highest Rated' },
  { value: 'recent', label: 'Recently Added' },
  { value: 'popular', label: 'Most Popular' },
  { value: 'name', label: 'Name A-Z' }
]

const dateRangeOptions = [
  { value: 'all', label: 'All Time' },
  { value: 'week', label: 'This Week' },
  { value: 'month', label: 'This Month' },
  { value: 'quarter', label: 'This Quarter' },
  { value: 'year', label: 'This Year' }
]

const AdvancedFilters = memo(function AdvancedFilters({
  filters,
  onFiltersChange,
  availableCategories = [],
  availableFeatures = [],
  className,
  variant = 'sidebar'
}: AdvancedFiltersProps) {
  const [expandedSections, setExpandedSections] = useState<Set<string>>(
    new Set(['categories', 'pricing', 'rating'])
  )

  const toggleSection = useCallback((section: string) => {
    setExpandedSections(prev => {
      const newSet = new Set(prev)
      if (newSet.has(section)) {
        newSet.delete(section)
      } else {
        newSet.add(section)
      }
      return newSet
    })
  }, [])

  const updateFilter = useCallback(<K extends keyof FilterOptions>(
    key: K,
    value: FilterOptions[K]
  ) => {
    onFiltersChange({ ...filters, [key]: value })
  }, [filters, onFiltersChange])

  const toggleArrayFilter = useCallback((
    key: 'categories' | 'pricing' | 'features',
    value: string
  ) => {
    const currentArray = filters[key]
    const newArray = currentArray.includes(value)
      ? currentArray.filter(item => item !== value)
      : [...currentArray, value]
    updateFilter(key, newArray)
  }, [filters, updateFilter])

  const clearFilters = useCallback(() => {
    onFiltersChange(defaultFilters)
  }, [onFiltersChange])

  const getActiveFiltersCount = useCallback(() => {
    let count = 0
    if (filters.categories.length > 0) count++
    if (filters.pricing.length > 0) count++
    if (filters.features.length > 0) count++
    if (filters.rating[0] > 0 || filters.rating[1] < 5) count++
    if (filters.verified) count++
    if (filters.featured) count++
    if (filters.isNew) count++
    if (filters.dateRange !== 'all') count++
    return count
  }, [filters])

  const FilterSection = memo(function FilterSection({
    title,
    icon,
    children,
    sectionKey,
    defaultExpanded = false
  }: {
    title: string
    icon: React.ReactNode
    children: React.ReactNode
    sectionKey: string
    defaultExpanded?: boolean
  }) {
    const isExpanded = expandedSections.has(sectionKey)

    return (
      <Collapsible open={isExpanded} onOpenChange={() => toggleSection(sectionKey)}>
        <CollapsibleTrigger asChild>
          <Button
            variant="ghost"
            className="w-full justify-between p-3 h-auto font-medium"
          >
            <div className="flex items-center gap-2">
              {icon}
              <span>{title}</span>
            </div>
            {isExpanded ? <ChevronUp className="h-4 w-4" /> : <ChevronDown className="h-4 w-4" />}
          </Button>
        </CollapsibleTrigger>
        <CollapsibleContent className="px-3 pb-3">
          {children}
        </CollapsibleContent>
      </Collapsible>
    )
  })

  const FiltersContent = memo(function FiltersContent() {
    return (
      <div className="space-y-4">
        {/* Active filters summary */}
        <div className="flex items-center justify-between">
          <div className="flex items-center gap-2">
            <SlidersHorizontal className="h-4 w-4" />
            <span className="font-medium">Filters</span>
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="text-xs">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </div>
          {getActiveFiltersCount() > 0 && (
            <Button variant="ghost" size="sm" onClick={clearFilters}>
              Clear All
            </Button>
          )}
        </div>

        {/* Sort */}
        <div className="space-y-2">
          <Label className="text-sm font-medium">Sort By</Label>
          <Select value={filters.sortBy} onValueChange={(value) => updateFilter('sortBy', value)}>
            <SelectTrigger>
              <SelectValue />
            </SelectTrigger>
            <SelectContent>
              {sortOptions.map(option => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        </div>

        {/* Categories */}
        <FilterSection
          title="Categories"
          icon={<Tag className="h-4 w-4" />}
          sectionKey="categories"
          defaultExpanded
        >
          <div className="space-y-2 max-h-48 overflow-y-auto">
            {availableCategories.map(category => (
              <div key={category} className="flex items-center space-x-2">
                <Checkbox
                  id={`category-${category}`}
                  checked={filters.categories.includes(category)}
                  onCheckedChange={() => toggleArrayFilter('categories', category)}
                />
                <Label
                  htmlFor={`category-${category}`}
                  className="text-sm font-normal cursor-pointer"
                >
                  {category}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Pricing */}
        <FilterSection
          title="Pricing"
          icon={<DollarSign className="h-4 w-4" />}
          sectionKey="pricing"
          defaultExpanded
        >
          <div className="space-y-2">
            {pricingOptions.map(option => (
              <div key={option.value} className="flex items-center space-x-2">
                <Checkbox
                  id={`pricing-${option.value}`}
                  checked={filters.pricing.includes(option.value)}
                  onCheckedChange={() => toggleArrayFilter('pricing', option.value)}
                />
                <Label
                  htmlFor={`pricing-${option.value}`}
                  className="text-sm font-normal cursor-pointer flex items-center gap-2"
                >
                  <span>{option.icon}</span>
                  {option.label}
                </Label>
              </div>
            ))}
          </div>
        </FilterSection>

        {/* Rating */}
        <FilterSection
          title="Rating"
          icon={<Star className="h-4 w-4" />}
          sectionKey="rating"
          defaultExpanded
        >
          <div className="space-y-3">
            <div className="px-2">
              <Slider
                value={filters.rating}
                onValueChange={(value) => updateFilter('rating', value as [number, number])}
                max={5}
                min={0}
                step={0.5}
                className="w-full"
              />
            </div>
            <div className="flex justify-between text-sm text-muted-foreground">
              <span>{filters.rating[0]} stars</span>
              <span>{filters.rating[1]} stars</span>
            </div>
          </div>
        </FilterSection>

        {/* Features */}
        {availableFeatures.length > 0 && (
          <FilterSection
            title="Features"
            icon={<Sparkles className="h-4 w-4" />}
            sectionKey="features"
          >
            <div className="space-y-2 max-h-48 overflow-y-auto">
              {availableFeatures.map(feature => (
                <div key={feature} className="flex items-center space-x-2">
                  <Checkbox
                    id={`feature-${feature}`}
                    checked={filters.features.includes(feature)}
                    onCheckedChange={() => toggleArrayFilter('features', feature)}
                  />
                  <Label
                    htmlFor={`feature-${feature}`}
                    className="text-sm font-normal cursor-pointer"
                  >
                    {feature}
                  </Label>
                </div>
              ))}
            </div>
          </FilterSection>
        )}

        {/* Quick filters */}
        <FilterSection
          title="Quick Filters"
          icon={<Filter className="h-4 w-4" />}
          sectionKey="quick"
        >
          <div className="space-y-3">
            <div className="flex items-center space-x-2">
              <Checkbox
                id="verified"
                checked={filters.verified}
                onCheckedChange={(checked) => updateFilter('verified', !!checked)}
              />
              <Label htmlFor="verified" className="text-sm font-normal cursor-pointer">
                Verified Only
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="featured"
                checked={filters.featured}
                onCheckedChange={(checked) => updateFilter('featured', !!checked)}
              />
              <Label htmlFor="featured" className="text-sm font-normal cursor-pointer">
                Featured Only
              </Label>
            </div>
            <div className="flex items-center space-x-2">
              <Checkbox
                id="new"
                checked={filters.isNew}
                onCheckedChange={(checked) => updateFilter('isNew', !!checked)}
              />
              <Label htmlFor="new" className="text-sm font-normal cursor-pointer">
                New Tools
              </Label>
            </div>
          </div>
        </FilterSection>

        {/* Date Range */}
        <FilterSection
          title="Date Added"
          icon={<Calendar className="h-4 w-4" />}
          sectionKey="date"
        >
          <RadioGroup
            value={filters.dateRange}
            onValueChange={(value) => updateFilter('dateRange', value)}
          >
            {dateRangeOptions.map(option => (
              <div key={option.value} className="flex items-center space-x-2">
                <RadioGroupItem value={option.value} id={`date-${option.value}`} />
                <Label htmlFor={`date-${option.value}`} className="text-sm font-normal cursor-pointer">
                  {option.label}
                </Label>
              </div>
            ))}
          </RadioGroup>
        </FilterSection>
      </div>
    )
  })

  if (variant === 'sheet') {
    return (
      <Sheet>
        <SheetTrigger asChild>
          <Button variant="outline" className={className}>
            <Filter className="h-4 w-4 mr-2" />
            Filters
            {getActiveFiltersCount() > 0 && (
              <Badge variant="secondary" className="ml-2 text-xs">
                {getActiveFiltersCount()}
              </Badge>
            )}
          </Button>
        </SheetTrigger>
        <SheetContent side="right" className="w-80">
          <SheetHeader>
            <SheetTitle>Filter Tools</SheetTitle>
          </SheetHeader>
          <div className="mt-6">
            <FiltersContent />
          </div>
        </SheetContent>
      </Sheet>
    )
  }

  return (
    <div className={cn("bg-card border rounded-lg p-4", className)}>
      <FiltersContent />
    </div>
  )
})

export default AdvancedFilters
