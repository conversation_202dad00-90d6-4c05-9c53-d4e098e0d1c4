"use client"

import { useState, useEffect, useMemo, useCallback } from "react"
import { Search, Grid, List, SlidersHorizontal, X, Plus, Loader2 } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue } from "@/components/ui/select"
import { Badge } from "@/components/ui/badge"
import { Separator } from "@/components/ui/separator"
import { Sheet, SheetContent, SheetDescription, SheetHeader, SheetTitle, SheetTrigger } from "@/components/ui/sheet"
import ToolCard from "@/components/tool-card"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { useRouter, useSearchParams } from "next/navigation"
import { toast } from "sonner"

interface Tool {
  id: string | number
  company_name: string
  description?: string
  short_description?: string
  logo_url?: string
  primary_task?: string
  pricing?: string
  rating?: number
  slug?: string
  is_featured?: boolean
  is_verified?: boolean
  click_count?: number
  created_at?: string
  visit_website_url?: string
  review_count?: number
  tags?: string[]
  new?: boolean
}

interface Category {
  id: string
  name: string
  count: number
}

interface ToolsPageClientProps {
  searchParams: {
    category?: string
    pricing?: string
    sortBy?: string
    search?: string
    features?: string
    view?: string
  }
  categories: Category[]
  pricingOptions: string[]
}

type SortOption = 'featured' | 'rating' | 'alphabetical' | 'newest' | 'popular'
type ViewMode = 'grid' | 'list'

export default function ToolsPageClient({
  searchParams,
  categories,
  pricingOptions
}: ToolsPageClientProps) {
  const router = useRouter()
  const urlSearchParams = useSearchParams()
  const [tools, setTools] = useState<Tool[]>([])
  const [loading, setLoading] = useState(true)
  const [viewMode, setViewMode] = useState<ViewMode>(searchParams.view === 'list' ? 'list' : 'grid')
  const [totalCount, setTotalCount] = useState(0)
  const [currentPage, setCurrentPage] = useState(1)
  const [searchQuery, setSearchQuery] = useState(searchParams.search || '')
  const [selectedCategory, setSelectedCategory] = useState(searchParams.category || '')
  const [selectedPricing, setSelectedPricing] = useState(searchParams.pricing || '')
  const [sortBy, setSortBy] = useState<SortOption>((searchParams.sortBy as SortOption) || 'featured')
  const [showFilters, setShowFilters] = useState(false)

  const itemsPerPage = 24

  // Fetch tools from Supabase
  const fetchTools = async () => {
    try {
      setLoading(true)
      const supabase = createBrowserClient()
      
      if (!supabase) {
        throw new Error('Supabase client not available')
      }

      let query = supabase
        .from('tools')
        .select('*', { count: 'exact' })

      // Apply filters
      if (searchQuery) {
        query = query.or(`company_name.ilike.%${searchQuery}%,description.ilike.%${searchQuery}%,short_description.ilike.%${searchQuery}%`)
      }

      if (selectedCategory) {
        query = query.eq('primary_task', selectedCategory)
      }

      if (selectedPricing && selectedPricing !== 'all') {
        if (selectedPricing === 'free') {
          query = query.or('pricing.ilike.%free%,pricing.ilike.%Free%')
        } else {
          query = query.eq('pricing', selectedPricing)
        }
      }

      // Apply sorting
      switch (sortBy) {
        case 'featured':
          query = query.order('is_featured', { ascending: false })
            .order('is_verified', { ascending: false })
            .order('rating', { ascending: false, nullsFirst: false })
          break
        case 'rating':
          query = query.order('rating', { ascending: false, nullsFirst: false })
          break
        case 'alphabetical':
          query = query.order('company_name', { ascending: true })
          break
        case 'newest':
          query = query.order('created_at', { ascending: false })
          break
        case 'popular':
          query = query.order('click_count', { ascending: false, nullsFirst: false })
          break
        default:
          query = query.order('is_featured', { ascending: false })
      }

      // Apply pagination
      const from = (currentPage - 1) * itemsPerPage
      const to = from + itemsPerPage - 1
      query = query.range(from, to)

      const { data, error, count } = await query

      if (error) {
        console.error('Error fetching tools:', error)
        toast.error('Failed to fetch tools')
        return
      }

      setTools(data || [])
      setTotalCount(count || 0)
    } catch (error) {
      console.error('Error in fetchTools:', error)
      toast.error('An error occurred while fetching tools')
    } finally {
      setLoading(false)
    }
  }

  // Fetch tools when filters change
  useEffect(() => {
    fetchTools()
  }, [searchQuery, selectedCategory, selectedPricing, sortBy, currentPage])

  // Update URL when filters change
  const updateURL = () => {
    const params = new URLSearchParams()
    if (searchQuery) params.set('search', searchQuery)
    if (selectedCategory) params.set('category', selectedCategory)
    if (selectedPricing) params.set('pricing', selectedPricing)
    if (sortBy !== 'featured') params.set('sortBy', sortBy)
    if (viewMode !== 'grid') params.set('view', viewMode)
    
    const newURL = `/tools${params.toString() ? `?${params.toString()}` : ''}`
    router.replace(newURL, { scroll: false })
  }

  useEffect(() => {
    updateURL()
  }, [searchQuery, selectedCategory, selectedPricing, sortBy, viewMode])

  // Handle search
  const handleSearch = (query: string) => {
    setSearchQuery(query)
    setCurrentPage(1)
  }

  // Handle category click
  const handleCategoryClick = (categoryName: string) => {
    setSelectedCategory(categoryName === selectedCategory ? '' : categoryName)
    setCurrentPage(1)
  }

  // Clear all filters
  const clearFilters = () => {
    setSearchQuery('')
    setSelectedCategory('')
    setSelectedPricing('')
    setSortBy('featured')
    setCurrentPage(1)
  }

  // Get stats
  const featuredCount = tools.filter(tool => tool.is_featured).length
  const verifiedCount = tools.filter(tool => tool.is_verified).length
  const averageRating = tools.length > 0 
    ? tools.reduce((sum, tool) => sum + (tool.rating || 0), 0) / tools.length 
    : 0

  // Active filters count
  const activeFiltersCount = [searchQuery, selectedCategory, selectedPricing].filter(Boolean).length

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="relative pt-24 pb-16 bg-gradient-to-br from-background via-background to-muted/20">
        <div className="container mx-auto px-4">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-bold mb-6 bg-gradient-to-r from-foreground to-foreground/70 bg-clip-text text-transparent">
              AI Tools Directory
            </h1>
            <p className="text-lg md:text-xl text-muted-foreground max-w-3xl mx-auto mb-8">
              Discover the best AI tools for your needs. Browse through {totalCount}+ carefully curated AI tools across various categories.
            </p>
          </div>

          {/* Search Bar */}
          <div className="max-w-2xl mx-auto mb-12">
            <HeroSearch
              onSearch={handleSearch}
              initialValue={searchQuery}
              placeholder="Search AI tools..."
            />
          </div>

          {/* Stats Cards */}
          <motion.div 
            className="grid grid-cols-2 md:grid-cols-4 gap-4 max-w-4xl mx-auto mb-12"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.3 }}
          >
            <Card className="text-center border-border/50 bg-card/50 backdrop-blur-sm">
              <CardContent className="pt-4 pb-4">
                <div className="text-2xl font-bold">{totalCount}</div>
                <div className="text-xs text-muted-foreground">Total Tools</div>
              </CardContent>
            </Card>
            <Card className="text-center border-border/50 bg-card/50 backdrop-blur-sm">
              <CardContent className="pt-4 pb-4">
                <div className="text-2xl font-bold">{featuredCount}</div>
                <div className="text-xs text-muted-foreground">Featured</div>
              </CardContent>
            </Card>
            <Card className="text-center border-border/50 bg-card/50 backdrop-blur-sm">
              <CardContent className="pt-4 pb-4">
                <div className="text-2xl font-bold">{verifiedCount}</div>
                <div className="text-xs text-muted-foreground">Verified</div>
              </CardContent>
            </Card>
            <Card className="text-center border-border/50 bg-card/50 backdrop-blur-sm">
              <CardContent className="pt-4 pb-4">
                <div className="text-2xl font-bold">{averageRating.toFixed(1)}</div>
                <div className="text-xs text-muted-foreground">Avg Rating</div>
              </CardContent>
            </Card>
          </motion.div>
        </div>
      </section>

      {/* Categories Section */}
      <section className="py-12 border-b border-border/50">
        <div className="container mx-auto px-4">
          <motion.div 
            className="text-center mb-8"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6 }}
          >
            <h2 className="text-2xl md:text-3xl font-bold mb-4">Browse by Category</h2>
            <p className="text-muted-foreground">Find AI tools organized by their primary use case</p>
          </motion.div>
          
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <ScrollingCategories 
              categories={categories}
              onCategoryClick={handleCategoryClick}
              selectedCategory={selectedCategory}
            />
          </motion.div>
        </div>
      </section>

      {/* Filters and Controls */}
      <section className="py-8 border-b border-border/50">
        <div className="container mx-auto px-4">
          <div className="flex flex-col md:flex-row justify-between items-start md:items-center gap-4">
            {/* Results Count and Active Filters */}
            <div className="flex items-center gap-4">
              <span className="text-sm text-muted-foreground">
                {loading ? 'Loading...' : `Showing ${tools.length} of ${totalCount} tools`}
              </span>
              
              {/* Active Filters */}
              <div className="flex items-center gap-2">
                {searchQuery && (
                  <Badge variant="secondary" className="text-xs">
                    Search: {searchQuery}
                  </Badge>
                )}
                {selectedCategory && (
                  <Badge variant="secondary" className="text-xs">
                    Category: {selectedCategory}
                  </Badge>
                )}
                {selectedPricing && (
                  <Badge variant="secondary" className="text-xs">
                    Pricing: {selectedPricing}
                  </Badge>
                )}
                {activeFiltersCount > 0 && (
                  <Button 
                    variant="ghost" 
                    size="sm" 
                    onClick={clearFilters}
                    className="text-xs h-6 px-2"
                  >
                    Clear all
                  </Button>
                )}
              </div>
            </div>

            {/* Controls */}
            <div className="flex items-center gap-4">
              {/* Filters Button */}
              <Popover open={showFilters} onOpenChange={setShowFilters}>
                <PopoverTrigger asChild>
                  <Button variant="outline" size="sm" className="gap-2">
                    <SlidersHorizontal className="h-4 w-4" />
                    Filters
                    {activeFiltersCount > 0 && (
                      <Badge variant="secondary" className="ml-1 h-5 w-5 p-0 text-xs">
                        {activeFiltersCount}
                      </Badge>
                    )}
                  </Button>
                </PopoverTrigger>
                <PopoverContent className="w-80" align="end">
                  <div className="space-y-4">
                    <div>
                      <label className="text-sm font-medium mb-2 block">Pricing</label>
                      <Select value={selectedPricing} onValueChange={setSelectedPricing}>
                        <SelectTrigger>
                          <SelectValue placeholder="All pricing" />
                        </SelectTrigger>
                        <SelectContent>
                          <SelectItem value="">All pricing</SelectItem>
                          <SelectItem value="free">Free</SelectItem>
                          {pricingOptions.map(option => (
                            <SelectItem key={option} value={option}>
                              {option}
                            </SelectItem>
                          ))}
                        </SelectContent>
                      </Select>
                    </div>
                  </div>
                </PopoverContent>
              </Popover>

              {/* Sort Options */}
              <div className="flex items-center gap-2">
                <span className="text-sm text-muted-foreground">Sort by:</span>
                <Select value={sortBy} onValueChange={(value: SortOption) => setSortBy(value)}>
                  <SelectTrigger className="w-32">
                    <SelectValue />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="featured">Featured</SelectItem>
                    <SelectItem value="rating">Rating</SelectItem>
                    <SelectItem value="alphabetical">A-Z</SelectItem>
                    <SelectItem value="newest">Newest</SelectItem>
                    <SelectItem value="popular">Popular</SelectItem>
                  </SelectContent>
                </Select>
              </div>

              {/* View Mode Toggle */}
              <div className="flex items-center border border-border rounded-lg p-1">
                <Button
                  variant={viewMode === 'grid' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('grid')}
                  className="h-8 w-8 p-0"
                >
                  <Grid3X3 className="h-4 w-4" />
                </Button>
                <Button
                  variant={viewMode === 'list' ? 'default' : 'ghost'}
                  size="sm"
                  onClick={() => setViewMode('list')}
                  className="h-8 w-8 p-0"
                >
                  <List className="h-4 w-4" />
                </Button>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Tools Grid/List */}
      <section className="py-12">
        <div className="container mx-auto px-4">
          <AnimatePresence mode="wait">
            {loading ? (
              <motion.div 
                className={cn(
                  "gap-6",
                  viewMode === 'grid' 
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" 
                    : "flex flex-col space-y-4"
                )}
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                exit={{ opacity: 0 }}
              >
                {Array.from({ length: 12 }).map((_, i) => (
                  <div key={i} className="h-64 bg-muted/60 rounded-lg animate-pulse"></div>
                ))}
              </motion.div>
            ) : tools.length > 0 ? (
              <motion.div 
                key={`${viewMode}-${sortBy}`}
                className={cn(
                  "gap-6",
                  viewMode === 'grid' 
                    ? "grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4" 
                    : "flex flex-col space-y-4"
                )}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                exit={{ opacity: 0, y: -20 }}
                transition={{ duration: 0.4 }}
              >
                {tools.map((tool, index) => (
                  <motion.div
                    key={tool.id}
                    initial={{ opacity: 0, y: 20 }}
                    animate={{ opacity: 1, y: 0 }}
                    transition={{ duration: 0.4, delay: index * 0.05 }}
                  >
                    <ToolCard
                      tool={{
                        id: tool.id?.toString() || '',
                        slug: tool.slug || '',
                        company_name: tool.company_name || 'Unknown Tool',
                        short_description: tool.description || tool.short_description || 'No description available',
                        visit_website_url: tool.visit_website_url || '#',
                        logo_url: tool.logo_url || '',
                        primary_task: tool.primary_task || 'General',
                        pricing: tool.pricing || 'Unknown',
                        rating: tool.rating || 0,
                        review_count: tool.review_count || 0,
                        is_verified: tool.is_verified || false,
                        is_featured: tool.is_featured || false,
                        click_count: tool.click_count || 0,
                        isNew: tool.new || false
                      }}
                      className={viewMode === 'list' ? 'flex-row' : ''}
                    />
                  </motion.div>
                ))}
              </motion.div>
            ) : (
              <motion.div 
                className="text-center py-16"
                initial={{ opacity: 0 }}
                animate={{ opacity: 1 }}
                transition={{ duration: 0.4 }}
              >
                <Search className="h-16 w-16 text-muted-foreground mx-auto mb-4" />
                <h3 className="text-xl font-semibold mb-2">No tools found</h3>
                <p className="text-muted-foreground mb-4">
                  {searchQuery || selectedCategory || selectedPricing
                    ? 'No tools match your current filters. Try adjusting your search criteria.'
                    : 'No tools available at the moment.'
                  }
                </p>
                {(searchQuery || selectedCategory || selectedPricing) && (
                  <Button 
                    variant="outline" 
                    onClick={clearFilters}
                  >
                    Clear All Filters
                  </Button>
                )}
              </motion.div>
            )}
          </AnimatePresence>

          {/* Pagination */}
          {!loading && tools.length > 0 && totalCount > itemsPerPage && (
            <motion.div 
              className="flex justify-center mt-12"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.4, delay: 0.2 }}
            >
              <div className="flex items-center gap-2">
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.max(1, prev - 1))}
                  disabled={currentPage === 1}
                >
                  Previous
                </Button>
                
                <div className="flex items-center gap-1">
                  {Array.from({ length: Math.min(5, Math.ceil(totalCount / itemsPerPage)) }, (_, i) => {
                    const pageNum = currentPage <= 3 ? i + 1 : currentPage - 2 + i
                    const totalPages = Math.ceil(totalCount / itemsPerPage)
                    
                    if (pageNum > totalPages) return null
                    
                    return (
                      <Button
                        key={pageNum}
                        variant={currentPage === pageNum ? 'default' : 'ghost'}
                        size="sm"
                        onClick={() => setCurrentPage(pageNum)}
                        className="w-8 h-8 p-0"
                      >
                        {pageNum}
                      </Button>
                    )
                  })}
                </div>
                
                <Button
                  variant="outline"
                  size="sm"
                  onClick={() => setCurrentPage(prev => Math.min(Math.ceil(totalCount / itemsPerPage), prev + 1))}
                  disabled={currentPage >= Math.ceil(totalCount / itemsPerPage)}
                >
                  Next
                </Button>
              </div>
            </motion.div>
          )}
        </div>
      </section>
    </div>
  )
}
