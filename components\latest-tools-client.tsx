"use client"

import { useState, useEffect } from "react"
import ToolCard from "@/components/tool-card"
import { createBrowserClient } from "@/lib/supabase/client-utils"

export default function LatestToolsClient() {
  const [tools, setTools] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchTools = async () => {
      setIsLoading(true)
      const supabase = createBrowserClient()

      try {
        const { data, error: supabaseError } = await supabase
          .from("tools")
          .select("*")
          .order("created_at", { ascending: false })
          .limit(6)

        if (supabaseError) throw supabaseError

        setTools(data || [])
      } catch (err: any) {
        console.error("Error fetching latest tools:", err.message)
        setError("Error loading latest tools. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchTools()
  }, [])

  if (isLoading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-600 dark:text-slate-400">{error}</p>
      </div>
    )
  }

  if (!tools || tools.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-600 dark:text-slate-400">No tools available at the moment.</p>
      </div>
    )
  }

  return (
    <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
      {tools.map((tool) => (
        <ToolCard key={tool.id} tool={tool} />
      ))}
    </div>
  )
}
