"use client"

import { useState } from "react"
import { use<PERSON><PERSON><PERSON> } from "next/navigation"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { toast } from "sonner"
import { z } from "zod"
import { useForm } from "react-hook-form"
import { zod<PERSON><PERSON>olver } from "@hookform/resolvers/zod"
import { Loader2, AlertCircle } from "lucide-react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { Textarea } from "@/components/ui/textarea"
import {
  Form,
  FormControl,
  FormDescription,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { Card, CardContent } from "@/components/ui/card"

// Form schema
const formSchema = z.object({
  tool_name: z.string().min(2, "Tool name must be at least 2 characters").max(100),
  description: z.string().min(20, "Description must be at least 20 characters").max(1000),
  website_url: z.string().url("Please enter a valid URL"),
  logo_url: z.string().url("Please enter a valid URL").optional().or(z.literal("")),
  category: z.string().min(1, "Please select a category"),
  pricing: z.string().min(1, "Please select a pricing model"),
  features: z.string().optional(),
  additional_info: z.string().optional(),
})

type FormValues = z.infer<typeof formSchema>

interface SubmitToolFormProps {
  user: any
  categories: string[]
}

export default function SubmitToolForm({ user, categories }: SubmitToolFormProps) {
  const router = useRouter()
  const [isSubmitting, setIsSubmitting] = useState(false)
  const [submitError, setSubmitError] = useState<string | null>(null)
  const [isSuccess, setIsSuccess] = useState(false)
  
  // Pricing options
  const pricingOptions = [
    "Free",
    "Freemium",
    "Paid",
    "Free Trial",
    "Subscription",
    "One-time Purchase",
    "Contact for Pricing",
  ]
  
  // Initialize form
  const form = useForm<FormValues>({
    resolver: zodResolver(formSchema),
    defaultValues: {
      tool_name: "",
      description: "",
      website_url: "",
      logo_url: "",
      category: "",
      pricing: "",
      features: "",
      additional_info: "",
    },
  })
  
  // Handle form submission
  const onSubmit = async (values: FormValues) => {
    setIsSubmitting(true)
    setSubmitError(null)
    
    try {
      const supabase = createBrowserClient()
      
      // Submit the tool
      const { error } = await supabase
        .from('tool_submissions')
        .insert({
          user_id: user.id,
          tool_name: values.tool_name,
          description: values.description,
          website_url: values.website_url,
          logo_url: values.logo_url || null,
          category: values.category,
          pricing: values.pricing,
          features: values.features ? values.features.split(',').map(f => f.trim()) : [],
          additional_info: values.additional_info || null,
          status: "pending",
          created_at: new Date().toISOString(),
        })
      
      if (error) throw error
      
      // Show success message
      setIsSuccess(true)
      toast.success("Tool submitted successfully", {
        description: "Your submission is now pending review. We'll notify you once it's approved.",
      })
      
      // Reset form
      form.reset()
      
      // Redirect to dashboard after a delay
      setTimeout(() => {
        router.push('/dashboard')
      }, 3000)
      
    } catch (error: any) {
      console.error("Error submitting tool:", error)
      setSubmitError(error.message || "Failed to submit tool. Please try again.")
      toast.error("Submission failed", {
        description: "There was a problem submitting your tool. Please try again.",
      })
    } finally {
      setIsSubmitting(false)
    }
  }
  
  if (isSuccess) {
    return (
      <Alert className="bg-green-50 text-green-800 border-green-200">
        <AlertCircle className="h-4 w-4 text-green-800" />
        <AlertTitle>Submission Successful</AlertTitle>
        <AlertDescription>
          <p className="mb-2">
            Thank you for submitting your AI tool! Your submission is now pending review.
          </p>
          <p>
            We'll notify you once it's approved. You'll be redirected to your dashboard in a moment.
          </p>
        </AlertDescription>
      </Alert>
    )
  }
  
  return (
    <Card>
      <CardContent className="pt-6">
        <Form {...form}>
          <form onSubmit={form.handleSubmit(onSubmit)} className="space-y-6">
            {submitError && (
              <Alert variant="destructive">
                <AlertCircle className="h-4 w-4" />
                <AlertTitle>Error</AlertTitle>
                <AlertDescription>{submitError}</AlertDescription>
              </Alert>
            )}
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="tool_name"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Tool Name *</FormLabel>
                    <FormControl>
                      <Input placeholder="Enter the name of your AI tool" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="website_url"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Website URL *</FormLabel>
                    <FormControl>
                      <Input placeholder="https://yourtool.com" {...field} />
                    </FormControl>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="description"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Description *</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Describe what your tool does and its key benefits" 
                      className="min-h-32"
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    Provide a clear and concise description of your tool (20-1000 characters).
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <FormField
                control={form.control}
                name="category"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Category *</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select a category" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {categories.map((category) => (
                          <SelectItem key={category} value={category}>
                            {category}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
              
              <FormField
                control={form.control}
                name="pricing"
                render={({ field }) => (
                  <FormItem>
                    <FormLabel>Pricing Model *</FormLabel>
                    <Select 
                      onValueChange={field.onChange} 
                      defaultValue={field.value}
                    >
                      <FormControl>
                        <SelectTrigger>
                          <SelectValue placeholder="Select pricing model" />
                        </SelectTrigger>
                      </FormControl>
                      <SelectContent>
                        {pricingOptions.map((option) => (
                          <SelectItem key={option} value={option}>
                            {option}
                          </SelectItem>
                        ))}
                      </SelectContent>
                    </Select>
                    <FormMessage />
                  </FormItem>
                )}
              />
            </div>
            
            <FormField
              control={form.control}
              name="logo_url"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Logo URL (Optional)</FormLabel>
                  <FormControl>
                    <Input placeholder="https://yourtool.com/logo.png" {...field} />
                  </FormControl>
                  <FormDescription>
                    Provide a URL to your tool's logo image (recommended size: 512x512px).
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="features"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Key Features (Optional)</FormLabel>
                  <FormControl>
                    <Input 
                      placeholder="Feature 1, Feature 2, Feature 3" 
                      {...field} 
                    />
                  </FormControl>
                  <FormDescription>
                    List key features separated by commas.
                  </FormDescription>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <FormField
              control={form.control}
              name="additional_info"
              render={({ field }) => (
                <FormItem>
                  <FormLabel>Additional Information (Optional)</FormLabel>
                  <FormControl>
                    <Textarea 
                      placeholder="Any additional information you'd like to share" 
                      className="min-h-24"
                      {...field} 
                    />
                  </FormControl>
                  <FormMessage />
                </FormItem>
              )}
            />
            
            <div className="flex justify-end">
              <Button type="submit" disabled={isSubmitting}>
                {isSubmitting ? (
                  <>
                    <Loader2 className="mr-2 h-4 w-4 animate-spin" />
                    Submitting...
                  </>
                ) : (
                  "Submit Tool"
                )}
              </Button>
            </div>
          </form>
        </Form>
      </CardContent>
    </Card>
  )
}
