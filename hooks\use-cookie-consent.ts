'use client'

import { useState, useEffect } from 'react'
import {
  CookieCategory,
  CookiePreferences,
  DEFAULT_PREFERENCES,
  getCookiePreferences,
  saveCookiePreferences,
  acceptAllCookies,
  acceptEssentialOnly,
  resetCookiePreferences,
  getConsentStatus,
  hasConsent,
  forceEnableAnalytics
} from '@/lib/cookie-consent'

/**
 * Client-side hook for React components to use cookie consent
 */
export function useCookieConsent() {
  const [preferences, setPreferences] = useState<CookiePreferences>(DEFAULT_PREFERENCES)
  const [needsConsent, setNeedsConsent] = useState(false)
  const [isLoaded, setIsLoaded] = useState(false)

  useEffect(() => {
    // Only run on client side
    if (typeof window === 'undefined') return

    const updateState = () => {
      // Force enable analytics for existing users
      forceEnableAnalytics()

      const currentPreferences = getCookiePreferences()
      const status = getConsentStatus()

      setPreferences(currentPreferences || DEFAULT_PREFERENCES)
      setNeedsConsent(status.needsConsent)
      setIsLoaded(true)
    }

    updateState()

    // Listen for consent changes
    const handleConsentChange = (event: CustomEvent) => {
      updateState()
    }

    window.addEventListener('cookiePreferencesChanged', handleConsentChange as EventListener)
    return () => {
      window.removeEventListener('cookiePreferencesChanged', handleConsentChange as EventListener)
    }
  }, [])

  return {
    preferences,
    hasConsent,
    saveCookiePreferences,
    acceptAllCookies,
    acceptEssentialOnly,
    resetCookiePreferences,
    getConsentStatus,
    needsConsent,
    isLoaded
  }
}
