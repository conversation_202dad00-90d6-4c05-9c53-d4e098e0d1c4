'use client'

import { useState, useEffect } from 'react'
import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { <PERSON><PERSON>, Settings, Shield, X } from 'lucide-react'
import { cn } from '@/lib/utils'
import {
  getConsentStatus,
  acceptAllCookies,
  acceptEssentialOnly,
  dismissBanner
} from '@/lib/cookie-consent'
import { useCookieConsent } from '@/hooks/use-cookie-consent'
import { CookieSettingsModal } from './CookieSettingsModal'

interface CookieBannerProps {
  className?: string
  position?: 'bottom' | 'top'
  variant?: 'minimal' | 'detailed'
}

export function CookieBanner({
  className,
  position = 'bottom',
  variant = 'detailed'
}: CookieBannerProps) {
  const [isVisible, setIsVisible] = useState(false)
  const [showSettings, setShowSettings] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)
  const { needsConsent } = useCookieConsent()

  useEffect(() => {
    // Check if banner should be shown with delay
    const checkBannerVisibility = () => {
      const status = getConsentStatus()
      if (status.needsConsent) {
        // Delay showing the banner by 2 seconds
        setTimeout(() => {
          setIsVisible(true)
          setTimeout(() => setIsAnimating(true), 50) // Small delay for animation
        }, 2000)
      } else {
        setIsVisible(false)
        setIsAnimating(false)
      }
    }

    checkBannerVisibility()

    // Listen for consent changes
    const handleConsentChange = () => {
      checkBannerVisibility()
    }

    window.addEventListener('cookiePreferencesChanged', handleConsentChange)
    return () => {
      window.removeEventListener('cookiePreferencesChanged', handleConsentChange)
    }
  }, [])

  const handleAcceptAll = async () => {
    setIsLoading(true)
    try {
      acceptAllCookies()
      setIsAnimating(false)
      setTimeout(() => setIsVisible(false), 300) // Smooth exit animation
    } catch (error) {
      console.error('Error accepting cookies:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleAcceptEssential = async () => {
    setIsLoading(true)
    try {
      acceptEssentialOnly()
      setIsAnimating(false)
      setTimeout(() => setIsVisible(false), 300) // Smooth exit animation
    } catch (error) {
      console.error('Error accepting essential cookies:', error)
    } finally {
      setIsLoading(false)
    }
  }

  const handleDismiss = () => {
    dismissBanner()
    setIsAnimating(false)
    setTimeout(() => setIsVisible(false), 300) // Smooth exit animation
  }

  const handleOpenSettings = () => {
    setShowSettings(true)
  }

  const handleCloseSettings = () => {
    setShowSettings(false)
    // Check if user made choices in settings
    const status = getConsentStatus()
    if (status.hasConsent) {
      setIsVisible(false)
    }
  }



  const MinimalBanner = () => (
    <div className="flex items-center justify-between gap-3 p-3">
      <div className="flex items-center gap-2 flex-1">
        <Cookie className="h-4 w-4 text-primary flex-shrink-0" />
        <p className="text-xs text-foreground">
          We use cookies to enhance your experience.
        </p>
      </div>
      <div className="flex items-center gap-1.5 flex-shrink-0">
        <Button
          variant="outline"
          size="sm"
          onClick={handleOpenSettings}
          disabled={isLoading}
          className="h-7 px-2 text-xs"
        >
          Settings
        </Button>
        <Button
          size="sm"
          onClick={handleAcceptAll}
          disabled={isLoading}
          className="h-7 px-3 text-xs"
        >
          Accept
        </Button>
      </div>
    </div>
  )

  const DetailedBanner = () => (
    <div className="p-3">
      <div className="flex items-start justify-between gap-2 mb-2">
        <div className="flex items-center gap-2">
          <div className="p-1 bg-primary/10 rounded-sm">
            <Cookie className="h-3.5 w-3.5 text-primary" />
          </div>
          <div>
            <h3 className="font-medium text-sm">All Tracking Active</h3>
            <Badge variant="outline" className="mt-0.5 text-xs h-4">
              <Shield className="h-2 w-2 mr-1" />
              Full Analytics
            </Badge>
          </div>
        </div>
        <Button
          variant="ghost"
          size="sm"
          onClick={handleDismiss}
          className="text-muted-foreground hover:text-foreground h-6 w-6 p-0"
        >
          <X className="h-3 w-3" />
        </Button>
      </div>

      <div className="space-y-2">
        <p className="text-xs text-muted-foreground leading-relaxed">
          We use cookies to enhance your experience. All tracking (Analytics, Marketing, Preferences) is active by default for comprehensive service improvement.
        </p>

        <div className="grid grid-cols-2 gap-1.5 text-xs">
          <div className="flex items-center gap-1">
            <div className="w-1 h-1 bg-green-500 rounded-full"></div>
            <span className="text-muted-foreground">Essential</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-1 h-1 bg-blue-500 rounded-full"></div>
            <span className="text-muted-foreground">Analytics</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-1 h-1 bg-purple-500 rounded-full"></div>
            <span className="text-muted-foreground">Marketing</span>
          </div>
          <div className="flex items-center gap-1">
            <div className="w-1 h-1 bg-orange-500 rounded-full"></div>
            <span className="text-muted-foreground">Preferences</span>
          </div>
        </div>

        <div className="flex gap-1.5 pt-1">
          <Button
            onClick={handleAcceptAll}
            disabled={isLoading}
            className="flex-1 h-7 text-xs"
          >
            Accept All
          </Button>
          <Button
            variant="outline"
            onClick={handleAcceptEssential}
            disabled={isLoading}
            className="flex-1 h-7 text-xs"
          >
            Essential
          </Button>
          <Button
            variant="outline"
            onClick={handleOpenSettings}
            disabled={isLoading}
            className="flex-1 h-7 text-xs"
          >
            <Settings className="h-3 w-3 mr-1" />
            More
          </Button>
        </div>

        <div className="text-xs text-muted-foreground pt-1 border-t">
          <p>
            Change in{' '}
            <a href="/privacy" className="text-primary hover:underline">
              Privacy Policy
            </a>
            {' '}or footer.
          </p>
        </div>
      </div>
    </div>
  )

  return (
    <>
      {isVisible && (
        <div className="fixed inset-0 z-50 pointer-events-none">
          <div
            className={cn(
              "absolute transition-all duration-300 ease-out pointer-events-auto",
              position === 'bottom'
                ? "bottom-4 left-4 right-4 sm:right-auto sm:w-96"
                : "top-4 left-4 right-4 sm:right-auto sm:w-96",
              isAnimating
                ? "opacity-100 translate-y-0 scale-100"
                : "opacity-0 translate-y-4 scale-95",
              className
            )}
          >
            <Card className="shadow-xl border border-border/50 backdrop-blur-md bg-background/95 rounded-lg">
              <CardContent className="p-0">
                {variant === 'minimal' ? <MinimalBanner /> : <DetailedBanner />}
              </CardContent>
            </Card>
          </div>
        </div>
      )}

      {/* Cookie Settings Modal */}
      <CookieSettingsModal
        open={showSettings}
        onOpenChange={setShowSettings}
        onClose={handleCloseSettings}
      />
    </>
  )
}

export default CookieBanner
