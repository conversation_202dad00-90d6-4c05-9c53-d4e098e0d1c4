"use client"

import type React from "react"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Button } from "@/components/ui/button"
import { Textarea } from "@/components/ui/textarea"
import { useToast } from "@/components/ui/use-toast"
import { Star } from "lucide-react"
import { Progress } from "@/components/ui/progress"

interface ToolReviewsProps {
  toolId: number
}

export default function ToolReviews({ toolId }: ToolReviewsProps) {
  const [reviews, setReviews] = useState<any[]>([])
  const [ratingStats, setRatingStats] = useState<Record<number, number>>({})
  const [totalReviews, setTotalReviews] = useState(0)
  const [averageRating, setAverageRating] = useState(0)
  const [isLoading, setIsLoading] = useState(true)
  const [rating, setRating] = useState(0)
  const [hoverRating, setHoverRating] = useState(0)
  const [comment, setComment] = useState("")
  const [isSubmitting, setIsSubmitting] = useState(false)

  const { toast } = useToast()
  const supabase = createBrowserClient()

  // Fetch reviews on component mount
  useEffect(() => {
    const fetchReviews = async () => {
      setIsLoading(true)

      try {
        // Fetch reviews
        const { data: reviewsData, error: reviewsError } = await supabase
          .from("reviews")
          .select(`
            id,
            rating,
            comment,
            created_at,
            profiles (
              id,
              full_name,
              avatar_url
            )
          `)
          .eq("tool_id", toolId)
          .order("created_at", { ascending: false })

        if (reviewsError) throw reviewsError

        // Calculate rating statistics
        const stats: Record<number, number> = { 1: 0, 2: 0, 3: 0, 4: 0, 5: 0 }
        let sum = 0

        reviewsData?.forEach((review) => {
          if (review.rating) {
            stats[review.rating] = (stats[review.rating] || 0) + 1
            sum += review.rating
          }
        })

        const total = reviewsData?.length || 0
        const avg = total > 0 ? sum / total : 0

        setReviews(reviewsData || [])
        setRatingStats(stats)
        setTotalReviews(total)
        setAverageRating(avg)
      } catch (error) {
        console.error("Error fetching reviews:", error)
        toast({
          title: "Error",
          description: "Failed to load reviews. Please try again later.",
          variant: "destructive",
        })
      } finally {
        setIsLoading(false)
      }
    }

    fetchReviews()
  }, [toolId, supabase, toast])

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault()

    if (rating === 0) {
      toast({
        title: "Rating required",
        description: "Please select a rating before submitting",
        variant: "destructive",
      })
      return
    }

    if (!comment.trim()) {
      toast({
        title: "Comment required",
        description: "Please write a comment before submitting",
        variant: "destructive",
      })
      return
    }

    setIsSubmitting(true)

    try {
      // In a real app, you'd get the user_id from auth
      const user_id = "anonymous-user"

      const { data, error } = await supabase.from("reviews").insert([
        {
          tool_id: toolId,
          user_id,
          rating,
          comment,
        },
      ])

      if (error) throw error

      toast({
        title: "Review submitted",
        description: "Thank you for your feedback!",
        variant: "default",
      })

      // Reset form
      setRating(0)
      setComment("")

      // Refresh reviews
      // In a real app, you'd update the reviews state with the new review
      // For simplicity, we'll just show a success message
    } catch (error) {
      console.error("Error submitting review:", error)
      toast({
        title: "Submission failed",
        description: "There was an error submitting your review. Please try again.",
        variant: "destructive",
      })
    } finally {
      setIsSubmitting(false)
    }
  }

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
        <div className="flex justify-center py-12">
          <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
        </div>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold mb-6">Customer Reviews</h2>

      {/* Rating Summary */}
      <div className="grid grid-cols-1 md:grid-cols-2 gap-8 mb-8">
        {/* Average Rating */}
        <div className="flex flex-col items-center justify-center">
          <div className="text-5xl font-bold mb-2">{averageRating.toFixed(1)}</div>
          <div className="flex items-center mb-1">
            {[1, 2, 3, 4, 5].map((star) => (
              <Star
                key={star}
                className={`h-6 w-6 ${
                  star <= Math.round(averageRating) ? "fill-amber-400 text-amber-400" : "text-gray-300"
                }`}
              />
            ))}
          </div>
          <div className="text-sm text-slate-500 dark:text-slate-400">
            Based on {totalReviews} {totalReviews === 1 ? "review" : "reviews"}
          </div>
        </div>

        {/* Rating Breakdown */}
        <div className="space-y-2">
          {[5, 4, 3, 2, 1].map((star) => (
            <div key={star} className="flex items-center">
              <div className="w-12 text-sm font-medium">{star} stars</div>
              <Progress value={((ratingStats[star] || 0) / totalReviews) * 100} className="h-2 mx-2 flex-grow" />
              <div className="w-12 text-sm text-right">{ratingStats[star] || 0}</div>
            </div>
          ))}
        </div>
      </div>

      {/* Review Form */}
      <div className="mb-8">
        <h3 className="text-xl font-semibold mb-4">Write a Review</h3>
        <form onSubmit={handleSubmit} className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">Your Rating</label>
            <div className="flex items-center">
              {Array.from({ length: 5 }).map((_, i) => (
                <button
                  key={i}
                  type="button"
                  onClick={() => setRating(i + 1)}
                  onMouseEnter={() => setHoverRating(i + 1)}
                  onMouseLeave={() => setHoverRating(0)}
                  className="text-amber-500 p-1 focus:outline-none"
                >
                  <Star
                    className={`h-8 w-8 ${(hoverRating || rating) > i ? "fill-current" : "stroke-current fill-none"}`}
                  />
                </button>
              ))}
            </div>
          </div>

          <div>
            <label htmlFor="comment" className="block text-sm font-medium mb-2">
              Your Review
            </label>
            <Textarea
              id="comment"
              placeholder="Share your experience with this tool..."
              value={comment}
              onChange={(e) => setComment(e.target.value)}
              rows={4}
              className="resize-none"
            />
          </div>

          <Button type="submit" disabled={isSubmitting}>
            {isSubmitting ? "Submitting..." : "Submit Review"}
          </Button>
        </form>
      </div>

      {/* Reviews List */}
      {reviews.length > 0 ? (
        <div className="space-y-6">
          {reviews.map((review) => (
            <div key={review.id} className="border-b border-slate-200 dark:border-slate-700 pb-6 last:border-0">
              <div className="flex items-start gap-4">
                <Avatar>
                  <AvatarImage src={review.profiles?.avatar_url || ""} />
                  <AvatarFallback>{review.profiles?.full_name?.charAt(0) || "U"}</AvatarFallback>
                </Avatar>

                <div className="flex-grow">
                  <div className="flex items-center justify-between mb-2">
                    <div>
                      <h4 className="font-semibold">{review.profiles?.full_name || "Anonymous User"}</h4>
                      <div className="flex items-center text-amber-500">
                        {Array.from({ length: 5 }).map((_, i) => (
                          <Star
                            key={i}
                            className={`h-4 w-4 ${i < review.rating ? "fill-current" : "stroke-current fill-none"}`}
                          />
                        ))}
                        <span className="ml-2 text-sm text-slate-600 dark:text-slate-400">
                          {new Date(review.created_at).toLocaleDateString()}
                        </span>
                      </div>
                    </div>
                  </div>

                  <p className="text-slate-700 dark:text-slate-300">{review.comment}</p>
                </div>
              </div>
            </div>
          ))}
        </div>
      ) : (
        <div className="text-center py-8">
          <p className="text-slate-600 dark:text-slate-400 mb-4">No reviews yet. Be the first to review this tool!</p>
        </div>
      )}
    </div>
  )
}
