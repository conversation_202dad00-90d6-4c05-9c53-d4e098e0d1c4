import { createServerClient } from "@/lib/supabase/server"
import Tool<PERSON>ard from "@/components/tool-card"

export default async function SearchResults({
  query,
  category,
}: {
  query: string
  category: string
}) {
  const supabase = await createServerClient()

  let supabaseQuery = supabase.from("tools").select("*")

  if (query) {
    supabaseQuery = supabaseQuery.or(
      `company_name.ilike.%${query}%,short_description.ilike.%${query}%,full_description.ilike.%${query}%`,
    )
  }

  if (category && category !== "all") {
    supabaseQuery = supabaseQuery.eq("primary_task", category)
  }

  const {
    data: tools,
    error,
    count,
  } = await supabaseQuery.order("is_featured", { ascending: false }).order("click_count", { ascending: false })

  if (error) {
    console.error("Error searching tools:", error.message)
    return (
      <div className="text-center py-12 bg-white rounded-lg shadow-sm">
        <p className="text-gray-600">Error searching tools. Please try again later.</p>
      </div>
    )
  }

  if (!tools || tools.length === 0) {
    return (
      <div className="text-center py-12 bg-white rounded-lg shadow-sm">
        <h2 className="text-2xl font-bold mb-2">No results found</h2>
        <p className="text-gray-600">
          Try adjusting your search or filter to find what you're looking for.
        </p>
      </div>
    )
  }

  return (
    <div>
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800">
          {tools.length} {tools.length === 1 ? "result" : "results"} found
        </h2>
      </div>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-6">
        {tools.map((tool) => (
          <div key={tool.id} className="h-full">
            <ToolCard tool={tool} />
          </div>
        ))}
      </div>
    </div>
  )
}
