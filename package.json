{"name": "my-v0-project", "version": "0.1.2", "private": true, "engines": {"node": "22.x"}, "scripts": {"dev": "next dev", "build": "next build", "start": "next start", "lint": "next lint", "check-sitemap": "node scripts/check-sitemap.js", "seo-check": "npm run check-sitemap"}, "dependencies": {"@hookform/resolvers": "^5.0.1", "@radix-ui/react-accordion": "^1.2.10", "@radix-ui/react-alert-dialog": "^1.1.13", "@radix-ui/react-aspect-ratio": "^1.1.6", "@radix-ui/react-avatar": "^1.1.9", "@radix-ui/react-checkbox": "^1.3.1", "@radix-ui/react-collapsible": "^1.1.10", "@radix-ui/react-context-menu": "^2.2.14", "@radix-ui/react-dialog": "^1.1.13", "@radix-ui/react-dropdown-menu": "^2.1.14", "@radix-ui/react-hover-card": "^1.1.13", "@radix-ui/react-label": "^2.1.6", "@radix-ui/react-menubar": "^1.1.14", "@radix-ui/react-navigation-menu": "^1.2.12", "@radix-ui/react-popover": "^1.1.13", "@radix-ui/react-progress": "^1.1.6", "@radix-ui/react-radio-group": "^1.3.6", "@radix-ui/react-scroll-area": "^1.2.8", "@radix-ui/react-select": "^2.2.4", "@radix-ui/react-separator": "^1.1.6", "@radix-ui/react-slider": "^1.3.4", "@radix-ui/react-slot": "^1.2.2", "@radix-ui/react-switch": "^1.2.4", "@radix-ui/react-tabs": "^1.1.11", "@radix-ui/react-toast": "^1.2.13", "@radix-ui/react-toggle": "^1.1.8", "@radix-ui/react-toggle-group": "^1.1.9", "@radix-ui/react-tooltip": "^1.2.6", "@supabase/ssr": "^0.6.1", "@supabase/supabase-js": "^2.49.4", "@svgr/webpack": "^8.1.0", "@tailwindcss/aspect-ratio": "^0.4.2", "@tailwindcss/typography": "^0.5.16", "@tanstack/react-query": "^5.76.1", "autoprefixer": "^10.4.21", "buffer": "^6.0.3", "caniuse-lite": "^1.0.30001718", "class-variance-authority": "^0.7.1", "clsx": "^2.1.1", "cmdk": "^1.1.1", "critters": "^0.0.23", "date-fns": "^4.1.0", "dotenv": "^16.5.0", "embla-carousel-react": "^8.6.0", "framer-motion": "^12.19.1", "input-otp": "^1.4.2", "js-cookie": "^3.0.5", "lru-cache": "^11.1.0", "lucide-react": "^0.511.0", "next": "^15.3.2", "next-themes": "^0.4.6", "postcss": "^8.5.3", "process": "^0.11.10", "react": "^19.1.0", "react-day-picker": "^9.7.0", "react-dom": "^19.1.0", "react-hook-form": "^7.56.4", "react-icons": "^5.5.0", "react-intersection-observer": "^9.16.0", "react-resizable-panels": "^3.0.2", "sonner": "^2.0.3", "tailwind-merge": "^3.3.0", "tailwindcss": "^3.4.1", "tailwindcss-animate": "^1.0.7", "vaul": "^1.1.2", "zod": "^3.24.4"}, "devDependencies": {"@next/bundle-analyzer": "^15.3.2", "@types/node": "^22.15.18", "@types/react": "^19.1.4", "@types/react-dom": "^19.1.5", "cross-env": "^7.0.3", "css-loader": "^7.1.2", "cssnano": "^7.0.7", "eslint": "9.27.0", "eslint-config-next": "15.3.2", "glob": "^11.0.2", "postcss-loader": "^8.1.1", "postcss-preset-env": "^10.1.6", "postcss-url": "^10.1.3", "rimraf": "^6.0.1", "sass-loader": "^16.0.5", "sharp": "^0.34.1", "style-loader": "^4.0.0", "typescript": "^5.8.3", "uuid": "^11.1.0"}}