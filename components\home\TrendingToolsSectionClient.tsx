"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { ArrowRight, Star, TrendingUp } from "lucide-react"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import MotionWrapper from "@/components/ui/MotionWrapper"

interface Tool {
  id: string
  name: string
  slug: string
  description?: string
  logo_url?: string
}

interface Category {
  id: string
  name: string
  count: number
  color: string
  tools: Tool[]
}

// Define available category colors
const categoryColors = [
  "bg-blue-500",
  "bg-pink-500",
  "bg-purple-500",
  "bg-amber-500",
  "bg-emerald-500",
  "bg-violet-500",
  "bg-red-500",
  "bg-sky-500",
  "bg-teal-500",
  "bg-fuchsia-500",
  "bg-indigo-500",
  "bg-rose-500",
]

export default function TrendingToolsSectionClient() {
  const [categories, setCategories] = useState<Category[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchCategories = async () => {
      try {
        setIsLoading(true)
        const supabase = createBrowserClient()

        // First get all distinct primary tasks (categories)
        const { data: taskCounts, error: distinctError } = await supabase
          .from('tools')
          .select('primary_task, id')
          .not('primary_task', 'is', null)

        if (distinctError) throw distinctError

        // Process the data to count occurrences of each primary task
        const taskCountMap: Record<string, number> = {}
        if (taskCounts && Array.isArray(taskCounts)) {
          taskCounts.forEach(item => {
            if (item.primary_task) {
              taskCountMap[item.primary_task] = (taskCountMap[item.primary_task] || 0) + 1
            }
          })
        }

        // Convert to array and sort by count
        const sortedCategories = Object.entries(taskCountMap)
          .map(([name, count], index) => ({
            id: name.toLowerCase().replace(/\s+/g, '-'),
            name,
            count,
            color: categoryColors[index % categoryColors.length],
            tools: [] as Tool[]
          }))
          .sort((a, b) => b.count - a.count)
          .slice(0, 12) // Limit to top 12 categories

        // Add special categories
        const allCategories: Category[] = [
          {
            id: "top-trends",
            name: "Top Trends",
            count: 0,
            color: "bg-primary",
            tools: []
          },
          {
            id: "latest",
            name: "Latest AI",
            count: 0,
            color: "bg-accent",
            tools: []
          },
          ...sortedCategories
        ]

        // Fetch tools for each category
        const categoriesWithTools = await Promise.all(
          allCategories.map(async (category) => {
            let query = supabase.from("tools").select("id, company_name, slug, short_description, logo_url")

            if (category.id === "top-trends") {
              // Top trends based on click count
              query = query.order("click_count", { ascending: false }).limit(4)
            } else if (category.id === "latest") {
              // Latest tools based on creation date
              query = query.order("created_at", { ascending: false }).limit(4)
            } else {
              // Category specific tools
              query = query
                .eq("primary_task", category.name)
                .order("click_count", { ascending: false })
                .limit(4)
            }

            const { data: tools, error } = await query

            if (error) {
              console.error(`Error fetching tools for ${category.name}:`, error)
              return category
            }

            if (!tools || tools.length === 0) {
              return category
            }

            return {
              ...category,
              tools: tools.map(tool => ({
                id: tool.id,
                name: tool.company_name,
                slug: tool.slug || "",
                description: tool.short_description,
                logo_url: tool.logo_url
              })),
              count: category.id === "top-trends" || category.id === "latest" 
                ? tools.length 
                : category.count
            }
          })
        )

        // Filter out categories with no tools
        const filteredCategories = categoriesWithTools.filter(category => category.tools.length > 0)
        setCategories(filteredCategories)
      } catch (err) {
        console.error("Error fetching trending tools:", err)
        setError("Failed to load categories. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchCategories()
  }, [])

  if (isLoading) {
    return (
      <section className="py-12 md:py-16 bg-secondary/30 dark:bg-secondary/10">
        <div className="container-wide">
          <div className="flex justify-center items-center py-16">
            <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary"></div>
          </div>
        </div>
      </section>
    )
  }

  if (error) {
    return (
      <section className="py-12 md:py-16 bg-secondary/30 dark:bg-secondary/10">
        <div className="container-wide">
          <div className="text-center py-8">
            <p className="text-muted-foreground">{error}</p>
          </div>
        </div>
      </section>
    )
  }

  return (
    <section className="py-12 md:py-16 bg-secondary/30 dark:bg-secondary/10">
      <div className="container-wide">
        <MotionWrapper animation="fadeIn">
          <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-8">
            <div>
              <h2 className="text-2xl md:text-3xl font-bold">Browse Categories</h2>
              <p className="mt-1 text-sm text-muted-foreground">
                Explore AI tools by popular categories
              </p>
            </div>

            <Link
              href="/tools"
              className="mt-2 sm:mt-0 inline-flex items-center gap-1 text-primary text-sm font-medium hover:underline group"
            >
              View all categories
              <ArrowRight size={16} className="ml-1 transition-transform group-hover:translate-x-1" />
            </Link>
          </div>
        </MotionWrapper>

        <MotionWrapper animation="fadeIn" delay="delay-200">
          <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
            {categories.map((category) => (
              <CategoryCard key={category.id} category={category} />
            ))}
          </div>
        </MotionWrapper>
      </div>
    </section>
  )
}

function CategoryCard({ category }: { category: Category }) {
  return (
    <Card className="h-full border border-input hover:shadow-md transition-all duration-300 overflow-hidden hover:border-primary/30 group">
      <CardContent className="p-4">
        {/* Header with category name and count */}
        <div className="flex items-center justify-between mb-4">
          <div className="flex items-center gap-2">
            {category.id === "latest" ? (
              <Star size={16} className="text-amber-500" />
            ) : category.id === "top-trends" ? (
              <TrendingUp size={16} className="text-primary" />
            ) : (
              <span className={`w-3 h-3 rounded-full ${category.color}`}></span>
            )}
            <h3 className="text-lg font-semibold truncate">{category.name}</h3>
          </div>
          <div className="flex items-center gap-2 flex-shrink-0">
            <Badge variant="outline" className="text-xs">
              {category.count}
            </Badge>
            <Link
              href={`/tools?category=${category.id}`}
              className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-primary hover:text-primary/80 bg-primary/10 hover:bg-primary/20 rounded-md transition-all duration-200 group/btn"
            >
              <span>View All</span>
              <svg className="w-3 h-3 transition-transform group-hover/btn:translate-x-0.5" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
              </svg>
            </Link>
          </div>
        </div>

        {/* Tools list with individual links */}
        <div className="space-y-2">
          {category.tools.map((tool) => (
            <div key={tool.id} className="group/tool">
              <Link
                href={`/Tool/${tool.slug || tool.name.toLowerCase().replace(/\s+/g, '-')}`}
                className="block p-3 rounded-lg hover:bg-muted/50 transition-colors border border-transparent hover:border-primary/20"
              >
                <div className="flex items-start justify-between gap-3">
                  {/* Tool name - larger and more prominent */}
                  <div className="flex-1 min-w-0">
                    <h4 className="text-base font-semibold text-foreground group-hover/tool:text-primary transition-colors leading-tight">
                      {tool.name.length > 30 ? `${tool.name.substring(0, 30)}...` : tool.name}
                    </h4>

                    {/* Tool details in the remaining space */}
                    <div className="mt-1 flex items-center gap-2 flex-wrap">
                      {tool.description && (
                        <span className="text-sm text-muted-foreground">
                          {tool.description.length > 50 ? `${tool.description.substring(0, 50)}...` : tool.description}
                        </span>
                      )}

                      {tool.pricing && (
                        <span className={`inline-flex items-center gap-1 px-2 py-1 rounded-full text-xs font-medium ${
                          tool.pricing.toLowerCase().includes('free')
                            ? 'text-emerald-700 bg-emerald-100 dark:text-emerald-300 dark:bg-emerald-900/30'
                            : 'text-amber-700 bg-amber-100 dark:text-amber-300 dark:bg-amber-900/30'
                        }`}>
                          {tool.pricing.toLowerCase().includes('free') && (
                            <div className="w-1.5 h-1.5 bg-emerald-500 rounded-full animate-pulse"></div>
                          )}
                          {tool.pricing}
                        </span>
                      )}
                    </div>
                  </div>

                  {/* Status indicators on the right */}
                  <div className="flex items-center gap-1.5 flex-shrink-0 mt-1">
                    {tool.featured && (
                      <div className="w-2 h-2 bg-amber-500 rounded-full" title="Featured" />
                    )}
                    {tool.verified && (
                      <div className="w-2 h-2 bg-blue-500 rounded-full" title="Verified" />
                    )}
                    <svg className="w-4 h-4 text-muted-foreground group-hover/tool:text-primary transition-colors" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                      <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M9 5l7 7-7 7" />
                    </svg>
                  </div>
                </div>
              </Link>
            </div>
          ))}
        </div>
      </CardContent>
    </Card>
  )
}
