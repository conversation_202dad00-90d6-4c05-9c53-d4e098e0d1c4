import { Metadata } from 'next'
import { notFound } from 'next/navigation'
import { createServerClient } from '@/lib/supabase/server'
import ToolPageClient from './tool-page-client'
import { generateToolStructuredData, generateBreadcrumbData, combineStructuredData } from '@/lib/seo/structured-data'
import { ToolJsonLd, BreadcrumbJsonLd } from '@/components/seo/json-ld-schema'

interface Props {
  params: { slug: string }
}

export async function generateMetadata({ params }: Props): Promise<Metadata> {
  const { slug } = params

  try {
    const supabase = await createServerClient()
    const { data: tool } = await supabase
      .from('tools')
      .select('*')
      .eq('slug', slug)
      .single()

    if (!tool) {
      return {
        title: 'Tool Not Found | AiAnyTool.com',
        description: 'The requested AI tool was not found in our directory.',
      }
    }

    // Dynamic title based on tool data
    const title = `${tool.company_name} - ${tool.primary_task || 'AI Tool'} | AiAnyTool.com`

    // Dynamic description using short_description or fallback
    const description = tool.short_description ||
      `${tool.company_name} is an AI tool for ${tool.primary_task || 'productivity'}. ${tool.full_description ? tool.full_description.substring(0, 120) + '...' : 'Read our detailed review, pricing information, and find the best alternatives.'}`

    // Dynamic keywords based on tool data
    const keywords = [
      tool.company_name,
      tool.primary_task,
      tool.secondary_task,
      tool.tertiary_task,
      'AI tool',
      'review',
      'pricing',
      'features',
      'alternatives',
      '2024',
      // Add pricing-related keywords if available
      ...(tool.pricing_model ? [tool.pricing_model, 'pricing'] : []),
      // Add category-specific keywords
      ...(tool.primary_task ? [tool.primary_task.toLowerCase(), `${tool.primary_task} AI`] : []),
    ].filter(Boolean)

    // Dynamic Open Graph title and description
    const ogTitle = `${tool.company_name} - ${tool.primary_task || 'AI Tool'}`
    const ogDescription = tool.short_description || description

    return {
      title,
      description,
      keywords,
      authors: [{ name: 'AiAnyTool Team', url: 'https://aianytool.com' }],
      openGraph: {
        title: ogTitle,
        description: ogDescription,
        url: `https://aianytool.com/Tool/${slug}`,
        type: 'article',
        publishedTime: tool.created_at,
        modifiedTime: tool.updated_at,
        authors: ['AiAnyTool Team'],
        section: tool.primary_task || 'AI Tools',
        tags: keywords,
        locale: 'en_US',
        siteName: 'AiAnyTool.com',
        images: tool.featured_image_url ? [
          {
            url: tool.featured_image_url.startsWith('http') ? tool.featured_image_url : `https://aianytool.com${tool.featured_image_url}`,
            secureUrl: tool.featured_image_url.startsWith('http') ? tool.featured_image_url : `https://aianytool.com${tool.featured_image_url}`,
            width: 1200,
            height: 630,
            alt: `${tool.company_name} - ${tool.primary_task || 'AI Tool'} Interface Screenshot | Review & Features`,
            type: 'image/jpeg',
          },
          // Add logo as secondary image
          ...(tool.logo_url ? [{
            url: tool.logo_url.startsWith('http') ? tool.logo_url : `https://aianytool.com${tool.logo_url}`,
            secureUrl: tool.logo_url.startsWith('http') ? tool.logo_url : `https://aianytool.com${tool.logo_url}`,
            width: 400,
            height: 400,
            alt: `${tool.company_name} Logo`,
            type: 'image/png',
          }] : [])
        ] : [
          {
            url: 'https://aianytool.com/og-default-tool.jpg',
            secureUrl: 'https://aianytool.com/og-default-tool.jpg',
            width: 1200,
            height: 630,
            alt: `${tool.company_name} - AI Tool Review & Analysis | AiAnyTool.com`,
            type: 'image/jpeg',
          }
        ],
        // Add article-specific properties
        ...(tool.pricing && {
          article: {
            publishedTime: tool.created_at,
            modifiedTime: tool.updated_at,
            authors: ['https://aianytool.com/about'],
            section: tool.primary_task || 'AI Tools',
            tags: keywords,
          }
        }),
      },
      twitter: {
        card: 'summary_large_image',
        title: ogTitle.length > 70 ? ogTitle.substring(0, 67) + '...' : ogTitle,
        description: ogDescription.length > 200 ? ogDescription.substring(0, 197) + '...' : ogDescription,
        images: tool.featured_image_url ? [
          tool.featured_image_url.startsWith('http') ? tool.featured_image_url : `https://aianytool.com${tool.featured_image_url}`
        ] : ['https://aianytool.com/og-default-tool.jpg'],
        creator: '@aianytool',
        site: '@aianytool',
      },
      alternates: {
        canonical: `https://aianytool.com/Tool/${slug}`,
      },
      other: {
        'article:author': 'AiAnyTool Team',
        'article:section': tool.primary_task || 'AI Tools',
        'article:tag': keywords.join(', '),
      },
    }
  } catch (error) {
    console.error('Error generating metadata:', error)
    return {
      title: 'AI Tool | AiAnyTool.com',
      description: 'Discover AI tools and software solutions.',
    }
  }
}

export default async function ToolPage({ params }: Props) {
  const { slug } = params

  // Get tool data on server
  let tool = null

  try {
    const supabase = await createServerClient()
    const { data } = await supabase
      .from('tools')
      .select('*')
      .eq('slug', slug)
      .single()
    tool = data
  } catch (error) {
    console.error('Error fetching tool:', error)
  }

  // If no tool found, show 404
  if (!tool) {
    notFound()
  }

  // Generate structured data
  const toolStructuredData = generateToolStructuredData(tool)
  const breadcrumbData = generateBreadcrumbData([
    { name: 'Home', url: 'https://aianytool.com' },
    { name: 'Tools', url: 'https://aianytool.com/tools' },
    { name: tool.company_name, url: `https://aianytool.com/Tool/${slug}` }
  ])

  const structuredDataScript = combineStructuredData([toolStructuredData, breadcrumbData])

  return (
    <>
      <script
        type="application/ld+json"
        dangerouslySetInnerHTML={{ __html: structuredDataScript }}
      />
      {/* Fallback SEO content if server data fails */}
      {!tool && (
        <noscript>
          <main>
            <h1>{slug.replace(/-/g, ' ')} - AI Tool</h1>
            <p>Discover {slug.replace(/-/g, ' ')}, an innovative AI tool. Find features, pricing, and reviews on AiAnyTool.com.</p>
            <div>Category: AI Tool</div>
            <div>Platform: Web-based</div>
            <a href={`https://aianytool.com/Tool/${slug}`}>View Tool Details</a>
          </main>
        </noscript>
      )}

      {/* Original design with server-rendered data */}
      <ToolPageClient slug={slug} initialTool={tool} />

      {/* JSON-LD Schema */}
      {tool && <ToolJsonLd tool={tool} />}

      <BreadcrumbJsonLd
        items={[
          { name: 'Home', url: 'https://aianytool.com' },
          { name: 'Tools', url: 'https://aianytool.com/tools' },
          { name: tool?.company_name || slug.replace(/-/g, ' '), url: `https://aianytool.com/Tool/${slug}` }
        ]}
      />
    </>
  )
}

