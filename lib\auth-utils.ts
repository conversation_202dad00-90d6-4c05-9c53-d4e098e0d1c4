import { createBrowserClient } from '@/lib/supabase/client-utils'

/**
 * Check if user exists in profiles table and get their role
 */
export async function getUserProfile(userId: string) {
  const supabase = createBrowserClient()
  if (!supabase) return null

  try {
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('role, full_name, avatar_url, created_at, email')
      .eq('id', userId)
      .single()

    if (error) {
      console.warn('Profile not found:', error.message)
      return null
    }

    return profile
  } catch (err) {
    console.warn('Error fetching profile:', err)
    return null
  }
}

/**
 * Create a new profile for a user
 */
export async function createUserProfile(userId: string, email: string, fullName?: string, role: 'user' | 'admin' = 'user') {
  const supabase = createBrowserClient()
  if (!supabase) return null

  try {
    const { data, error } = await supabase
      .from('profiles')
      .insert({
        id: userId,
        email,
        full_name: fullName || email.split('@')[0],
        role,
        created_at: new Date().toISOString(),
        updated_at: new Date().toISOString()
      })
      .select()
      .single()

    if (error) {
      console.error('Error creating profile:', error.message)
      return null
    }

    return data
  } catch (err) {
    console.error('Error creating profile:', err)
    return null
  }
}

/**
 * Check if email exists in existing profiles (for existing users)
 */
export async function checkExistingUser(email: string) {
  const supabase = createBrowserClient()
  if (!supabase) return null

  try {
    const { data: profile, error } = await supabase
      .from('profiles')
      .select('id, role, full_name, email')
      .eq('email', email)
      .single()

    if (error) {
      return null
    }

    return profile
  } catch (err) {
    return null
  }
}
