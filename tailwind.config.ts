import type { Config } from "tailwindcss";

const config: Config = {
    darkMode: ["class"],
    content: [
    "./pages/**/*.{js,ts,jsx,tsx,mdx}",
    "./components/**/*.{js,ts,jsx,tsx,mdx}",
    "./app/**/*.{js,ts,jsx,tsx,mdx}",
    "*.{js,ts,jsx,tsx,mdx}"
  ],
  theme: {
  	extend: {
      // Add custom animation delay utilities
      transitionDelay: {
        '200': '200ms',
        '400': '400ms',
        '600': '600ms',
        '800': '800ms',
      },
  		colors: {
  			background: 'hsl(var(--background))',
  			foreground: 'hsl(var(--foreground))',
  			card: {
  				DEFAULT: 'hsl(var(--card))',
  				foreground: 'hsl(var(--card-foreground))'
  			},
  			popover: {
  				DEFAULT: 'hsl(var(--popover))',
  				foreground: 'hsl(var(--popover-foreground))'
  			},
  			primary: {
  				DEFAULT: 'hsl(var(--primary))',
  				foreground: 'hsl(var(--primary-foreground))'
  			},
  			secondary: {
  				DEFAULT: 'hsl(var(--secondary))',
  				foreground: 'hsl(var(--secondary-foreground))'
  			},
  			muted: {
  				DEFAULT: 'hsl(var(--muted))',
  				foreground: 'hsl(var(--muted-foreground))'
  			},
  			accent: {
  				DEFAULT: 'hsl(var(--accent))',
  				foreground: 'hsl(var(--accent-foreground))'
  			},
  			destructive: {
  				DEFAULT: 'hsl(var(--destructive))',
  				foreground: 'hsl(var(--destructive-foreground))'
  			},
  			border: 'hsl(var(--border))',
  			input: 'hsl(var(--input))',
  			ring: 'hsl(var(--ring))',
  			chart: {
  				'1': 'hsl(var(--chart-1))',
  				'2': 'hsl(var(--chart-2))',
  				'3': 'hsl(var(--chart-3))',
  				'4': 'hsl(var(--chart-4))',
  				'5': 'hsl(var(--chart-5))'
  			},
  			sidebar: {
  				DEFAULT: 'hsl(var(--sidebar-background))',
  				foreground: 'hsl(var(--sidebar-foreground))',
  				primary: 'hsl(var(--sidebar-primary))',
  				'primary-foreground': 'hsl(var(--sidebar-primary-foreground))',
  				accent: 'hsl(var(--sidebar-accent))',
  				'accent-foreground': 'hsl(var(--sidebar-accent-foreground))',
  				border: 'hsl(var(--sidebar-border))',
  				ring: 'hsl(var(--sidebar-ring))'
  			}
  		},
  		borderRadius: {
  			lg: 'var(--radius)',
  			md: 'calc(var(--radius) - 2px)',
  			sm: 'calc(var(--radius) - 4px)'
  		},
  		keyframes: {
  			'accordion-down': {
  				from: {
  					height: '0'
  				},
  				to: {
  					height: 'var(--radix-accordion-content-height)'
  				}
  			},
  			'accordion-up': {
  				from: {
  					height: 'var(--radix-accordion-content-height)'
  				},
  				to: {
  					height: '0'
  				}
  			},
            'pulse-slow': {
                '0%, 100%': {
                    opacity: '1'
                },
                '50%': {
                    opacity: '0.7'
                }
            },
            'shimmer': {
                '0%': {
                    backgroundPosition: '-200% 0'
                },
                '100%': {
                    backgroundPosition: '200% 0'
                }
            },
            'fadeIn': {
                from: {
                    opacity: '0'
                },
                to: {
                    opacity: '1'
                }
            },
            'slideUp': {
                from: {
                    opacity: '0',
                    transform: 'translateY(20px)'
                },
                to: {
                    opacity: '1',
                    transform: 'translateY(0)'
                }
            },
            'slideDown': {
                from: {
                    opacity: '0',
                    transform: 'translateY(-20px)'
                },
                to: {
                    opacity: '1',
                    transform: 'translateY(0)'
                }
            },
            'slideInRight': {
                from: {
                    opacity: '0',
                    transform: 'translateX(-20px)'
                },
                to: {
                    opacity: '1',
                    transform: 'translateX(0)'
                }
            },
            'scaleIn': {
                from: {
                    opacity: '0',
                    transform: 'scale(0.9)'
                },
                to: {
                    opacity: '1',
                    transform: 'scale(1)'
                }
            }
  		},
  		animation: {
  			'accordion-down': 'accordion-down 0.2s ease-out',
  			'accordion-up': 'accordion-up 0.2s ease-out',
            'pulse-slow': 'pulse-slow 3s cubic-bezier(0.4, 0, 0.6, 1) infinite',
            'shimmer': 'shimmer 2s linear infinite',
            'fadeIn': 'fadeIn 0.6s ease-out forwards',
            'slideUp': 'slideUp 0.6s ease-out forwards',
            'slideDown': 'slideDown 0.6s ease-out forwards',
            'slideInRight': 'slideInRight 0.6s ease-out forwards',
            'scaleIn': 'scaleIn 0.6s ease-out forwards'
  		}
  	}
  },
  plugins: [require("tailwindcss-animate")],
};
export default config;
