"use client"

import { useState, useEffect, memo } from 'react'
import Image from 'next/image'
import { cn } from '@/lib/utils'

interface OptimizedImageProps {
  src: string
  alt: string
  width: number
  height: number
  className?: string
  priority?: boolean
  sizes?: string
  quality?: number
  fill?: boolean
  style?: React.CSSProperties
  objectFit?: 'contain' | 'cover' | 'fill' | 'none' | 'scale-down'
  objectPosition?: string
  placeholder?: 'blur' | 'empty'
  blurDataURL?: string
  lazyLoad?: boolean
  lowQualityPreview?: boolean
  onLoad?: () => void
  fallbackSrc?: string
}

// تحويل الصورة إلى صورة منخفضة الجودة للتحميل المسبق
const generateLowQualityUrl = (src: string): string => {
  if (!src || src.startsWith('data:')) return src;

  // إذا كانت الصورة من Cloudinary، استخدم تحويلات Cloudinary
  if (src.includes('cloudinary.com')) {
    return src.replace('/upload/', '/upload/q_10,w_50/');
  }

  // إذا كانت الصورة من Imgix، استخدم تحويلات Imgix
  if (src.includes('imgix.net')) {
    return `${src}${src.includes('?') ? '&' : '?'}q=10&w=50`;
  }

  // صورة احتياطية بسيطة للصور الأخرى
  return src;
};

const OptimizedImage = ({
  src,
  alt,
  width,
  height,
  className,
  priority = false,
  sizes = '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw',
  quality = 75,
  fill = false,
  style,
  objectFit = 'cover',
  objectPosition = 'center',
  placeholder = 'empty',
  blurDataURL,
  lazyLoad = !priority,
  lowQualityPreview = false,
  onLoad,
  fallbackSrc = '/images/placeholder.png',
}: OptimizedImageProps) => {
  const [isLoaded, setIsLoaded] = useState(false)
  const [isError, setIsError] = useState(false)
  const [inView, setInView] = useState(!lazyLoad) // Default to true if not lazy loading

  // Simple intersection observer replacement
  useEffect(() => {
    if (lazyLoad && typeof window !== 'undefined') {
      const observer = new IntersectionObserver(
        ([entry]) => {
          if (entry.isIntersecting) {
            setInView(true)
            observer.disconnect()
          }
        },
        { rootMargin: '200px' }
      )

      const element = document.getElementById(`img-${src}`)
      if (element) {
        observer.observe(element)
      }

      return () => observer.disconnect()
    }
  }, [lazyLoad, src])
  
  // إنشاء URL للصورة المصغرة المستخدمة قبل تحميل الصورة الأصلية
  const thumbnailSrc = lowQualityPreview 
    ? `${src.split('?')[0]}?quality=10&width=50` 
    : undefined
  
  // حالة تحميل الصورة
  const [imgSrc, setImgSrc] = useState<string | undefined>(
    priority 
      ? src 
      : thumbnailSrc
  )
  
  // تغيير مصدر الصورة عندما تصبح مرئية في الشاشة
  useEffect(() => {
    if (lazyLoad && inView && imgSrc !== src) {
      setImgSrc(src)
    }
  }, [inView, lazyLoad, src, imgSrc])
  
  // معالجة الخطأ: إذا فشل تحميل الصورة، استخدم الصورة الاحتياطية
  const handleError = () => {
    setIsError(true)
    if (fallbackSrc && fallbackSrc !== src) {
      setImgSrc(fallbackSrc)
    }
  }
  
  // معالجة التحميل الناجح للصورة
  const handleImageLoad = () => {
    setIsLoaded(true)
    if (onLoad) onLoad()
    
    // إذا كانت الصورة الأولية هي المصغرة، قم بتحميل الصورة كاملة الجودة
    if (imgSrc === thumbnailSrc) {
      setImgSrc(src)
    }
  }
  
  // إنشاء استعلامات الوسائط المتعددة للصور المستجيبة
  const responsiveSizes = sizes || (
    fill 
      ? '100vw' 
      : width >= 768 
        ? `${Math.min(width, 1200)}px` 
        : '100vw'
  )
  
  // خصائص النمط المشتقة
  const imageStyle = {
    ...style,
    objectFit,
    objectPosition,
    // إضافة تأثير التلاشي للصور المحملة حديثًا
    opacity: isLoaded ? 1 : 0.5,
    transition: 'opacity 0.3s ease-in-out',
  }
  
  return (
    <div
      id={`img-${src}`}
      className={cn(
        'overflow-hidden relative',
        isLoaded ? 'animate-none' : 'animate-pulse',
        className
      )}
      style={{
        width: fill ? '100%' : width,
        height: fill ? '100%' : height,
        background: '#f5f5f5',
      }}
    >
      {/* تحميل الصورة فقط عندما تكون ذات أولوية أو في نطاق الرؤية */}
      {(priority || inView || !lazyLoad) && imgSrc && (
        <Image 
          src={imgSrc}
          alt={alt}
          width={width}
          height={height}
          quality={quality}
          sizes={responsiveSizes}
          priority={priority}
          fill={fill}
          style={imageStyle}
          onLoad={handleImageLoad}
          onError={handleError}
          placeholder={placeholder}
          blurDataURL={blurDataURL}
          loading={priority ? 'eager' : 'lazy'}
        />
      )}
      
      {/* عرض مؤشر التحميل إذا لم تكن الصورة محمّلة بعد */}
      {!isLoaded && !isError && (
        <div className="absolute inset-0 flex items-center justify-center bg-gray-100 dark:bg-gray-800">
          <div className="w-6 h-6 border-2 border-primary border-t-transparent rounded-full animate-spin" />
        </div>
      )}
    </div>
  )
}

// استخدام memo لتجنب إعادة العرض غير الضرورية
export default memo(OptimizedImage)
