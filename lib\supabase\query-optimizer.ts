/**
 * Supabase Query Optimizer
 * Optimizes queries to prevent timeouts and improve performance
 */

import { supabase } from './client'
import { QUERY_TIMEOUTS, QUERY_LIMITS, withQueryTimeout } from '../query-timeout-config'

export interface QueryOptions {
  timeout?: number
  limit?: number
  retries?: number
  useCache?: boolean
  cacheKey?: string
  cacheDuration?: number
}

export interface SearchFilters {
  category?: string
  pricing?: string
  features?: string[]
  verified?: boolean
  featured?: boolean
  sortBy?: string
}

/**
 * Optimized tools query builder
 */
export class ToolsQueryBuilder {
  private supabase: any
  private baseColumns = `
    id,
    company_name,
    short_description,
    logo_url,
    primary_task,
    pricing,
    visit_website_url,
    detail_url,
    slug,
    is_featured,
    is_verified,
    click_count,
    rating,
    created_at,
    updated_at
  `

  constructor() {
    this.supabase = supabase
    if (!this.supabase) {
      throw new Error('Failed to initialize Supabase client')
    }
  }

  /**
   * Calculate and update average ratings for tools based on reviews
   */
  async updateToolRatings(): Promise<void> {
    try {
      // Get all tools with their review averages
      const { data: toolRatings, error } = await this.supabase
        .from('reviews')
        .select(`
          tool_id,
          rating
        `)

      if (error) {
        console.error('Error fetching reviews for rating calculation:', error)
        return
      }

      // Calculate average ratings per tool
      const ratingMap = new Map<number, { sum: number; count: number }>()

      toolRatings?.forEach((review: any) => {
        const toolId = review.tool_id
        const rating = review.rating

        if (!ratingMap.has(toolId)) {
          ratingMap.set(toolId, { sum: 0, count: 0 })
        }

        const current = ratingMap.get(toolId)!
        current.sum += rating
        current.count += 1
      })

      // Update tools table with calculated averages
      const updatePromises = Array.from(ratingMap.entries()).map(async ([toolId, { sum, count }]) => {
        const averageRating = Math.round((sum / count) * 10) / 10 // Round to 1 decimal place

        return this.supabase
          .from('tools')
          .update({ rating: averageRating })
          .eq('id', toolId)
      })

      await Promise.all(updatePromises)
      console.log(`Updated ratings for ${ratingMap.size} tools`)

    } catch (error) {
      console.error('Error updating tool ratings:', error)
    }
  }

  /**
   * Build an optimized query for tools
   */
  buildQuery(filters: SearchFilters = {}, options: QueryOptions = {}) {
    const {
      category,
      pricing,
      features = [],
      verified,
      featured,
      sortBy = 'default'
    } = filters

    const {
      limit = QUERY_LIMITS.TOOLS_GRID
    } = options

    let query = this.supabase
      .from('tools')
      .select(this.baseColumns)

    // Apply filters in order of selectivity (most selective first)
    
    // 1. Featured filter (highly selective)
    if (featured !== undefined) {
      query = query.eq('is_featured', featured)
    }

    // 2. Verified filter (moderately selective)
    if (verified !== undefined) {
      query = query.eq('is_verified', verified)
    }

    // 3. Category filter (moderately selective)
    if (category && category !== 'all') {
      const categoryValue = this.formatCategoryValue(category)
      query = query.eq('primary_task', categoryValue)
    }

    // 4. Pricing filter (less selective)
    if (pricing && pricing !== 'all') {
      query = query.eq('pricing', pricing)
    }

    // Skip complex feature filtering to avoid timeout
    // Features filtering is now handled client-side for better performance

    // Apply ordering
    query = this.applyOrdering(query, sortBy)

    // Apply limit with safety cap
    const safeLimit = Math.min(limit, QUERY_LIMITS.MAX_TOOLS)
    query = query.limit(safeLimit)

    return query
  }

  /**
   * Build a search query with optimizations
   */
  buildSearchQuery(searchTerm: string, filters: SearchFilters = {}, options: QueryOptions = {}) {
    const {
      category,
      pricing,
      verified,
      featured,
      sortBy = 'relevance'
    } = filters

    const {
      limit = QUERY_LIMITS.SEARCH_RESULTS
    } = options

    let query = this.supabase
      .from('tools')
      .select(this.baseColumns)

    // Apply filters first (more selective)
    if (featured !== undefined) {
      query = query.eq('is_featured', featured)
    }

    if (verified !== undefined) {
      query = query.eq('is_verified', verified)
    }

    if (category && category !== 'all') {
      const categoryValue = this.formatCategoryValue(category)
      query = query.eq('primary_task', categoryValue)
    }

    if (pricing && pricing !== 'all') {
      query = query.eq('pricing', pricing)
    }

    // Apply search only if term is meaningful
    if (searchTerm && searchTerm.trim().length >= 2) {
      const cleanTerm = searchTerm.trim()
      // Use simplified search to avoid timeout
      query = query.or(
        `company_name.ilike.%${cleanTerm}%,short_description.ilike.%${cleanTerm}%`
      )
    }

    // Apply ordering
    query = this.applyOrdering(query, sortBy)

    // Apply conservative limit for search
    const safeLimit = Math.min(limit, QUERY_LIMITS.MAX_SEARCH)
    query = query.limit(safeLimit)

    return query
  }

  /**
   * Execute query with timeout and fallback
   */
  async executeQuery(query: any, options: QueryOptions = {}) {
    const {
      timeout = QUERY_TIMEOUTS.MEDIUM,
      retries = 1,
      useCache = true,
      cacheKey,
      cacheDuration = 300000 // 5 minutes
    } = options

    // Check cache first
    if (useCache && cacheKey) {
      const cached = this.getFromCache(cacheKey)
      if (cached) {
        return { data: cached, error: null, fromCache: true }
      }
    }

    let lastError: any = null

    for (let attempt = 0; attempt <= retries; attempt++) {
      try {
        const result = await withQueryTimeout(query, timeout)
        const { data, error } = result

        if (error) {
          throw error
        }

        // Cache successful results
        if (useCache && cacheKey && data) {
          this.setCache(cacheKey, data, cacheDuration)
        }

        return { data: data || [], error: null }
      } catch (error: any) {
        lastError = error
        console.warn(`Query attempt ${attempt + 1} failed:`, error.message)

        // If it's a timeout, try fallback on last attempt
        if (attempt === retries && this.isTimeoutError(error)) {
          console.warn('Query timed out, trying fallback...')
          return this.executeFallbackQuery()
        }

        // Don't retry for non-retryable errors
        if (!this.isRetryableError(error)) {
          break
        }

        // Wait before retry
        if (attempt < retries) {
          await this.delay(1000 * Math.pow(2, attempt))
        }
      }
    }

    return { data: [], error: lastError }
  }

  /**
   * Execute a simple fallback query
   */
  private async executeFallbackQuery() {
    try {
      const fallbackQuery = this.supabase
        .from('tools')
        .select(this.baseColumns)
        .order('is_featured', { ascending: false })
        .order('is_verified', { ascending: false })
        .limit(20)

      const { data, error } = await withQueryTimeout(fallbackQuery, QUERY_TIMEOUTS.FAST)

      if (error) {
        throw error
      }

      return { data: data || [], error: null, fromFallback: true }
    } catch (error) {
      return { data: [], error }
    }
  }

  /**
   * Apply ordering to query
   */
  private applyOrdering(query: any, sortBy: string) {
    switch (sortBy) {
      case 'newest':
      case 'recent':
        // Get tools added or updated in the last 60 days, prioritizing recent updates
        const sixtyDaysAgo = new Date()
        sixtyDaysAgo.setDate(sixtyDaysAgo.getDate() - 60)
        const dateFilter = sixtyDaysAgo.toISOString()

        return query
          .or(`created_at.gte.${dateFilter},updated_at.gte.${dateFilter}`)
          .order('updated_at', { ascending: false })
          .order('created_at', { ascending: false })
      case 'popular':
        return query.order('click_count', { ascending: false })
      case 'top-rated':
        // Filter tools with actual ratings and sort by rating first
        return query
          .gt('rating', 0)
          .order('rating', { ascending: false })
          .order('click_count', { ascending: false })
      case 'featured':
        return query
          .order('is_featured', { ascending: false })
          .order('rating', { ascending: false })
          .order('click_count', { ascending: false })
      case 'relevance':
      default:
        return query
          .order('is_featured', { ascending: false })
          .order('is_verified', { ascending: false })
          .order('rating', { ascending: false })
          .order('click_count', { ascending: false })
    }
  }

  /**
   * Format category value for query
   */
  private formatCategoryValue(category: string): string {
    if (category.includes('-')) {
      return category
        .split('-')
        .map(word => word.charAt(0).toUpperCase() + word.slice(1))
        .join(' ')
    }
    return category
  }

  /**
   * Check if error is timeout-related
   */
  private isTimeoutError(error: any): boolean {
    const message = error.message?.toLowerCase() || ''
    return message.includes('timeout') || 
           message.includes('canceling statement') ||
           message.includes('query timeout')
  }

  /**
   * Check if error is retryable
   */
  private isRetryableError(error: any): boolean {
    const message = error.message?.toLowerCase() || ''
    const retryableErrors = ['timeout', 'network', 'connection', 'temporary']
    const nonRetryableErrors = ['syntax', 'permission', 'not found', 'invalid']
    
    if (nonRetryableErrors.some(err => message.includes(err))) {
      return false
    }
    
    return retryableErrors.some(err => message.includes(err))
  }

  /**
   * Simple cache implementation
   */
  private getFromCache(key: string): any {
    try {
      const cached = sessionStorage.getItem(`query_cache_${key}`)
      if (cached) {
        const { data, timestamp, duration } = JSON.parse(cached)
        if (Date.now() - timestamp < duration) {
          return data
        }
        sessionStorage.removeItem(`query_cache_${key}`)
      }
    } catch (e) {
      // Ignore cache errors
    }
    return null
  }

  private setCache(key: string, data: any, duration: number): void {
    try {
      const cacheData = {
        data,
        timestamp: Date.now(),
        duration
      }
      sessionStorage.setItem(`query_cache_${key}`, JSON.stringify(cacheData))
    } catch (e) {
      // Ignore cache errors
    }
  }

  /**
   * Delay utility
   */
  private delay(ms: number): Promise<void> {
    return new Promise(resolve => setTimeout(resolve, ms))
  }
}

/**
 * Create a new tools query builder instance
 */
export function createToolsQueryBuilder(): ToolsQueryBuilder {
  return new ToolsQueryBuilder()
}

/**
 * Quick helper for simple tools queries
 */
export async function queryTools(filters: SearchFilters = {}, options: QueryOptions = {}) {
  const builder = createToolsQueryBuilder()
  const query = builder.buildQuery(filters, options)
  return builder.executeQuery(query, options)
}

/**
 * Quick helper for search queries
 */
export async function searchTools(searchTerm: string, filters: SearchFilters = {}, options: QueryOptions = {}) {
  const builder = createToolsQueryBuilder()
  const query = builder.buildSearchQuery(searchTerm, filters, options)
  return builder.executeQuery(query, options)
}

export default ToolsQueryBuilder
