import { createServerClient } from "@/lib/supabase/server"
import ToolCard from "@/components/tool-card"

interface SimilarToolsProps {
  currentToolId: number
  category: string
}

export default async function SimilarTools({ currentToolId, category }: SimilarToolsProps) {
  const supabase = await createServerClient()
  const { data: tools, error } = await supabase
    .from("tools")
    .select("*")
    .eq("primary_task", category)
    .neq("id", currentToolId)
    .limit(6)

  if (error) {
    console.error("Error fetching similar tools:", error.message)
    return (
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
        <p className="text-slate-600 dark:text-slate-400 text-center py-8">
          Error loading similar tools. Please try again later.
        </p>
      </div>
    )
  }

  if (!tools || tools.length === 0) {
    return (
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
        <p className="text-slate-600 dark:text-slate-400 text-center py-8">No similar tools found.</p>
      </div>
    )
  }

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
      <h2 className="text-2xl font-bold mb-6">Similar Tools</h2>

      <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
        {tools.map((tool) => (
          <ToolCard key={tool.id} tool={tool} />
        ))}
      </div>
    </div>
  )
}
