"use client"

import { useState, useEffect } from "react"
import <PERSON> from "next/link"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { But<PERSON> } from "@/components/ui/button"
import { ArrowRight } from "lucide-react"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"
import ToolCard from "@/components/tool-card"

interface FeaturedToolsCarouselProps {
  variant?: "featured" | "top-rated" | "recently-added"
}

export default function FeaturedToolsCarousel({ variant = "featured" }: FeaturedToolsCarouselProps) {
  const [tools, setTools] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  // Title and description based on variant
  const sectionContent = {
    "featured": {
      title: "Featured Tools",
      description: "Discover the most popular and powerful AI tools that are transforming industries."
    },
    "top-rated": {
      title: "Top Rated Tools",
      description: "Explore the highest-rated AI tools loved by our community."
    },
    "recently-added": {
      title: "Recently Added",
      description: "Stay up to date with the latest AI innovations and newly added tools."
    }
  }

  useEffect(() => {
    const fetchTools = async () => {
      setLoading(true)
      try {
        const supabase = createBrowserClient()
        
        let query = supabase
          .from("tools")
          .select("*")
          .limit(12)
        
        // Apply different filters based on variant
        if (variant === "featured") {
          query = query.eq("is_featured", true).order("popularity", { ascending: false })
        } else if (variant === "top-rated") {
          query = query.order("rating", { ascending: false })
        } else if (variant === "recently-added") {
          query = query.order("created_at", { ascending: false })
        }
        
        const { data, error } = await query
        
        if (error) {
          console.error(`Error fetching ${variant} tools:`, error)
          setError(`Failed to load ${variant} tools. Please try again.`)
        } else if (data) {
          console.log(`Loaded ${data.length} ${variant} tools`)
          setTools(data)
        }
      } catch (err) {
        console.error("Error in tools fetch:", err)
        setError("An unexpected error occurred. Please try again.")
      } finally {
        setLoading(false)
      }
    }
    
    fetchTools()
  }, [variant])
  
  // Loading UI
  if (loading) {
    return (
      <div className="text-center py-12">
        <div className="inline-block h-8 w-8 animate-spin rounded-full border-4 border-solid border-current border-r-transparent align-[-0.125em] motion-reduce:animate-[spin_1.5s_linear_infinite]"></div>
        <p className="mt-2 text-slate-600 dark:text-slate-400">Loading {variant} tools...</p>
      </div>
    )
  }

  // Error UI
  if (error) {
    return (
      <div className="text-center text-red-500 py-12">
        <p>{error}</p>
      </div>
    )
  }

  // Empty state
  if (tools.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-600 dark:text-slate-400">No {variant} tools found.</p>
      </div>
    )
  }

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <div>
          <h2 className="text-xl font-bold text-slate-900 dark:text-white">
            {sectionContent[variant].title}
          </h2>
          <p className="text-sm text-slate-600 dark:text-slate-400">
            {sectionContent[variant].description}
          </p>
        </div>
        
        <Link href={`/tools${variant !== 'featured' ? `?filter=${variant}` : ''}`} className="text-sm font-medium text-blue-600 dark:text-blue-400 hover:text-blue-700 dark:hover:text-blue-300 flex items-center">
          See all <ArrowRight className="ml-1 h-3.5 w-3.5" />
        </Link>
      </div>
      
      {/* Horizontal scrollable carousel without nav buttons to match image */}
      <div className="relative w-full overflow-x-auto pb-4 hide-scrollbar">
        <div className="flex space-x-4 w-max">
          {tools.map((tool) => (
            <div key={tool.id} className="w-[280px] flex-none">
              <ToolCard tool={tool} />
            </div>
          ))}
        </div>
      </div>
    </div>
  )
}
