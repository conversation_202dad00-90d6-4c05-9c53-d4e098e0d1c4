'use client'

import type { AuthProvider, AuthProviderType } from './types'
import { SupabaseAuthProvider } from './supabase-provider'


// تحديد المزود المستخدم من متغيرات البيئة
export function getAuthProviderType(): AuthProviderType {
  // يمكنك تغيير هذا لاختبار مزودين مختلفين
  const provider = process.env.NEXT_PUBLIC_AUTH_PROVIDER as AuthProviderType
  
  // إذا لم يتم تحديد المزود، استخدم Supabase كافتراضي
  if (provider === 'supabase') {
    return provider
  }

  // التحقق من وجود متغيرات البيئة لتحديد المزود تلقائياً
  if (process.env.NEXT_PUBLIC_SUPABASE_URL && process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
    return 'supabase'
  }

  // افتراضي إلى Supabase
  return 'supabase'
}

// Factory function لإنشاء المزود المناسب
export function createAuthProvider(providerType?: AuthProviderType): AuthProvider {
  const type = providerType || getAuthProviderType()
  
  switch (type) {
    case 'supabase':
      return new SupabaseAuthProvider()
    default:
      throw new Error(`Unsupported auth provider: ${type}`)
  }
}

// Hook للاستخدام في React components
export function useAuthProvider(): AuthProvider {
  const providerType = getAuthProviderType()
  return createAuthProvider(providerType)
}

// معلومات المزود الحالي
export function getProviderInfo() {
  const type = getAuthProviderType()
  
  const providers = {
    supabase: {
      name: 'Supabase',
      description: 'Open source Firebase alternative',
      features: ['Email/Password', 'OAuth', 'Database integration'],
      color: '#3ECF8E'
    },

  }
  
  return {
    type,
    ...providers[type]
  }
}
