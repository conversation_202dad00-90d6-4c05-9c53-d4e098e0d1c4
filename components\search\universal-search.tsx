"use client"

import { useRef, useEffect, useCallback } from "react"
import { useRouter } from "next/navigation"
import { Search, Loader2 } from "lucide-react"
import { Input } from "@/components/ui/input"
import { Button } from "@/components/ui/button"
import { useEnhancedSearch } from "@/hooks/use-enhanced-search"
import InstantResults from "@/components/search/instant-results"
import { cn } from "@/lib/utils"

interface SearchFilters {
  category?: string
  pricing?: string
  features?: string[]
  sortBy?: string
}

// Types for different search modes
type SearchMode = 'navigation' | 'instant-filter' | 'hybrid'
type SearchContext = 'general' | 'tools' | 'categories'

export interface UniversalSearchProps {
  // Core Configuration (optional for backward compatibility)
  mode?: SearchMode
  context?: SearchContext
  
  // Appearance (نفس التصميم الحالي)
  variant?: 'header' | 'hero' | 'page' | 'sidebar'
  size?: 'sm' | 'md' | 'lg' | 'xl'
  placeholder?: string
  className?: string
  
  // Behavior
  initialValue?: string
  autoFocus?: boolean
  showSearchButton?: boolean
  showKeyboardShortcut?: boolean
  showInstantResults?: boolean  // Added for backward compatibility
  enhancedSearch?: boolean      // Added for backward compatibility
  
  // Styling (نفس التصميم الحالي)
  rounded?: 'none' | 'sm' | 'md' | 'lg' | 'xl' | 'full'
  glass?: boolean
  fullWidth?: boolean
  border?: boolean              // Added for backward compatibility
  
  // Search Configuration
  maxResults?: number
  debounceMs?: number
  minQueryLength?: number
  
  // Filters (for instant-filter and hybrid modes)
  filters?: SearchFilters
  onFiltersChange?: (filters: SearchFilters) => void
  
  // Callbacks
  onSearch?: (query: string, filters?: SearchFilters) => void
  onInstantFilter?: (query: string) => void
  onResultSelect?: (result: any) => void
}

// Size classes (نفس UnifiedSearch بالضبط)
const sizeClasses = {
  sm: "h-8 text-xs px-3",
  md: "h-10 text-sm px-4",
  lg: "h-12 text-base px-5",
  xl: "h-14 text-lg px-6"
}

// Rounded classes (نفس UnifiedSearch بالضبط)
const roundedClasses = {
  none: "rounded-none",
  sm: "rounded-sm",
  md: "rounded-md",
  lg: "rounded-lg",
  xl: "rounded-xl",
  full: "rounded-full"
}

// Variant styles (نفس UnifiedSearch بالضبط)
const variantStyles = {
  header: {
    container: "relative",
    input: "glass-dark border-border/50 focus:border-primary/50",
    iconSize: "h-4 w-4",
    iconPosition: "left-3"
  },
  hero: {
    container: "relative",
    input: "border-2 border-border/50 focus:border-primary/50 shadow-lg",
    iconSize: "h-5 w-5",
    iconPosition: "left-4"
  },
  page: {
    container: "relative",
    input: "border border-border focus:border-primary",
    iconSize: "h-4 w-4",
    iconPosition: "left-3"
  },
  sidebar: {
    container: "relative",
    input: "border border-border/30 focus:border-primary/70 bg-background/50",
    iconSize: "h-4 w-4",
    iconPosition: "left-3"
  }
}

export default function UniversalSearch({
  mode = 'navigation',  // Default mode for backward compatibility
  context = 'general',
  variant = 'page',
  size = 'md',
  placeholder = "Search...",
  className,
  initialValue = "",
  autoFocus = false,
  showSearchButton = false,
  showKeyboardShortcut = false,
  showInstantResults = true,  // Added for backward compatibility
  enhancedSearch = false,     // Added for backward compatibility
  rounded = 'lg',
  glass = false,
  fullWidth = true,
  border = true,              // Added for backward compatibility
  maxResults = 8,
  debounceMs = 300,
  minQueryLength = 1,
  filters,
  onFiltersChange,
  onSearch,
  onInstantFilter,
  onResultSelect
}: UniversalSearchProps) {
  const router = useRouter()
  const searchRef = useRef<HTMLDivElement>(null)

  // Enhanced search hook (نفس منطق UnifiedSearch)
  const {
    query,
    results: searchResults,
    isLoading: searchLoading,
    isOpen: searchOpen,
    selectedIndex,
    handleInputChange: originalHandleInputChange,
    handleKeyDown: handleSearchKeyDown,
    closeDropdown: closeSearchDropdown,
    clearSearch,
    setIsOpen: setSearchOpen
  } = useEnhancedSearch({
    maxResults,
    filters,
    enhancedSearch: showInstantResults,
    initialValue
  })

  // Custom input change handler for instant filter
  const handleInputChange = (value: string) => {
    originalHandleInputChange(value)

    // Call instant filter callback for modes that support it
    if ((mode === 'instant-filter' || mode === 'hybrid') && onInstantFilter) {
      onInstantFilter(value)
    }
  }

  // Optimized search submission with better performance and debugging
  const handleSubmit = useCallback((e: React.FormEvent) => {
    e.preventDefault()
    const trimmedQuery = query.trim()

    if (!trimmedQuery) return

    // Close dropdown immediately for better UX
    closeSearchDropdown()

    if (onSearch) {
      onSearch(trimmedQuery, filters)
    } else {
      // Build search URL for navigation
      const searchUrl = `/search?q=${encodeURIComponent(trimmedQuery)}`

      // Add filters if they exist
      const params = new URLSearchParams()
      params.set('q', trimmedQuery)

      if (filters?.category && filters.category !== 'all') {
        params.set('category', filters.category)
      }
      if (filters?.pricing && filters.pricing !== 'all') {
        params.set('pricing', filters.pricing)
      }
      if (filters?.sortBy && filters.sortBy !== 'relevance') {
        params.set('sortBy', filters.sortBy)
      }
      if (filters?.features && filters.features.length > 0) {
        params.set('features', filters.features.join(','))
      }

      const finalUrl = `/search?${params.toString()}`

      // Navigate to search results page
      router.push(finalUrl)
    }
  }, [query, filters, onSearch, router, closeSearchDropdown])

  // Handle result selection (نفس منطق UnifiedSearch)
  const handleResultSelect = (result: any) => {
    if (onResultSelect) {
      onResultSelect(result)
    } else {
      if (result.slug) {
        router.push(`/Tool/${result.slug}`)
      }
    }
    closeSearchDropdown()
  }

  // Handle view all results (نفس منطق UnifiedSearch)
  const handleViewAllResults = () => {
    if (query.trim()) {
      router.push(`/search?q=${encodeURIComponent(query.trim())}`)
      closeSearchDropdown()
    }
  }

  // Handle instant filter for specific modes
  useEffect(() => {
    if ((mode === 'instant-filter' || mode === 'hybrid') && onInstantFilter) {
      // For instant filter and hybrid modes, call the callback on every query change
      const timeoutId = setTimeout(() => {
        onInstantFilter(query)  // Only pass query, not filters
      }, 300) // 300ms debounce

      return () => clearTimeout(timeoutId)
    }
  }, [query, mode, onInstantFilter])

  // Get variant styles
  const styles = variantStyles[variant]

  // Build input classes (نفس الـ classes من UnifiedSearch بالضبط)
  const inputClasses = cn(
    sizeClasses[size],
    roundedClasses[rounded],
    styles.input,
    glass && "glass-dark",
    !border && "border-0",
    fullWidth ? "w-full" : "w-auto",
    "pl-10 pr-4 focus-visible:ring-2 focus-visible:ring-primary/20 theme-transition touch-manipulation transition-all duration-200",
    showSearchButton && "pr-12",
    showKeyboardShortcut && "pr-16",
    // Mobile-specific improvements
    "min-h-[44px] text-base sm:text-sm",
    // Loading state styling
    searchLoading && "opacity-75"
  )

  // Handle click outside to close search (نفس منطق UnifiedSearch)
  useEffect(() => {
    const handleClickOutside = (event: MouseEvent) => {
      if (searchRef.current && !searchRef.current.contains(event.target as Node)) {
        closeSearchDropdown()
      }
    }

    document.addEventListener('mousedown', handleClickOutside)
    return () => document.removeEventListener('mousedown', handleClickOutside)
  }, [closeSearchDropdown])

  // تحديد نوع النتائج المطلوب عرضها
  const shouldShowInstantResults = showInstantResults && (mode === 'navigation' || mode === 'hybrid') && searchOpen && query.trim().length > 0

  return (
    <div ref={searchRef} className={cn(styles.container, "search-container relative z-[9998]", className)}>
      <form onSubmit={handleSubmit} className="relative">
        <Input
          type="search"
          value={query}
          onChange={(e) => handleInputChange(e.target.value)}
          onKeyDown={(e) => {
            const selectedResult = handleSearchKeyDown(e)
            if (selectedResult) {
              // Handle result selection from keyboard navigation
              handleResultSelect(selectedResult)
            }
          }}
          onFocus={() => showInstantResults && query.trim().length > 0 && setSearchOpen(true)}
          placeholder={placeholder}
          className={inputClasses}
          autoFocus={autoFocus}
          autoComplete="off"
          autoCorrect="off"
          autoCapitalize="off"
          spellCheck="false"
          data-search="true"
        />

        {/* Search Icon */}
        <Search className={cn(
          styles.iconSize,
          styles.iconPosition,
          "absolute top-1/2 transform -translate-y-1/2 text-muted-foreground transition-colors",
          query && "text-primary/70"
        )} />

        {/* Loading Indicator */}
        {searchLoading && (
          <div className="absolute right-3 top-1/2 transform -translate-y-1/2 z-10">
            <Loader2 className="h-4 w-4 animate-spin text-primary" />
          </div>
        )}

        {/* Search Button - Optimized */}
        {showSearchButton && !searchLoading && (
          <Button
            type="submit"
            size="sm"
            className="absolute right-1 top-1/2 transform -translate-y-1/2 h-8 w-8 p-0 rounded-full bg-primary hover:bg-primary/90 text-primary-foreground transition-all duration-200 focus:ring-2 focus:ring-primary/20 shadow-sm"
            disabled={!query.trim() || searchLoading}
            onClick={(e) => {
              // Prevent double submission
              if (!query.trim() || searchLoading) {
                e.preventDefault()
                return
              }
            }}
          >
            <Search className="h-3.5 w-3.5" />
            <span className="sr-only">Search</span>
          </Button>
        )}

        {/* Keyboard Shortcut */}
        {showKeyboardShortcut && !searchLoading && !showSearchButton && (
          <div className="absolute right-2 top-1/2 transform -translate-y-1/2 flex items-center">
            <kbd className="hidden lg:inline-flex items-center px-1.5 py-0.5 text-xs bg-muted/80 text-muted-foreground rounded border border-border/50">
              ⌘K
            </kbd>
          </div>
        )}
      </form>

      {/* Instant Results with Portal */}
      {shouldShowInstantResults && (
        <InstantResults
          results={searchResults}
          isLoading={searchLoading}
          isOpen={searchOpen}
          selectedIndex={selectedIndex}
          query={query}
          onResultSelect={handleResultSelect}
          onViewAll={handleViewAllResults}
          containerRef={searchRef}
          className="mt-2"
        />
      )}
    </div>
  )
}
