/**
 * Application Configuration
 * Centralized configuration for the AI Tools Directory
 */

// Environment variables with validation
export const env = {
  NODE_ENV: process.env.NODE_ENV || 'development',
  NEXT_PUBLIC_SUPABASE_URL: process.env.NEXT_PUBLIC_SUPABASE_URL || '',
  NEXT_PUBLIC_SUPABASE_ANON_KEY: process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY || '',
  NEXT_PUBLIC_SITE_URL: process.env.NEXT_PUBLIC_SITE_URL || 'http://localhost:3000',
} as const

// Validate required environment variables only in runtime (not during build)
const requiredEnvVars = [
  'NEXT_PUBLIC_SUPABASE_URL',
  'NEXT_PUBLIC_SUPABASE_ANON_KEY'
] as const

// Only validate environment variables if we're not in build mode
if (typeof window !== 'undefined' || process.env.NODE_ENV === 'development') {
  for (const envVar of requiredEnvVars) {
    if (!process.env[envVar]) {
      console.warn(`Missing environment variable: ${envVar}`)
    }
  }
}

// Application metadata
export const siteConfig = {
  name: "AI Any Tool",
  title: "AI Tools Directory - Discover and Compare the Best AI Tools",
  description: "Discover the best AI tools for productivity, creativity, and business. Our comprehensive directory helps you find and compare the perfect AI solutions for your needs.",
  keywords: [
    "AI tools",
    "artificial intelligence", 
    "machine learning tools",
    "productivity tools",
    "AI directory",
    "best AI tools",
    "AI software",
    "AI comparison"
  ],
  url: env.NEXT_PUBLIC_SITE_URL,
  ogImage: `${env.NEXT_PUBLIC_SITE_URL}/og-image.jpg`,
  creator: "AI Any Tool Team",
  social: {
    twitter: "@aianytool",
    github: "https://github.com/aianytool",
    linkedin: "https://linkedin.com/company/aianytool"
  }
} as const

// Navigation configuration
export const navigation = {
  main: [
    { href: "/", label: "Home", icon: "Sparkles" },
    { href: "/tools", label: "Tools", icon: "Wrench" },
    { href: "/categories", label: "Categories", icon: "Grid" },
  ],
  footer: [
    {
      title: "Product",
      links: [
        { href: "/tools", label: "Browse Tools" },
        { href: "/categories", label: "Categories" },
        { href: "/submit", label: "Submit Tool" },
        { href: "/featured", label: "Featured" }
      ]
    },
    {
      title: "Company",
      links: [
        { href: "/about", label: "About" },
        { href: "/contact", label: "Contact" },
        { href: "/careers", label: "Careers" }
      ]
    },
    {
      title: "Support",
      links: [
        { href: "/help", label: "Help Center" },
        { href: "/privacy", label: "Privacy" },
        { href: "/terms", label: "Terms" },
        { href: "/faq", label: "FAQ" }
      ]
    }
  ]
} as const

// Search configuration
export const searchConfig = {
  debounceMs: 300,
  pageSize: 12,
  maxSuggestions: 5,
  cacheTimeout: 5 * 60 * 1000, // 5 minutes
  sortOptions: [
    { value: 'featured', label: 'Featured' },
    { value: 'rating', label: 'Highest Rated' },
    { value: 'recent', label: 'Recently Added' },
    { value: 'popular', label: 'Most Popular' },
    { value: 'name', label: 'Name A-Z' }
  ],
  pricingOptions: [
    { value: 'free', label: 'Free' },
    { value: 'freemium', label: 'Freemium' },
    { value: 'paid', label: 'Paid' },
    { value: 'subscription', label: 'Subscription' }
  ]
} as const

// UI configuration
export const uiConfig = {
  breakpoints: {
    sm: 640,
    md: 768,
    lg: 1024,
    xl: 1280,
    '2xl': 1536
  },
  animations: {
    duration: {
      fast: 150,
      normal: 300,
      slow: 500
    },
    easing: {
      default: 'cubic-bezier(0.4, 0, 0.2, 1)',
      bounce: 'cubic-bezier(0.68, -0.55, 0.265, 1.55)'
    }
  },
  grid: {
    columns: {
      mobile: 1,
      tablet: 2,
      desktop: 3,
      wide: 4
    },
    gap: {
      sm: 4,
      md: 6,
      lg: 8
    }
  }
} as const

// Feature flags
export const features = {
  enableAnalytics: true, // Enable analytics in all environments for testing
  enablePWA: true,
  enableOfflineMode: false,
  enableNotifications: true,
  enableDarkMode: true,
  enableSearch: true,
  enableFilters: true,
  enableFavorites: true,
  enableReviews: true,
  enableSubmissions: true,
  enableAdmin: false,
  enableBlog: true,
  enableNewsletter: true,
  enableSocialLogin: true,
  enableEmailLogin: true,
  enableCookieConsent: false // Disabled for better UX - using modern approach
} as const

// API configuration
export const apiConfig = {
  timeout: 10000, // 10 seconds
  retries: 3,
  retryDelay: 1000, // 1 second
  endpoints: {
    tools: '/api/tools',
    search: '/api/search',
    suggestions: '/api/suggestions',
    categories: '/api/categories',
    reviews: '/api/reviews',
    favorites: '/api/favorites'
  }
} as const

// Performance configuration
export const performanceConfig = {
  imageOptimization: {
    quality: 75,
    formats: ['webp', 'avif'],
    sizes: '(max-width: 768px) 100vw, (max-width: 1200px) 50vw, 33vw'
  },
  caching: {
    staticAssets: 31536000, // 1 year
    apiResponses: 300, // 5 minutes
    pages: 3600 // 1 hour
  },
  bundleAnalysis: env.NODE_ENV === 'development'
} as const

// Error tracking configuration
export const errorConfig = {
  enableErrorBoundary: true,
  enableConsoleLogging: env.NODE_ENV === 'development',
  enableRemoteLogging: env.NODE_ENV === 'production',
  maxErrorsPerSession: 10,
  errorRetentionDays: 30
} as const

// Security configuration
export const securityConfig = {
  enableCSP: env.NODE_ENV === 'production',
  enableHSTS: env.NODE_ENV === 'production',
  enableXSSProtection: true,
  enableFrameGuard: true,
  sessionTimeout: 24 * 60 * 60 * 1000, // 24 hours
  maxLoginAttempts: 5,
  lockoutDuration: 15 * 60 * 1000 // 15 minutes
} as const

// Cookie consent configuration
export const cookieConsentConfig = {
  enabled: features.enableCookieConsent,
  bannerPosition: 'bottom' as const,
  bannerVariant: 'detailed' as const,
  autoShow: true,
  respectDoNotTrack: true,
  consentVersion: '1.0',
  expiryDays: 365,
  categories: {
    essential: {
      required: true,
      enabled: true
    },
    analytics: {
      required: false,
      enabled: true, // Enable analytics by default
      description: 'Help us improve our website by analyzing usage patterns'
    },
    marketing: {
      required: false,
      enabled: true, // Enable marketing by default for comprehensive tracking
      description: 'Allow personalized ads and marketing content'
    },
    preferences: {
      required: false,
      enabled: true, // Enable preferences by default for better user experience
      description: 'Remember your settings and preferences'
    }
  }
} as const



// Export all configurations
export const config = {
  env,
  site: siteConfig,
  navigation,
  search: searchConfig,
  ui: uiConfig,
  features,
  api: apiConfig,
  performance: performanceConfig,
  error: errorConfig,
  security: securityConfig,
  cookieConsent: cookieConsentConfig
} as const

export default config
