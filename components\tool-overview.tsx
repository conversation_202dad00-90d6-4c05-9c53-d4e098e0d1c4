import Image from "next/image"
import { <PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON> } from "lucide-react"
import { Carousel, CarouselContent, CarouselItem, CarouselNext, CarouselPrevious } from "@/components/ui/carousel"

interface ToolOverviewProps {
  tool: any
}

export default function ToolOverview({ tool }: ToolOverviewProps) {
  // Mock screenshots for the gallery
  const screenshots = [
    tool.featured_image_url || "/placeholder.svg?height=400&width=800",
    "/placeholder.svg?height=400&width=800&text=Screenshot+2",
    "/placeholder.svg?height=400&width=800&text=Screenshot+3",
  ]

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md overflow-hidden">
      <div className="p-6">
        {/* Screenshots Gallery */}
        <div className="mb-8">
          <h3 className="text-xl font-semibold mb-4">Screenshots</h3>
          <Carousel className="w-full">
            <CarouselContent>
              {screenshots.map((screenshot, index) => (
                <CarouselItem key={index} className="basis-full">
                  <div className="relative h-64 md:h-80 w-full rounded-lg overflow-hidden">
                    <Image
                      src={screenshot || "/placeholder.svg"}
                      alt={`${tool.company_name} screenshot ${index + 1}`}
                      fill
                      className="object-cover"
                    />
                  </div>
                </CarouselItem>
              ))}
            </CarouselContent>
            <CarouselPrevious className="left-2" />
            <CarouselNext className="right-2" />
          </Carousel>
        </div>

        {/* Description */}
        <div className="mb-8">
          <h3 className="text-xl font-semibold mb-4">Description</h3>
          <div className="prose dark:prose-invert max-w-none">
            <div dangerouslySetInnerHTML={{ __html: tool.full_description || tool.short_description }} />
          </div>
        </div>

        {/* Features and Pros */}
        <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* Features */}
          <div>
            <h3 className="text-xl font-semibold mb-4 flex items-center">
              <CheckCheck className="mr-2 h-5 w-5 text-blue-500" />
              Key Features
            </h3>
            <ul className="space-y-2">
              {tool.features &&
                tool.features.map((feature: any, index: number) => (
                  <li key={index} className="flex items-start gap-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-blue-500 mt-0.5 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <polyline points="9 11 12 14 22 4"></polyline>
                      <path d="M21 12v7a2 2 0 0 1-2 2H5a2 2 0 0 1-2-2h11"></path>
                    </svg>
                    <span>{typeof feature === 'string' ? feature : (feature?.name || feature?.value || String(feature))}</span>
                  </li>
                ))}
            </ul>
          </div>

          {/* Pros */}
          <div>
            <h3 className="text-xl font-semibold mb-4 flex items-center">
              <CheckCircle className="mr-2 h-5 w-5 text-emerald-500" />
              Pros
            </h3>
            <ul className="space-y-2">
              {tool.pros &&
                tool.pros.map((pro: any, index: number) => (
                  <li key={index} className="flex items-start gap-2">
                    <svg
                      xmlns="http://www.w3.org/2000/svg"
                      className="h-5 w-5 text-emerald-500 mt-0.5 flex-shrink-0"
                      viewBox="0 0 24 24"
                      fill="none"
                      stroke="currentColor"
                      strokeWidth="2"
                      strokeLinecap="round"
                      strokeLinejoin="round"
                    >
                      <path d="M22 11.08V12a10 10 0 1 1-5.93-9.14"></path>
                      <polyline points="22 4 12 14.01 9 11.01"></polyline>
                    </svg>
                    <span>{typeof pro === 'string' ? pro : (pro?.name || pro?.value || String(pro))}</span>
                  </li>
                ))}
            </ul>
          </div>
        </div>

        {/* Applicable Tasks */}
        {tool.applicable_tasks && tool.applicable_tasks.length > 0 && (
          <div className="mt-8">
            <h3 className="text-xl font-semibold mb-4">Applicable Tasks</h3>
            <div className="flex flex-wrap gap-2">
              {tool.applicable_tasks.map((task: any, index: number) => (
                <span key={index} className="px-3 py-1 bg-slate-100 dark:bg-slate-700 rounded-full text-sm">
                  {typeof task === 'string' ? task : (task?.name || task?.value || String(task))}
                </span>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  )
}
