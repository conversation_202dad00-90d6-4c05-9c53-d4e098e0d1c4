"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import { <PERSON>R<PERSON>, Star, TrendingUp, Clock, Zap } from "lucide-react"
import { But<PERSON> } from "@/components/ui/button"
import ToolGrid from "@/components/tools/ToolGrid"
import MotionWrapper from "@/components/ui/MotionWrapper"
import Grad<PERSON>Background from "@/components/ui/GradientBackground"

interface ToolsSectionProps {
  title: string
  description: string
  queryType: "featured" | "top-rated" | "recent" | "all"
  limit?: number
  variant?: "primary" | "secondary" | "accent" | "subtle" | "none"
}

export default function ToolsSection({
  title,
  description,
  queryType,
  limit = 20,
  variant = "none"
}: ToolsSectionProps) {
  // Start with default values that work for SSR
  const [deviceLimit, setDeviceLimit] = useState(8) // Start with mobile default
  const [isMobile, setIsMobile] = useState(true) // Start with mobile default
  const [isTablet, setIsTablet] = useState(false)
  const [isClient, setIsClient] = useState(false)

  // First useEffect just to mark when we're client-side
  useEffect(() => {
    setIsClient(true)
  }, [])

  // تحسين الأداء: تقليل معالجة تغيير حجم الشاشة
  useEffect(() => {
    if (!isClient) return;

    // Debounce function مع تأخير أطول لتقليل المعالجة
    let resizeTimer: NodeJS.Timeout;

    const handleResize = () => {
      clearTimeout(resizeTimer);

      resizeTimer = setTimeout(() => {
        const width = window.innerWidth;
        const isMobileView = width < 768;
        const isTabletView = width >= 768 && width < 1280;

        // تحديث الحالة فقط إذا تغيرت القيم
        if (isMobile !== isMobileView) setIsMobile(isMobileView);
        if (isTablet !== isTabletView) setIsTablet(isTabletView);

        // تقليل عدد الأدوات المعروضة لتحسين الأداء
        let newLimit;
        if (width >= 1280) {
          newLimit = 16; // تقليل من 20 إلى 16
        } else if (width >= 768) {
          newLimit = 8; // تقليل من 12 إلى 8
        } else {
          newLimit = 6; // تقليل من 8 إلى 6
        }
        
        if (deviceLimit !== newLimit) {
          setDeviceLimit(newLimit);
        }
      }, 200); // زيادة التأخير من 100ms إلى 200ms
    }

    // Initial calculation
    handleResize();

    // Add event listener مع passive للأداء
    window.addEventListener('resize', handleResize, { passive: true });

    // Cleanup
    return () => {
      window.removeEventListener('resize', handleResize);
      clearTimeout(resizeTimer);
    }
  }, [isClient, isMobile, isTablet, deviceLimit])

  return (
    <section className="py-4">
      <div className="container-wide">
        <MotionWrapper animation="fadeIn">
          <div className="flex items-center justify-between mb-4">
            <div>
              <h2 className="text-2xl md:text-3xl font-bold">{title}</h2>
              <p className="mt-1 text-sm text-muted-foreground">
                {description}
              </p>
            </div>
            <Link
              href="/tools"
              className="hidden sm:inline-flex items-center gap-1 rounded-lg border border-border bg-background px-4 py-2 text-sm font-medium hover:bg-accent hover:border-accent-foreground/20 theme-transition group"
            >
              View all
              <ArrowRight size={16} className="ml-1 transition-transform group-hover:translate-x-1" />
            </Link>
          </div>
        </MotionWrapper>

        <MotionWrapper animation="fadeIn" delay="delay-200">
          <ToolGrid
            queryType={queryType}
            limit={deviceLimit}
            columnsPerRow={isMobile ? 1 : isTablet ? 2 : 4}
          />
        </MotionWrapper>

        {/* Mobile View All Button */}
        <MotionWrapper animation="fadeIn" delay="delay-300" className="mt-4 text-center sm:hidden">
          <Button asChild variant="outline" className="inline-flex items-center gap-2 group theme-transition">
            <Link href="/tools">
              View all tools
              <ArrowRight size={16} className="ml-1 transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </MotionWrapper>

        {/* Desktop View More Button */}
        <MotionWrapper animation="fadeIn" delay="delay-400" className="mt-6 text-center hidden sm:block">
          <Button
            asChild
            variant="outline"
            size="lg"
            className="inline-flex items-center gap-2 px-8 py-3 text-base font-medium rounded-xl border-2 border-primary/20 bg-primary/5 hover:bg-primary/10 hover:border-primary/40 text-primary hover:text-primary transition-all duration-300 group shadow-sm hover:shadow-md"
          >
            <Link href={getMoreLink(queryType)} className="flex items-center gap-2">
              {getButtonIcon(queryType)}
              <span>{getMoreButtonText(queryType)}</span>
              <ArrowRight size={18} className="transition-transform group-hover:translate-x-1" />
            </Link>
          </Button>
        </MotionWrapper>
      </div>
    </section>
  )
}

// Helper functions for dynamic button text and links
function getMoreButtonText(queryType: string): string {
  switch (queryType) {
    case "featured":
      return "View More Featured Tools"
    case "top-rated":
      return "View More Top Rated Tools"
    case "recent":
      return "View More Recent Tools"
    case "popular":
      return "View More Popular Tools"
    default:
      return "View More Tools"
  }
}

function getMoreLink(queryType: string): string {
  switch (queryType) {
    case "featured":
      return "/tools?sort=featured"
    case "top-rated":
      return "/tools?sort=top-rated"
    case "recent":
      return "/tools?sort=newest"
    case "popular":
      return "/tools?sort=popular"
    default:
      return "/tools"
  }
}

function getButtonIcon(queryType: string): JSX.Element {
  switch (queryType) {
    case "featured":
      return <Star size={18} className="text-primary" />
    case "top-rated":
      return <TrendingUp size={18} className="text-primary" />
    case "recent":
      return <Clock size={18} className="text-primary" />
    case "popular":
      return <Zap size={18} className="text-primary" />
    default:
      return <Star size={18} className="text-primary" />
  }
}
