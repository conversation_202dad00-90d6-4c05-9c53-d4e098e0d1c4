'use client'

import { useEffect, useState } from "react"
import ToolCard from "@/components/tool-card"
import { <PERSON>ert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { supabase } from "@/lib/supabase/client"
import { LoadingGrid } from "@/components/ui/loading-grid"

interface ToolGridHomeProps {
  queryType: "featured" | "top-rated" | "recent" | "all"
  limit?: number
  columnsPerRow?: number
}

export default function ToolGridHome({
  queryType = "featured",
  limit = 12,
  columnsPerRow = 4,
}: ToolGridHomeProps) {
  const [tools, setTools] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchTools = async () => {
      setLoading(true)
      try {
        // Start building the query
        let query = supabase.from("tools").select("*")

        // Apply query type filter
        if (queryType === "featured") {
          query = query.eq("is_featured", true).order("click_count", { ascending: false })
        } else if (queryType === "top-rated") {
          query = query.order("rating", { ascending: false })
        } else if (queryType === "recent") {
          query = query.order("created_at", { ascending: false })
        } else {
          // Default sorting for "all"
          query = query.order("click_count", { ascending: false })
        }

        // Apply limit
        query = query.limit(limit)

        // Execute the query
        const { data, error } = await query

        if (error) {
          throw error
        }

        setTools(data || [])
      } catch (err) {
        console.error("Error fetching tools:", err)
        setError(err instanceof Error ? err : new Error('Unknown error'))
      } finally {
        setLoading(false)
      }
    }

    fetchTools()
  }, [queryType, limit])

  // Determine grid columns class based on columnsPerRow
  const gridColsClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 sm:grid-cols-2",
    3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
  }[columnsPerRow] || "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"

  if (loading) {
    return <LoadingGrid count={limit} columns={columnsPerRow} />
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load tools. Please try again later.
        </AlertDescription>
      </Alert>
    )
  }

  // If no tools found
  if (!tools || tools.length === 0) {
    return (
      <Alert variant="default" className="bg-muted">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>No tools found</AlertTitle>
        <AlertDescription>
          No tools available in this category.
        </AlertDescription>
      </Alert>
    )
  }

  // Render the grid
  return (
    <div className={`grid ${gridColsClasses} gap-4`}>
      {tools.map((tool) => (
        <ToolCard key={tool.id} tool={tool} />
      ))}
    </div>
  )
}
