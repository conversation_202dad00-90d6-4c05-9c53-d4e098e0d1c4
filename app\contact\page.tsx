'use client'

import { <PERSON>ada<PERSON> } from 'next'
import { Card, CardContent, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { Button } from '@/components/ui/button'
import { useToast } from '@/hooks/use-toast'
import { Mail, Clock, MessageSquare, Users, Copy } from 'lucide-react'

export default function ContactPage() {
  const { toast } = useToast()

  const copyToClipboard = async (email: string) => {
    try {
      await navigator.clipboard.writeText(email)
      toast({
        title: "Email Copied!",
        description: `${email} has been copied to your clipboard.`,
        variant: "default",
      })
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = email
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)

      toast({
        title: "Email Copied!",
        description: `${email} has been copied to your clipboard.`,
        variant: "default",
      })
    }
  }
  return (
    <div className="min-h-screen bg-background py-24">
      <div className="container mx-auto max-w-4xl px-4">
        {/* Header */}
        <div className="text-center mb-16">
          <Badge variant="outline" className="mb-4">Contact Us</Badge>
          <h1 className="text-4xl md:text-5xl font-bold mb-6">
            Get in Touch
          </h1>
          <p className="text-xl text-muted-foreground max-w-3xl mx-auto leading-relaxed">
            We're here to help! Whether you have questions about our AI tools directory, want to submit a new tool,
            or need support, we'd love to hear from you. Reach out to us directly via email.
          </p>
        </div>

        {/* Main Contact Section */}
        <div className="max-w-4xl mx-auto">
          <Card className="mb-12 border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
            <CardHeader className="text-center pb-6">
              <CardTitle className="flex items-center justify-center gap-3 text-2xl">
                <Mail className="h-7 w-7 text-primary" />
                Contact Our Team
              </CardTitle>
            </CardHeader>
            <CardContent className="text-center">
              <p className="text-lg text-muted-foreground mb-8 max-w-2xl mx-auto">
                Ready to connect? Send us an email and we'll get back to you within 24-48 hours during business days.
              </p>

              <div className="grid md:grid-cols-2 gap-6 max-w-2xl mx-auto">
                <div className="bg-background/80 backdrop-blur-sm rounded-lg p-6 border">
                  <div className="flex items-center justify-center mb-4">
                    <div className="bg-primary/10 p-3 rounded-full">
                      <Users className="h-6 w-6 text-primary" />
                    </div>
                  </div>
                  <h3 className="font-semibold mb-2">General Support</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Questions, feedback, or general inquiries
                  </p>
                  <div className="space-y-3">
                    <a
                      href="mailto:<EMAIL>"
                      className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors"
                    >
                      <Mail className="h-4 w-4" />
                      <EMAIL>
                    </a>
                    <div className="flex justify-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard('<EMAIL>')}
                        className="text-xs"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy Email
                      </Button>
                    </div>
                  </div>
                </div>

                <div className="bg-background/80 backdrop-blur-sm rounded-lg p-6 border">
                  <div className="flex items-center justify-center mb-4">
                    <div className="bg-primary/10 p-3 rounded-full">
                      <MessageSquare className="h-6 w-6 text-primary" />
                    </div>
                  </div>
                  <h3 className="font-semibold mb-2">Business Inquiries</h3>
                  <p className="text-sm text-muted-foreground mb-4">
                    Partnerships, tool submissions, or business matters
                  </p>
                  <div className="space-y-3">
                    <a
                      href="mailto:<EMAIL>"
                      className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors"
                    >
                      <Mail className="h-4 w-4" />
                      <EMAIL>
                    </a>
                    <div className="flex justify-center">
                      <Button
                        variant="outline"
                        size="sm"
                        onClick={() => copyToClipboard('<EMAIL>')}
                        className="text-xs"
                      >
                        <Copy className="h-3 w-3 mr-1" />
                        Copy Email
                      </Button>
                    </div>
                  </div>
                </div>
              </div>
            </CardContent>
          </Card>

          {/* Additional Information */}
          <div className="grid md:grid-cols-3 gap-6">
            <Card>
              <CardHeader>
                <CardTitle className="flex items-center gap-2">
                  <Clock className="h-5 w-5" />
                  Response Time
                </CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  We typically respond within 24-48 hours during business days. For urgent matters, please mention "URGENT" in your subject line.
                </p>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>What to Include</CardTitle>
              </CardHeader>
              <CardContent>
                <ul className="text-sm text-muted-foreground space-y-2">
                  <li>• Clear subject line</li>
                  <li>• Detailed description of your inquiry</li>
                  <li>• Any relevant links or attachments</li>
                  <li>• Your preferred response method</li>
                </ul>
              </CardContent>
            </Card>

            <Card>
              <CardHeader>
                <CardTitle>Common Inquiries</CardTitle>
              </CardHeader>
              <CardContent className="space-y-3">
                <div>
                  <h4 className="font-medium mb-1 text-sm">Tool Submissions</h4>
                  <p className="text-xs text-muted-foreground">
                    Want to add your AI tool to our directory? Contact us with your tool details.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1 text-sm">Partnership Opportunities</h4>
                  <p className="text-xs text-muted-foreground">
                    Interested in collaborating? We'd love to explore partnership possibilities.
                  </p>
                </div>
                <div>
                  <h4 className="font-medium mb-1 text-sm">Technical Support</h4>
                  <p className="text-xs text-muted-foreground">
                    Having issues with our website? Let us know and we'll help you out.
                  </p>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </div>
    </div>
  )
}
