'use client'

import { <PERSON><PERSON> } from '@/components/ui/button'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Badge } from '@/components/ui/badge'
import { useToast } from '@/hooks/use-toast'
import { useStats } from '@/hooks/use-stats'
import {
  Users, Target, Zap, Heart, ArrowRight, Mail, Copy,
  Globe, Shield, Clock, Star, Lightbulb, Rocket,
  CheckCircle, TrendingUp, Award, MessageSquare
} from 'lucide-react'
import Link from 'next/link'

export default function AboutPage() {
  const { toast } = useToast()
  const stats = useStats()

  const copyToClipboard = async (email: string) => {
    try {
      await navigator.clipboard.writeText(email)
      toast({
        title: "Email Copied!",
        description: `${email} has been copied to your clipboard.`,
        variant: "default",
      })
    } catch (err) {
      // Fallback for older browsers
      const textArea = document.createElement('textarea')
      textArea.value = email
      document.body.appendChild(textArea)
      textArea.select()
      document.execCommand('copy')
      document.body.removeChild(textArea)

      toast({
        title: "Email Copied!",
        description: `${email} has been copied to your clipboard.`,
        variant: "default",
      })
    }
  }
  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section */}
      <section className="py-24 px-4">
        <div className="container mx-auto max-w-4xl text-center">
          <Badge variant="outline" className="mb-4">About AiAnyTool</Badge>
          <h1 className="text-4xl md:text-6xl font-bold mb-6 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
            Discover the Future of AI Tools
          </h1>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            We're on a mission to help you find the perfect AI tools for your needs. 
            From productivity to creativity, we've got you covered.
          </p>
        </div>
      </section>

      {/* Mission Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto max-w-6xl">
          <div className="grid md:grid-cols-2 gap-12 items-center">
            <div>
              <h2 className="text-3xl font-bold mb-6">Our Mission</h2>
              <p className="text-lg text-muted-foreground mb-6">
                At AiAnyTool.com, we believe that AI should be accessible to everyone. 
                Our platform serves as your comprehensive guide to the ever-expanding world of artificial intelligence tools.
              </p>
              <p className="text-lg text-muted-foreground mb-8">
                Whether you're a developer, designer, marketer, or entrepreneur, 
                we help you discover the AI tools that can transform your workflow and boost your productivity.
              </p>
              <Button asChild>
                <Link href="/tools">
                  Explore Tools <ArrowRight className="ml-2 h-4 w-4" />
                </Link>
              </Button>
            </div>
            <div className="grid grid-cols-2 gap-4">
              <Card className="border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <Users className="h-8 w-8 text-primary" />
                    <Badge variant="secondary" className="text-xs">Growing</Badge>
                  </div>
                  <CardTitle className="text-2xl font-bold">
                    {stats.isLoading ? '...' : stats.totalTools.toLocaleString()}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="font-medium">AI Tools Listed</CardDescription>
                  <p className="text-xs text-muted-foreground mt-1">Carefully curated and verified</p>
                </CardContent>
              </Card>

              <Card className="border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <Target className="h-8 w-8 text-primary" />
                    <Badge variant="secondary" className="text-xs">Diverse</Badge>
                  </div>
                  <CardTitle className="text-2xl font-bold">
                    {stats.isLoading ? '...' : stats.categories}+
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="font-medium">Categories</CardDescription>
                  <p className="text-xs text-muted-foreground mt-1">From productivity to creativity</p>
                </CardContent>
              </Card>

              <Card className="border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <Heart className="h-8 w-8 text-primary" />
                    <Badge variant="secondary" className="text-xs">Verified</Badge>
                  </div>
                  <CardTitle className="text-2xl font-bold">
                    {stats.isLoading ? '...' : stats.verifiedTools.toLocaleString()}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="font-medium">Verified Tools</CardDescription>
                  <p className="text-xs text-muted-foreground mt-1">
                    {stats.isLoading ? 'Loading...' : `${Math.round((stats.verifiedTools / stats.totalTools) * 100)}% verified quality`}
                  </p>
                </CardContent>
              </Card>

              <Card className="border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
                <CardHeader className="pb-3">
                  <div className="flex items-center justify-between">
                    <Clock className="h-8 w-8 text-primary" />
                    <Badge variant="secondary" className="text-xs">Featured</Badge>
                  </div>
                  <CardTitle className="text-2xl font-bold">
                    {stats.isLoading ? '...' : stats.featuredTools}
                  </CardTitle>
                </CardHeader>
                <CardContent>
                  <CardDescription className="font-medium">Featured Tools</CardDescription>
                  <p className="text-xs text-muted-foreground mt-1">
                    Hand-picked premium selections
                  </p>
                </CardContent>
              </Card>
            </div>
          </div>
        </div>
      </section>

      {/* Values Section */}
      <section className="py-16 px-4 bg-muted/30">
        <div className="container mx-auto max-w-4xl text-center">
          <h2 className="text-3xl font-bold mb-12">What We Stand For</h2>
          <div className="grid md:grid-cols-3 gap-8">
            <div>
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Users className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Community First</h3>
              <p className="text-muted-foreground">
                We believe in building a community where everyone can discover and share amazing AI tools.
              </p>
            </div>
            <div>
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Target className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Quality Focus</h3>
              <p className="text-muted-foreground">
                Every tool in our directory is carefully reviewed to ensure it meets our quality standards.
              </p>
            </div>
            <div>
              <div className="w-16 h-16 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-4">
                <Zap className="h-8 w-8 text-primary" />
              </div>
              <h3 className="text-xl font-semibold mb-3">Innovation</h3>
              <p className="text-muted-foreground">
                We're constantly evolving to bring you the latest and greatest in AI technology.
              </p>
            </div>
          </div>
        </div>
      </section>

      {/* Our Story Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto max-w-4xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-6">Our Story</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Born from the need to navigate the rapidly evolving AI landscape, AiAnyTool.com started as a simple idea:
              make AI tools accessible and discoverable for everyone.
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8">
            <Card className="border-2 border-primary/20">
              <CardHeader>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <Lightbulb className="h-5 w-5 text-primary" />
                  </div>
                  <CardTitle>The Vision</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  We envision a world where AI tools are not just for tech experts, but accessible to creators,
                  entrepreneurs, students, and professionals from all walks of life. Our platform bridges the gap
                  between complex AI technology and practical everyday applications.
                </p>
              </CardContent>
            </Card>

            <Card className="border-2 border-primary/20">
              <CardHeader>
                <div className="flex items-center gap-3 mb-2">
                  <div className="w-10 h-10 bg-primary/10 rounded-full flex items-center justify-center">
                    <Rocket className="h-5 w-5 text-primary" />
                  </div>
                  <CardTitle>The Journey</CardTitle>
                </div>
              </CardHeader>
              <CardContent>
                <p className="text-muted-foreground">
                  What started as a personal collection of useful AI tools has grown into a comprehensive directory
                  trusted by thousands of users worldwide. We're constantly evolving, adding new features,
                  and expanding our collection to serve our growing community better.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Features Section */}
      <section className="py-16 px-4 bg-muted/30">
        <div className="container mx-auto max-w-6xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-6">Why Choose AiAnyTool.com?</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              We're more than just a directory. We're your trusted guide in the AI revolution.
            </p>
          </div>

          <div className="grid md:grid-cols-2 lg:grid-cols-4 gap-6">
            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <CheckCircle className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-lg">Verified Tools</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Every tool is tested and verified by our team before being listed.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <TrendingUp className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-lg">Latest Trends</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Stay updated with the newest AI tools and emerging technologies.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Award className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-lg">Expert Reviews</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Detailed reviews and ratings from our community of users.
                </p>
              </CardContent>
            </Card>

            <Card className="text-center">
              <CardHeader>
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Globe className="h-6 w-6 text-primary" />
                </div>
                <CardTitle className="text-lg">Global Community</CardTitle>
              </CardHeader>
              <CardContent>
                <p className="text-sm text-muted-foreground">
                  Join thousands of AI enthusiasts from around the world.
                </p>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* Contact Section */}
      <section className="py-16 px-4">
        <div className="container mx-auto max-w-4xl">
          <div className="text-center mb-12">
            <h2 className="text-3xl font-bold mb-6">Get in Touch</h2>
            <p className="text-lg text-muted-foreground max-w-2xl mx-auto">
              Have questions, suggestions, or want to collaborate? We'd love to hear from you!
            </p>
          </div>

          <div className="grid md:grid-cols-2 gap-8 max-w-2xl mx-auto">
            <Card className="border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
              <CardHeader className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <MessageSquare className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>General Support</CardTitle>
                <CardDescription>Questions, feedback, or general inquiries</CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors"
                >
                  <Mail className="h-4 w-4" />
                  <EMAIL>
                </a>
                <div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard('<EMAIL>')}
                    className="text-xs"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy Email
                  </Button>
                </div>
              </CardContent>
            </Card>

            <Card className="border-2 border-primary/20 bg-gradient-to-br from-primary/5 to-primary/10">
              <CardHeader className="text-center">
                <div className="w-12 h-12 bg-primary/10 rounded-full flex items-center justify-center mx-auto mb-3">
                  <Users className="h-6 w-6 text-primary" />
                </div>
                <CardTitle>Business Inquiries</CardTitle>
                <CardDescription>Partnerships, tool submissions, collaborations</CardDescription>
              </CardHeader>
              <CardContent className="text-center space-y-4">
                <a
                  href="mailto:<EMAIL>"
                  className="inline-flex items-center gap-2 text-primary hover:text-primary/80 font-medium transition-colors"
                >
                  <Mail className="h-4 w-4" />
                  <EMAIL>
                </a>
                <div>
                  <Button
                    variant="outline"
                    size="sm"
                    onClick={() => copyToClipboard('<EMAIL>')}
                    className="text-xs"
                  >
                    <Copy className="h-3 w-3 mr-1" />
                    Copy Email
                  </Button>
                </div>
              </CardContent>
            </Card>
          </div>
        </div>
      </section>

      {/* CTA Section */}
      <section className="py-20 px-4 bg-gradient-to-r from-primary/10 via-purple-500/10 to-primary/10">
        <div className="container mx-auto max-w-4xl text-center">
          <Badge variant="outline" className="mb-4">Join Our Community</Badge>
          <h2 className="text-4xl font-bold mb-6 bg-gradient-to-r from-primary to-purple-600 bg-clip-text text-transparent">
            Ready to Discover Your Next AI Tool?
          </h2>
          <p className="text-xl text-muted-foreground mb-8 max-w-2xl mx-auto">
            Join thousands of innovators, creators, and professionals who trust AiAnyTool.com
            to find the perfect AI solutions for their needs.
          </p>

          <div className="grid md:grid-cols-3 gap-4 mb-8 max-w-2xl mx-auto">
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Free Forever</span>
            </div>
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Daily Updates</span>
            </div>
            <div className="flex items-center justify-center gap-2 text-sm text-muted-foreground">
              <CheckCircle className="h-4 w-4 text-green-500" />
              <span>Expert Reviews</span>
            </div>
          </div>

          <div className="flex flex-col sm:flex-row gap-4 justify-center">
            <Button asChild size="lg" className="bg-gradient-to-r from-primary to-purple-600 hover:from-primary/90 hover:to-purple-600/90">
              <Link href="/tools">
                <Zap className="mr-2 h-4 w-4" />
                Explore AI Tools
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild className="border-primary/20 hover:bg-primary/5">
              <Link href="/submit">
                <Rocket className="mr-2 h-4 w-4" />
                Submit Your Tool
              </Link>
            </Button>
            <Button variant="outline" size="lg" asChild className="border-primary/20 hover:bg-primary/5">
              <Link href="/contact">
                <Mail className="mr-2 h-4 w-4" />
                Contact Us
              </Link>
            </Button>
          </div>
        </div>
      </section>
    </div>
  )
}
