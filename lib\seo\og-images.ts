/**
 * Professional Open Graph Images Configuration
 * Optimized for all social media platforms
 */

export interface OGImageConfig {
  url: string
  secureUrl: string
  width: number
  height: number
  alt: string
  type: string
}

// Standard OG image dimensions for different platforms
export const OG_DIMENSIONS = {
  // Facebook/Meta recommended
  FACEBOOK: { width: 1200, height: 630 },
  // Twitter large image
  TWITTER_LARGE: { width: 1200, height: 630 },
  // Twitter summary
  TWITTER_SUMMARY: { width: 400, height: 400 },
  // LinkedIn
  LINKEDIN: { width: 1200, height: 627 },
  // Instagram
  INSTAGRAM: { width: 1080, height: 1080 },
  // WhatsApp
  WHATSAPP: { width: 400, height: 400 },
  // General square
  SQUARE: { width: 1200, height: 1200 },
} as const

/**
 * Generate optimized OG images for homepage
 */
export function getHomepageOGImages(): OGImageConfig[] {
  return [
    {
      url: 'https://aianytool.com/og-image.jpg',
      secureUrl: 'https://aianytool.com/og-image.jpg',
      width: OG_DIMENSIONS.FACEBOOK.width,
      height: OG_DIMENSIONS.FACEBOOK.height,
      alt: 'AiAnyTool.com - Your Ultimate AI Tools Directory with 1000+ Curated AI Tools',
      type: 'image/jpeg',
    },
    {
      url: 'https://aianytool.com/og-image-square.jpg',
      secureUrl: 'https://aianytool.com/og-image-square.jpg',
      width: OG_DIMENSIONS.SQUARE.width,
      height: OG_DIMENSIONS.SQUARE.height,
      alt: 'AiAnyTool.com - AI Tools Directory for Business and Personal Use',
      type: 'image/jpeg',
    },
  ]
}

/**
 * Generate optimized OG images for tool pages
 */
export function getToolOGImages(tool: any): OGImageConfig[] {
  const images: OGImageConfig[] = []
  
  // Primary featured image
  if (tool.featured_image_url) {
    const imageUrl = tool.featured_image_url.startsWith('http') 
      ? tool.featured_image_url 
      : `https://aianytool.com${tool.featured_image_url}`
    
    images.push({
      url: imageUrl,
      secureUrl: imageUrl,
      width: OG_DIMENSIONS.FACEBOOK.width,
      height: OG_DIMENSIONS.FACEBOOK.height,
      alt: `${tool.company_name} - ${tool.primary_task || 'AI Tool'} Interface Screenshot | Review & Features`,
      type: 'image/jpeg',
    })
  }
  
  // Logo as secondary image
  if (tool.logo_url) {
    const logoUrl = tool.logo_url.startsWith('http') 
      ? tool.logo_url 
      : `https://aianytool.com${tool.logo_url}`
    
    images.push({
      url: logoUrl,
      secureUrl: logoUrl,
      width: OG_DIMENSIONS.TWITTER_SUMMARY.width,
      height: OG_DIMENSIONS.TWITTER_SUMMARY.height,
      alt: `${tool.company_name} Logo`,
      type: 'image/png',
    })
  }
  
  // Fallback default image
  if (images.length === 0) {
    images.push({
      url: 'https://aianytool.com/og-default-tool.jpg',
      secureUrl: 'https://aianytool.com/og-default-tool.jpg',
      width: OG_DIMENSIONS.FACEBOOK.width,
      height: OG_DIMENSIONS.FACEBOOK.height,
      alt: `${tool.company_name} - AI Tool Review & Analysis | AiAnyTool.com`,
      type: 'image/jpeg',
    })
  }
  
  return images
}

/**
 * Generate optimized OG images for category pages
 */
export function getCategoryOGImages(category: string, toolsCount: number): OGImageConfig[] {
  return [
    {
      url: 'https://aianytool.com/og-category.jpg',
      secureUrl: 'https://aianytool.com/og-category.jpg',
      width: OG_DIMENSIONS.FACEBOOK.width,
      height: OG_DIMENSIONS.FACEBOOK.height,
      alt: `${category} AI Tools Directory - ${toolsCount}+ Curated Tools | AiAnyTool.com`,
      type: 'image/jpeg',
    },
    {
      url: 'https://aianytool.com/og-category-square.jpg',
      secureUrl: 'https://aianytool.com/og-category-square.jpg',
      width: OG_DIMENSIONS.SQUARE.width,
      height: OG_DIMENSIONS.SQUARE.height,
      alt: `Best ${category} AI Tools Collection`,
      type: 'image/jpeg',
    }
  ]
}

/**
 * Generate optimized OG images for tools listing page
 */
export function getToolsPageOGImages(): OGImageConfig[] {
  return [
    {
      url: 'https://aianytool.com/og-tools.jpg',
      secureUrl: 'https://aianytool.com/og-tools.jpg',
      width: OG_DIMENSIONS.FACEBOOK.width,
      height: OG_DIMENSIONS.FACEBOOK.height,
      alt: 'Browse AI Tools Directory - 1000+ Curated AI Tools | AiAnyTool.com',
      type: 'image/jpeg',
    }
  ]
}

/**
 * Generate optimized OG images for categories page
 */
export function getCategoriesPageOGImages(): OGImageConfig[] {
  return [
    {
      url: 'https://aianytool.com/og-categories.jpg',
      secureUrl: 'https://aianytool.com/og-categories.jpg',
      width: OG_DIMENSIONS.FACEBOOK.width,
      height: OG_DIMENSIONS.FACEBOOK.height,
      alt: 'AI Tool Categories Directory - 50+ Categories | AiAnyTool.com',
      type: 'image/jpeg',
    }
  ]
}

/**
 * Generate optimized OG images for about page
 */
export function getAboutPageOGImages(): OGImageConfig[] {
  return [
    {
      url: 'https://aianytool.com/og-about.jpg',
      secureUrl: 'https://aianytool.com/og-about.jpg',
      width: OG_DIMENSIONS.FACEBOOK.width,
      height: OG_DIMENSIONS.FACEBOOK.height,
      alt: 'About AiAnyTool.com - Your Trusted AI Tools Directory Team',
      type: 'image/jpeg',
    }
  ]
}

/**
 * Validate image URL and dimensions
 */
export function validateOGImage(image: OGImageConfig): boolean {
  // Check minimum dimensions (Facebook requirements)
  if (image.width < 200 || image.height < 200) {
    console.warn(`OG image too small: ${image.width}x${image.height}`)
    return false
  }
  
  // Check maximum dimensions
  if (image.width > 8192 || image.height > 8192) {
    console.warn(`OG image too large: ${image.width}x${image.height}`)
    return false
  }
  
  // Check aspect ratio for main images
  const ratio = image.width / image.height
  const recommendedRatio = 1.91 // 1200x630
  
  if (image.width === 1200 && image.height === 630) {
    // Perfect ratio
    return true
  } else if (Math.abs(ratio - recommendedRatio) > 0.2) {
    console.warn(`OG image ratio not optimal: ${ratio.toFixed(2)} (recommended: ${recommendedRatio})`)
  }
  
  return true
}

/**
 * Get Twitter-optimized image URL
 */
export function getTwitterImage(images: OGImageConfig[]): string {
  // Prefer 1200x630 images for Twitter
  const twitterImage = images.find(img => 
    img.width === OG_DIMENSIONS.TWITTER_LARGE.width && 
    img.height === OG_DIMENSIONS.TWITTER_LARGE.height
  )
  
  return twitterImage?.url || images[0]?.url || 'https://aianytool.com/og-image.jpg'
}

/**
 * Generate dynamic OG image URL (for future implementation)
 */
export function generateDynamicOGImage(params: {
  title: string
  category?: string
  logo?: string
  type: 'tool' | 'category' | 'general'
}): string {
  // This would integrate with a service like Vercel OG or similar
  const baseUrl = 'https://aianytool.com/api/og'
  const searchParams = new URLSearchParams({
    title: params.title,
    type: params.type,
    ...(params.category && { category: params.category }),
    ...(params.logo && { logo: params.logo }),
  })
  
  return `${baseUrl}?${searchParams.toString()}`
}

/**
 * Image optimization recommendations
 */
export const OG_IMAGE_BEST_PRACTICES = {
  // File formats
  FORMATS: {
    JPEG: 'Best for photos and complex images',
    PNG: 'Best for logos and simple graphics with transparency',
    WebP: 'Modern format with better compression (not universally supported)',
  },
  
  // File size recommendations
  FILE_SIZE: {
    MAX_SIZE: '8MB', // Facebook limit
    RECOMMENDED: '300KB', // For fast loading
    MINIMUM_QUALITY: '85%', // JPEG quality
  },
  
  // Dimensions
  DIMENSIONS: {
    FACEBOOK: '1200x630px (1.91:1 ratio)',
    TWITTER: '1200x630px for large image card',
    LINKEDIN: '1200x627px',
    INSTAGRAM: '1080x1080px (square)',
  },
  
  // Content guidelines
  CONTENT: {
    TEXT_SIZE: 'Minimum 24px font size',
    TEXT_AREA: 'Keep text in center 80% of image',
    BRANDING: 'Include logo but not too prominent',
    CONTRAST: 'Ensure good contrast for readability',
  },
} as const

/**
 * Check if image meets social media requirements
 */
export function checkImageCompliance(image: OGImageConfig): {
  isCompliant: boolean
  warnings: string[]
  recommendations: string[]
} {
  const warnings: string[] = []
  const recommendations: string[] = []
  
  // Check dimensions
  if (image.width < 1200 || image.height < 630) {
    warnings.push('Image smaller than recommended 1200x630')
  }
  
  // Check aspect ratio
  const ratio = image.width / image.height
  if (Math.abs(ratio - 1.91) > 0.1) {
    recommendations.push('Consider using 1.91:1 aspect ratio for better display')
  }
  
  // Check alt text
  if (!image.alt || image.alt.length < 10) {
    warnings.push('Alt text should be descriptive and at least 10 characters')
  }
  
  // Check secure URL
  if (!image.secureUrl || !image.secureUrl.startsWith('https://')) {
    warnings.push('Secure URL (HTTPS) is required for social media')
  }
  
  const isCompliant = warnings.length === 0
  
  return { isCompliant, warnings, recommendations }
}
