"use client"

import React, { useState, useRef } from "react"
import { Search } from "lucide-react"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import FloatingSearchModal from "./floating-search-modal"
import { useFormattedStats } from "@/hooks/use-stats"

interface SearchIconButtonProps {
  variant?: "header" | "mobile"
  size?: "sm" | "md" | "lg"
  className?: string
  showTooltip?: boolean
}

export default function SearchIconButton({
  variant = "header",
  size = "md",
  className,
  showTooltip = true
}: SearchIconButtonProps) {
  const [isSearchOpen, setIsSearchOpen] = useState(false)
  const { totalToolsFormatted, statsLoading } = useFormattedStats()
  const buttonRef = useRef<HTMLButtonElement>(null)

  const sizeClasses = {
    sm: "h-8 w-8",
    md: "h-9 w-9",
    lg: "h-10 w-10"
  }

  const iconSizes = {
    sm: "h-4 w-4",
    md: "h-4 w-4",
    lg: "h-5 w-5"
  }

  const variantClasses = {
    header: "border-border/50 bg-background/80 hover:bg-background shadow-sm",
    mobile: "border-border/50 bg-background/80 hover:bg-background shadow-sm"
  }

  const handleSearchClick = () => {
    setIsSearchOpen(true)
  }

  const placeholder = statsLoading 
    ? "Search AI tools..." 
    : `Search ${totalToolsFormatted}+ AI tools...`

  return (
    <>
      <Button
        ref={buttonRef}
        variant="outline"
        size="icon"
        onClick={handleSearchClick}
        className={cn(
          "rounded-full theme-transition touch-manipulation transition-colors duration-200 hover:text-primary",
          sizeClasses[size],
          variantClasses[variant],
          "min-h-[44px] min-w-[44px]", // Ensure touch-friendly size
          className
        )}
        aria-label="Open search"
        title={showTooltip ? "Search AI tools" : undefined}
      >
        <Search className={cn(iconSizes[size])} />
      </Button>

      <FloatingSearchModal
        isOpen={isSearchOpen}
        onClose={() => setIsSearchOpen(false)}
        placeholder={placeholder}
        triggerRef={buttonRef}
      />
    </>
  )
}
