"use client"

import { ReactNode } from "react"
import { cn } from "@/lib/utils"

interface SearchContainerProps {
  children: ReactNode
  className?: string
  showGradientEffect?: boolean
  maxWidth?: 'sm' | 'md' | 'lg' | 'xl' | '2xl' | '3xl' | 'full'
}

const maxWidthClasses = {
  sm: 'max-w-sm',
  md: 'max-w-md', 
  lg: 'max-w-lg',
  xl: 'max-w-xl',
  '2xl': 'max-w-2xl',
  '3xl': 'max-w-3xl',
  full: 'max-w-full'
}

export default function SearchContainer({
  children,
  className,
  showGradientEffect = true,
  maxWidth = '3xl'
}: SearchContainerProps) {
  return (
    <div className={cn(maxWidthClasses[maxWidth], "mx-auto", className)}>
      <div className="relative group w-full z-30">
        {/* Gradient Background Effect */}
        {showGradientEffect && (
          <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-purple-500/20 to-primary/20 rounded-2xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500"></div>
        )}

        {/* Main Search Container */}
        <div className="relative bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-slate-700/30 z-30">
          {children}
        </div>
      </div>
    </div>
  )
}
