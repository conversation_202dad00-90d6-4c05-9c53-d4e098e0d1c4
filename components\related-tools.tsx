import { createServerClient } from "@/lib/supabase/server"
import Link from "next/link"
import Image from "next/image"

export default async function RelatedTools({
  currentToolId,
  category,
}: {
  currentToolId: number
  category: string
}) {
  const supabase = createServerClient()
  const { data: tools, error } = await supabase
    .from("tools")
    .select("id, company_name, logo_url, slug")
    .eq("primary_task", category)
    .neq("id", currentToolId)
    .limit(5)

  if (error) {
    console.error("Error fetching related tools:", error.message)
    return null
  }

  if (!tools || tools.length === 0) {
    return null
  }

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-4">Related Tools</h3>
      <div className="space-y-4">
        {tools.map((tool) => (
          <Link
            key={tool.id}
            href={`/Tool/${tool.slug}`}
            className="flex items-center gap-3 p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
          >
            <div className="w-10 h-10 bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
              <Image
                src={tool.logo_url || "/placeholder.svg?height=40&width=40"}
                alt={tool.company_name}
                width={32}
                height={32}
                className="object-contain"
              />
            </div>
            <span className="font-medium">{tool.company_name}</span>
          </Link>
        ))}
      </div>
    </div>
  )
}
