import { LoadingGrid } from "@/components/ui/loading-grid"

export default function ToolsLoading() {
  return (
    <div className="min-h-screen bg-background pt-24 pb-16">
      <div className="container mx-auto px-4">
        <div className="flex flex-col md:flex-row gap-6">
          {/* Sidebar skeleton */}
          <div className="hidden md:block w-64 shrink-0">
            <div className="sticky top-24">
              <h2 className="text-xl font-bold mb-4">Filters</h2>
              <div className="animate-pulse space-y-4">
                <div className="h-10 bg-muted rounded-md w-full"></div>
                <div className="h-40 bg-muted rounded-md w-full"></div>
                <div className="h-32 bg-muted rounded-md w-full"></div>
              </div>
            </div>
          </div>
          
          {/* Main content skeleton */}
          <div className="flex-1">
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center gap-4 mb-6">
              <div className="h-8 bg-muted rounded-md w-48 animate-pulse"></div>
              <div className="h-10 bg-muted rounded-md w-32 animate-pulse"></div>
            </div>
            
            <div className="h-12 bg-muted rounded-md w-full mb-6 animate-pulse"></div>
            
            <LoadingGrid count={12} columns={3} />
          </div>
        </div>
      </div>
    </div>
  )
}
