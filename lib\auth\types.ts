// طبقة التجريد للتوثيق - تدعم Supabase
export interface AuthUser {
  id: string
  email?: string
  name?: string
  avatar_url?: string
  role?: string
  created_at?: string

  // Supabase specific fields
  user_metadata?: Record<string, any>
}

export interface AuthError {
  message: string
  code?: string
}

export interface AuthResponse {
  data?: any
  error?: string
}

export interface AuthProvider {
  // Core authentication methods
  getUser(): Promise<AuthUser | null>
  signIn(email: string, password: string): Promise<AuthResponse>
  signUp(email: string, password: string, fullName?: string): Promise<AuthResponse>
  signOut(): Promise<void>
  
  // OAuth methods
  signInWithGoogle?(): Promise<AuthResponse>
  signInWithGitHub?(): Promise<AuthResponse>
  
  // Session management
  getSession(): Promise<any>
  refreshSession?(): Promise<AuthResponse>
  
  // Event listeners
  onAuthStateChange?(callback: (user: AuthUser | null) => void): () => void
}

export type AuthProviderType = 'supabase'

export interface AuthConfig {
  provider: AuthProviderType
  supabase?: {
    url: string
    anonKey: string
  }

}
