import { useState, useEffect } from 'react';
import { getFromCache, setCache, generateCacheKey } from '@/lib/cache-utils';

interface UseFetchOptions<T> {
  initialData?: T;
  revalidateOnFocus?: boolean;
  revalidateOnReconnect?: boolean;
  dedupingInterval?: number;
  onSuccess?: (data: T) => void;
  onError?: (error: Error) => void;
}

interface FetchState<T> {
  data: T | undefined;
  error: Error | null;
  isLoading: boolean;
  isValidating: boolean;
}

/**
 * Custom hook for data fetching with SWR pattern
 * @param key Cache key for the data
 * @param fetcher Function to fetch the data
 * @param options Configuration options
 * @returns Fetch state and mutate function
 */
export function useOptimizedFetch<T>(
  key: string | null,
  fetcher: () => Promise<T>,
  options: UseFetchOptions<T> = {}
) {
  const {
    initialData,
    revalidateOnFocus = true,
    revalidateOnReconnect = true,
    dedupingInterval = 2000, // 2 seconds
    onSuccess,
    onError,
  } = options;

  const [state, setState] = useState<FetchState<T>>({
    data: initialData,
    error: null,
    isLoading: !initialData && !!key,
    isValidating: false,
  });

  const [lastFetchTime, setLastFetchTime] = useState<number>(0);

  // Function to fetch data and update state
  const fetchData = async (shouldDedupe = true) => {
    if (!key) return;

    // Check if we should dedupe this request
    const now = Date.now();
    if (shouldDedupe && now - lastFetchTime < dedupingInterval) {
      return;
    }

    // Try to get from cache first
    const cachedData = getFromCache<T>(key);
    if (cachedData) {
      setState((prev) => ({
        ...prev,
        data: cachedData,
        isLoading: false,
      }));

      // Still validate in the background
      setState((prev) => ({ ...prev, isValidating: true }));
    } else if (!state.isValidating) {
      // If not validating already, set loading state
      setState((prev) => ({
        ...prev,
        isLoading: true,
        isValidating: true,
      }));
    }

    try {
      setLastFetchTime(now);
      const newData = await fetcher();
      
      // Update cache
      setCache(key, newData);
      
      // Update state
      setState({
        data: newData,
        error: null,
        isLoading: false,
        isValidating: false,
      });
      
      if (onSuccess) {
        onSuccess(newData);
      }
    } catch (error) {
      setState({
        data: state.data, // Keep the previous data
        error: error as Error,
        isLoading: false,
        isValidating: false,
      });
      
      if (onError) {
        onError(error as Error);
      }
    }
  };

  // Initial fetch
  useEffect(() => {
    if (key) {
      fetchData(false);
    }
  }, [key]);

  // Revalidate on focus
  useEffect(() => {
    if (!revalidateOnFocus) return;

    const onFocus = () => {
      fetchData();
    };

    window.addEventListener('focus', onFocus);
    return () => {
      window.removeEventListener('focus', onFocus);
    };
  }, [revalidateOnFocus, key]);

  // Revalidate on reconnect
  useEffect(() => {
    if (!revalidateOnReconnect) return;

    const onOnline = () => {
      fetchData();
    };

    window.addEventListener('online', onOnline);
    return () => {
      window.removeEventListener('online', onOnline);
    };
  }, [revalidateOnReconnect, key]);

  // Function to manually revalidate
  const mutate = async () => {
    await fetchData(false);
  };

  return {
    ...state,
    mutate,
  };
}
