import { MetadataRoute } from 'next'
import { createClient } from '@supabase/supabase-js'

// Allow dynamic generation but cache for performance
export const dynamic = 'force-dynamic'
export const revalidate = 1800 // Revalidate every 30 minutes for better freshness

export default async function sitemap(): Promise<MetadataRoute.Sitemap> {
  const baseUrl = 'https://aianytool.com'

  // Static pages with high priority
  const staticPages: MetadataRoute.Sitemap = [
    {
      url: baseUrl,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 1.0,
    },
    {
      url: `${baseUrl}/tools`,
      lastModified: new Date(),
      changeFrequency: 'daily',
      priority: 0.9,
    },
    {
      url: `${baseUrl}/categories`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.8,
    },
    {
      url: `${baseUrl}/submit`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.7,
    },
    {
      url: `${baseUrl}/search`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.6,
    },
    {
      url: `${baseUrl}/privacy`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/terms`,
      lastModified: new Date(),
      changeFrequency: 'monthly',
      priority: 0.5,
    },
    {
      url: `${baseUrl}/dashboard`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.4,
    },
    {
      url: `${baseUrl}/profile`,
      lastModified: new Date(),
      changeFrequency: 'weekly',
      priority: 0.3,
    },
  ]

  try {
    // Create a simple client without cookies for sitemap generation
    // This avoids the dynamic server usage error
    if (!process.env.NEXT_PUBLIC_SUPABASE_URL || !process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY) {
      console.warn('Supabase environment variables not available for sitemap generation')
      return staticPages
    }

    const supabase = createClient(
      process.env.NEXT_PUBLIC_SUPABASE_URL,
      process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY,
      {
        auth: {
          persistSession: false,
          autoRefreshToken: false,
        }
      }
    )

    // Get all tools for dynamic pages using unlimited pagination to bypass Supabase limits
    let allTools = []
    let from = 0
    const batchSize = 1000
    let hasMore = true
    let batchCount = 0

    console.log('🔄 Starting sitemap generation with unlimited pagination...')

    while (hasMore) {
      const { data: toolsBatch, error: batchError } = await supabase
        .from('tools')
        .select('slug, updated_at, created_at')
        .not('slug', 'is', null)
        .order('id', { ascending: true }) // Use id for consistent pagination
        .range(from, from + batchSize - 1)

      if (batchError) {
        console.error('Error fetching tools batch:', batchError)
        break
      }

      if (toolsBatch && toolsBatch.length > 0) {
        allTools.push(...toolsBatch)
        from += batchSize
        batchCount++
        hasMore = toolsBatch.length === batchSize

        // Log progress for monitoring
        if (batchCount % 5 === 0 || !hasMore) {
          console.log(`📊 Sitemap progress: ${allTools.length} tools fetched (batch ${batchCount})`)
        }
      } else {
        hasMore = false
      }
    }

    const tools = allTools
    const error = null

    console.log(`✅ Sitemap generation complete: ${tools.length} tools processed in ${batchCount} batches`)

    if (error) {
      console.error('Error fetching tools for sitemap:', error)
      return staticPages
    }

    // Generate tool pages
    const toolPages: MetadataRoute.Sitemap = tools?.map((tool) => ({
      url: `${baseUrl}/Tool/${tool.slug}`,
      lastModified: new Date(tool.updated_at || tool.created_at),
      changeFrequency: 'weekly' as const,
      priority: 0.7,
    })) || []

    // Get categories for category pages - Get all categories without limit
    const { data: categoriesData } = await supabase
      .from('tools')
      .select('primary_task')
      .not('primary_task', 'is', null)

    const uniqueCategories = [...new Set(categoriesData?.map(item => item.primary_task) || [])]

    const categoryPages: MetadataRoute.Sitemap = uniqueCategories.map((category) => ({
      url: `${baseUrl}/category/${encodeURIComponent(category.toLowerCase().replace(/\s+/g, '-'))}`,
      lastModified: new Date(),
      changeFrequency: 'weekly' as const,
      priority: 0.6,
    }))

    // Log detailed sitemap generation info
    console.log(`Generated sitemap with:`)
    console.log(`- Static pages: ${staticPages.length}`)
    console.log(`- Tool pages: ${toolPages.length}`)
    console.log(`- Category pages: ${categoryPages.length}`)
    console.log(`- Total URLs: ${staticPages.length + toolPages.length + categoryPages.length}`)

    return [...staticPages, ...toolPages, ...categoryPages]

  } catch (error) {
    console.error('Error generating sitemap:', error)
    // Return static pages only if there's an error
    return staticPages
  }
}
