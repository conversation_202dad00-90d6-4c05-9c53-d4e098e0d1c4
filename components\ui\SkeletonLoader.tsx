"use client"

import { cn } from "@/lib/utils"

interface SkeletonProps {
  className?: string
  variant?: "default" | "card" | "text" | "avatar" | "button" | "input"
  width?: string | number
  height?: string | number
  animate?: boolean
}

/**
 * Skeleton loader component for showing loading states
 * @param className Additional CSS classes
 * @param variant Predefined variants for common use cases
 * @param width Custom width
 * @param height Custom height
 * @param animate Whether to animate the skeleton
 * @returns Skeleton component
 */
export function Skeleton({
  className,
  variant = "default",
  width,
  height,
  animate = true,
  ...props
}: SkeletonProps & React.HTMLAttributes<HTMLDivElement>) {
  // Base styles
  let variantStyles = ""
  
  // Apply variant-specific styles
  switch (variant) {
    case "card":
      variantStyles = "w-full h-[200px] rounded-xl"
      break
    case "text":
      variantStyles = "h-4 w-full rounded"
      break
    case "avatar":
      variantStyles = "h-12 w-12 rounded-full"
      break
    case "button":
      variantStyles = "h-10 w-24 rounded-md"
      break
    case "input":
      variantStyles = "h-10 w-full rounded-md"
      break
    default:
      variantStyles = "w-full h-4 rounded"
  }
  
  // Apply custom dimensions if provided
  const styles = {
    width: width ? (typeof width === 'number' ? `${width}px` : width) : undefined,
    height: height ? (typeof height === 'number' ? `${height}px` : height) : undefined,
  }
  
  return (
    <div
      className={cn(
        "bg-muted/60 dark:bg-muted/30",
        animate && "animate-pulse",
        variantStyles,
        className
      )}
      style={styles}
      {...props}
    />
  )
}

/**
 * Text skeleton with multiple lines
 */
export function TextSkeleton({
  lines = 3,
  lastLineWidth = "60%",
  className,
  ...props
}: {
  lines?: number
  lastLineWidth?: string | number
  className?: string
} & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div className={cn("space-y-2", className)} {...props}>
      {Array.from({ length: lines - 1 }).map((_, i) => (
        <Skeleton key={i} variant="text" className="w-full" />
      ))}
      <Skeleton
        variant="text"
        className="last:mb-0"
        width={lastLineWidth}
      />
    </div>
  )
}

/**
 * Card skeleton with title, content and actions
 */
export function CardSkeleton({
  hasImage = true,
  hasFooter = true,
  className,
  ...props
}: {
  hasImage?: boolean
  hasFooter?: boolean
  className?: string
} & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "rounded-xl border bg-card p-4 shadow-sm",
        className
      )}
      {...props}
    >
      {hasImage && (
        <Skeleton className="mb-4 h-[140px] w-full rounded-md" />
      )}
      <Skeleton className="mb-2 h-6 w-1/2" />
      <TextSkeleton lines={2} className="mb-4" />
      {hasFooter && (
        <div className="flex items-center justify-between">
          <Skeleton className="h-8 w-16 rounded-md" />
          <Skeleton className="h-8 w-8 rounded-full" />
        </div>
      )}
    </div>
  )
}

/**
 * Tool card skeleton for tool listings
 */
export function ToolCardSkeleton({
  className,
  ...props
}: {
  className?: string
} & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "rounded-xl border bg-card p-4 shadow-sm",
        className
      )}
      {...props}
    >
      <div className="flex items-center gap-3 mb-3">
        <Skeleton variant="avatar" className="h-10 w-10" />
        <div className="flex-1">
          <Skeleton className="h-5 w-3/4 mb-1" />
          <Skeleton className="h-3 w-1/2" />
        </div>
      </div>
      <Skeleton className="h-16 w-full mb-3" />
      <div className="flex gap-2 mt-2">
        <Skeleton className="h-6 w-16 rounded-full" />
        <Skeleton className="h-6 w-20 rounded-full" />
      </div>
    </div>
  )
}

/**
 * Grid of tool card skeletons
 */
export function ToolGridSkeleton({
  count = 6,
  className,
  ...props
}: {
  count?: number
  className?: string
} & React.HTMLAttributes<HTMLDivElement>) {
  return (
    <div
      className={cn(
        "grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4",
        className
      )}
      {...props}
    >
      {Array.from({ length: count }).map((_, i) => (
        <ToolCardSkeleton key={i} />
      ))}
    </div>
  )
}
