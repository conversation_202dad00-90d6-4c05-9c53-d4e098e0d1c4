"use client"

import { useState, useEffect } from "react"
import { createBrowserClient } from "@/lib/supabase/client-utils"

interface Stats {
  totalTools: number
  verifiedTools: number
  featuredTools: number
  categories: number
  isLoading: boolean
  error: string | null
}

export function useStats(): Stats {
  const [stats, setStats] = useState<Stats>({
    totalTools: 1000, // Fallback value
    verifiedTools: 500, // Fallback value
    featuredTools: 100, // Fallback value
    categories: 20, // Fallback value
    isLoading: true,
    error: null
  })

  useEffect(() => {
    const fetchStats = async () => {
      try {
        // Check if we're on the client side
        if (typeof window === 'undefined') {
          return
        }

        const supabase = createBrowserClient()

        // Check if supabase client was created successfully
        if (!supabase) {
          throw new Error('Failed to create Supabase client')
        }

        // Get total tools count
        const { count: totalCount, error: totalError } = await supabase
          .from('tools')
          .select('*', { count: 'exact', head: true })

        if (totalError) {
          throw new Error(`Failed to get total tools: ${totalError.message}`)
        }

        // Get verified tools count
        const { count: verifiedCount, error: verifiedError } = await supabase
          .from('tools')
          .select('*', { count: 'exact', head: true })
          .eq('is_verified', true)

        if (verifiedError) {
          throw new Error(`Failed to get verified tools: ${verifiedError.message}`)
        }

        // Get featured tools count
        const { count: featuredCount, error: featuredError } = await supabase
          .from('tools')
          .select('*', { count: 'exact', head: true })
          .eq('is_featured', true)

        if (featuredError) {
          throw new Error(`Failed to get featured tools: ${featuredError.message}`)
        }

        // Get unique categories count
        const { data: categoriesData, error: categoriesError } = await supabase
          .from('tools')
          .select('primary_task')
          .not('primary_task', 'is', null)

        if (categoriesError) {
          throw new Error(`Failed to get categories: ${categoriesError.message}`)
        }

        const uniqueCategories = new Set(
          categoriesData?.map(item => item.primary_task).filter(Boolean) || []
        )

        setStats({
          totalTools: totalCount || 0,
          verifiedTools: verifiedCount || 0,
          featuredTools: featuredCount || 0,
          categories: uniqueCategories.size,
          isLoading: false,
          error: null
        })

      } catch (err) {
        console.error('Error fetching stats:', err)
        // Keep fallback values and just update loading state
        setStats(prev => ({
          ...prev,
          isLoading: false,
          error: null // Don't show error to user, just use fallback values
        }))
      }
    }

    // Add a small delay to ensure the component is mounted
    const timer = setTimeout(fetchStats, 100)

    return () => clearTimeout(timer)
  }, [])

  return stats
}

// Hook for formatted stats text
export function useFormattedStats() {
  const stats = useStats()
  
  const formatNumber = (num: number): string => {
    if (num >= 1000) {
      return `${(num / 1000).toFixed(1)}k`
    }
    return num.toString()
  }

  return {
    ...stats,
    totalToolsFormatted: formatNumber(stats.totalTools),
    verifiedToolsFormatted: formatNumber(stats.verifiedTools),
    featuredToolsFormatted: formatNumber(stats.featuredTools),
    categoriesFormatted: formatNumber(stats.categories)
  }
}
