"use client"

import { useState, useEffect, Suspense, lazy, ComponentType, ReactNode } from 'react'
import { useInView } from 'react-intersection-observer'

interface ProgressiveHydrationProps {
  // المكون الذي سيتم تحميله بشكل تدريجي
  children: ReactNode
  // المكون البديل الذي سيتم عرضه أثناء التحميل
  fallback?: ReactNode
  // عتبة الرؤية (0-1)
  threshold?: number
  // هامش الجذر للمراقب
  rootMargin?: string
  // ما إذا كان يجب تشغيل المراقب مرة واحدة فقط
  triggerOnce?: boolean
  // ما إذا كان المكون ذو أولوية (سيتم تحميله فورًا)
  priority?: boolean
  // ما إذا كان يجب تخطي الترطيب
  skipHydration?: boolean
  // معرف المكون
  id?: string
  // فئة CSS للحاوية
  className?: string
  // تأخير التحميل بالمللي ثانية
  delay?: number
}

/**
 * مكون للترطيب التدريجي للمكونات
 * يساعد على تحسين الأداء عن طريق تأجيل ترطيب المكونات حتى تكون مرئية
 */
export function ProgressiveHydration({
  children,
  fallback = null,
  threshold = 0.1,
  rootMargin = '300px 0px',
  triggerOnce = true,
  priority = false,
  skipHydration = false,
  id,
  className = '',
  delay = 0,
}: ProgressiveHydrationProps) {
  const [isClient, setIsClient] = useState(false)
  const [shouldHydrate, setShouldHydrate] = useState(false)
  
  // استخدام IntersectionObserver للتحميل المتأخر
  const { ref, inView } = useInView({
    threshold,
    rootMargin,
    triggerOnce,
    skip: priority, // تخطي المراقبة إذا كان المكون ذو أولوية
  })

  // تجنب أخطاء عدم تطابق الترميز بين الخادم والعميل
  useEffect(() => {
    setIsClient(true)
    
    // إذا كان المكون ذو أولوية، قم بترطيبه فورًا
    if (priority) {
      if (delay > 0) {
        const timer = setTimeout(() => {
          setShouldHydrate(true)
        }, delay)
        return () => clearTimeout(timer)
      } else {
        setShouldHydrate(true)
      }
    }
  }, [priority, delay])
  
  // عند ظهور المكون في العرض، قم بترطيبه
  useEffect(() => {
    if (inView) {
      if (delay > 0) {
        const timer = setTimeout(() => {
          setShouldHydrate(true)
        }, delay)
        return () => clearTimeout(timer)
      } else {
        setShouldHydrate(true)
      }
    }
  }, [inView, delay])

  // إذا لم نكن في العميل وتم تخطي الترطيب، عرض المحتوى مباشرة
  if (!isClient && skipHydration) {
    return <>{children}</>
  }
  
  // إذا لم نكن في العميل، عرض المكون البديل
  if (!isClient) {
    return <>{fallback}</>
  }
  
  return (
    <div 
      ref={!priority ? ref : undefined} 
      id={id}
      className={className}
    >
      {shouldHydrate ? (
        <Suspense fallback={fallback}>
          {children}
        </Suspense>
      ) : (
        fallback
      )}
    </div>
  )
}

/**
 * مساعد لإنشاء مكون متأخر التحميل
 * @param importFunc دالة استيراد المكون
 * @returns مكون متأخر التحميل
 */
export function createLazyComponent<P = any>(
  importFunc: () => Promise<{ default: ComponentType<P> }>
) {
  return lazy(importFunc)
}

/**
 * مكون لتقسيم الصفحة إلى أجزاء يتم تحميلها تدريجيًا
 */
export function PageChunk({
  children,
  priority = false,
  fallback = null,
  delay = 0,
  id,
  className = '',
}: {
  children: ReactNode
  priority?: boolean
  fallback?: ReactNode
  delay?: number
  id?: string
  className?: string
}) {
  return (
    <ProgressiveHydration
      priority={priority}
      fallback={fallback}
      delay={delay}
      id={id}
      className={className}
    >
      {children}
    </ProgressiveHydration>
  )
}

/**
 * مثال على الاستخدام:
 * 
 * // إنشاء مكون متأخر التحميل
 * const HeavySection = createLazyComponent(() => import('@/components/heavy-section'))
 * 
 * // استخدام المكون في الصفحة
 * <PageChunk priority>
 *   <Header />
 * </PageChunk>
 * 
 * <PageChunk>
 *   <MainContent />
 * </PageChunk>
 * 
 * <PageChunk delay={500}>
 *   <HeavySection />
 * </PageChunk>
 */
