"use client"

import { <PERSON>, Sun } from "lucide-react"
import { useTheme } from "next-themes"
import { But<PERSON> } from "@/components/ui/button"
import { useState, useEffect } from "react"
import { cn } from "@/lib/utils"

export function ModeToggle() {
  const { setTheme, resolvedTheme } = useTheme()
  const [mounted, setMounted] = useState(false)
  const [isAnimating, setIsAnimating] = useState(false)

  useEffect(() => {
    setMounted(true)
  }, [])

  const toggleTheme = () => {
    setIsAnimating(true)
    setTheme(resolvedTheme === "dark" ? "light" : "dark")

    // Reset animation state after animation completes
    setTimeout(() => {
      setIsAnimating(false)
    }, 500)
  }

  // Don't render anything until mounted to avoid hydration mismatch
  if (!mounted) {
    return (
      <Button
        variant="ghost"
        size="icon"
        className="rounded-full w-10 h-10 bg-background/80 backdrop-blur-sm border border-border/50"
        disabled
      >
        <div className="h-[1.2rem] w-[1.2rem] animate-pulse bg-muted rounded-full" />
      </Button>
    )
  }

  const isDark = resolvedTheme === "dark"

  return (
    <Button
      onClick={toggleTheme}
      variant="ghost"
      size="icon"
      className={cn(
        "group relative rounded-full w-10 h-10 overflow-hidden",
        "bg-background/80 backdrop-blur-sm border border-border/50",
        "hover:bg-primary/10 hover:border-primary/30 hover:text-primary",
        "transition-all duration-300 ease-in-out",
        "hover:scale-105 active:scale-95",
        "shadow-sm hover:shadow-md",
        "hover:shadow-primary/20 dark:hover:shadow-primary/30",
        isAnimating && "animate-pulse"
      )}
    >
      {/* Background gradient effect */}
      <div className={cn(
        "absolute inset-0 opacity-0 group-hover:opacity-100 transition-opacity duration-300",
        "bg-gradient-to-br from-primary/5 to-accent/5"
      )} />

      {/* Sun Icon */}
      <Sun className={cn(
        "absolute h-[1.2rem] w-[1.2rem] transition-all duration-500 ease-in-out",
        "text-muted-foreground group-hover:text-primary",
        isDark
          ? "rotate-90 scale-0 opacity-0"
          : "rotate-0 scale-100 opacity-100",
        isAnimating && "animate-spin"
      )} />

      {/* Moon Icon */}
      <Moon className={cn(
        "absolute h-[1.2rem] w-[1.2rem] transition-all duration-500 ease-in-out",
        "text-muted-foreground group-hover:text-primary",
        isDark
          ? "rotate-0 scale-100 opacity-100"
          : "-rotate-90 scale-0 opacity-0",
        isAnimating && "animate-pulse"
      )} />

      {/* Ripple effect */}
      <div className={cn(
        "absolute inset-0 rounded-full opacity-0 group-active:opacity-20",
        "bg-primary transition-opacity duration-200",
        "animate-ping"
      )} style={{ animationDuration: '0.3s', animationIterationCount: 1 }} />

      <span className="sr-only">
        Switch to {isDark ? 'light' : 'dark'} mode
      </span>
    </Button>
  )
}
