"use client"

import { useState, useEffect, useCallback, memo } from "react"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { But<PERSON> } from "@/components/ui/button"
// Dropdown menu imports moved to AuthSection component
import { ModeToggle } from "./mode-toggle"

import {
  Menu,
  X,
  <PERSON><PERSON><PERSON>,
  <PERSON>ch,
  Grid,
  Plus,
  Search
} from "lucide-react"
import { No<PERSON>lickerAuth, NoFlickerMobileAuth } from "@/components/header/no-flicker-auth"
import SearchIconButton from "@/components/search/search-icon-button"
import { useFormattedStats } from "@/hooks/use-stats"

const Header = memo(function Header() {
  const [isMenuOpen, setIsMenuOpen] = useState(false)
  const [isScrolled, setIsScrolled] = useState(false)

  const pathname = usePathname()
  // تم نقل منطق التوثيق إلى AuthSection لتجنب hydration mismatch
  const { totalToolsFormatted, isLoading: statsLoading } = useFormattedStats()

  // Handle scroll effect
  const handleScroll = useCallback(() => {
    setIsScrolled(window.scrollY > 20)
  }, [])

  useEffect(() => {
    handleScroll()
    window.addEventListener("scroll", handleScroll, { passive: true })
    return () => window.removeEventListener("scroll", handleScroll)
  }, [handleScroll])





  // تم نقل handleSignOut إلى AuthSection

  const navLinks = [
    { href: "/", label: "Home", icon: <Sparkles className="w-3.5 h-3.5 mr-1" /> },
    { href: "/tools", label: "Tools", icon: <Wrench className="w-3.5 h-3.5 mr-1" /> },
    { href: "/categories", label: "Categories", icon: <Grid className="w-3.5 h-3.5 mr-1" /> },
    { href: "/submit", label: "Submit Tool", icon: <Plus className="w-3.5 h-3.5 mr-1" /> },

  ]

  // Enhanced keyboard navigation and scroll detection
  useEffect(() => {
    const handleKeyDown = (event: KeyboardEvent) => {
      if (event.key === 'Escape' && isMenuOpen) {
        setIsMenuOpen(false)
      }
    }

    const handleScroll = () => {
      setIsScrolled(window.scrollY > 10)
    }

    if (isMenuOpen) {
      document.addEventListener('keydown', handleKeyDown)
      // Prevent body scroll when menu is open
      document.body.style.overflow = 'hidden'
    } else {
      document.body.style.overflow = 'unset'
    }

    window.addEventListener('scroll', handleScroll, { passive: true })

    return () => {
      document.removeEventListener('keydown', handleKeyDown)
      document.removeEventListener('scroll', handleScroll)
      document.body.style.overflow = 'unset'
    }
  }, [isMenuOpen])

  return (
    <header className={`sticky top-0 z-50 w-full theme-transition ${
      isScrolled
        ? 'glass-dark shadow-lg dark:shadow-2xl dark:shadow-primary/10 py-1.5 sm:py-2'
        : 'bg-transparent dark:bg-background/20 backdrop-blur-sm py-2 sm:py-3'
    }`}>
      <div className="container mx-auto px-3 sm:px-4 lg:px-8 max-w-7xl">
        <div className="flex items-center justify-between gap-2 sm:gap-3 lg:gap-4">
          <div className="flex items-center flex-shrink-0 min-w-0">
            <Link href="/" className="flex items-center group">
              <div className="flex items-center">
                <div className="relative bg-gradient-to-br from-purple-500 to-pink-600 text-white font-bold rounded-lg w-8 h-8 sm:w-9 sm:h-9 flex items-center justify-center text-sm sm:text-base shadow-sm group-hover:shadow-lg transition-all duration-300 group-hover:scale-105">
                  <span className="relative z-10">AI</span>
                  <div className="absolute inset-0 bg-gradient-to-br from-purple-400 to-pink-500 rounded-lg opacity-0 group-hover:opacity-100 transition-opacity duration-300 blur-sm"></div>
                </div>
                <span className="text-sm sm:text-base lg:text-lg font-bold ml-1.5 sm:ml-2 dark:text-white transition-colors duration-300 truncate">
                  AI <span className="bg-clip-text text-transparent bg-gradient-to-r from-purple-500 to-pink-600 dark:from-purple-400 dark:to-pink-400">AnyTool</span>
                </span>
              </div>
            </Link>
          </div>

          {/* Desktop Navigation - Hidden on mobile and small tablets */}
          <nav className="hidden lg:flex items-center space-x-1 xl:space-x-2 ml-4 xl:ml-6 flex-grow justify-center max-w-2xl xl:max-w-3xl">
            {navLinks.map((link) => (
              <Link
                key={link.href}
                href={link.href}
                className={`flex items-center px-2 xl:px-3 py-2 rounded-full text-sm font-medium theme-transition ${
                  pathname === link.href
                    ? "bg-primary/10 text-primary shadow-sm"
                    : "text-muted-foreground hover-primary hover:scale-105"
                }`}
              >
                {link.icon}
                {link.label}
              </Link>
            ))}
          </nav>

          {/* Desktop and Medium Screen Layout */}
          <div className="hidden md:flex items-center gap-2 xl:gap-3 flex-shrink-0">
            <SearchIconButton
              variant="header"
              size="md"
            />
            <ModeToggle />
            <NoFlickerAuth />
          </div>

          {/* Mobile/Tablet Menu and Search - Show when desktop nav is hidden */}
          <div className="flex md:hidden items-center gap-2">
            <SearchIconButton
              variant="mobile"
              size="md"
            />
            <Button
              variant="outline"
              size="icon"
              className="rounded-full glass-dark shadow-sm theme-transition h-10 w-10 min-h-[44px] min-w-[44px] touch-manipulation"
              onClick={() => setIsMenuOpen(!isMenuOpen)}
            >
              {isMenuOpen ? <X className="h-5 w-5" /> : <Menu className="h-5 w-5" />}
              <span className="sr-only">Toggle menu</span>
            </Button>
          </div>
        </div>
      </div>

      {/* Mobile/Tablet Navigation */}
      {isMenuOpen && (
        <>
          {/* Enhanced Overlay */}
          <div
            className="fixed inset-0 bg-black/30 backdrop-blur-sm z-[90] lg:hidden transition-opacity duration-300"
            onClick={() => setIsMenuOpen(false)}
            aria-label="Close menu"
          />

          {/* Menu */}
          <div className={`lg:hidden mobile-menu border-t border-border py-4 fixed left-0 right-0 shadow-xl dark:shadow-2xl dark:shadow-primary/10 theme-transition z-[100] ${isScrolled ? 'top-[60px]' : 'top-[72px]'} max-h-[calc(100vh-60px)] overflow-y-auto`}>
            <div className="container mx-auto px-3 sm:px-4 space-y-4">

              {/* Enhanced Close Button */}
              <div className="flex justify-between items-center mb-2">
                <h3 className="text-lg font-semibold text-foreground">Menu</h3>
                <button
                  onClick={() => setIsMenuOpen(false)}
                  className="p-2 rounded-full hover:bg-destructive/10 hover:text-destructive transition-all duration-200 touch-manipulation group"
                  aria-label="Close menu"
                >
                  <X className="h-5 w-5 text-muted-foreground group-hover:text-destructive transition-colors" />
                </button>
              </div>


            <nav className="flex flex-col space-y-2">
              {navLinks.map((link) => (
                <Link
                  key={link.href}
                  href={link.href}
                  className={`flex items-center px-4 py-4 rounded-xl text-base font-medium transition-all duration-200 min-h-[48px] touch-manipulation ${
                    pathname === link.href
                      ? "bg-primary/15 text-primary border border-primary/20"
                      : "text-foreground hover:text-primary hover:bg-muted/70 border border-transparent"
                  }`}
                  onClick={() => setIsMenuOpen(false)}
                >
                  {link.icon}
                  {link.label}
                </Link>
              ))}

              {/* Auth section - Show on tablets where desktop auth is hidden */}
              <div className="pt-3 mt-2 border-t border-border md:hidden">
                <NoFlickerMobileAuth onMenuClose={() => setIsMenuOpen(false)} />
              </div>
              </nav>
            </div>
          </div>
        </>
      )}
    </header>
  )
})

export default Header
