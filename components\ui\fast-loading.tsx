"use client"

import { useEffect, useState } from 'react'
import { Loader2 } from 'lucide-react'

interface FastLoadingProps {
  isLoading: boolean
  children: React.ReactNode
  fallback?: React.ReactNode
  timeout?: number
}

export default function FastLoading({
  isLoading,
  children,
  fallback,
  timeout = 3000
}: FastLoadingProps) {
  const [showFallback, setShowFallback] = useState(false)

  useEffect(() => {
    if (isLoading) {
      const timer = setTimeout(() => {
        setShowFallback(true)
      }, 100) // Show fallback after 100ms

      return () => clearTimeout(timer)
    } else {
      setShowFallback(false)
    }
  }, [isLoading])

  if (isLoading && showFallback) {
    return (
      <div className="flex items-center justify-center py-8">
        {fallback || (
          <div className="text-center">
            <Loader2 className="h-6 w-6 animate-spin mx-auto text-primary" />
            <p className="text-sm text-muted-foreground mt-2">Loading...</p>
          </div>
        )}
      </div>
    )
  }

  if (isLoading) {
    return null // Don't show anything for the first 100ms
  }

  return <>{children}</>
}

// Optimized skeleton components
export function SkeletonCard() {
  return (
    <div className="animate-pulse">
      <div className="bg-muted/60 h-48 rounded-lg mb-4"></div>
      <div className="bg-muted/60 h-4 rounded w-3/4 mb-2"></div>
      <div className="bg-muted/60 h-4 rounded w-1/2"></div>
    </div>
  )
}

export function SkeletonGrid({ count = 12 }: { count?: number }) {
  return (
    <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
      {Array.from({ length: count }).map((_, i) => (
        <SkeletonCard key={i} />
      ))}
    </div>
  )
}

export function SkeletonStats() {
  return (
    <div className="grid grid-cols-2 gap-4 sm:grid-cols-4 animate-pulse">
      {Array.from({ length: 4 }).map((_, i) => (
        <div key={i} className="text-center">
          <div className="h-10 bg-muted/60 rounded mb-2 w-20 mx-auto"></div>
          <div className="h-4 bg-muted/60 rounded w-16 mx-auto"></div>
        </div>
      ))}
    </div>
  )
}

export function SkeletonSearch() {
  return (
    <div className="animate-pulse">
      <div className="h-12 bg-muted/60 rounded-full w-full max-w-xl mx-auto mb-3"></div>
      <div className="flex gap-2 overflow-x-auto py-2 max-w-4xl mx-auto">
        {Array.from({ length: 6 }).map((_, i) => (
          <div key={i} className="h-8 w-20 bg-muted/60 rounded-full flex-shrink-0"></div>
        ))}
      </div>
    </div>
  )
}
