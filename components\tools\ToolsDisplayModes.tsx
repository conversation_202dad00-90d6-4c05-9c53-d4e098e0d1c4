"use client"

import { useState, useEffect } from 'react'
import { Button } from '@/components/ui/button'
import { Badge } from '@/components/ui/badge'
import { Card, CardContent } from '@/components/ui/card'
import { Tooltip, TooltipContent, TooltipProvider, TooltipTrigger } from '@/components/ui/tooltip'
import { Grid3X3, List, LayoutGrid, Star, ExternalLink, Heart, Award, ArrowRight } from 'lucide-react'
import { createBrowserClient } from '@/lib/supabase/client-utils'
import Link from 'next/link'
import { cn } from '@/lib/utils'
import ToolCard from '@/components/tool-card'

interface Tool {
  id: string
  company_name: string
  short_description: string
  logo_url?: string
  url?: string
  rating?: number
  pricing?: string
  primary_task?: string
  is_featured: boolean
  is_verified: boolean
  created_at: string
}

interface ToolsDisplayModesProps {
  searchQuery?: string
  category?: string
  pricing?: string
  sortBy?: string
  features?: string[]
}

export default function ToolsDisplayModes({
  searchQuery = "",
  category = "",
  pricing = "",
  sortBy = "featured",
  features = []
}: ToolsDisplayModesProps) {
  const [tools, setTools] = useState<Tool[]>([])
  const [categorizedTools, setCategorizedTools] = useState<Record<string, Tool[]>>({})
  const [loading, setLoading] = useState(true)
  const [displayMode, setDisplayMode] = useState<'cards' | 'icons'>('cards')
  const [visibleCount, setVisibleCount] = useState(100)
  const [categorySort, setCategorySort] = useState<'size' | 'alphabetical'>('size')

  useEffect(() => {
    const fetchTools = async () => {
      try {
        setLoading(true)
        const supabase = createBrowserClient()

        console.log('Fetching tools with params:', { searchQuery, category, pricing, sortBy, features })

        let query = supabase.from('tools').select('*')

        // Apply filters
        if (searchQuery) {
          query = query.or(
            `company_name.ilike.%${searchQuery}%,short_description.ilike.%${searchQuery}%,full_description.ilike.%${searchQuery}%`
          )
        }

        if (category) {
          query = query.ilike('primary_task', `%${category.replace(/-/g, ' ')}%`)
        }

        if (features.length > 0) {
          features.forEach(feature => {
            query = query.ilike('features', `%${feature.replace(/-/g, ' ')}%`)
          })
        }

        // Apply sorting
        switch (sortBy) {
          case 'featured':
            query = query.order('is_featured', { ascending: false }).order('rating', { ascending: false })
            break
          case 'newest':
            query = query.order('created_at', { ascending: false })
            break
          case 'popular':
            query = query.order('rating', { ascending: false })
            break
          case 'rating':
            query = query.order('rating', { ascending: false })
            break
          default:
            query = query.order('is_featured', { ascending: false }).order('rating', { ascending: false })
        }

        const { data, error } = await query

        console.log('Tools query result:', { data: data?.length, error })

        if (error) {
          console.error('Error fetching tools:', error)
          return
        }

        if (data) {
          console.log('Setting tools:', data.length)
          setTools(data)

          // Group tools by category for the bottom section
          const grouped = data.reduce((acc: Record<string, Tool[]>, tool) => {
            if (tool.primary_task) {
              if (!acc[tool.primary_task]) {
                acc[tool.primary_task] = []
              }
              acc[tool.primary_task].push(tool)
            }
            return acc
          }, {})

          // Sort categories
          const sortedCategories = Object.keys(grouped).sort((a, b) => {
            if (categorySort === 'alphabetical') {
              return a.localeCompare(b)
            }
            return grouped[b].length - grouped[a].length
          })

          const sortedGrouped: Record<string, Tool[]> = {}
          sortedCategories.forEach(category => {
            sortedGrouped[category] = grouped[category]
              .sort((a, b) => (b.is_featured ? 1 : 0) - (a.is_featured ? 1 : 0))
              .slice(0, 10)
          })

          setCategorizedTools(sortedGrouped)
        }
      } catch (error) {
        console.error('Error fetching tools:', error)
      } finally {
        setLoading(false)
      }
    }

    fetchTools()
  }, [searchQuery, category, pricing, sortBy, features, categorySort])

  const visibleTools = tools.slice(0, visibleCount)
  const remainingTools = tools.slice(visibleCount)

  const IconView = ({ tool }: { tool: Tool }) => (
    <TooltipProvider>
      <Tooltip>
        <TooltipTrigger asChild>
          <div className="group cursor-pointer p-3 rounded-xl hover:bg-muted/50 transition-all duration-200 hover:scale-105">
            <div className="flex flex-col items-center gap-2">
              {tool.logo_url ? (
                <img
                  src={tool.logo_url}
                  alt={tool.company_name}
                  className="w-12 h-12 rounded-lg object-cover border border-border group-hover:border-primary/30 transition-colors"
                />
              ) : (
                <div className="w-12 h-12 rounded-lg bg-gradient-to-br from-primary/20 to-primary/40 flex items-center justify-center border border-border group-hover:border-primary/30 transition-colors">
                  <span className="text-lg font-bold text-primary">
                    {tool.company_name.charAt(0).toUpperCase()}
                  </span>
                </div>
              )}
              
              <div className="text-center">
                <h3 className="text-sm font-medium truncate max-w-[80px] group-hover:text-primary transition-colors">
                  {tool.company_name}
                </h3>
                {tool.is_featured && (
                  <Badge variant="secondary" className="text-xs mt-1 bg-amber-50 text-amber-700 border-amber-200">
                    <Star className="h-2 w-2 mr-1" />
                    Featured
                  </Badge>
                )}
              </div>
            </div>
          </div>
        </TooltipTrigger>
        <TooltipContent side="bottom" className="max-w-xs">
          <div className="space-y-2">
            <h4 className="font-semibold">{tool.company_name}</h4>
            <p className="text-sm text-muted-foreground">{tool.short_description}</p>
            {tool.primary_task && (
              <Badge variant="outline" className="text-xs">
                {tool.primary_task}
              </Badge>
            )}
          </div>
        </TooltipContent>
      </Tooltip>
    </TooltipProvider>
  )

  if (loading) {
    return (
      <div className="space-y-8">
        <div className="flex justify-between items-center">
          <div className="h-8 bg-muted rounded w-48 animate-pulse"></div>
          <div className="flex gap-2">
            <div className="h-10 w-20 bg-muted rounded animate-pulse"></div>
            <div className="h-10 w-20 bg-muted rounded animate-pulse"></div>
          </div>
        </div>
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {Array(12).fill(0).map((_, i) => (
            <div key={i} className="h-64 bg-muted rounded-xl animate-pulse"></div>
          ))}
        </div>
      </div>
    )
  }

  return (
    <div className="space-y-8">
      {/* Display Mode Controls */}
      <div className="flex justify-between items-center">
        <h2 className="text-2xl font-bold">
          {tools.length} AI Tools {searchQuery && `for "${searchQuery}"`}
        </h2>
        
        <div className="flex items-center gap-2">
          <Button
            variant={displayMode === 'cards' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setDisplayMode('cards')}
          >
            <LayoutGrid className="h-4 w-4 mr-2" />
            Cards
          </Button>
          <Button
            variant={displayMode === 'icons' ? 'default' : 'outline'}
            size="sm"
            onClick={() => setDisplayMode('icons')}
          >
            <Grid3X3 className="h-4 w-4 mr-2" />
            Icons
          </Button>
        </div>
      </div>

      {/* Main Tools Display */}
      {displayMode === 'cards' ? (
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
          {visibleTools.map((tool) => (
            <ToolCard key={tool.id} tool={tool} />
          ))}
        </div>
      ) : (
        <div className="grid grid-cols-4 sm:grid-cols-6 md:grid-cols-8 lg:grid-cols-10 xl:grid-cols-12 gap-4">
          {visibleTools.map((tool) => (
            <IconView key={tool.id} tool={tool} />
          ))}
        </div>
      )}

      {/* Load More Button */}
      {remainingTools.length > 0 && (
        <div className="text-center">
          <Button
            size="lg"
            onClick={() => setVisibleCount(prev => prev + 100)}
            className="group"
          >
            Load More Tools ({remainingTools.length} remaining)
            <ArrowRight className="h-4 w-4 ml-2 group-hover:translate-x-1 transition-transform" />
          </Button>
        </div>
      )}

      {/* Categories Section */}
      {Object.keys(categorizedTools).length > 0 && (
        <div className="space-y-8 pt-8 border-t border-border">
          <div className="flex justify-between items-center">
            <h2 className="text-2xl font-bold">Browse by Category</h2>
            <div className="flex items-center gap-2">
              <span className="text-sm text-muted-foreground">Sort:</span>
              <Button
                variant={categorySort === 'size' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCategorySort('size')}
              >
                By Size
              </Button>
              <Button
                variant={categorySort === 'alphabetical' ? 'default' : 'outline'}
                size="sm"
                onClick={() => setCategorySort('alphabetical')}
              >
                A-Z
              </Button>
            </div>
          </div>

          {Object.entries(categorizedTools).map(([categoryName, categoryTools]) => (
            <div key={categoryName} className="space-y-4">
              <div className="flex items-center justify-between">
                <h3 className="text-xl font-semibold flex items-center gap-2">
                  {categoryName}
                  <Badge variant="outline">{categoryTools.length} tools</Badge>
                </h3>
                <Link href={`/tools?category=${categoryName.toLowerCase().replace(/\s+/g, '-')}`}>
                  <Button variant="ghost" size="sm" className="group">
                    View All
                    <ArrowRight className="h-3 w-3 ml-1 group-hover:translate-x-0.5 transition-transform" />
                  </Button>
                </Link>
              </div>

              {displayMode === 'cards' ? (
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4 gap-4">
                  {categoryTools.map((tool) => (
                    <ToolCard key={tool.id} tool={tool} />
                  ))}
                </div>
              ) : (
                <div className="grid grid-cols-6 sm:grid-cols-8 md:grid-cols-10 lg:grid-cols-12 xl:grid-cols-15 gap-3">
                  {categoryTools.map((tool) => (
                    <IconView key={tool.id} tool={tool} />
                  ))}
                </div>
              )}
            </div>
          ))}
        </div>
      )}
    </div>
  )
}
