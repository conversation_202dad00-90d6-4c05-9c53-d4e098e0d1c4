import { NextRequest, NextResponse } from 'next/server'
import { createServerClient } from '@/lib/supabase/server'

export const dynamic = 'force-dynamic'

export async function GET(
  request: NextRequest,
  { params }: { params: { slug: string } }
) {
  try {
    const { slug } = params
    console.log('🔍 API: Searching for tool with slug:', slug)

    const supabase = await createServerClient()

    if (!supabase) {
      return NextResponse.json({ error: 'Database not available' }, { status: 500 })
    }

    // Try exact slug match first
    const { data: tool, error } = await supabase
      .from('tools')
      .select('*')
      .eq('slug', slug)
      .maybeSingle()

    if (error) {
      console.error('❌ API: Database error:', error)
      return NextResponse.json({ error: 'Database error' }, { status: 500 })
    }

    if (!tool) {
      console.error('❌ API: Tool not found with slug:', slug)
      return NextResponse.json({ error: 'Tool not found' }, { status: 404 })
    }

    console.log('✅ API: Tool found:', tool.company_name)
    return NextResponse.json({ tool })

  } catch (error: any) {
    console.error('❌ API: Error fetching tool data:', error)
    return NextResponse.json(
      { error: 'Internal server error', details: error.message },
      { status: 500 }
    )
  }
}
