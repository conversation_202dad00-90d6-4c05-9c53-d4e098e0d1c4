'use client'

import { supabase } from '@/lib/supabase/client'
import type { AuthProvider, AuthUser, AuthResponse } from './types'
import type { User } from '@supabase/supabase-js'
import { normalizeAuthError, isUserExistsError } from './auth-utils'

export class SupabaseAuthProvider implements AuthProvider {
  private supabase: any

  constructor() {
    this.supabase = supabase
  }



  async getUser(): Promise<AuthUser | null> {
    if (!this.supabase) return null

    try {
      // استخدام getUser بدلاً من getSession للحصول على أحدث البيانات
      const { data: { user }, error } = await this.supabase.auth.getUser()

      if (error || !user) {
        return null
      }

      return await this.mapSupabaseUser(user)
    } catch (error) {
      console.warn('Error getting user:', error)
      return null
    }
  }

  private async mapSupabaseUser(authUser: User): Promise<AuthUser> {
    // استخدام البيانات الأساسية فوراً لتسريع العملية
    const baseUser = {
      id: authUser.id,
      email: authUser.email,
      name: authUser.user_metadata?.full_name || authUser.user_metadata?.name || authUser.email?.split('@')[0] || 'User',
      avatar_url: authUser.user_metadata?.avatar_url,
      role: 'user', // افتراضي
      created_at: authUser.created_at,
      user_metadata: authUser.user_metadata
    }

    // جلب البروفايل في الخلفية دون انتظار (fire and forget)
    this.loadUserProfile(authUser.id).catch(error => {
      console.warn('Background profile load failed:', error)
    })

    return baseUser
  }

  private async loadUserProfile(userId: string): Promise<void> {
    try {
      const { data: profile, error } = await this.supabase
        .from('profiles')
        .select('role, created_at, updated_at')
        .eq('id', userId)
        .single()

      // إنشاء profile إذا لم يكن موجوداً
      if (!profile && error?.code === 'PGRST116') {
        await this.supabase
          .from('profiles')
          .insert({
            id: userId,
            role: 'user',
            created_at: new Date().toISOString(),
            updated_at: new Date().toISOString()
          })
      }
    } catch (error) {
      console.warn('Error in background profile load:', error)
    }
  }

  async signIn(email: string, password: string): Promise<AuthResponse> {
    if (!this.supabase) return { error: 'Supabase not initialized' }

    try {
      const { data, error } = await this.supabase.auth.signInWithPassword({
        email,
        password
      })

      if (error) {
        console.error('Supabase signIn error:', error)
        // Use utility function to normalize error messages
        const errorMessage = normalizeAuthError(error.message)
        return { error: errorMessage }
      }

      return { data }
    } catch (error: any) {
      return { error: error.message || 'Sign in failed' }
    }
  }

  async signUp(email: string, password: string, fullName?: string): Promise<AuthResponse> {
    if (!this.supabase) return { error: 'Supabase not initialized' }

    try {
      console.log('🔄 Starting signup process for:', email)
      console.log('🌐 Redirect URL:', process.env.NEXT_PUBLIC_SITE_URL
        ? `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`
        : `${window.location.origin}/auth/callback`)

      const { data, error } = await this.supabase.auth.signUp({
        email,
        password,
        options: {
          data: {
            full_name: fullName
          },
          // Use production URL for email confirmation
          emailRedirectTo: process.env.NEXT_PUBLIC_SITE_URL
            ? `${process.env.NEXT_PUBLIC_SITE_URL}/auth/callback`
            : `${window.location.origin}/auth/callback`
        }
      })

      console.log('📊 Signup response data:', data)
      console.log('❌ Signup response error:', error)

      if (error) {
        console.error('🚨 Supabase signUp error:', error)

        // Check for specific error messages that indicate user already exists
        if (error.message.includes('User already registered') ||
            error.message.includes('already been registered') ||
            error.message.includes('already registered')) {
          return { error: 'This email is already registered. Please sign in instead.' }
        }

        // Use utility function to normalize other error messages
        const errorMessage = normalizeAuthError(error.message)
        return { error: errorMessage }
      }

      // Check if this is a repeated signup (user already exists)
      if (data.user) {
        console.log('📊 User data received:', {
          id: data.user.id,
          email: data.user.email,
          created_at: data.user.created_at,
          email_confirmed_at: data.user.email_confirmed_at,
          session: !!data.session
        })

        // If user exists but no session was created, check if it's a repeated signup
        if (!data.session && data.user.created_at) {
          const createdTime = new Date(data.user.created_at).getTime()
          const now = new Date().getTime()
          const timeDiff = now - createdTime

          // If user was created more than 1 minute ago, it's definitely an existing user
          if (timeDiff > 60000) { // 1 minute
            console.log('🚨 Repeated signup detected for existing user')
            if (data.user.email_confirmed_at) {
              return { error: 'This email is already registered and confirmed. Please sign in instead.' }
            } else {
              return { error: 'This email is already registered but not confirmed. Please check your email for the confirmation link.' }
            }
          }
        }

        console.log('✅ User signup successful - new user or confirmation email sent')
        return { data }
      }

      console.log('⚠️ No user data returned from signup')
      return { data }
    } catch (error: any) {
      console.error('🚨 Supabase signUp catch error:', error)
      return { error: error.message || 'Account creation failed. Please try again.' }
    }
  }

  async signOut(): Promise<void> {
    if (this.supabase) {
      await this.supabase.auth.signOut()
    }
  }

  async signInWithGoogle(): Promise<AuthResponse> {
    if (!this.supabase) {
      console.error('❌ Supabase not initialized for Google OAuth')
      return { error: 'Supabase not initialized' }
    }

    try {
      console.log('🔄 Starting Google OAuth with Supabase...')

      // الحصول على redirect_to من URL الحالي
      const urlParams = new URLSearchParams(window.location.search)
      const redirectTo = urlParams.get('redirect_to') || '/'
      const callbackUrl = `${window.location.origin}/auth/callback?redirect_to=${encodeURIComponent(redirectTo)}`

      console.log('🌐 Google OAuth callback URL:', callbackUrl)

      const { data, error } = await this.supabase.auth.signInWithOAuth({
        provider: 'google',
        options: {
          redirectTo: callbackUrl
        }
      })

      console.log('📊 Supabase Google OAuth response:', { data, error })

      if (error) {
        console.error('❌ Supabase Google OAuth error:', error)
        return { error: error.message }
      }

      console.log('✅ Google OAuth initiated successfully')
      return { data }
    } catch (error: any) {
      console.error('🚨 Google OAuth exception:', error)
      return { error: error.message || 'Google sign in failed' }
    }
  }

  async signInWithGitHub(): Promise<AuthResponse> {
    if (!this.supabase) return { error: 'Supabase not initialized' }

    try {
      // الحصول على redirect_to من URL الحالي
      const urlParams = new URLSearchParams(window.location.search)
      const redirectTo = urlParams.get('redirect_to') || '/'
      const callbackUrl = `${window.location.origin}/auth/callback?redirect_to=${encodeURIComponent(redirectTo)}`

      const { data, error } = await this.supabase.auth.signInWithOAuth({
        provider: 'github',
        options: {
          redirectTo: callbackUrl
        }
      })

      if (error) {
        return { error: error.message }
      }

      return { data }
    } catch (error: any) {
      return { error: error.message || 'GitHub sign in failed' }
    }
  }

  async getSession(): Promise<any> {
    if (!this.supabase) return null

    try {
      const { data: { session } } = await this.supabase.auth.getSession()
      return session
    } catch (error) {
      return null
    }
  }

  onAuthStateChange(callback: (user: AuthUser | null) => void): () => void {
    if (!this.supabase) return () => {}

    const { data: { subscription } } = this.supabase.auth.onAuthStateChange(
      async (event: string, session: any) => {
        if (session?.user) {
          const user = await this.mapSupabaseUser(session.user)
          callback(user)
        } else {
          callback(null)
        }
      }
    )

    return () => subscription.unsubscribe()
  }
}
