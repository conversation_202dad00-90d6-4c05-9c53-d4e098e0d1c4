'use client'

import { useState, useEffect } from 'react'
import { useAuth } from '@/hooks/use-auth'
import { createBrowserClient } from '@/lib/supabase/client-utils'
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from '@/components/ui/card'
import { Button } from '@/components/ui/button'
import { Input } from '@/components/ui/input'
import { LoadingSpinner } from '@/components/loading-spinner'
import { SavedToolCard } from './SavedToolCard'
import { Heart, Search, Filter } from 'lucide-react'
import { toast } from 'sonner'

interface SavedTool {
  id: string
  tool_id: number
  created_at: string
  tool: {
    id: number
    name: string
    description: string
    logo_url: string
    website_url: string
    category: string
    pricing_type: string
    rating: number
    slug: string
  }
}

export function SavedToolsTab() {
  const [user, setUser] = useState<any>(null)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const [supabase, setSupabase] = useState<any>(null)
  const [savedTools, setSavedTools] = useState<SavedTool[]>([])
  const [filteredTools, setFilteredTools] = useState<SavedTool[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState('')
  const [selectedCategory, setSelectedCategory] = useState('all')

  useEffect(() => {
    const client = createBrowserClient()
    setSupabase(client)

    // Get real user data
    const fetchUser = async () => {
      if (!client) return

      try {
        const { data: { user: authUser }, error } = await client.auth.getUser()

        if (error || !authUser) {
          // For development, use mock user
          const mockUser = {
            id: 'dev-user-123',
            email: '<EMAIL>',
            name: 'Development User'
          }
          setUser(mockUser)
          setIsAuthenticated(true)
        } else {
          setUser(authUser)
          setIsAuthenticated(true)
        }
      } catch (error) {
        console.error('Error fetching user:', error)
        // Use mock user for development
        const mockUser = {
          id: 'dev-user-123',
          email: '<EMAIL>',
          name: 'Development User'
        }
        setUser(mockUser)
        setIsAuthenticated(true)
      }
    }

    fetchUser()
  }, [])

  useEffect(() => {
    if (user && supabase && isAuthenticated) {
      fetchSavedTools()
    }
  }, [user, supabase, isAuthenticated])

  useEffect(() => {
    filterTools()
  }, [savedTools, searchQuery, selectedCategory])

  const fetchSavedTools = async () => {
    if (!user || !supabase) return

    try {
      setIsLoading(true)

      const { data, error } = await supabase
        .from('favorites')
        .select(`
          id,
          tool_id,
          created_at,
          tools (
            id,
            company_name,
            short_description,
            logo_url,
            visit_website_url,
            primary_task,
            pricing,
            slug
          )
        `)
        .eq('user_id', user.id)
        .order('created_at', { ascending: false })

      if (error) {
        console.warn('Error fetching saved tools:', error.message)
        setSavedTools([])
        return
      }

      // Process the data to match expected format
      const processedData = data?.map(item => ({
        id: item.id,
        tool_id: item.tool_id,
        created_at: item.created_at,
        tool: {
          id: item.tools?.id || 0,
          name: item.tools?.company_name || 'Unknown Tool',
          description: item.tools?.short_description || '',
          logo_url: item.tools?.logo_url || '',
          website_url: item.tools?.visit_website_url || '',
          category: item.tools?.primary_task || 'uncategorized',
          pricing_type: item.tools?.pricing || 'free',
          rating: 0, // We'll calculate this separately if needed
          slug: item.tools?.slug || ''
        }
      })).filter(item => item.tool.id) || []

      setSavedTools(processedData)
    } catch (error: any) {
      console.warn('Error fetching saved tools:', error.message)
      setSavedTools([])
      // Don't show error toast for missing data, just log it
    } finally {
      setIsLoading(false)
    }
  }

  const filterTools = () => {
    let filtered = savedTools

    // Filter by search query
    if (searchQuery) {
      filtered = filtered.filter(item =>
        (item.tool.name && item.tool.name.toLowerCase().includes(searchQuery.toLowerCase())) ||
        (item.tool.description && item.tool.description.toLowerCase().includes(searchQuery.toLowerCase()))
      )
    }

    // Filter by category
    if (selectedCategory !== 'all') {
      filtered = filtered.filter(item =>
        item.tool.category && item.tool.category.toLowerCase() === selectedCategory.toLowerCase()
      )
    }

    setFilteredTools(filtered)
  }

  const handleRemoveFromSaved = async (favoriteId: string) => {
    if (!supabase) return

    try {
      const { error } = await supabase
        .from('favorites')
        .delete()
        .eq('id', favoriteId)

      if (error) {
        console.warn('Error removing from saved:', error.message)
        toast.error('Failed to remove tool')
        return
      }

      setSavedTools(prev => prev.filter(item => item.id !== favoriteId))
      toast.success('Tool removed from saved list')
    } catch (error: any) {
      console.warn('Error removing from saved:', error.message)
      toast.error('Failed to remove tool')
    }
  }

  // Get unique categories
  const categories = ['all', ...new Set(savedTools.map(item => item.tool.category))]

  if (isLoading) {
    return (
      <div className="flex items-center justify-center py-8">
        <LoadingSpinner size={32} />
        <span className="ml-2 text-muted-foreground">Loading saved tools...</span>
      </div>
    )
  }

  return (
    <Card className="border-border/60 dark:border-accent/10 shadow-md">
      <CardHeader>
        <CardTitle>Your Saved AI Tools</CardTitle>
        <CardDescription>
          These are the AI tools you've bookmarked for later reference
        </CardDescription>
      </CardHeader>
      <CardContent>
          {savedTools.length > 0 && (
            <div className="flex flex-col sm:flex-row gap-4 mb-6">
              <div className="relative flex-1">
                <Search className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <Input
                  placeholder="Search saved tools..."
                  value={searchQuery}
                  onChange={(e) => setSearchQuery(e.target.value)}
                  className="pl-10"
                />
              </div>
              <div className="relative">
                <Filter className="absolute left-3 top-3 h-4 w-4 text-muted-foreground" />
                <select
                  value={selectedCategory}
                  onChange={(e) => setSelectedCategory(e.target.value)}
                  className="pl-10 pr-4 py-2 border border-input bg-background rounded-md text-sm focus:outline-none focus:ring-2 focus:ring-ring"
                >
                  {categories.map(category => (
                    <option key={category} value={category}>
                      {category === 'all' ? 'All Categories' : category}
                    </option>
                  ))}
                </select>
              </div>
            </div>
          )}

          {savedTools.length === 0 ? (
            <div className="text-center py-8">
              <Heart className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No saved tools yet</h3>
              <p className="text-muted-foreground mb-4">
                Start exploring and save tools you find interesting
              </p>
              <Button asChild>
                <a href="/tools">Browse Tools</a>
              </Button>
            </div>
          ) : filteredTools.length === 0 ? (
            <div className="text-center py-8">
              <Search className="h-12 w-12 text-muted-foreground mx-auto mb-4" />
              <h3 className="text-lg font-semibold mb-2">No tools found</h3>
              <p className="text-muted-foreground">
                Try adjusting your search or filter criteria
              </p>
            </div>
          ) : (
            <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-4">
              {filteredTools.map((item) => (
                <SavedToolCard
                  key={item.id}
                  savedTool={item}
                  onRemove={() => handleRemoveFromSaved(item.id)}
                />
              ))}
            </div>
          )}
        </CardContent>
      </Card>
  )
}
