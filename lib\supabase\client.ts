import { createClient } from "@supabase/supabase-js"

const supabaseUrl = process.env.NEXT_PUBLIC_SUPABASE_URL
const supabaseAnonKey = process.env.NEXT_PUBLIC_SUPABASE_ANON_KEY

if (!supabaseUrl || !supabaseAnonKey) {
  console.warn("Missing Supabase environment variables")
  // Return a mock client for build time
  if (typeof window === 'undefined') {
    console.warn("Supabase client not available during build")
  }
}

// Global singleton instance
let globalSupabaseInstance: any = null

// Create singleton function
function createSupabaseClient() {
  if (globalSupabaseInstance) {
    return globalSupabaseInstance
  }

  // Return null if environment variables are missing
  if (!supabaseUrl || !supabaseAnonKey) {
    return null
  }

  globalSupabaseInstance = createClient(supabaseUrl, supabaseAnonKey, {
    auth: {
      persistSession: true,
      autoRefreshToken: true,
      detectSessionInUrl: true,
      flowType: 'pkce',
      storage: typeof window !== 'undefined' ? window.localStorage : undefined,
      storageKey: 'aianytool-auth',
      // Ensure email confirmation is required
      confirmEmailChangeByOtp: true
    },
    global: {
      headers: {
        'x-client-info': 'aianytool-web'
      }
    }
  })

  return globalSupabaseInstance
}

// Export the singleton instance
export const supabase = createSupabaseClient()
