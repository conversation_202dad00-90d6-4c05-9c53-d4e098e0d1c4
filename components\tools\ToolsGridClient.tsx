'use client'

import { useEffect, useState } from "react"
import ToolC<PERSON> from "@/components/tool-card"
import { Alert, AlertDescription, AlertTitle } from "@/components/ui/alert"
import { AlertCircle } from "lucide-react"
import { supabase } from "@/lib/supabase/client"
import { LoadingGrid } from "@/components/ui/loading-grid"

interface ToolsGridClientProps {
  searchQuery?: string
  category?: string
  pricing?: string
  sortBy?: string
  features?: string[]
  limit?: number
  columnsPerRow?: number
}

export default function ToolsGridClient({
  searchQuery = "",
  category = "",
  pricing = "",
  sortBy = "featured",
  features = [],
  limit = 12,
  columnsPerRow = 4,
}: ToolsGridClientProps) {
  const [tools, setTools] = useState<any[]>([])
  const [loading, setLoading] = useState(true)
  const [error, setError] = useState<Error | null>(null)

  useEffect(() => {
    const fetchTools = async () => {
      setLoading(true)
      try {
        // Start building the query
        let query = supabase.from("tools").select("*")

        // Apply search filter if provided
        if (searchQuery) {
          query = query.or(
            `company_name.ilike.%${searchQuery}%,short_description.ilike.%${searchQuery}%,full_description.ilike.%${searchQuery}%`
          )
        }

        // Apply category filter if provided
        if (category) {
          query = query.eq("primary_task", category.replace(/-/g, ' '))
        }

        // Apply pricing filter if provided
        if (pricing) {
          query = query.eq("pricing", pricing)
        }

        // Apply features filter if provided
        if (features.length > 0) {
          features.forEach(feature => {
            query = query.contains("features", [feature.replace(/-/g, ' ')])
          })
        }

        // Apply sorting
        if (sortBy === "newest") {
          query = query.order("created_at", { ascending: false })
        } else if (sortBy === "popular") {
          query = query.order("click_count", { ascending: false })
        } else if (sortBy === "rating") {
          query = query.order("rating", { ascending: false })
        } else {
          // Default to featured
          query = query.order("is_featured", { ascending: false }).order("click_count", { ascending: false })
        }

        // Apply limit
        query = query.limit(limit)

        // Execute the query
        const { data, error } = await query

        if (error) {
          throw error
        }

        setTools(data || [])
      } catch (err) {
        console.error("Error fetching tools:", err)
        setError(err instanceof Error ? err : new Error('Unknown error'))
      } finally {
        setLoading(false)
      }
    }

    fetchTools()
  }, [searchQuery, category, pricing, sortBy, features, limit])

  // Determine grid columns class based on columnsPerRow
  const gridColsClasses = {
    1: "grid-cols-1",
    2: "grid-cols-1 sm:grid-cols-2",
    3: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3",
    4: "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4",
  }[columnsPerRow] || "grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 xl:grid-cols-4"

  if (loading) {
    return <LoadingGrid count={limit} columns={columnsPerRow} />
  }

  if (error) {
    return (
      <Alert variant="destructive">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>Error</AlertTitle>
        <AlertDescription>
          Failed to load tools. Please try again later.
        </AlertDescription>
      </Alert>
    )
  }

  // If no tools found
  if (!tools || tools.length === 0) {
    return (
      <Alert variant="default" className="bg-muted">
        <AlertCircle className="h-4 w-4" />
        <AlertTitle>No tools found</AlertTitle>
        <AlertDescription>
          Try adjusting your search or filter criteria to find more tools.
        </AlertDescription>
      </Alert>
    )
  }

  // Render the grid
  return (
    <div className={`grid ${gridColsClasses} gap-4`}>
      {tools.map((tool) => (
        <ToolCard key={tool.id} tool={tool} />
      ))}
    </div>
  )
}
