"use client"

import Link from "next/link"
import { <PERSON><PERSON> } from "@/components/ui/button"

// Static auth section - no state, no flickering
export function StaticAuthSection() {
  return (
    <Button 
      asChild 
      size="sm" 
      className="rounded-full bg-primary hover:bg-primary/90 text-primary-foreground px-4 py-1.5 h-9 shadow-sm hover:shadow-lg theme-transition hover-button text-sm min-h-[44px] touch-manipulation"
    >
      <Link href="/auth">Sign In</Link>
    </Button>
  )
}

export function StaticMobileAuthSection({ onMenuClose }: { onMenuClose: () => void }) {
  return (
    <div className="space-y-2">
      <Button 
        asChild 
        className="w-full rounded-xl bg-primary hover:bg-primary/90 shadow-md hover:shadow-lg py-4 h-12 text-base text-primary-foreground font-medium touch-manipulation border border-primary/20"
      >
        <Link href="/auth" onClick={onMenuClose}>
          Sign In
        </Link>
      </Button>
    </div>
  )
}
