"use client"

import { useState, useEffect } from "react"
import Too<PERSON><PERSON><PERSON> from "@/components/tool-card"
import Link from "next/link"
import { ChevronRight } from "lucide-react"
import { createBrowserClient } from "@/lib/supabase/client-utils"

export default function TopRatedToolsClient() {
  const [tools, setTools] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchTools = async () => {
      setIsLoading(true)
      const supabase = createBrowserClient()

      try {
        // Get tools with actual ratings, sorted by rating first
        const { data, error: supabaseError } = await supabase
          .from("tools")
          .select("*")
          .gt("rating", 0)  // Only tools with actual ratings
          .order("rating", { ascending: false })
          .order("click_count", { ascending: false })  // Secondary sort by popularity
          .limit(6)

        if (supabaseError) throw supabaseError

        setTools(data || [])
      } catch (err: any) {
        console.error("Error fetching top rated tools:", err.message)
        setError("Error loading top rated tools. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchTools()
  }, [])

  if (isLoading) {
    return (
      <div className="text-center py-12">
        <div className="animate-spin rounded-full h-8 w-8 border-b-2 border-primary mx-auto"></div>
      </div>
    )
  }

  if (error) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-600 dark:text-slate-400">{error}</p>
      </div>
    )
  }

  if (!tools || tools.length === 0) {
    return (
      <div className="text-center py-12">
        <p className="text-slate-600 dark:text-slate-400">No tools available at the moment.</p>
      </div>
    )
  }

  return (
    <div>
      <div className="flex justify-between items-center mb-6">
        <h2 className="text-2xl font-bold">Top Rated Tools</h2>
        <Link href="/top-rated" className="text-blue-600 dark:text-blue-400 flex items-center hover:underline">
          View all <ChevronRight className="h-4 w-4 ml-1" />
        </Link>
      </div>
      <p className="text-slate-600 dark:text-slate-400 mb-8">Discover AI tools with the highest user ratings and reviews</p>

      <div className="grid grid-cols-1 sm:grid-cols-2 lg:grid-cols-3 gap-6">
        {tools.map((tool) => (
          <ToolCard key={tool.id} tool={tool} />
        ))}
      </div>
    </div>
  )
}
