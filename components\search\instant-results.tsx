"use client"

import React, { useEffect, useState } from "react"
import { createPortal } from "react-dom"
import { useRouter } from "next/navigation"
import { ArrowR<PERSON>, Star, Clock, ExternalLink, Loader2, Wrench, Folder, Tag, Layers, BookOpen, Building2, <PERSON><PERSON><PERSON> } from "lucide-react"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { Button } from "@/components/ui/button"
import { cn } from "@/lib/utils"
import { SearchResult } from "@/lib/search/search-service"
import SearchLoadingSkeleton from "./search-loading-skeleton"

interface InstantResultsProps {
  results: SearchResult[]
  isLoading: boolean
  isOpen: boolean
  selectedIndex: number
  query: string
  onResultSelect: (result: SearchResult) => void
  onViewAll: () => void
  className?: string
  style?: React.CSSProperties
  containerRef?: React.RefObject<HTMLElement>
}

// Enhanced helper function to get icon, label, and styling for result type
function getResultTypeInfo(resultType?: string) {
  switch (resultType) {
    case 'tool':
      return {
        icon: Wrench,
        label: 'Tool',
        color: 'text-blue-600 dark:text-blue-400',
        bgColor: 'bg-blue-50 dark:bg-blue-900/30',
        borderColor: 'border-blue-200 dark:border-blue-800',
        emoji: '🔧'
      }
    case 'category':
      return {
        icon: Folder,
        label: 'Category',
        color: 'text-green-600 dark:text-green-400',
        bgColor: 'bg-green-50 dark:bg-green-900/30',
        borderColor: 'border-green-200 dark:border-green-800',
        emoji: '📁'
      }
    case 'subcategory':
      return {
        icon: Layers,
        label: 'Subcategory',
        color: 'text-purple-600 dark:text-purple-400',
        bgColor: 'bg-purple-50 dark:bg-purple-900/30',
        borderColor: 'border-purple-200 dark:border-purple-800',
        emoji: '📂'
      }
    case 'article':
    case 'blog':
      return {
        icon: BookOpen,
        label: 'Article',
        color: 'text-orange-600 dark:text-orange-400',
        bgColor: 'bg-orange-50 dark:bg-orange-900/30',
        borderColor: 'border-orange-200 dark:border-orange-800',
        emoji: '📖'
      }
    case 'company':
      return {
        icon: Building2,
        label: 'Company',
        color: 'text-indigo-600 dark:text-indigo-400',
        bgColor: 'bg-indigo-50 dark:bg-indigo-900/30',
        borderColor: 'border-indigo-200 dark:border-indigo-800',
        emoji: '🏢'
      }
    case 'feature':
      return {
        icon: Sparkles,
        label: 'Feature',
        color: 'text-pink-600 dark:text-pink-400',
        bgColor: 'bg-pink-50 dark:bg-pink-900/30',
        borderColor: 'border-pink-200 dark:border-pink-800',
        emoji: '✨'
      }
    default:
      return {
        icon: Wrench,
        label: 'Tool',
        color: 'text-blue-600 dark:text-blue-400',
        bgColor: 'bg-blue-50 dark:bg-blue-900/30',
        borderColor: 'border-blue-200 dark:border-blue-800',
        emoji: '🔧'
      }
  }
}

export default function InstantResults({
  results,
  isLoading,
  isOpen,
  selectedIndex,
  query,
  onResultSelect,
  onViewAll,
  className,
  style,
  containerRef
}: InstantResultsProps) {
  const router = useRouter()
  const [mounted, setMounted] = useState(false)
  const [portalContainer, setPortalContainer] = useState<HTMLElement | null>(null)
  const [position, setPosition] = useState({ top: 0, left: 0, width: 0 })

  useEffect(() => {
    setMounted(true)

    // Create or get portal container
    let container = document.getElementById('instant-results-portal')
    if (!container) {
      container = document.createElement('div')
      container.id = 'instant-results-portal'
      container.style.position = 'fixed'
      container.style.top = '0'
      container.style.left = '0'
      container.style.zIndex = '999999'
      container.style.pointerEvents = 'none'
      document.body.appendChild(container)
    }
    setPortalContainer(container)

    return () => {
      // Clean up portal container when component unmounts
      const container = document.getElementById('instant-results-portal')
      if (container && container.children.length === 0) {
        document.body.removeChild(container)
      }
    }
  }, [])

  useEffect(() => {
    if (containerRef?.current && isOpen) {
      const rect = containerRef.current.getBoundingClientRect()
      const viewportHeight = window.innerHeight
      const viewportWidth = window.innerWidth

      // Calculate position with viewport constraints
      let top = rect.bottom + window.scrollY
      let left = rect.left + window.scrollX
      let width = rect.width

      // Adjust for small screens
      if (viewportWidth < 640) { // sm breakpoint
        // On mobile, use more of the screen width and adjust positioning
        const margin = 16
        left = margin
        width = viewportWidth - (margin * 2)

        // Ensure dropdown doesn't go below viewport
        const maxHeight = Math.min(viewportHeight * 0.6, 400)
        if (top + maxHeight > viewportHeight + window.scrollY) {
          top = rect.top + window.scrollY - maxHeight - 8
        }
      }

      setPosition({ top, left, width })
    }
  }, [containerRef, isOpen])

  // Enhanced mobile scroll handling
  useEffect(() => {
    if (!isOpen) return

    let isScrolling = false

    const handleTouchStart = (e: TouchEvent) => {
      const target = e.target as HTMLElement
      const resultsContainer = target.closest('[data-instant-results]')

      if (resultsContainer) {
        isScrolling = true
      }
    }

    const handleTouchMove = (e: TouchEvent) => {
      const target = e.target as HTMLElement
      const resultsContainer = target.closest('[data-instant-results]')

      if (resultsContainer && isScrolling) {
        // Allow scrolling within the results container
        e.stopPropagation()

        // Check if we're at the top or bottom of the scroll container
        const scrollTop = resultsContainer.scrollTop
        const scrollHeight = resultsContainer.scrollHeight
        const clientHeight = resultsContainer.clientHeight

        // Prevent overscroll
        if ((scrollTop === 0 && e.touches[0].clientY > e.touches[0].clientY) ||
            (scrollTop + clientHeight >= scrollHeight && e.touches[0].clientY < e.touches[0].clientY)) {
          e.preventDefault()
        }
      }
    }

    const handleTouchEnd = () => {
      isScrolling = false
    }

    // Add event listeners
    document.addEventListener('touchstart', handleTouchStart, { passive: true })
    document.addEventListener('touchmove', handleTouchMove, { passive: false })
    document.addEventListener('touchend', handleTouchEnd, { passive: true })

    return () => {
      document.removeEventListener('touchstart', handleTouchStart)
      document.removeEventListener('touchmove', handleTouchMove)
      document.removeEventListener('touchend', handleTouchEnd)
    }
  }, [isOpen])

  if (!mounted || !isOpen || !query || query.trim().length === 0) return null

  // Show loading skeleton
  if (isLoading) {
    return portalContainer ? createPortal(
      <div
        className="fixed"
        style={{
          top: position.top,
          left: position.left,
          width: position.width,
          zIndex: 999999,
          pointerEvents: 'auto'
        }}
      >
        <SearchLoadingSkeleton className={className} itemCount={3} />
      </div>,
      portalContainer
    ) : null
  }

  const resultsContent = (
    <div
      className="fixed"
      style={{
        top: position.top,
        left: position.left,
        width: position.width,
        zIndex: 999999,
        pointerEvents: 'auto'
      }}
    >
      <Card
        className="max-h-[60vh] sm:max-h-80 lg:max-h-96 overflow-hidden shadow-2xl border border-border/50 bg-background/98 backdrop-blur-xl rounded-xl animate-in fade-in-0 zoom-in-95 duration-200"
        style={{
          boxShadow: '0 25px 50px -12px rgba(0, 0, 0, 0.25), 0 0 0 1px rgba(0, 0, 0, 0.05)',
          // Prevent body scroll on mobile when scrolling inside
          touchAction: 'manipulation'
        }}
      >
      <CardContent className="p-0">

        {/* Results */}
        {results.length > 0 && (
          <>
            <div className="px-4 py-2 text-xs font-medium text-muted-foreground bg-muted/20 border-b border-border/30">
              Found {results.length} result{results.length !== 1 ? 's' : ''}
            </div>
            <div
              data-instant-results="true"
              className="max-h-[40vh] sm:max-h-48 lg:max-h-64 overflow-y-auto scrollbar-thin scrollbar-thumb-muted scrollbar-track-transparent"
              style={{
                // Enable smooth scrolling on mobile
                WebkitOverflowScrolling: 'touch',
                // Prevent body scroll when scrolling inside this container
                touchAction: 'pan-y',
                // Ensure scrolling works on mobile
                overscrollBehavior: 'contain',
                // Better mobile scrolling
                scrollBehavior: 'smooth'
              }}
              onTouchStart={(e) => {
                // Prevent event bubbling to avoid interfering with body scroll
                e.stopPropagation()
              }}
              onTouchMove={(e) => {
                // Allow scrolling within this container
                e.stopPropagation()
              }}
              onWheel={(e) => {
                // Prevent wheel events from bubbling to body
                e.stopPropagation()
              }}
            >
              {results.map((result, index) => {
                const typeInfo = getResultTypeInfo(result.result_type)
                const TypeIcon = typeInfo.icon
                
                return (
                  <button
                    key={result.id}
                    onMouseDown={() => onResultSelect(result)}
                    className={cn(
                      "w-full flex items-center gap-3 px-4 py-3 text-left hover:bg-muted/50 transition-all duration-150 border-b border-border/20 last:border-b-0 focus:outline-none focus:bg-muted/50 focus:ring-2 focus:ring-primary/20 focus:ring-inset cursor-pointer",
                      selectedIndex === index && "bg-muted/50"
                    )}
                  >
                    {/* Logo/Icon with Type Indicator */}
                    <div className="relative w-10 h-10 rounded-lg bg-gradient-to-br from-slate-100 to-slate-200 dark:from-slate-800 dark:to-slate-700 flex items-center justify-center flex-shrink-0 shadow-sm">
                      {result.logo_url ? (
                        <img
                          src={result.logo_url}
                          alt={result.company_name || 'Tool'}
                          className="w-8 h-8 rounded-md object-cover shadow-sm"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.style.display = 'none'
                            target.nextElementSibling?.classList.remove('hidden')
                          }}
                        />
                      ) : null}
                      <TypeIcon className={cn("h-5 w-5", typeInfo.color, result.logo_url && "hidden")} />

                      {/* Type indicator badge */}
                      <div className={cn(
                        "absolute -top-1 -right-1 w-5 h-5 rounded-full flex items-center justify-center text-[10px] font-medium shadow-sm border-2 border-white dark:border-slate-900",
                        typeInfo.bgColor,
                        typeInfo.borderColor
                      )}>
                        <span className="text-[8px]">{typeInfo.emoji}</span>
                      </div>
                    </div>

                    {/* Content */}
                    <div className="flex-1 min-w-0">
                      <div className="flex items-center gap-2">
                        <h4 className="font-medium text-sm truncate">
                          {result.company_name || 'Unnamed Tool'}
                        </h4>
                        
                        {/* Enhanced Result type badge */}
                        <span className={cn(
                          "inline-flex items-center gap-1 px-2 py-0.5 rounded-full text-[9px] font-medium border",
                          typeInfo.bgColor,
                          typeInfo.color,
                          typeInfo.borderColor
                        )}>
                          <span className="text-[8px]">{typeInfo.emoji}</span>
                          {typeInfo.label}
                        </span>
                        
                        {result.is_verified && (
                          <Badge variant="outline" className="h-5 px-1 bg-blue-500/10 text-blue-600 border-blue-200 dark:border-blue-900 dark:bg-blue-500/20">
                            <Star className="h-3 w-3 mr-0.5 fill-current" />
                            <span className="text-[10px] font-medium">Verified</span>
                          </Badge>
                        )}
                      </div>
                      <p className="text-xs text-muted-foreground line-clamp-1">
                        {result.short_description || 'No description available'}
                      </p>
                      
                      {/* Enhanced additional info in one row */}
                      <div className="flex items-center mt-1 gap-1.5 flex-wrap">
                        {/* Category */}
                        {result.primary_task && (
                          <span className="inline-flex items-center gap-1 px-2 py-0.5 rounded-md text-[10px] font-medium bg-slate-50 text-slate-700 dark:bg-slate-800/50 dark:text-slate-300 border border-slate-200 dark:border-slate-700">
                            <Folder className="h-2.5 w-2.5" />
                            {result.primary_task}
                          </span>
                        )}

                        {/* Pricing with enhanced styling */}
                        {result.pricing && (
                          <span className={cn(
                            "inline-flex items-center gap-1 px-2 py-0.5 rounded-md text-[10px] font-medium border",
                            result.pricing.toLowerCase().includes('free')
                              ? "bg-emerald-50 text-emerald-700 border-emerald-200 dark:bg-emerald-900/30 dark:text-emerald-300 dark:border-emerald-800"
                              : "bg-amber-50 text-amber-700 border-amber-200 dark:bg-amber-900/30 dark:text-amber-300 dark:border-amber-800"
                          )}>
                            <span className="text-[8px]">
                              {result.pricing.toLowerCase().includes('free') ? '🆓' : '💰'}
                            </span>
                            {result.pricing}
                          </span>
                        )}

                        {/* View count with enhanced styling */}
                        {result.click_count && result.click_count > 0 && (
                          <span className="inline-flex items-center gap-1 text-[10px] text-muted-foreground bg-slate-50 dark:bg-slate-800/50 px-2 py-0.5 rounded-md border border-slate-200 dark:border-slate-700">
                            <Clock className="h-2.5 w-2.5" />
                            {result.click_count.toLocaleString()}
                          </span>
                        )}
                      </div>
                    </div>

                    <ArrowRight className="h-4 w-4 text-muted-foreground" />
                  </button>
                )
              })}
            </div>

            {/* View All Button */}
            <div className="p-3 border-t border-border/30 bg-muted/10">
              <Button
                variant="ghost"
                size="sm"
                className="w-full text-xs justify-between hover:bg-primary/10 hover:text-primary transition-colors"
                onClick={onViewAll}
              >
                View all results for "{query}"
                <ArrowRight className="h-3 w-3 ml-1" />
              </Button>
            </div>
          </>
        )}

        {/* No Results - only show when user has typed something */}
        {results.length === 0 && query && query.trim().length > 0 && (
          <div className="py-6 px-4 text-center">
            <div className="mb-3 flex justify-center">
              <div className="rounded-full bg-muted/50 p-3">
                <Wrench className="h-5 w-5 text-muted-foreground" />
              </div>
            </div>
            <h3 className="font-medium mb-1 text-sm">No results found</h3>
            <p className="text-xs text-muted-foreground mb-4">
              No tools found for "{query.length > 20 ? query.substring(0, 20) + '...' : query}"
            </p>
            <Button
              variant="outline"
              size="sm"
              className="text-xs hover:bg-primary/10 hover:text-primary hover:border-primary/50"
              onClick={onViewAll}
            >
              Browse all tools
              <ArrowRight className="h-3 w-3 ml-1" />
            </Button>
          </div>
        )}
      </CardContent>
    </Card>
    </div>
  )

  return portalContainer ? createPortal(resultsContent, portalContainer) : null
}
