"use client"

import { useState, useEffect, useCallback } from "react"
import Link from "next/link"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { ArrowRight, Star, TrendingUp, Grid3X3, X } from "lucide-react"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { motion } from "@/lib/motion-stub"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

import { toast } from "sonner"
import { useDebounce } from '@/hooks/use-debounce'
import { useRef } from "react"

import { useProgressiveEnhancement } from "@/hooks/use-progressive-enhancement"
import { StaggerContainer, StaggerItem } from "@/components/ui/micro-animations"
import UniversalSearch from "@/components/search/universal-search"

interface Tool {
  id: string
  name: string
  slug: string
  description?: string
  logo_url?: string
  pricing?: string
  featured?: boolean
  verified?: boolean
}

interface Category {
  id: string
  name: string
  icon?: React.ElementType
  count: number
  color: string
  tools: Tool[]
}

interface CategoriesPageClientProps {
  initialSearch?: string
}

// Define available category colors
const categoryColors = [
  "bg-blue-500",
  "bg-pink-500",
  "bg-purple-500",
  "bg-amber-500",
  "bg-emerald-500",
  "bg-violet-500",
  "bg-red-500",
  "bg-sky-500",
  "bg-teal-500",
  "bg-fuchsia-500",
  "bg-indigo-500",
  "bg-rose-500",
]

export default function CategoriesPageClient({
  initialSearch
}: CategoriesPageClientProps) {
  const router = useRouter()
  const [categories, setCategories] = useState<Category[]>([])
  const [allCategories, setAllCategories] = useState<Category[]>([]) // Store all categories for filtering
  const [displayCount] = useState(48) // Increased to 48 categories for more content
  const [isLoading, setIsLoading] = useState(true)
  const [searchQuery, setSearchQuery] = useState(initialSearch || '')
  const [showMore, setShowMore] = useState(false)
  const [totalToolsCount, setTotalToolsCount] = useState(0) // Store actual total tools count
  const [showSearchHint, setShowSearchHint] = useState(false)


  // Refs for keyboard shortcuts
  const searchInputRef = useRef<HTMLInputElement>(null)

  // Progressive enhancement
  const { reducedMotion } = useProgressiveEnhancement()

  // Debounced search term for better performance
  const debouncedSearchQuery = useDebounce(searchQuery, 300)

  // Handle instant filtering for the universal search
  const handleInstantFilter = useCallback((query: string, filters?: any) => {
    setSearchQuery(query)
    // The existing useEffect will handle the actual filtering
  }, [])

  // Show search hint when user starts typing
  useEffect(() => {
    if (searchQuery.length > 0) {
      setShowSearchHint(true)
      // Hide hint after 5 seconds
      const timer = setTimeout(() => setShowSearchHint(false), 5000)
      return () => clearTimeout(timer)
    } else {
      setShowSearchHint(false)
    }
  }, [searchQuery])

  // Keyboard Shortcuts
  useEffect(() => {
    const handleKeyDown = (e: KeyboardEvent) => {
      // Only trigger shortcuts when not typing in an input
      if (e.target instanceof HTMLInputElement || e.target instanceof HTMLTextAreaElement) {
        return
      }

      if (e.ctrlKey || e.metaKey) {
        switch (e.key) {
          case 'k': // Ctrl+K for search
            e.preventDefault()
            searchInputRef.current?.focus()
            toast.success("Search focused! Start typing...")
            break
        }
      }

      // Escape key to clear search
      if (e.key === 'Escape') {
        if (searchQuery) {
          setSearchQuery('')
          toast.success("Search cleared!")
        }
      }
    }

    document.addEventListener('keydown', handleKeyDown)
    return () => document.removeEventListener('keydown', handleKeyDown)
  }, [searchQuery])

  // Track if we're on the client side
  const [isClient, setIsClient] = useState(false)

  // First useEffect just to mark when we're client-side
  useEffect(() => {
    setIsClient(true)
  }, [])

  // Fetch categories and their tools with caching
  useEffect(() => {
    // Skip if not on client side yet
    if (!isClient) return;

    // Check if we have cached data (with expiration check)
    const getCachedData = () => {
      try {
        const cachedData = sessionStorage.getItem('categories-page-data')
        const cacheTimestamp = sessionStorage.getItem('categories-page-timestamp')

        if (cachedData && cacheTimestamp) {
          const cacheAge = Date.now() - parseInt(cacheTimestamp)
          // Cache expires after 5 minutes
          if (cacheAge < 5 * 60 * 1000) {
            return JSON.parse(cachedData)
          } else {
            // Clear expired cache
            sessionStorage.removeItem('categories-page-data')
            sessionStorage.removeItem('categories-page-timestamp')
          }
        }
      } catch (e) {
        console.warn("Failed to read from cache:", e)
      }
      return null
    }

    const fetchCategoriesAndTools = async () => {
      try {
        // Check for cached data first
        const cachedCategories = getCachedData()
        if (cachedCategories) {
          setCategories(cachedCategories)
          setIsLoading(false)

          // Still fetch fresh data in the background
          setTimeout(() => fetchFreshData(false), 1000)
          return
        }

        await fetchFreshData(true)
      } catch (error) {
        console.error("Error in fetchCategoriesAndTools:", error)
        setIsLoading(false)
        toast.error("Failed to load categories")
      }
    }

    const fetchFreshData = async (showLoading = true) => {
      try {
        if (showLoading) {
          setIsLoading(true)
        }

        const supabase = createBrowserClient()
        if (!supabase) {
          throw new Error("Failed to initialize Supabase client")
        }

        // First get the total count of all tools
        const { count: totalTools, error: totalCountError } = await supabase
          .from('tools')
          .select('*', { count: 'exact', head: true })

        if (totalCountError) {
          console.error("Error fetching total tools count:", totalCountError)
        } else {
          setTotalToolsCount(totalTools || 0)
        }

        // Then get all distinct primary tasks (categories)
        const { data: taskCounts, error: distinctError } = await supabase
          .from('tools')
          .select('primary_task, id')
          .not('primary_task', 'is', null)

        if (distinctError) {
          console.error("Error fetching distinct tasks:", distinctError)
          throw distinctError
        }

        // Process the data to count occurrences of each primary task
        const taskCountMap: Record<string, number> = {}
        if (taskCounts && Array.isArray(taskCounts)) {
          taskCounts.forEach(item => {
            if (item.primary_task) {
              taskCountMap[item.primary_task] = (taskCountMap[item.primary_task] || 0) + 1
            }
          })
        }

        // Convert to the expected format
        const formattedTaskCounts = Object.entries(taskCountMap).map(([primary_task, count]) => ({
          primary_task,
          count
        })).sort((a, b) => b.count - a.count)

        // Handle special categories first
        const specialCategories = [
          {
            id: "top-trends",
            name: "Top 50 Trends [24H]",
            count: 0,
            tools: [] as Tool[],
            color: categoryColors[11],
          },
          {
            id: "latest",
            name: "Latest AI",
            count: 0,
            tools: [] as Tool[],
            color: categoryColors[10],
          }
        ]

        // Create a set to track unique category IDs
        const uniqueCategoryIds = new Set<string>()

        // Transform categories and count tools for each
        const regularCategories: Category[] = []

        // Process each distinct primary task
        if (formattedTaskCounts && Array.isArray(formattedTaskCounts)) {
          for (let i = 0; i < formattedTaskCounts.length; i++) {
            const task = formattedTaskCounts[i]
            const taskName = task.primary_task
            if (!taskName) continue

            const categoryId = taskName.toLowerCase().replace(/\s+/g, '-')

            // Check for duplicates
            if (uniqueCategoryIds.has(categoryId)) {
              continue
            }

            uniqueCategoryIds.add(categoryId)

            regularCategories.push({
              id: categoryId,
              name: taskName,
              count: typeof task.count === 'number' ? task.count : 0,
              tools: [] as Tool[],
              color: categoryColors[i % categoryColors.length],
            })
          }
        }

        // Merge special and regular categories - special categories will come first
        const allCategories = [...specialCategories, ...regularCategories]

        // Now fetch tools for each category
        const categoriesWithToolsPromises = allCategories.map(async (category) => {
          let query = supabase.from("tools").select("id, company_name, slug, logo_url, short_description, pricing, is_featured, is_verified").limit(12) // 12 tools per category

          if (category.id === "latest") {
            // For "Latest AI" category, get the most recently added tools
            query = query.order("created_at", { ascending: false })
          } else if (category.id === "top-trends") {
            // For "Top 50 Trends" category, get most viewed/rated tools
            query = query.order("click_count", { ascending: false })
          } else {
            // For regular categories, filter by primary_task
            query = query.eq("primary_task", category.name)
          }

          const { data: toolsData, error: toolsError } = await query

          if (toolsError) {
            console.error(`Error fetching tools for ${category.name}:`, toolsError)
            return { ...category, tools: [] as Tool[], count: 0 }
          }

          // Map the tools data to match our Tool interface
          const mappedTools: Tool[] = (toolsData || []).map(tool => ({
            id: String(tool.id),
            name: tool.company_name || "",
            slug: tool.slug || "",
            logo_url: tool.logo_url || "",
            description: tool.short_description || "",
            pricing: tool.pricing || "",
            featured: Boolean(tool.is_featured),
            verified: Boolean(tool.is_verified)
          }))

          return {
            ...category,
            tools: mappedTools,
            count: category.count || mappedTools.length
          }
        })

        // Wait for all tools to be fetched
        const categoriesWithTools = await Promise.all(categoriesWithToolsPromises)

        // Filter out categories with no tools
        const finalCategories = categoriesWithTools.filter(cat => cat.count > 0)

        // Cache the results with timestamp
        try {
          sessionStorage.setItem('categories-page-data', JSON.stringify(finalCategories))
          sessionStorage.setItem('categories-page-timestamp', Date.now().toString())
        } catch (e) {
          console.warn("Failed to cache categories:", e)
        }

        setAllCategories(finalCategories) // Store all categories
        setCategories(finalCategories)

        // Calculate and log stats immediately
        const calculatedTotalTools = finalCategories.reduce((sum, cat) => {
          const count = typeof cat.count === 'number' && cat.count > 0 ? cat.count : 0
          return sum + count
        }, 0)

        // Debug logging for stats
        if (process.env.NODE_ENV === 'development') {
          console.log('Final categories loaded:', {
            count: finalCategories.length,
            totalTools: calculatedTotalTools,
            sampleCategories: finalCategories.slice(0, 3).map(cat => ({ name: cat.name, count: cat.count })),
            allCategories: finalCategories.map(cat => ({ name: cat.name, count: cat.count }))
          })
        }




      } catch (error) {
        console.error("Error fetching categories with tools:", error)
        if (showLoading) {
          toast.error("Failed to load categories")
        }
      } finally {
        setIsLoading(false)
      }
    }

    fetchCategoriesAndTools()
  }, [isClient])



  // Enhanced search effect with debouncing
  useEffect(() => {
    if (!allCategories.length) return

    if (!debouncedSearchQuery.trim()) {
      // If no search query, show all categories
      setCategories(allCategories)
      setShowMore(false)
      return
    }

    // Enhanced search logic - search in category name and tools
    const searchTerm = debouncedSearchQuery.toLowerCase().trim()
    const filtered = allCategories.filter(category => {
      // Search in category name
      const nameMatch = category.name.toLowerCase().includes(searchTerm)

      // Search in tools within the category
      const toolsMatch = category.tools.some(tool =>
        tool.name.toLowerCase().includes(searchTerm) ||
        (tool.description && tool.description.toLowerCase().includes(searchTerm))
      )

      return nameMatch || toolsMatch
    })

    setCategories(filtered)
    setShowMore(false) // Reset show more when searching
  }, [debouncedSearchQuery, allCategories])

  // Filter categories based on search (for display)
  const filteredCategories = categories

  // Get category stats - with better fallbacks and validation
  const totalCategories = Math.max(allCategories.length, categories.length, 0)
  const categoriesToCount = allCategories.length > 0 ? allCategories : categories
  // Use actual total tools count from database, fallback to category sum if not available
  const totalTools = totalToolsCount > 0 ? totalToolsCount : categoriesToCount.reduce((sum, cat) => {
    const count = typeof cat.count === 'number' && cat.count > 0 ? cat.count : 0
    return sum + count
  }, 0)

  // Debug logging for stats calculation
  if (process.env.NODE_ENV === 'development') {
    console.log('Stats calculation:', {
      allCategoriesLength: allCategories.length,
      categoriesLength: categories.length,
      totalCategories,
      totalTools,
      totalToolsCount,
      categoriesToCount: categoriesToCount.slice(0, 5).map(cat => ({ name: cat.name, count: cat.count }))
    })
  }



  // Track if data has been loaded to prevent unnecessary re-renders
  const [hasTriggeredRerender, setHasTriggeredRerender] = useState(false)

  useEffect(() => {
    if (allCategories.length > 0 && (totalTools > 0 || totalToolsCount > 0) && !hasTriggeredRerender) {
      setHasTriggeredRerender(true)

      // Debug log when data is loaded
      if (process.env.NODE_ENV === 'development') {
        console.log('Data loaded successfully:', {
          allCategoriesLength: allCategories.length,
          totalTools,
          totalToolsCount
        })
      }
    }
  }, [allCategories.length, totalTools, totalToolsCount, hasTriggeredRerender])



  // Debug logging in development (only when data changes significantly)
  useEffect(() => {
    if (process.env.NODE_ENV === 'development' && allCategories.length > 0) {
      console.log('Categories stats (final):', {
        allCategoriesLength: allCategories.length,
        categoriesLength: categories.length,
        totalCategories,
        totalTools,
        totalToolsCount,
        isLoading
      })
    }
  }, [allCategories.length, totalToolsCount, isLoading])

  return (
    <div className="min-h-screen bg-background">
      {/* Hero Section - Minimal */}
      <section className="relative pt-16 pb-4">
        <div className="container mx-auto px-4">
          <div className="text-center mb-3">
            <motion.h1
              className="text-2xl md:text-3xl font-bold mb-2 text-gradient"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6 }}
            >
              Browse Categories
            </motion.h1>
            <motion.p
              key={`hero-text-${isLoading ? 'loading' : 'loaded'}`}
              className="text-sm md:text-base text-muted-foreground max-w-2xl mx-auto"
              initial={{ opacity: 0, y: 20 }}
              animate={{ opacity: 1, y: 0 }}
              transition={{ duration: 0.6, delay: 0.1 }}
            >
              {(() => {
                // Always use the most up-to-date data
                const currentCategories = allCategories.length > 0 ? allCategories : categories
                const realCategories = currentCategories.length
                // Use the actual total tools count from database instead of summing category counts
                const realTools = totalToolsCount > 0 ? totalToolsCount : currentCategories.reduce((sum, cat) => {
                  const count = typeof cat.count === 'number' && cat.count > 0 ? cat.count : 0
                  return sum + count
                }, 0)

                // Debug logging to see what's happening
                if (process.env.NODE_ENV === 'development') {
                  console.log('Hero text calculation:', {
                    allCategoriesLength: allCategories.length,
                    categoriesLength: categories.length,
                    realCategories,
                    realTools,
                    totalToolsCount,
                    isLoading,
                    currentCategories: currentCategories.slice(0, 3).map(cat => ({ name: cat.name, count: cat.count }))
                  })
                }

                if (isLoading) {
                  return "Loading categories and tools..."
                }

                if (realCategories > 0 && realTools > 0) {
                  return `Explore ${realCategories} categories with ${realTools.toLocaleString()}+ AI tools. Find the perfect solution for your specific needs.`
                }

                if (realCategories > 0) {
                  return `Explore ${realCategories} categories of AI tools. Find the perfect solution for your specific needs.`
                }

                return "Discover AI tools organized by categories. Find the perfect solution for your specific needs."
              })()}
            </motion.p>
          </div>

          {/* Search Bar - Universal Search Component */}
          <motion.div
            className="max-w-2xl mx-auto"
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.6, delay: 0.2 }}
          >
            <div className="relative group w-full z-30">
              {/* Gradient Background Effect */}
              <div className="absolute inset-0 bg-gradient-to-r from-primary/20 via-purple-500/20 to-primary/20 rounded-2xl blur-xl opacity-30 group-hover:opacity-50 transition-opacity duration-500"></div>

              {/* Main Search Container */}
              <div className="relative bg-white/90 dark:bg-slate-900/90 backdrop-blur-xl rounded-2xl shadow-2xl border border-white/20 dark:border-slate-700/30 z-30">
                <div className="flex items-center p-3">
                  <div className="flex-1">
                    <UniversalSearch
                      mode="instant-filter"
                      context="categories"
                      variant="hero"
                      size="lg"
                      placeholder="Search categories..."
                      className="w-full"
                      rounded="full"
                      glass={false}
                      showKeyboardShortcut={false}
                      fullWidth={true}
                      showInstantResults={false}
                      maxResults={8}
                      autoFocus={false}
                      showSearchButton={false}
                      initialValue={searchQuery}
                      onInstantFilter={handleInstantFilter}
                      onSearch={(query) => {
                        if (query.trim()) {
                          router.push(`/search?q=${encodeURIComponent(query.trim())}`)
                        }
                      }}
                    />
                  </div>

                  {/* Search Button */}
                  <div className="flex-shrink-0 ml-3">
                    <button
                      type="button"
                      onClick={() => {
                        if (searchQuery?.trim()) {
                          router.push(`/search?q=${encodeURIComponent(searchQuery.trim())}`)
                        }
                      }}
                      className="h-10 px-4 bg-gradient-to-r from-primary to-primary/90 hover:from-primary/90 hover:to-primary active:from-primary/80 active:to-primary/80 text-white font-semibold transition-all duration-200 hover:shadow-lg hover:scale-105 active:scale-95 flex items-center gap-2 rounded-full touch-manipulation"
                    >
                      <svg className="h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path strokeLinecap="round" strokeLinejoin="round" strokeWidth={2} d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z" />
                      </svg>
                      <span className="hidden sm:inline">Search</span>
                    </button>
                  </div>
                </div>
              </div>
            </div>

            {/* Search Hint - Mobile Friendly */}
            <motion.div
              className="mt-3 text-center"
              initial={{ opacity: 0 }}
              animate={{ opacity: 1 }}
              transition={{ duration: 0.4, delay: 0.4 }}
            >
              <div className="inline-flex items-center gap-2 px-3 py-2 bg-purple-50/80 dark:bg-purple-950/30 text-purple-700 dark:text-purple-300 rounded-full text-xs sm:text-sm border border-purple-200/50 dark:border-purple-800/50 backdrop-blur-sm">
                <div className="flex items-center gap-1">
                  <Grid3X3 className="h-3 w-3 sm:h-4 sm:w-4" />
                  <span className="font-medium">Tip:</span>
                </div>
                <span className="hidden sm:inline">Type to filter categories instantly or press Enter for full search</span>
                <span className="sm:hidden">Type to filter or press Enter</span>
                <div className="hidden sm:flex items-center gap-1 ml-1">
                  <span className="text-purple-600 dark:text-purple-400">•</span>
                  <span>Results update as you type</span>
                </div>
              </div>
            </motion.div>
          </motion.div>
        </div>
      </section>

      {/* Dynamic Search Hint - Shows when user is searching */}
      {showSearchHint && searchQuery.length > 0 && (
        <motion.div
          initial={{ opacity: 0, y: -10 }}
          animate={{ opacity: 1, y: 0 }}
          exit={{ opacity: 0, y: -10 }}
          className="fixed top-16 sm:top-20 left-0 right-0 z-40 bg-gradient-to-r from-purple-500/95 to-pink-500/95 backdrop-blur-xl text-white py-3 px-4 shadow-xl border-b border-white/20"
        >
          <div className="container mx-auto px-4 text-center">
            <div className="flex items-center justify-center gap-2 text-sm sm:text-base">
              <Grid3X3 className="h-4 w-4 flex-shrink-0" />
              <span className="font-medium truncate">
                Searching for "{searchQuery}" - Check categories below
              </span>
              <button
                onClick={() => setShowSearchHint(false)}
                className="ml-2 hover:bg-white/20 rounded-full p-1 transition-colors flex-shrink-0"
                aria-label="Close hint"
              >
                <X className="h-3 w-3" />
              </button>
            </div>
          </div>
        </motion.div>
      )}

      {/* Categories Section */}
      <section className="pt-2 pb-6 bg-secondary/20 dark:bg-secondary/5 theme-transition">
        <div className="container mx-auto px-4">
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ duration: 0.5 }}
          >
            <div className="flex flex-col sm:flex-row justify-between items-start sm:items-center mb-3">
              <div>
                <h2 className="text-2xl md:text-3xl font-bold text-gradient">
                  {debouncedSearchQuery ? 'Search Results' : 'All Categories'}
                </h2>
                <p
                  key={`${totalCategories}-${filteredCategories.length}-${debouncedSearchQuery}`}
                  className="mt-2 text-sm md:text-base text-muted-foreground"
                >
                  {debouncedSearchQuery ? (
                    `Found ${filteredCategories.length} categories matching "${debouncedSearchQuery}"`
                  ) : totalCategories > 0 ? (
                    `Discover AI tools organized in ${totalCategories} specialized categories`
                  ) : (
                    "Discover AI tools organized by specialized categories"
                  )}
                </p>
              </div>

              <Link
                href="/tools"
                className="mt-3 sm:mt-0 inline-flex items-center gap-2 px-4 py-2 text-primary text-sm font-medium hover:text-primary/80 bg-primary/10 hover:bg-primary/20 rounded-lg transition-all duration-200 group"
              >
                <span>View all tools</span>
                <ArrowRight size={16} className="transition-transform group-hover:translate-x-1" />
              </Link>
            </div>
          </motion.div>

          {isLoading ? (
            <div className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4">
              {Array.from({ length: 12 }).map((_, index) => (
                <Card key={index} className="h-full border border-slate-200 dark:border-slate-800 overflow-hidden animate-pulse">
                  <CardContent className="p-4">
                    <div className="flex items-center justify-between mb-4">
                      <div className="h-5 w-24 bg-slate-200 dark:bg-slate-700 rounded"></div>
                      <div className="h-5 w-8 bg-slate-200 dark:bg-slate-700 rounded-full"></div>
                    </div>
                    <div className="space-y-2">
                      {Array.from({ length: 4 }).map((_, i) => (
                        <div key={i} className="flex items-center gap-2">
                          <div className="w-4 h-4 rounded-full bg-slate-200 dark:bg-slate-700"></div>
                          <div className="h-3 bg-slate-200 dark:bg-slate-700 rounded w-full"></div>
                        </div>
                      ))}
                    </div>
                  </CardContent>
                </Card>
              ))}
            </div>
          ) : filteredCategories.length === 0 ? (
            <div className="p-8 text-center">
              <div className="max-w-md mx-auto">
                <div className="w-16 h-16 mx-auto mb-4 rounded-full bg-muted/50 flex items-center justify-center">
                  <Grid3X3 className="w-8 h-8 text-muted-foreground" />
                </div>
                <h3 className="text-lg font-semibold text-foreground mb-2">No categories found</h3>
                <p className="text-muted-foreground mb-4">
                  {debouncedSearchQuery ? `No categories match your search for "${debouncedSearchQuery}". Try a different search term.` : 'No categories found'}
                </p>
                {debouncedSearchQuery && (
                  <Button
                    onClick={() => setSearchQuery('')}
                    variant="outline"
                    className="inline-flex items-center gap-2"
                  >
                    Clear Search
                    <ArrowRight className="w-4 h-4" />
                  </Button>
                )}
              </div>
            </div>
          ) : (
            <>
              <StaggerContainer
                className="grid grid-cols-1 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4 gap-4"
                disabled={reducedMotion}
              >
                {filteredCategories.slice(0, showMore ? filteredCategories.length : displayCount).map((category) => (
                  <StaggerItem key={category.id} disabled={reducedMotion}>
                    <CategoryCard category={category} />
                  </StaggerItem>
                ))}
              </StaggerContainer>

              {/* Show More Button - Only show when not searching */}
              {filteredCategories.length > displayCount && !showMore && !debouncedSearchQuery && (
                <div className="mt-4 text-center">
                  <Button
                    onClick={() => setShowMore(true)}
                    variant="outline"
                    size="lg"
                    className="inline-flex items-center gap-2 px-6 py-3 text-sm font-medium rounded-xl border-2 border-primary/20 bg-primary/5 hover:bg-primary/10 hover:border-primary/40 text-primary hover:text-primary transition-all duration-300"
                  >
                    <span>Show More Categories</span>
                    <span className="text-xs bg-primary/20 px-2 py-1 rounded-full">
                      +{filteredCategories.length - displayCount}
                    </span>
                  </Button>
                </div>
              )}
            </>
          )}

          <motion.div
            className="mt-4 text-center"
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            transition={{ delay: 0.3 }}
          >
            <Button
              asChild
              variant="outline"
              size="lg"
              className="inline-flex items-center gap-2 px-6 py-3 text-sm font-medium rounded-xl border-2 border-primary/20 bg-primary/5 hover:bg-primary/10 hover:border-primary/40 text-primary hover:text-primary transition-all duration-300 group shadow-sm hover:shadow-md"
            >
              <Link href="/tools" className="flex items-center gap-2">
                <Grid3X3 size={16} className="text-primary" />
                <span>Explore All Tools</span>
                <ArrowRight size={16} className="transition-transform group-hover:translate-x-1" />
              </Link>
            </Button>
          </motion.div>
        </div>
      </section>
    </div>
  )
}

// Helper function to get responsive text classes based on category name length
function getCategoryNameClasses(categoryName: string) {
  const length = categoryName.length

  if (length > 35) {
    return "text-xs leading-tight font-medium"
  } else if (length > 28) {
    return "text-xs sm:text-sm leading-tight font-semibold"
  } else if (length > 22) {
    return "text-sm leading-tight font-semibold"
  } else if (length > 18) {
    return "text-sm sm:text-base font-semibold"
  } else if (length > 14) {
    return "text-base font-semibold"
  } else {
    return "text-base sm:text-lg font-semibold"
  }
}

// Helper function to truncate category name intelligently with better word breaking
function getTruncatedName(categoryName: string) {
  // For very short names, return as is
  if (categoryName.length <= 20) {
    return categoryName
  }

  // For medium names, try smart truncation
  if (categoryName.length <= 35) {
    return categoryName
  }

  // For long names, use intelligent word breaking
  const words = categoryName.split(' ')
  let truncated = ''
  let wordCount = 0

  for (const word of words) {
    const testLength = truncated + (truncated ? ' ' : '') + word
    if (testLength.length > 28 && wordCount > 0) {
      break
    }
    truncated += (truncated ? ' ' : '') + word
    wordCount++
  }

  // If we couldn't fit even one word, force truncate
  if (wordCount === 0) {
    return `${categoryName.substring(0, 25)}...`
  }

  // If we truncated, add ellipsis
  return truncated.length < categoryName.length ? `${truncated}...` : truncated
}

// Category card component
function CategoryCard({ category }: { category: Category }) {
  if (!category) {
    console.error("Received null or undefined category")
    return null
  }

  return (
    <Card className="h-full glass-dark hover:shadow-lg dark:hover:shadow-2xl dark:hover:shadow-primary/10 theme-transition overflow-hidden hover:scale-105 group/card group-hover/title:shadow-xl group-hover/title:border-primary/30 group-hover/title:bg-primary/5 dark:group-hover/title:bg-primary/10">
      <CardContent className="p-4">
        {/* Header with category name, count, and view all button */}
        <div className="flex items-start justify-between mb-4 gap-2">
          <Link
            href={`/tools?category=${encodeURIComponent(category.name)}`}
            className="flex items-center gap-2 flex-1 min-w-0 group/title cursor-pointer relative overflow-hidden rounded-lg p-2 -m-2 hover:bg-primary/5 dark:hover:bg-primary/10 transition-all duration-200"
            title={`Click to view all ${category.name} tools`}
          >
            {/* Subtle background effect on hover */}
            <div className="absolute inset-0 bg-gradient-to-r from-primary/0 via-primary/5 to-primary/0 opacity-0 group-hover/title:opacity-100 transition-opacity duration-300"></div>

            <div className="relative flex items-start gap-2 min-w-0 w-full">
              <div className="flex-shrink-0 mt-0.5">
                {category.id === "latest" ? (
                  <Star size={14} className="text-primary group-hover/title:text-primary/80 group-hover/title:scale-110 transition-all duration-200" />
                ) : category.id === "top-trends" ? (
                  <TrendingUp size={14} className="text-primary group-hover/title:text-primary/80 group-hover/title:scale-110 transition-all duration-200" />
                ) : (
                  <span className={`w-3 h-3 rounded-full ${category.color} group-hover/title:scale-125 group-hover/title:shadow-lg transition-all duration-200 block`}></span>
                )}
              </div>
              <h3 className={`text-foreground group-hover/title:text-primary transition-colors duration-200 flex items-center gap-1 ${getCategoryNameClasses(category.name)}`}>
                <span
                  className="min-w-0 flex-1 leading-tight"
                  title={category.name.length > 20 ? category.name : undefined}
                  style={{
                    display: '-webkit-box',
                    WebkitLineClamp: category.name.length > 25 ? 2 : 1,
                    WebkitBoxOrient: 'vertical',
                    overflow: 'hidden',
                    wordBreak: 'break-word',
                    hyphens: 'auto'
                  }}
                >
                  {getTruncatedName(category.name)}
                </span>
                <ArrowRight
                  size={12}
                  className="opacity-0 group-hover/title:opacity-100 transition-all duration-200 transform translate-x-0 group-hover/title:translate-x-1 text-primary flex-shrink-0 mt-0.5"
                />
              </h3>
            </div>
          </Link>
          <div className="flex flex-col sm:flex-row items-end sm:items-center gap-1 sm:gap-2 flex-shrink-0">
            <Badge variant="outline" className="text-xs bg-secondary text-secondary-foreground border-border whitespace-nowrap">
              {category.count}
            </Badge>
            <Link
              href={`/tools?category=${encodeURIComponent(category.name)}`}
              className="inline-flex items-center gap-1 px-2 py-1 text-xs font-medium text-primary hover:text-primary/80 bg-primary/10 hover:bg-primary/20 rounded-md transition-all duration-200 group/btn whitespace-nowrap"
            >
              <span className="hidden sm:inline">View All</span>
              <span className="sm:hidden">All</span>
              <ArrowRight size={12} className="transition-transform group-hover/btn:translate-x-0.5" />
            </Link>
          </div>
        </div>

        {/* Tools list with individual clickable items */}
        <div className="space-y-1">
          {(category.tools || []).slice(0, 8).map((tool) => (
            <div key={tool.id} className="relative group/tool">
              <Link
                href={`/Tool/${tool.slug || tool.name.toLowerCase().replace(/\s+/g, '-')}`}
                className="block"
              >
                <div
                  className="flex items-center justify-between gap-3 p-2 rounded-lg hover:bg-muted/50 transition-all duration-200 border border-transparent hover:border-border/30 hover:shadow-sm"
                  title={tool.description || `${tool.name} - AI productivity tool`}
                >
                  {/* Left side: Logo and tool name */}
                  <div className="flex items-center gap-3 min-w-0 flex-1">
                    {tool.logo_url ? (
                      <div className="relative w-6 h-6 rounded-lg overflow-hidden flex-shrink-0 shadow-sm border border-border/20 bg-white dark:bg-gray-800">
                        <Image
                          src={tool.logo_url}
                          alt={tool.name}
                          fill
                          className="object-cover"
                          onError={(e) => {
                            const target = e.target as HTMLImageElement
                            target.style.display = 'none'
                          }}
                        />
                      </div>
                    ) : (
                      <div className="w-6 h-6 rounded-lg bg-gradient-to-br from-muted to-muted/70 flex-shrink-0 shadow-sm border border-border/20 flex items-center justify-center">
                        <div className="w-2.5 h-2.5 rounded bg-muted-foreground/20"></div>
                      </div>
                    )}
                    <div className="min-w-0 flex-1">
                      <div className="text-sm font-semibold text-foreground group-hover/tool:text-primary transition-colors truncate">
                        {tool.name.length > 20 ? `${tool.name.substring(0, 20)}...` : tool.name}
                      </div>
                    </div>
                  </div>

                  {/* Right side: Tool details and status */}
                  <div className="flex items-center gap-2 flex-shrink-0">
                    {tool.pricing && (
                      <span className={`text-xs px-2 py-1 rounded-lg font-medium shadow-sm border transition-all duration-200 hover:scale-105 ${
                        tool.pricing.toLowerCase().includes('free')
                          ? 'text-emerald-700 bg-emerald-50 border-emerald-200 dark:text-emerald-300 dark:bg-emerald-900/20 dark:border-emerald-800'
                          : 'text-amber-700 bg-amber-50 border-amber-200 dark:text-amber-300 dark:bg-amber-900/20 dark:border-amber-800'
                      }`}>
                        {tool.pricing.toLowerCase().includes('free') && (
                          <span className="inline-block w-1.5 h-1.5 bg-emerald-500 rounded-full animate-pulse mr-1"></span>
                        )}
                        {tool.pricing.toLowerCase().includes('free') ? 'Free' : 'Paid'}
                      </span>
                    )}

                    {/* Status indicators */}
                    <div className="flex items-center gap-1.5">
                      {tool.featured && (
                        <div className="relative">
                          <div className="w-2 h-2 bg-gradient-to-br from-amber-400 to-amber-500 rounded-full shadow-sm" title="Featured Tool"></div>
                          <div className="absolute inset-0 w-2 h-2 bg-amber-400 rounded-full animate-ping opacity-30"></div>
                        </div>
                      )}
                      {tool.verified && (
                        <div className="relative">
                          <div className="w-2 h-2 bg-gradient-to-br from-blue-500 to-blue-600 rounded-full shadow-sm" title="Verified Tool"></div>
                          <div className="absolute inset-0 w-2 h-2 bg-blue-500 rounded-full animate-ping opacity-30"></div>
                        </div>
                      )}
                    </div>

                    <div className="flex items-center justify-center w-6 h-6 rounded-lg bg-muted/30 group-hover/tool:bg-primary/10 transition-all duration-200 group-hover/tool:scale-110">
                      <ArrowRight size={12} className="text-muted-foreground group-hover/tool:text-primary transition-colors" />
                    </div>
                  </div>
                </div>
              </Link>

              {/* Tooltip for description */}
              {tool.description && (
                <div className="absolute left-0 top-full mt-2 w-64 p-3 bg-popover border border-border rounded-lg shadow-lg z-50 opacity-0 invisible group-hover/tool:opacity-100 group-hover/tool:visible transition-all duration-200 pointer-events-none">
                  <div className="text-xs text-popover-foreground leading-relaxed">
                    {tool.description}
                  </div>
                  <div className="absolute -top-1 left-4 w-2 h-2 bg-popover border-l border-t border-border rotate-45"></div>
                </div>
              )}
            </div>
          ))}
        </div>

        {/* Show indicator if there are more tools than shown */}
        {category.count > 8 && (
          <div className="pt-3 text-center border-t border-border/50 mt-3">
            <span className="text-xs text-muted-foreground">
              +{category.count - 8} more tools
            </span>
          </div>
        )}
      </CardContent>
    </Card>
  )
}