"use client"

import { useEffect } from "react"
import { useRouter } from "next/navigation"
import { useAuth } from "@/hooks/use-auth"
import { Loader2, Lock } from "lucide-react"
import { Button } from "@/components/ui/button"
import { Card, CardContent, CardDescription, CardHeader, CardTitle } from "@/components/ui/card"
import Link from "next/link"

interface RequireAuthProps {
  children: React.ReactNode
  fallbackMessage?: string
  redirectTo?: string
  showFallback?: boolean
}

export function RequireAuth({
  children,
  fallbackMessage = "You need to sign in to access this page",
  redirectTo = "/auth",
  showFallback = true
}: RequireAuthProps) {
  const { user, isLoading, isAuthenticated } = useAuth()
  const router = useRouter()

  useEffect(() => {
    // إذا انتهى التحميل ولا يوجد مستخدم، وجه إلى صفحة التوثيق
    if (!isLoading && !isAuthenticated && !showFallback) {
      const currentPath = window.location.pathname
      const redirectUrl = `${redirectTo}?redirect_to=${encodeURIComponent(currentPath)}`
      router.push(redirectUrl)
    }
  }, [isLoading, isAuthenticated, redirectTo, router, showFallback])

  // أثناء التحميل
  if (isLoading) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Checking authentication status...</p>
        </div>
      </div>
    )
  }

  // إذا لم يكن مسجل دخول وتريد إظهار رسالة
  if (!isAuthenticated && showFallback) {
    return (
      <div className="min-h-screen flex items-center justify-center p-4">
        <Card className="w-full max-w-md">
          <CardHeader className="text-center">
            <div className="mx-auto mb-4 flex h-12 w-12 items-center justify-center rounded-full bg-muted">
              <Lock className="h-6 w-6" />
            </div>
            <CardTitle>Sign In Required</CardTitle>
            <CardDescription>{fallbackMessage}</CardDescription>
          </CardHeader>
          <CardContent className="space-y-4">
            <Button asChild className="w-full">
              <Link href={`${redirectTo}?redirect_to=${encodeURIComponent(window.location.pathname)}`}>
                Sign In
              </Link>
            </Button>
            <Button variant="outline" asChild className="w-full">
              <Link href="/">Back to Home</Link>
            </Button>
          </CardContent>
        </Card>
      </div>
    )
  }

  // إذا لم يكن مسجل دخول ولا تريد إظهار رسالة (سيتم التوجيه تلقائياً)
  if (!isAuthenticated && !showFallback) {
    return (
      <div className="min-h-screen flex items-center justify-center">
        <div className="text-center">
          <Loader2 className="h-8 w-8 animate-spin mx-auto mb-4" />
          <p className="text-muted-foreground">Redirecting...</p>
        </div>
      </div>
    )
  }

  // إذا كان مسجل دخول، اعرض المحتوى
  return <>{children}</>
}

// مكون مبسط للاستخدام السريع
export function ProtectedPage({ children }: { children: React.ReactNode }) {
  return (
    <RequireAuth showFallback={false}>
      {children}
    </RequireAuth>
  )
}

// Hook للتحقق من التوثيق في المكونات
export function useRequireAuth(redirectTo: string = "/auth") {
  const { user, isLoading, isAuthenticated } = useAuth()
  const router = useRouter()

  useEffect(() => {
    if (!isLoading && !isAuthenticated) {
      const currentPath = window.location.pathname
      const redirectUrl = `${redirectTo}?redirect_to=${encodeURIComponent(currentPath)}`
      router.push(redirectUrl)
    }
  }, [isLoading, isAuthenticated, redirectTo, router])

  return { user, isLoading, isAuthenticated }
}
