"use client"

import { useState, useEffect } from "react"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { Heart } from "lucide-react"
import { toast } from "sonner"
import { createBrowserClient } from "@/lib/supabase/client-utils"
import { useRouter } from "next/navigation"

export default function FavoriteButton({ toolId }: { toolId: number }) {
  const [isFavorite, setIsFavorite] = useState(false)
  const [isLoading, setIsLoading] = useState(true)
  const [isAuthenticated, setIsAuthenticated] = useState(false)
  const router = useRouter()

  // Check if tool is in favorites on component mount
  useEffect(() => {
    const checkFavoriteStatus = async () => {
      try {
        const supabase = createBrowserClient()

        // Check if user is authenticated
        const { data: { session } } = await supabase.auth.getSession()

        if (!session) {
          setIsAuthenticated(false)
          setIsLoading(false)
          return
        }

        setIsAuthenticated(true)

        // Check if tool is in favorites
        const { data, error } = await supabase
          .from('favorites')
          .select('*')
          .eq('tool_id', toolId)
          .eq('user_id', session.user.id)
          .single()

        if (error && error.code !== 'PGRST116') {
          console.error("Error checking favorite status:", error)
          return
        }

        setIsFavorite(!!data)
      } catch (error) {
        console.error("Error checking favorite status:", error)
      } finally {
        setIsLoading(false)
      }
    }

    checkFavoriteStatus()
  }, [toolId])

  const toggleFavorite = async () => {
    try {
      setIsLoading(true)
      const supabase = createBrowserClient()

      // Check if user is authenticated
      const { data: { session } } = await supabase.auth.getSession()

      if (!session) {
        toast.error("Please sign in to add favorites")
        router.push('/auth')
        return
      }

      if (isFavorite) {
        // Remove from favorites
        const { error } = await supabase
          .from('favorites')
          .delete()
          .eq('tool_id', toolId)
          .eq('user_id', session.user.id)

        if (error) throw error

        setIsFavorite(false)
        toast.success("Removed from favorites")
      } else {
        // Add to favorites
        const { error } = await supabase
          .from('favorites')
          .insert({
            tool_id: toolId,
            user_id: session.user.id,
          })

        if (error) throw error

        setIsFavorite(true)
        toast.success("Added to favorites")
      }
    } catch (error: any) {
      console.error("Error toggling favorite:", error)
      toast.error(error.message || "Failed to update favorites")
    } finally {
      setIsLoading(false)
    }
  }

  return (
    <Button
      variant="outline"
      size="icon"
      disabled={isLoading}
      onClick={toggleFavorite}
      title={isFavorite ? "Remove from favorites" : "Add to favorites"}
      className={isFavorite ? "text-red-500 border-red-200 dark:border-red-800" : ""}
    >
      <Heart className={`h-4 w-4 ${isFavorite ? "fill-current" : ""}`} />
    </Button>
  )
}
