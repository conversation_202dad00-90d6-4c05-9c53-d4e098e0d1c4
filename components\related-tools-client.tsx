"use client"

import { useState, useEffect } from "react"
import Link from "next/link"
import Image from "next/image"
import { createBrowserClient } from "@/lib/supabase/client-utils"

interface RelatedToolsClientProps {
  currentToolId: number
  category: string
}

export default function RelatedToolsClient({ currentToolId, category }: RelatedToolsClientProps) {
  const [tools, setTools] = useState<any[]>([])
  const [isLoading, setIsLoading] = useState(true)
  const [error, setError] = useState<string | null>(null)

  useEffect(() => {
    const fetchRelatedTools = async () => {
      setIsLoading(true)
      const supabase = createBrowserClient()

      try {
        const { data, error: supabaseError } = await supabase
          .from("tools")
          .select("id, company_name, logo_url, slug")
          .eq("primary_task", category)
          .neq("id", currentToolId)
          .limit(5)

        if (supabaseError) throw supabaseError

        setTools(data || [])
      } catch (err: any) {
        console.error("Error fetching related tools:", err.message)
        setError("Error loading related tools. Please try again later.")
      } finally {
        setIsLoading(false)
      }
    }

    fetchRelatedTools()
  }, [currentToolId, category])

  if (isLoading) {
    return (
      <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
        <h3 className="text-xl font-semibold mb-4">Related Tools</h3>
        <div className="animate-pulse space-y-3">
          {[...Array(3)].map((_, i) => (
            <div key={i} className="flex items-center gap-3">
              <div className="w-10 h-10 bg-slate-200 dark:bg-slate-700 rounded-md"></div>
              <div className="h-4 bg-slate-200 dark:bg-slate-700 rounded w-3/4"></div>
            </div>
          ))}
        </div>
      </div>
    )
  }

  if (error) {
    return null
  }

  if (!tools || tools.length === 0) {
    return null
  }

  return (
    <div className="bg-white dark:bg-slate-800 rounded-lg shadow-md p-6">
      <h3 className="text-xl font-semibold mb-4">Related Tools</h3>
      <div className="space-y-4">
        {tools.map((tool) => (
          <Link
            key={tool.id}
            href={`/Tool/${tool.slug}`}
            className="flex items-center gap-3 p-2 rounded-lg hover:bg-slate-100 dark:hover:bg-slate-700 transition-colors"
          >
            <div className="w-10 h-10 bg-slate-100 dark:bg-slate-700 rounded-md flex items-center justify-center">
              <Image
                src={tool.logo_url || "/placeholder.svg?height=40&width=40"}
                alt={tool.company_name}
                width={32}
                height={32}
                className="object-contain"
              />
            </div>
            <span className="font-medium">{tool.company_name}</span>
          </Link>
        ))}
      </div>
    </div>
  )
}
